{"name": "@ali/b2b-finance-web-dtao-xfd-uniapp", "version": "1.0.0", "description": "ice.js uniapp web scaffold", "dependencies": {"@ali/dialog": "^1.3.1", "@ali/iec-dtao-utils": "0.0.9", "@ali/uni-api": "^3.1.41", "@ali/universal-windvane": "^3.0.3", "@ali/wormhole-context": "^1.0.0", "@alife/dtao-iec-spm-log": "0.0.9", "@alife/fui-icon": "0.0.6", "@alipay/mayijie": "1.1.0", "@ice/runtime": "^1.0.0", "ahooks": "^3.8.1", "antd-mobile": "^5.37.1", "antd-mobile-icons": "^0.3.0", "classnames": "^2.5.1", "compressorjs": "^1.2.1", "lodash-es": "^4.17.21", "numeral": "^2.0.6", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@ali/build-plugin-pegasus-project": "^2.0.0", "@ali/eslint-config-att": "^1.0.6", "@ali/eslint-config-fin": "latest", "@ali/fin-lint": "^1.1.11", "@ali/ice-plugin-def": "^1.2.4", "@ali/ice-plugin-spm": "^3.0.1", "@ali/ice-plugin-uniapp": "^0.1.0", "@applint/spec": "^1.2.3", "@ice/app": "^3.0.0", "@ice/plugin-pha": "^3.0.4", "@types/lodash-es": "^4.17.12", "@types/node": "^18.11.17", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "body-parser": "^1.20.3", "eslint": "^6.8.0", "husky": "^0.14.3", "postcss-px-to-viewport-8-plugin": "^1.2.5", "stylelint": "^14.16.1", "typescript": "^4.9.5"}, "scripts": {"start": "ice start", "build": "ice build", "eslint": "eslint ./src --cache --ext .js,.jsx,.ts,.tsx", "eslint:fix": "npm run eslint -- --fix", "stylelint": "stylelint \"src/**/*.{css,scss,less}\" --cache", "stylelint:fix": "npm run stylelint -- --fix", "lint": "mad lint", "precommit": "mad lint --staged", "postcommit": "mad lint --postcommit"}, "publishConfig": {"registry": "http://registry.npm.alibaba-inc.com"}, "repository": "**************************:b2b-finance-web/dtao-xfd-uniapp.git"}