import { defineConfig } from '@ice/app';
import { PegasusPlugin } from '@ali/build-plugin-pegasus-project';
import uniapp from '@ali/ice-plugin-uniapp';
import spm from '@ali/ice-plugin-spm';

// The project config, see https://ice3.alibaba-inc.com/v3/docs/guide/basic/config
export default defineConfig({
  // Set your configs here.
  plugins: [
    PegasusPlugin({
      previewMode: 'local',
      documentOnly: true,
      npm: false,
    }),
    uniapp(),
    spm(),
  ],

  postcss: {
    plugins: [
      ['postcss-px-to-viewport-8-plugin',
        {
          unitToConvert: 'px',
          viewportWidth: 375,
          unitPrecision: 3,
          viewportUnit: 'vw',
        },
      ],
    ],
  },
  server: {
    // Wormhole only support server bundle with cjs format.
    format: 'cjs',
    ignores: [
      {
        resourceRegExp: /^uuid$/,
      },
      {
        resourceRegExp: /^axios$/,
      },
      {
        resourceRegExp: /^@alife\/fui-icon$/,
      },
      {
        resourceRegExp: /^@alipay\/xmas-fetch$/,
      },
    ],
    bundle: true,
  },
  routes: {
    defineRoutes: (route) => {
      route('/lp-ssd-simple', 'ssd/credit/lp-simple/index.tsx');
      route('/lp-ssr', 'credit/lp-ssr/index.tsx');
      route('/lp-ssd-ssr', 'ssd/credit/lp-ssr/index.tsx');
      route('/lp-simple', 'credit/lp-simple/index.tsx');
      route('/ssd-agreement-sandbox', 'ssd/agreement-sandbox/index.tsx');
      route('/', 'layout-ssd.tsx', () => {
        // 首支一体页
        route('/ssd-home-plus', 'ssd/home-plus/index.tsx');

        // 支用页面
        route('/ssd-loan-apply', 'ssd/loan/apply/index.tsx');
        route('/ssd-loan-result', 'ssd/loan/result/index.tsx');
        route('/ssd-bank-list', 'ssd/loan/bank-list/index.tsx');
        route('/ssd-credit-lp', 'ssd/credit/lp/index.tsx');
        route('/ssd-home', 'ssd/home/<USER>');
        route('/ssd-center', 'ssd/center/index.tsx');
        route('/ssd-my-quota', 'ssd/center/my-quota/index.tsx');
        route('/ssd-signed-agreements', 'ssd/center/signed-agreements/index.tsx');
        route('/ssd-coupon', 'ssd/center/coupon/index.tsx');
        route('/ssd-invalid-coupon', 'ssd/center/invalid-coupon/index.tsx');

        // 还款页面
        route('/ssd-loan-contract', 'ssd/repay/loan-contract/index.tsx');
        route('/ssd-repay-bill', 'ssd/repay/bill/index.tsx');
        route('/ssd-repay-batch-apply', 'ssd/repay/batch-apply/index.tsx');
        route('/ssd-repay-early-apply', 'ssd/repay/early-apply/index.tsx');
        route('/ssd-repay-plan', 'ssd/repay/plan/index.tsx');
        route('/ssd-repay-result', 'ssd/repay/result/index.tsx');

        // 关闭页面
        route('/ssd-close-home', 'ssd/close/home/<USER>');
      });
      route('/', 'layout.tsx', () => {
        route('/credit-fallback', 'credit/fallback/index.tsx');
        route('/inst-list', 'credit/inst-list/index.tsx');
        route('/credit-identity', 'credit/identity/index.tsx');
        route('/credit-new-identity', 'credit/new-identity/index.tsx');
        route('/credit-institution', 'credit/institution/index.tsx');
        route('/credit-lp', 'credit/lp/index.tsx');
        route('/credit-result', 'credit/result/index.tsx');
        route('/credit-sign', 'credit/sign/index.tsx');

        route('/loan-apply', 'loan/apply/index.tsx');
        route('/loan-result', 'loan/result/index.tsx');
        route('/loan-sign', 'loan/sign/index.tsx');

        route('/repay-early-apply', 'repay/early-apply/index.tsx');
        route('/repay-bill', 'repay/bill/index.tsx');
        route('/repay-batch-apply', 'repay/batch-apply/index.tsx');
        route('/loan-contract', 'repay/loan-contract/index.tsx');
        route('/repay-plan', 'repay/plan/index.tsx');
        route('/repay-result', 'repay/result/index.tsx');

        route('/record-list', 'record/list/index.tsx');
        route('/loan-detail', 'record/loan-detail/index.tsx');
        route('/repay-detail', 'record/repay-detail/index.tsx');

        route('/cert-manage', 'center/cert-manage/index.tsx');
        route('/cert-update', 'center/cert-update/index.tsx');
        route('/phone-update', 'center/phone-update/index.tsx');
        route('/profile-sign', 'center/profile-sign/index.tsx');
        route('/signed-agreements', 'center/signed-agreements/index.tsx');
        route('/signed-agreement-preview', 'center/signed-agreement-preview/index.tsx');
        route('/quota-rate', 'center/quota-rate/index.tsx');
        route('/bind-card', 'center/bind-card/index.tsx');
        route('/discount-coupon', 'center/discount-coupon/index.tsx');
        route('/invalid-coupon', 'center/invalid-coupon/index.tsx');
        route('/phone-manage', 'center/phone-manage/index.tsx');
        route('/bank-card', 'center/bank-card/index.tsx');
        route('/close-home', 'center/close-home/index.tsx');

        route('/home', 'home/index.tsx');
        route('/index', 'index/index.tsx');
      });
    },
    ignoreFiles: ['**/components/**', '**/**'],
  },
  codeSplitting: 'page',
  ssg: false,
  ssr: false,
});
