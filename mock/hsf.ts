/**
 * @file 动态mock
 */

import type { Request, Response } from '@ice/app';
import bodyParser from 'body-parser';

export default {
  'POST /hsf/com.alibaba.fin.tao.blp.customer.mtop.api.AgreementQueryMtopFacade': (
    request: Request,
    response: Response,
  ) => {
    bodyParser.json()(request, response, () => {
      const { method, args } = request.body;
      const { creditPlatform, bizType } = args[0];
      if (method === 'queryUnSignAgreementList') {
        if (creditPlatform === 'MAYI_ZHIXIN' && bizType === 'PLATFORM') {
          response.send({
            headers: {},
            success: true,
            model: {
              unSignedAgreementGroupList: [
                {
                  source: 'AGREEMENT_CENTER',
                  unSignedAgreementList: [
                    {
                      code: 'XFD_SERVICE_AGREEMENT',
                      forceRead: false,
                      name: '借钱服务协议',
                      source: 'AGREEMENT_CENTER',
                      minReadTime: 0,
                      order: 0,
                    },
                    {
                      code: 'XFD_INDIVIDUAL_INFO_AUTH',
                      name: '借钱服务个人信息处理规则',
                      source: 'AGREEMENT_CENTER',
                      order: 0,
                    },
                  ],
                },
                {
                  institution: 'MAYI_ZHIXIN',
                  extension: {
                    passthroughInfo: {
                      agreementExposeAuth: {
                        forceRead: false,
                        agreementSignScene: {
                          exposeScene: 'SIDELOAN_PLATFORM_NEW_SIGN',
                          agreementSignList: [
                            {
                              agreementType: 'SIDELOAN_USER_AGREEMENT',
                              mandatoryReadingTime: 0,
                              forceRead: false,
                              agreementTemplateCode: 'SIDELOAN_USER_AGREEMENT#MYZX#1',
                              agreementName: '个人消费信贷综合服务协议',
                              contractTemplateCode: 'SIDELOAN_USER_AGREEMENT',
                              contractVersion: '2.0004',
                            },
                            {
                              agreementType: 'SIDELOAN_SIGNATURE',
                              mandatoryReadingTime: 0,
                              forceRead: false,
                              agreementTemplateCode: 'SIDELOAN_SIGNATURE#MYZX#1',
                              agreementName: '签章与存证协议',
                              contractTemplateCode: 'SIDELOAN_SIGNATURE',
                              contractVersion: '2.0003',
                            },
                            {
                              agreementType: 'SIDELOAN_INFORMATION_AUTHZ',
                              mandatoryReadingTime: 0,
                              forceRead: false,
                              agreementTemplateCode: 'SIDELOAN_INFORMATION_AUTHZ#MYZX#1',
                              agreementName: '个人信息处理规则',
                              contractTemplateCode: 'SIDELOAN_INFORMATION_AUTHZ',
                              contractVersion: '2.0003',
                            },
                          ],
                          signScene: 'AUTH',
                        },
                        contractSignCacheKey:
                          'newExposeCacheagreementTemplateList_SIDELOAN_PLATFORM_NEW_SIGN_1735044200594_2088002006449906_2873712693654408962',
                      },
                    },
                  },
                  source: 'MAYI_ZHIXIN',
                  unSignedAgreementList: [
                    {
                      code: 'SIDELOAN_USER_AGREEMENT',
                      forceRead: false,
                      name: '个人消费信贷综合服务协议',
                      source: 'MAYI_ZHIXIN',
                      version: 'SIDELOAN_USER_AGREEMENT-2.0004',
                      minReadTime: 0,
                      order: 0,
                    },
                    {
                      code: 'SIDELOAN_SIGNATURE',
                      forceRead: false,
                      name: '签章与存证协议',
                      source: 'MAYI_ZHIXIN',
                      version: 'SIDELOAN_SIGNATURE-2.0003',
                      minReadTime: 0,
                      order: 0,
                    },
                    {
                      code: 'SIDELOAN_INFORMATION_AUTHZ',
                      forceRead: false,
                      name: '个人信息处理规则',
                      source: 'MAYI_ZHIXIN',
                      version: 'SIDELOAN_INFORMATION_AUTHZ-2.0003',
                      minReadTime: 0,
                      order: 0,
                    },
                  ],
                },
              ],
              '@type':
                'com.alibaba.fin.tao.blp.customer.mtop.api.response.UnSignedAgreementListQueryResponse',
            },
            '@type': 'com.taobao.mtop.common.Result',
            httpStatusCode: 200,
          });
        } else if (bizType === 'PLATFORM_AND_CREDIT') {
          response.send({
            headers: {},
            success: true,
            model: {
              unSignedAgreementGroupList: [
                {
                  source: 'AGREEMENT_CENTER',
                  bizType: 'PLATFORM',
                  unSignedAgreementList: [
                    {
                      code: 'SIDELOAN_DEDUCT_AUTHZ',
                      forceRead: false,
                      minReadTime: 0,
                      name: '淘天平台协议1',
                      order: 0,
                      source: 'MAYI_ZHIAGREEMENT_CENTERXIN',
                      version: 'SIDELOAN_DEDUCT_AUTHZ-2.0001',
                    },
                    {
                      code: 'SIDELOAN_LOAN_AGREEMENT',
                      forceRead: false,
                      minReadTime: 0,
                      name: '淘天平台协议2',
                      order: 0,
                      source: 'AGREEMENT_CENTER',
                      version: 'SIDELOAN_LOAN_AGREEMENT_WMXT-2.0001',
                    },
                  ],
                },
                {
                  extension: {
                    isDeductAlipayAgreementExist: true,
                    isCreditAgreementExist: false,
                    passthroughInfo: {
                      agreementExposeAuth: {
                        forceRead: false,
                        agreementSignScene: {
                          exposeScene: 'SIDELOAN_LOAN',
                          agreementSignList: [
                            {
                              agreementType: 'SIDELOAN_DEDUCT_AUTHZ',
                              mandatoryReadingTime: 0,
                              forceRead: false,
                              agreementTemplateCode: 'SIDELOAN_DEDUCT_AUTHZ#MYZX#1',
                              agreementName: '付款授权服务协议',
                              contractTemplateCode: 'SIDELOAN_DEDUCT_AUTHZ',
                              contractVersion: '2.0001',
                            },
                            {
                              agreementType: 'SIDELOAN_LOAN_AGREEMENT',
                              institution: {
                                code: 'WMXT',
                                fssuCode: 'J1010100091000020855',
                              },
                              mandatoryReadingTime: 0,
                              forceRead: false,
                              agreementTemplateCode: 'SIDELOAN_LOAN_AGREEMENT#WMXT#1',
                              agreementName: '借据',
                              contractTemplateCode: 'SIDELOAN_LOAN_AGREEMENT_WMXT',
                              contractVersion: '2.0001',
                            },
                          ],
                          signScene: 'LOAN',
                        },
                        contractSignCacheKey:
                          'newExposeCacheagreementTemplateList_SIDELOAN_LOAN_1730720361604_2088602335623141_7971305165601568663',
                      },
                    },
                  },
                  institution: 'MAYI_ZHIXIN',
                  source: 'MAYI_ZHIXIN',
                  bizType: 'PLATFORM',
                  unSignedAgreementList: [
                    {
                      code: 'SIDELOAN_DEDUCT_AUTHZ',
                      forceRead: false,
                      minReadTime: 0,
                      name: '智信平台协议1',
                      order: 0,
                      source: 'MAYI_ZHIXIN',
                      version: 'SIDELOAN_DEDUCT_AUTHZ-2.0001',
                    },
                    {
                      code: 'SIDELOAN_LOAN_AGREEMENT',
                      forceRead: false,
                      minReadTime: 0,
                      name: '智信平台协议2',
                      order: 0,
                      source: 'MAYI_ZHIXIN',
                      version: 'SIDELOAN_LOAN_AGREEMENT_WMXT-2.0001',
                    },
                    {
                      code: 'SIDELOAN_LOAN_AGREEMENT',
                      forceRead: false,
                      minReadTime: 0,
                      name: '智信平台协议3',
                      order: 0,
                      source: 'MAYI_ZHIXIN',
                      version: 'SIDELOAN_LOAN_AGREEMENT_WMXT-2.0001',
                    },
                  ],
                },
                {
                  extension: {
                    isDeductAlipayAgreementExist: true,
                    isCreditAgreementExist: false,
                    fundSupplierCode: 'NBBANK',
                    passthroughInfo: {
                      creditInstConsult: {
                        bb: 1,
                      },
                      agreementExposeCredit: {
                        forceRead: false,
                        agreementSignScene: {
                          exposeScene: 'SIDELOAN_LOAN',
                          agreementSignList: [
                            {
                              agreementType: 'SIDELOAN_DEDUCT_AUTHZ',
                              mandatoryReadingTime: 0,
                              forceRead: false,
                              agreementTemplateCode: 'SIDELOAN_DEDUCT_AUTHZ#MYZX#1',
                              agreementName: '付款授权服务协议',
                              contractTemplateCode: 'SIDELOAN_DEDUCT_AUTHZ',
                              contractVersion: '2.0001',
                            },
                            {
                              agreementType: 'SIDELOAN_LOAN_AGREEMENT',
                              institution: {
                                code: 'WMXT',
                                fssuCode: 'J1010100091000020855',
                              },
                              mandatoryReadingTime: 0,
                              forceRead: false,
                              agreementTemplateCode: 'SIDELOAN_LOAN_AGREEMENT#WMXT#1',
                              agreementName: '借据',
                              contractTemplateCode: 'SIDELOAN_LOAN_AGREEMENT_WMXT',
                              contractVersion: '2.0001',
                            },
                          ],
                          signScene: 'LOAN',
                        },
                        contractSignCacheKey:
                          'newExposeCacheagreementTemplateList_SIDELOAN_LOAN_1730720361604_2088602335623141_7971305165601568663',
                      },
                    },
                  },
                  institution: 'MAYI_ZHIXIN',
                  source: 'MAYI_ZHIXIN',
                  bizType: 'CREDIT',
                  unSignedAgreementList: [
                    {
                      code: 'SIDELOAN_DEDUCT_AUTHZ',
                      forceRead: false,
                      minReadTime: 0,
                      name: '智信授信协议1',
                      order: 0,
                      source: 'MAYI_ZHIXIN',
                      version: 'SIDELOAN_DEDUCT_AUTHZ-2.0001',
                    },
                    {
                      code: 'SIDELOAN_LOAN_AGREEMENT',
                      forceRead: false,
                      minReadTime: 0,
                      name: '智信授信协议2',
                      order: 0,
                      source: 'MAYI_ZHIXIN',
                      version: 'SIDELOAN_LOAN_AGREEMENT_WMXT-2.0001',
                    },
                    {
                      code: 'SIDELOAN_LOAN_AGREEMENT',
                      forceRead: false,
                      minReadTime: 0,
                      name: '智信授信协议3',
                      order: 0,
                      source: 'MAYI_ZHIXIN',
                      version: 'SIDELOAN_LOAN_AGREEMENT_WMXT-2.0001',
                    },
                    {
                      code: 'SIDELOAN_LOAN_AGREEMENT',
                      forceRead: false,
                      minReadTime: 0,
                      name: '智信授信协议4',
                      order: 0,
                      source: 'MAYI_ZHIXIN',
                      version: 'SIDELOAN_LOAN_AGREEMENT_WMXT-2.0001',
                    },
                    {
                      code: 'SIDELOAN_LOAN_AGREEMENT',
                      forceRead: false,
                      minReadTime: 0,
                      name: '智信授信协议5',
                      order: 0,
                      source: 'MAYI_ZHIXIN',
                      version: 'SIDELOAN_LOAN_AGREEMENT_WMXT-2.0001',
                    },
                  ],
                },
              ],
            },
            '@type': 'com.taobao.mtop.common.Result',
            httpStatusCode: 200,
          });
        } else {
          response.send({
            headers: {},
            success: true,
            model: {
              unSignedAgreementGroupList: [
                {
                  source: 'AGREEMENT_CENTER',
                  unSignedAgreementList: [
                    {
                      code: 'XFD_SERVICE_AGREEMENT',
                      forceRead: false,
                      name: '借钱服务协议',
                      source: 'AGREEMENT_CENTER',
                      minReadTime: 0,
                      order: 0,
                    },
                    {
                      code: 'XFD_INDIVIDUAL_INFO_AUTH',
                      name: '借钱服务个人信息处理规则',
                      source: 'AGREEMENT_CENTER',
                      order: 0,
                    },
                  ],
                },
              ],
              '@type':
                'com.alibaba.fin.tao.blp.customer.mtop.api.response.UnSignedAgreementListQueryResponse',
            },
            '@type': 'com.taobao.mtop.common.Result',
            httpStatusCode: 200,
          });
        }
      }
    });
  },
};
