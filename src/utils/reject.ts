/**
 * @file 获取异常信息
 */

import { get } from 'lodash-es';
import { ErrorMessageMap } from '@/common/constant';
import { log } from '@alife/dtao-iec-spm-log';

export function getRejectReason(data?: any) {
  try {
    const rejectCode = get(data, 'rejectReason.rejectCode');
    const rejectMessage = ErrorMessageMap[rejectCode] || ErrorMessageMap.BUSY_DEFUALT;
    return rejectMessage;
  } catch (e) {
    return ErrorMessageMap.BUSY_DEFUALT;
  }
}

export function addRejectLog(data?: any) {
  try {
    const admitted = get(data, 'admitted');
    if (admitted) {
      log.addErrorLog('loan-trial-front-check');
    }
  } catch (e) {}
}
