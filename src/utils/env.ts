/**
 * @file 环境
 */

import { env } from '@ali/iec-dtao-utils';
import { Device } from '@ali/uni-api';

export function isHarmonyOS() {
  try {
    const deviceInfo = Device.getInfo();
    return deviceInfo?.platform === 'HarmonyOS';
  } catch (e) {
    return false;
  }
}

export function isAndroidOrisHarmonyOS() {
  return isHarmonyOS() || env.isAndroid();
}

export function isDev() {
  try {
    return process.env.ICE_CORE_MODE === 'development';
  } catch (e) {
    return false;
  }
}
