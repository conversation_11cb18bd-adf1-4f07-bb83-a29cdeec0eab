/**
 * @file session storage
 */

import { SessionKVStorage } from '@ali/uni-api';

const SSD_PUSH_PAGE_SESSION = 'SSD_PUSH_PAGE_SESSION';

export function setPushPageSession() {
  try {
    window.sessionStorage.setItem(SSD_PUSH_PAGE_SESSION, 'true');
  } catch (e) {}
}

export function removePushPageSession() {
  try {
    window.sessionStorage.removeItem(SSD_PUSH_PAGE_SESSION);
  } catch (e) {}
}

export function removePushPage() {
  try {
    SessionKVStorage.removeItem({
      key: SSD_PUSH_PAGE_SESSION,
    });
  } catch (e) {
    removePushPageSession();
  }
}

export function setPushPage() {
  try {
    SessionKVStorage.setItem({
      key: SSD_PUSH_PAGE_SESSION,
      value: 'true',
    });
  } catch (e) {
    setPushPageSession();
  }
}

export function getPushPageSession() {
  try {
    return window.sessionStorage.getItem(SSD_PUSH_PAGE_SESSION);
  } catch (e) {
    return '';
  }
}

export function getPushPage() {
  try {
    const res = SessionKVStorage.getItem({
      key: SSD_PUSH_PAGE_SESSION,
    });
    return res;
  } catch (e) {
    return getPushPageSession();
  }
}

export function checkPushPage() {
  try {
    const isPushPage = getPushPage();
    if (isPushPage === 'true') {
      return true;
    }
    return false;
  } catch (e) {
    return false;
  }
}


export function getOriginFromSessionStorage() {
  try {
    return window.localStorage.getItem('IEC_DTAO_ORIGIN_STORAGE');
  } catch (e) {
    return null;
  }
}
