/**
 * @file 一些特殊的拦截逻辑
 */

import { navigatorToOutside } from './link';

// 劫持a标签跳转事件
export function handleATarget(id: string) {
  const compulsoryContainer = document.getElementById(id);
  if (compulsoryContainer) {
    compulsoryContainer.addEventListener('click', (e) => {
      e.preventDefault();
      const target = e?.target;
      // @ts-ignore
      if (target && target?.localName === 'a') {
        // 获取到a标签上的链接
      // @ts-ignore
        const url = target?.getAttribute('href');
        if (url) {
          navigatorToOutside(url);
        }
      }
    });
  }
}

export function loadAddressScript(onReady: () => void) {
  const script = document.createElement('script');
  script.type = 'text/javascript';
  script.src = '//g.alicdn.com/b2b-finance-web/dtao-iec-assets/0.0.2/json/address.js';
  document.body.appendChild(script);
  script.onload = () => {
    onReady();
  };
}
