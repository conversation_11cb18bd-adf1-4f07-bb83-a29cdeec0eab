/**
 * @file SSR兼容的公共方法
 */

import { last } from 'lodash-es';

export function getPathname() {
  try {
    return last(location.pathname.split('/')) || '';
  } catch (e) {
    return '';
  }
}

export function getQueryParams() {
  try {
    const queryString = location.search;
    const params = {};
    const pairs = (queryString[0] === '?' ? queryString.substr(1) : queryString).split('&');
    for (let i = 0; i < pairs.length; i++) {
      const pair = pairs[i].split('=');
      params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1] || '');
    }
    return params;
  } catch (e) {
    return {};
  }
}
