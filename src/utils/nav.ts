/**
 * @file navBar处理方法
 */

import { NavBar } from '@ali/uni-api';

export async function onScroll() {
  window.addEventListener('scroll', async () => {
    const scrollTop = window.scrollY || window.pageYOffset;
    if (scrollTop > 100) {
      await NavBar.setBgColor({
        color: '#ffe3d2',
      });
    } else {
      await NavBar.setBgColor({
        color: 'rgba(0,0,0,0)',
      });
    }
  });
}

export async function setNavBgColor({ color = '#fff' }) {
  try {
    await NavBar.setBgColor({
      color,
    });
  } catch (e) {}
}

export async function setImmersiveFalse() {
  try {
    await NavBar.setImmersive({
      immersive: false,
    });
  } catch (e) {}
}


export async function handleSetPageTitleImg() {
  try {
    await NavBar.setTitleImage({
      url: 'https://gw.alicdn.com/imgextra/i3/O1CN01o1lBeN1M5SlR1r7Fl_!!6000000001383-2-tps-138-66.png',
    });
  } catch (error) {}
}
