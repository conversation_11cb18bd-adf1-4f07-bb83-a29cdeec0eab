// @ts-nocheck
/* eslint-disable */
import Compressor from 'compressorjs';
import { getTimeStamp, _ } from '@/utils';
import WindVane from '@ali/universal-windvane';
import { Modal } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';

export const takePhotonWinVane = (
  callback: Function,
  errorCallBack: Function,
  mode = 'both'
): void => {
  const params = {
    // 安卓端0标识不自动上传，IOS0标识不自动上传
    type: '0',
    // 表示调起照相机
    mode,
    // 符合工信部要求，减少不必要的授权
    reducePermission: true,
    // 图片按base64/png上传服务器，需要返回base64的拍摄照片资源
    needBase64: true,
    // 是否压缩，一般开启
    needZoom: true,
  };
  WindVane.call(
    'WVCamera',
    'takePhoto',
    params,
    (photo: any) => {
      // 图片对象是空，或者没有base64Data数据，如果是取消图片上传，也是这里
      if (_.isEmpty(photo) || !_.get(photo, 'base64Data')) {
        callback({
          empty: true,
        });
        return;
      }
      const fileName = `${getTimeStamp()}_temp_camera.jpeg`;
      const fileBase64Data = `data:image/jpeg;base64,${photo.base64Data}`;
      const fileObj = base64toFileForWinVane(fileBase64Data, fileName);
      callback({
        target: {
          files: [fileObj],
        },
      });
    },
    (e: any) => {
      // 用户取消操作，不报警
      errorCallBack(e);
      log.addErrorLog('takePhotoWinVaneError', e);
    }
  );
};

export const removeHttpSchema = (url = '') => url.replace(/^https?:\/\//, '//');

export const temp2normal = (url: string) => {
  const currentUrl = url ? url.replace(/^(OSS:)*/i, '') : '';
  if (!currentUrl) {
    return null;
  }
  const normalUrl = `OSS:${currentUrl}`;
  const index = normalUrl.indexOf('?');
  if (index === -1) {
    return normalUrl;
  }
  return normalUrl.slice(0, index);
};

export const compressor = async (file: File): Promise<File> => {
  return new Promise((resolve) => {
    new Compressor(file, {
      strict: false,
      maxWidth: 2000,
      maxHeight: 2000,
      convertSize: 5000000,
      checkOrientation: false,
      success(result) {
        const fileName = _.get(result, 'name') || _.get(file, 'name');
        const fileType = _.get(result, 'type') || _.get(file, 'type');
        const newFile = new File([result], fileName, {
          type: fileType,
        });
        resolve(newFile);
      },
      error() {
        resolve(file);
      },
    });
  });
};

export const base64toFileForWinVane = (base: string, filename: string): File => {
  const arr = base.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], filename, { type: mime });
};
