/**
 * @file 公共跳转方法
 */

import { Navigator, Router } from '@ali/uni-api';
import { genFullUrl, genFullPageId } from './index';
import { PAGES } from '@/common/constant';

export async function resetToPage(pathName, query?) {
  const url = genFullUrl(pathName, query);
  const pageID = genFullPageId(pathName, query);
  try {
    await Router.resetToPage({
      pageID,
    });
  } catch (e) {
    location.href = url;
  }
}

export async function replacePage(pathName, query?) {
  const url = genFullUrl(pathName, query);
  const pageID = genFullPageId(pathName, query);
  try {
    await Router.replacePage({
      pageID,
    });
  } catch (e) {
    window.location.replace(url);
  }
}

export async function pushPage(pathName, query?) {
  const url = genFullUrl(pathName, query);
  const pageID = genFullPageId(pathName, query);
  try {
    await Router.pushPage({
      pageID,
    });
  } catch (e) {
    location.href = url;
  }
}

export async function navigatorOpenURL(pathName, query?) {
  const url = genFullUrl(pathName, query);
  try {
    await Navigator.openURL({
      url,
    });
  } catch (e) {
    location.href = url;
  }
}

export function navigatorToAlipay(link) {
  if (!link) return;
  if (link.startsWith('alipays://')) {
    openExternalURL(link);
  } else {
    navigatorOpenURL(link);
    // openExternalURL(
    //   `alipays://platformapi/startapp?appId=20000067&url=${encodeURIComponent(link)}`,
    // );
  }
}

export async function openExternalURL(url?: string) {
  if (!url) {
    return;
  }
  try {
    await Navigator.openExternalURL({
      url,
    });
  } catch (e) {
    location.href = url;
  }
}

export async function navigatorToOutside(url) {
  try {
    await Navigator.openURL({
      url,
    });
  } catch (e) {
    location.href = url;
  }
}

export async function locationReplace(pathName, query?) {
  window.location.replace(genFullUrl(pathName, query));
}

export async function popPage() {
  try {
    // 可能会被降级
    await Router.popPage();
  } catch (e) {
    window.history.back();
  }
}

export function toLoanSign(id?: string) {
  replacePage(PAGES.LoanSign, { id });
}

export function toLoanResult(id?: string) {
  replacePage(PAGES.LoanResult, { id });
}

export function toSsdLoanResult(id?: string) {
  replacePage(PAGES.SsdLoanResult, { id });
}

export function toLoanBindCard() {
  pushPage(PAGES.BindCard);
}

export function toHome() {
  resetToPage(PAGES.Home);
}

export function toSsdHome() {
  resetToPage(PAGES.SsdHome);
}

export function resetToSsdHomePlus() {
  resetToPage(PAGES.SsdHomePlus);
}

export function toSsdHomePlus() {
  locationReplace(PAGES.SsdHomePlus);
}

export function toIndex() {
  resetToPage('index');
}

export function reload() {
  window.location.reload();
}

export function toSsdLoanApply() {
  Router.pushPage({
    pageID: 'ssd-loan-apply',
  });
}

export default {
  resetToPage,
  replacePage,
  locationReplace,
  pushPage,
  popPage,
  navigatorOpenURL,
  navigatorToOutside,
  navigatorToAlipay,
  toLoanSign,
  toLoanResult,
  toLoanBindCard,
  toHome,
  reload,
  toSsdLoanApply,
  toSsdHomePlus,
  resetToSsdHomePlus,
};
