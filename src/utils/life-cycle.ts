/**
 * @file 页面生命周期方法
 * <AUTHOR>
 */

export type lifeCycleHandler = () => void;

export function visibilityChange(onVisible: lifeCycleHandler, onHide?: lifeCycleHandler) {
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
      onVisible();
    } else {
      onHide && onHide();
    }
  });
  return () => {
    document.removeEventListener('visibilitychange', () => {});
  };
}

export function resize(onReduce: lifeCycleHandler, onReset: lifeCycleHandler) {
  const client_h = document.documentElement.clientHeight;
  window.addEventListener('resize', () => {
    const body_h = document.documentElement.clientHeight;
    if (client_h > body_h) {
      onReduce();
    } else {
      onReset();
    }
  });
  return () => {
    window.removeEventListener('resize', () => {});
  };
}
