/**
 * @file storage管理
 */

import { get, isNumber } from 'lodash-es';
import { getTimeStamp } from './index';

export function setOneConfigStore(payload?: any) {
  try {
    const XFD_UNIAPP_ONE_CONFIG = 'XFD_UNIAPP_ONE_CONFIG';
    if (window?.localStorage) {
      window.localStorage.setItem(XFD_UNIAPP_ONE_CONFIG, JSON.stringify(payload));
    }
  } catch (e) {}
}

export function getOneConfigStore() {
  try {
    const XFD_UNIAPP_ONE_CONFIG = 'XFD_UNIAPP_ONE_CONFIG';
    if (window?.localStorage) {
      const strRes = window.localStorage.getItem(XFD_UNIAPP_ONE_CONFIG);
      if (strRes) {
        return JSON.parse(strRes);
      }
      return null;
    }
    return null;
  } catch (e) {
    return null;
  }
}

export function setCreditConsultStore(payload?: any) {
  try {
    const XFD_UNIAPP_CREDIT_CONSULT = 'XFD_UNIAPP_CREDIT_CONSULT';
    if (window?.localStorage) {
      window.localStorage.setItem(XFD_UNIAPP_CREDIT_CONSULT, JSON.stringify(payload));
    }
  } catch (e) {}
}

export function getCreditConsultStore() {
  try {
    const XFD_UNIAPP_CREDIT_CONSULT = 'XFD_UNIAPP_CREDIT_CONSULT';
    if (window?.localStorage) {
      const strRes = window.localStorage.getItem(XFD_UNIAPP_CREDIT_CONSULT);
      if (strRes) {
        return JSON.parse(strRes);
      }
      return null;
    }
    return null;
  } catch (e) {
    return null;
  }
}

export function checkOneConfigGray(key: string) {
  try {
    const oneConfigStore = getOneConfigStore();
    return get(oneConfigStore, `gray.${key}`);
  } catch (e) {
    return false;
  }
}

export function setAmountValue(value?: string) {
  try {
    if (window?.localStorage) {
      window.localStorage.setItem('XFD_UNIAPP_AMOUNT_VALUE', JSON.stringify({
        value,
        time: getTimeStamp(),
      }));
    }
  } catch (e) {}
}

export function getAmountValue() {
  try {
    if (window?.localStorage) {
      const valueStr = window.localStorage.getItem('XFD_UNIAPP_AMOUNT_VALUE');
      if (valueStr) {
        const currentTime = getTimeStamp();
        const data = JSON.parse(valueStr);
        if (data) {
          const { value, time } = data;
          if (time && currentTime) {
            if (currentTime - time > 2 * 60 * 60 * 1000) {
              return null;
            }
            return value;
          }
        }
      }
    }
    return null;
  } catch (e) {
    return null;
  }
}

export function setRouterTimeStore() {
  try {
    const XFD_UNIAPP_ONE_CONFIG = 'XFD_UNIAPP_ROUTER_TIME_STORAGE';
    if (window?.localStorage) {
      window.localStorage.setItem(XFD_UNIAPP_ONE_CONFIG, JSON.stringify({
        routerTime: getTimeStamp(),
      }));
    }
  } catch (e) {}
}

export function getRouterDuration() {
  try {
    const XFD_UNIAPP_ONE_CONFIG = 'XFD_UNIAPP_ROUTER_TIME_STORAGE';
    if (window?.localStorage) {
      const routeStoreStr = window.localStorage.getItem(XFD_UNIAPP_ONE_CONFIG);
      if (routeStoreStr) {
        const routerStore = JSON.parse(routeStoreStr);
        if (routerStore?.routerTime && isNumber(routerStore?.routerTime)) {
          return getTimeStamp() - routerStore?.routerTime;
        }
      }
    }
    return '';
  } catch (e) {
    return '';
  }
}
