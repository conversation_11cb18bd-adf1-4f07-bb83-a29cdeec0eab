import { aes, log } from '@alife/dtao-iec-spm-log';
import { App } from '@ali/uni-api';
import { _ } from '@/utils';
import { isDev } from '@/utils/env';

/**
 * 初始化 AES 埋点
 * 获取环境信息并初始化 aes
 */
export async function initAes() {
  let envType;
  if (isDev()) {
    envType = 'dev';
  } else {
    try {
      // 获取应用信息
      const appInfo = await App.getInfo();
      envType = _.get(
        {
          release: 'prod',
          pre: 'pre',
          daily: 'daily',
          dev: 'dev',
        },
        appInfo?.env || 'release'
      );
      // 可枚举的值列表
      // release 线上环境
      // pre 预发环境
      // daily 日常环境
    } catch (error) {
      log.addOtherLog('aes-error', error);
    }
  }
  // 初始化 aes
  // 数据环境，用于区分不同的系统环境，仅支持：prod（正式）、grey（灰度）、pre（预发）、daily（日常）、dev（本地开发），请不要自由发挥

  return aes.init({
    pid: 'YFcRiF',
    user_type: '0',
    env: envType,
  });
}
