/**
 * @file GOC预警埋点
 */

import { PAGES, CREDIT_PLATFORM } from '@/common/constant';
import { log } from '@alife/dtao-iec-spm-log';
import { includes, get } from 'lodash-es';
import { getQueryParams } from './index';
import { getRouterDuration } from './storage';

interface RouterPageLogOptions {
  creditConsultData?: any;
  routerPage?: any;
}

// 路由页面日志
// 蚂蚁总量：所有/-visit&creditPlatform=MAYI_ZHIXIN日志就是
// 蚂蚁成功率：success=true日志量/总量
// 蚂蚁失败量：success=false日志量
// 蚂蚁成功量：success=true日志量
// 阿里云总量：所有/-visit&creditPlatform=MAYI_ZHIXIN日志就是
// 阿里云成功率：success=true日志量/总量
// 阿里云失败量：success=false日志量
// 阿里云成功量：success=true日志量
export function routerPageLog(options: RouterPageLogOptions) {
  try {
    const { routerPage } = options;
    const creditPlatform = options?.creditConsultData?.creditPlatform;
    let success = false;
    if (routerPage && includes(CREDIT_PLATFORM, creditPlatform)) {
      success = true;
    }
    log.addVisitLog(PAGES.Index, {
      // 路由页面
      routerPage,
      // 授信平台
      creditPlatform,
      // 是否路由成功
      success,
    });
  } catch (e) {}
}

interface LpPageLogOptions {
  creditConsultData?: any;
  routerPage?: any;
}

// 阿里云LP页面异常
// 阿里云总量：所有credit-lp-visit
// 阿里云成功率：success=true日志量/总量
// 阿里云失败量：success=false日志量
// 阿里云成功量：success=true日志量
export function lpAliyunPageLog(options: LpPageLogOptions) {
  try {
    let success = false;
    const { creditConsultData = {}, routerPage } = options;
    const { admitted, canCreditApply } = creditConsultData;
    const routerDuration = getRouterDuration();
    if (routerPage === PAGES.CreditLpSSR || routerPage === PAGES.CreditSimpleLp) {
      success = true;
    }
    // 曝光埋点
    log.addVisitLog(PAGES.CreditLp, {
      admitted,
      canCreditApply,
      success,
      routerDuration,
    });
  } catch (e) {}
}

// 蚂蚁智信LP页面异常
// 蚂蚁智信总量：所有lp-ssd-simple-visit
// 蚂蚁智信成功率：success=true日志量/总量
// 蚂蚁智信失败量：success=false日志量
// 蚂蚁智信成功量：success=true日志量
export function lpMayizhixinPageLog(options: LpPageLogOptions) {
  try {
    let success = false;
    const { creditConsultData = {}, routerPage } = options;
    const { admitted, canCreditApply } = creditConsultData;
    const routerDuration = getRouterDuration();
    if (routerPage === PAGES.SsdCreditLpSimple) {
      success = true;
    }
    // 曝光埋点
    log.addVisitLog(PAGES.SsdCreditLpSimple, {
      admitted,
      canCreditApply,
      success,
      routerDuration,
    });
  } catch (e) {}
}

interface HomePageOptions {
  shemaRes?: any;
  routerPage?: string;
  message?: string;
}

// 阿里云home页面异常
// 阿里云总量：所有home-visit
// 阿里云成功率：success=true日志量/总量
// 阿里云失败量：success=false日志量
// 阿里云成功量：success=true日志量
export function homeAliyunPageLog(options: HomePageOptions) {
  try {
    const toParam = get(getQueryParams(), 'to');
    const { shemaRes, message } = options;
    const routerDuration = getRouterDuration();
    let success = false;
    if (shemaRes) {
      success = true;
    }
    log.addVisitLog(PAGES.Home, {
      to: toParam,
      success,
      message,
      routerDuration,
    });
  } catch (e) {}
}

// 蚂蚁智信home页面异常
// 蚂蚁智信总量：所有ssd-home-plus-visit
// 蚂蚁智信成功率：success=true日志量/总量
// 蚂蚁智信失败量：success=false日志量
// 蚂蚁智信成功量：success=true日志量
export function homeMayizhixinPageLog(options: HomePageOptions) {
  try {
    const toParam = get(getQueryParams(), 'to');
    const { routerPage, message } = options;
    let success = false;
    const routerDuration = getRouterDuration();
    if (routerPage === PAGES.SsdHomePlus) {
      success = true;
    }
    log.addVisitLog(PAGES.SsdHomePlus, {
      to: toParam,
      success,
      message,
      routerDuration,
    });
  } catch (e) {}
}

interface LoanPageLogOptions {
  message?: string;
  scheme?: any;
}

// 阿里云支用页面异常
// 阿里云总量：所有loan-apply-visit
// 阿里云成功率：success=true日志量/总量
// 阿里云失败量：success=false日志量
// 阿里云成功量：success=true日志量
export function loanAliyunPageLog(options: LoanPageLogOptions) {
  try {
    let success = false;
    const { scheme, message } = options;
    if (scheme) {
      success = true;
    }
    log.addVisitLog(PAGES.LoanApply, {
      success,
      message,
      creditPlatform: 'ALIYUN',
    });
  } catch (e) {}
}

// 蚂蚁智信支用页面异常
// 蚂蚁智信总量：所有ssd-loan-apply-visit
// 蚂蚁智信成功率：success=true日志量/总量
// 蚂蚁智信失败量：success=false日志量
// 蚂蚁智信成功量：success=true日志量
export function loanMayizhixinPageLog(options: LoanPageLogOptions) {
  try {
    let success = false;
    const { scheme, message } = options;
    if (scheme) {
      success = true;
    }
    log.addVisitLog(PAGES.SsdLoanApply, {
      success,
      message,
      creditPlatform: 'MAYI_ZHIXIN',
    });
  } catch (e) {}
}

interface RecordListPageLogOptions {
  isSSD: boolean;
  success: boolean;
}

// 支用&还款列表页面异常
// 总量：所有record-list-other
// 成功率：success=true日志量/总量
// 失败量：success=false日志量
// 成功量：success=true日志量
export function recordListPageLog(options: RecordListPageLogOptions) {
  try {
    const { success, isSSD } = options;
    log.addOtherLog('record-list', {
      success,
      creditPlatform: isSSD ? 'MAYI_ZHIXIN' : 'ALIYUN',
    });
  } catch (e) {}
}

interface RepayBillPageLogOptions {
  creditPlatform: 'MAYI_ZHIXIN' | 'ALIYUN';
  message?: string;
  success: boolean;
  surplusBillStatus?: string;
}

// 账单还款页面页面异常
// 总量：所有repay-bill-visit/ssd-repay-bill-visit
// 成功率：success=true日志量/总量
// 失败量：success=false日志量
// 成功量：success=true日志量
export function repayBillPageLog(options: RepayBillPageLogOptions) {
  try {
    const { creditPlatform, success, surplusBillStatus } = options;
    const page = creditPlatform === 'ALIYUN' ? PAGES.RepayBill : PAGES.SsdRepayBill;
    log.addVisitLog(page, {
      creditPlatform,
      success,
      surplusBillStatus,
    });
  } catch (e) {}
}

interface LoanContractLogOptions {
  creditPlatform: 'MAYI_ZHIXIN' | 'ALIYUN';
  success: boolean;
  message?: string;
}

// 提前还款页面异常
// 总量：所有repay-bill-visit/ssd-repay-bill-visit
// 成功率：success=true日志量/总量
// 失败量：success=false日志量
// 成功量：success=true日志量
export function loanContractPageLog(options: LoanContractLogOptions) {
  try {
    const { creditPlatform, message, success } = options;
    const page = creditPlatform === 'ALIYUN' ? PAGES.LoanContract : PAGES.SsdLoanContract;
    log.addVisitLog(page, {
      creditPlatform,
      message,
      success,
    });
  } catch (e) {}
}

interface BindCardPageLogOptions {
  success: boolean;
  message?: string;
}

// 银行绑卡页面异常
export function bindCardPageLog(options: BindCardPageLogOptions) {
  try {
    const { message, success } = options;
    log.addVisitLog(PAGES.BindCard, {
      message,
      success,
      creditPlatform: 'MAYI_ZHIXIN',
    });
  } catch (e) {}
}

interface LoanSignPageLogOptions {
  success: boolean;
  errorMsg?: string;
}

// 支用核身页面异常
export function loanSignPagePageLog(options: LoanSignPageLogOptions) {
  try {
    const { errorMsg, success } = options;
    setTimeout(() => {
      log.addVisitLog(PAGES.LoanSign, {
        errorMsg,
        success,
        creditPlatform: 'ALIYUN',
      });
    }, 300);
  } catch (e) {}
}

interface CreditSignPageLogOptions {
  creditApplyOrderId?: string;
  authenticationToken?: string;
  message?: string;
}

// 授信核身页面异常
export function creditSignPageLog(options: CreditSignPageLogOptions) {
  try {
    const { message, creditApplyOrderId, authenticationToken } = options;
    let success = false;
    if (creditApplyOrderId && authenticationToken) {
      success = true;
    }
    setTimeout(() => {
      log.addVisitLog(PAGES.CreditSign, {
        message,
        success,
        creditPlatform: 'ALIYUN',
      });
    }, 300);
  } catch (e) {}
}

interface ForceAgreementPageOptions {
  success: boolean;
  message: string;
  creditPlatform: 'MAYI_ZHIXIN' | 'ALIYUN';
}

// 强制阅读页面异常
export function forceAgreementPageLog(options: ForceAgreementPageOptions) {
  try {
    const { success, message, creditPlatform } = options;
    log.addOtherLog('force-agreement', {
      success,
      message,
      creditPlatform,
    });
  } catch (e) {}
}

interface AlipayCashierLogOptions {
  success: boolean;
  message?: string;
  res?: any;
}

// 还款收银台异常
export function alipayCashierLog(options: AlipayCashierLogOptions) {
  try {
    const { message, success } = options;
    log.addVisitLog('alipay-cashier', {
      message,
      success,
      creditPlatform: 'MAYI_ZHIXIN',
    });
  } catch (e) {}
}
