import dayjs from 'dayjs';
import {
  isString,
  get,
  find,
  set,
  map,
  isEmpty,
  includes,
  first,
  toString,
  omit,
  isNull,
  join,
  forEach,
  filter,
  cloneDeep,
  pick,
  isFunction,
  throttle,
  debounce,
  concat,
  replace,
  last,
  upperCase,
  keys,
  reduce,
  take,
  split,
} from 'lodash-es';
import { number, url as urlUtil, uid, moment, env } from '@ali/iec-dtao-utils';
import numeral from 'numeral';
import { log } from '@alife/dtao-iec-spm-log';
import {
  ErrorMessageMap,
  FAIL_BIZ_CREDIT_CONTRACT_NOT_EXIST_EXCEPTION,
  ZZFW_TB_MARKET_LINK_PRE,
} from '@/common/constant';
import { isDev } from '@/utils/env';
import { App } from '@ali/uni-api';

export const _ = {
  get,
  isString,
  set,
  find,
  map,
  isEmpty,
  includes,
  first,
  toString,
  omit,
  isNull,
  join,
  forEach,
  filter,
  cloneDeep,
  pick,
  isFunction,
  throttle,
  debounce,
  concat,
  replace,
  last,
  upperCase,
  keys,
  reduce,
};

export const { money_US, money, amountFormat, numberFormat } = number;
export const moneyInt = (num) => money(num, '0,0');

export const { query: urlQuery, queryAll: urlQueryAll } = urlUtil;

// function getQuery(query?: any) {
//   if (query && isObject(query)) {
//     const queryClone = cloneDeep(query);
//     set(queryClone, 'x-ssr', true);
//     return queryClone;
//   }
//   return {
//     'x-ssr': true,
//   };
// }

export const addAntdFormErrorLog = (errorFields) => {
  if (_.isEmpty(errorFields)) return;
  errorFields.forEach((e) => {
    log.addLog(e.name.join('.'), 'error', { message: e.errors[0] });
  });
};

export const getPageName = (pathname): any => {
  return pathname ? last(pathname.split('/')) : '';
};

export const genFullUrl = (pathName, query?) => {
  let url;
  const UniAppPath = `${window.location.origin}/wow/z/uniapp/1100010/b2b-finance-web/dtao-xfd-uniapp/`;
  if (/^https?:\/\//.test(pathName)) {
    return pathName;
  }
  if (process.env.ICE_CORE_MODE === 'development') {
    const { guan_socket, guan_debugtool } = urlQueryAll() || {};
    url = `${window.location.origin}/${urlUtil.bindQueryToUrl(`${pathName}`, {
      ...(query || {}),
      guan_debugtool,
      guan_socket,
    })}`;
  } else {
    url = `${UniAppPath}${urlUtil.bindQueryToUrl(pathName, query)}`;
  }
  return url;
};

export const genFullPageId = (pathName, query?) => {
  return `${urlUtil.bindQueryToUrl(`b2b-finance-web/dtao-xfd-uniapp/${pathName}`, query)}`;
};

export const dayjsFormat = (date, format = 'YYYY-MM-DD') => {
  return date ? dayjs(date).format(format) : '--';
};

export const dayjsYYMD = (date, format = 'YYYY-M-D') => {
  return date ? dayjs(date).format(format) : '--';
};

export const isToday = (time) => {
  const today = dayjs().format('YYYY-MM-DD');
  const day = time && dayjs(time).format('YYYY-MM-DD');

  return today === day;
};

export const findTargetErrorMessage = (targetErrorName, errorFields) => {
  if (!targetErrorName || !errorFields) return null;
  const targetError = find(errorFields, (f) => {
    if (isString(targetErrorName)) {
      return f.name.join('.') === targetErrorName;
    } else {
      return f.name.join('.') === targetErrorName.join('.');
    }
  });

  return get(targetError, 'errors[0]');
};

export const noop = () => {};

export const getTimeStamp = (): number => {
  return new Date().getTime();
};

export const idempotentId = (max = 999999) => {
  return `${new Date().getTime()}-${Math.floor(Math.random() * Math.floor(max))}`;
};

export const requestId = () => {
  return `xfd_${uid.get()}`;
};

export function getQueryParams() {
  const queryString = window.location.search;
  const params = {};
  const pairs = (queryString[0] === '?' ? queryString.substr(1) : queryString).split('&');

  for (let i = 0; i < pairs.length; i++) {
    const pair = pairs[i].split('=');
    params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1] || '');
  }

  return params;
}

// 判断是否大于0
export const isGreaterThan0 = (a) => {
  return numeral(a).value() > numeral(0).value();
};

// 判断是否等于0
export const isEqual0 = (val?: string | number) => {
  if (val) {
    const valNum = number.getNumber(val);
    if (valNum === 0) {
      return true;
    }
  }
  return false;
};

export const isNumEqual = (a, b) => {
  return numeral(a).value() === numeral(b).value();
};

// 判断a是否大于b
export const isGreaterThan = (a, b) => {
  return numeral(a).value() > numeral(b).value();
};

export const mtopExceptionErrorCode = (mtopError) => {
  return _.get(mtopError, 'errorList[0].code');
};

export const isMtopErrorCreditContractNotExist = (mtopError) => {
  const isHit = mtopExceptionErrorCode(mtopError) === FAIL_BIZ_CREDIT_CONTRACT_NOT_EXIST_EXCEPTION;
  if (isHit) {
    log.addErrorLog('credit-contract-not-exist');
  }
  return isHit;
};

export const getErrorCodeFromRejectRes = (res) => {
  return _.get(res, 'rejectReason.rejectCode');
};

export const getFailedCodeFromOrder = (res) => {
  return _.get(res, 'failedReason.failedCode');
};

export const getMsgByErrorCode = (code, msgMap?) => {
  return _.get(msgMap || ErrorMessageMap, code) || ErrorMessageMap.BUSY_DEFUALT;
};

export const getFailedMsg = (code) => {
  if (!code) return '';

  return _.get(ErrorMessageMap, code);
};

export const getRejectMsgFromMsgMap = (res) => {
  return getMsgByErrorCode(getErrorCodeFromRejectRes(res));
};

export const isThemis = () => {
  try {
    if (import.meta.renderer === 'server') {
      return true;
    }
    if (window.navigator) {
      return window.navigator.userAgent.match(/Themis/i);
    }
    if (navigator.userAgent) {
      return navigator.userAgent.match(/Themis/i);
    }
    return false;
  } catch (e) {
    return false;
  }
};

export const isValidDate = (date) => {
  return dayjs(date).isValid();
};

export const isNowBefore = (date) => {
  if (!isValidDate(date)) return false;

  return dayjs().isBefore(dayjs(date));
};

export const isNowAfter = (date) => {
  if (!isValidDate(date)) return false;
  return dayjs().isAfter(dayjs(date));
};

export function getMD(date: any) {
  if (!date) {
    return '--';
  }
  return moment.format(date, 'MM月DD日');
}

export function getYMD_CN(date: any) {
  if (!date) {
    return '--';
  }
  return moment.format(date, 'YYYY年MM月DD日');
}

export function getYMD(date: any) {
  if (!date) {
    return '--';
  }
  return moment.YMD_CUSTOM(date, '/');
}

export function getApplyTime() {
  return new Date().getTime();
}

export function addMinutes(t, num) {
  return dayjs(t).add(num, 'minutes');
}

export function beforeMinutes(t, num) {
  if (t) {
    const after = dayjs(t).add(num, 'minutes');
    return dayjs().isBefore(dayjs(after));
  }
  return false;
}

export function diffMinutesFromNow(t: any) {
  const now = dayjs(t);
  return now.diff(dayjs(), 'minutes');
}

export function fieldDesensitization(str, startIndex = 3, endIndex = 3, starCount = -1) {
  const strLength = str.length;

  if (!strLength || strLength <= startIndex + endIndex) {
    return '';
  }

  const prefix = str.substr(0, startIndex);
  const suffix = str.substr(strLength - endIndex, strLength);

  let stars = '';
  // 默认*号数量与实际隐藏字符一致
  if (starCount === -1) {
    stars = Array(str.length - startIndex - endIndex + 1).join('*');
  } else {
    stars = Array(starCount + 1).join('*');
  }

  return `${prefix}${stars}${suffix}`;
}

// 判断年龄是否大于等于18岁
export function analyzeIDCardIsOver18(cardNo: string) {
  const yearBirth = Number(cardNo.substring(6, 10));
  const monthBirth = Number(cardNo.substring(10, 12));
  const dayBirth = Number(cardNo.substring(12, 14));
  const myDate = new Date();
  const monthNow = myDate.getMonth() + 1;
  const dayNow = myDate.getDate();
  let age = myDate.getFullYear() - yearBirth;
  if (monthNow < monthBirth || (monthNow === monthBirth && dayNow < dayBirth)) {
    age--;
  }

  return age >= 18;
}

export function getBankCardNo(bankCarNo?: string) {
  try {
    if (!bankCarNo) {
      return '';
    }
    return bankCarNo?.substring(bankCarNo.length - 4, bankCarNo.length);
  } catch (e) {
    return '';
  }
}

export function isMtopCodeTimeout(code) {
  return _.includes(['FAIL_SYS_SERVICE_TIMEOUT', 'FAIL_TIME_OUT', 'FAIL_SYS_TIME_OUT'], code);
}

// 判断是否是淘宝端
export function isTbApp() {
  try {
    const isWhite = _.get(getQueryParams(), 'white');
    if (!isDev() && !env.isTb() && !isWhite) {
      log.addOtherLog('yun-not-tb', { ua: navigator?.userAgent });
      window.location.replace(
        `${ZZFW_TB_MARKET_LINK_PRE}${encodeURIComponent(window.location.href)}`,
      );
      return false;
    }
  } catch (error) {
    return true;
  }
  return true;
}

export function checkPre() {
  try {
    const { origin } = window.location;
    if (origin === 'https://pre-pages-fast.m.taobao.com') {
      return true;
    }
    return false;
  } catch (e) {
    return false;
  }
}

export function getOrigin() {
  try {
    const origin = _.get(getQueryParams(), 'tracelog', '');
    return origin;
  } catch (e) {
    return '';
  }
}

export function getApplyExtension() {
  try {
    const origin = getOrigin();
    return {
      origin,
    };
  } catch (e) {
    return {
      origin: '',
    };
  }
}

export function genErrorLogParams(error) {
  if (!error) return;

  return {
    jsonError: JSON.stringify(error),
    code: error?.message,
    mtopErrorCode: error?.errorMessage,
  };
}

export function isVersionGreater(versionA: string, versionB: string): boolean {
  // 将版本号拆分为数组，并转换为数字类型
  const splitVersion = (version: string) => version?.split?.('.')?.map?.(Number);
  const partsA = splitVersion(versionA);
  const partsB = splitVersion(versionB);

  const maxLength = Math.max(partsA.length, partsB.length);

  for (let i = 0; i < maxLength; i++) {
    const numA = partsA[i] || 0;
    const numB = partsB[i] || 0;

    if (numA > numB) {
      return true;
    } else if (numA < numB) {
      return false;
    }
  }

  return false; // 相等的情况
}

// 获取手淘版本号
export async function getAppVersion(length?: number): Promise<string> {
  try {
    const version = await App.getVersion();
    if (version) {
      if (length) {
        // 取前几个版本
        return join(take(split(version, '.'), length), '.');
      }
      return version;
    }
    throw new Error();
  } catch (error) {
    return '';
  }
}
