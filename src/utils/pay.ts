/**
 * @file 唤起收银台
 */

import { isHarmonyOS } from './bridges';
import { Cashier } from '@ali/uni-api';
import { log } from '@alife/dtao-iec-spm-log';
import WindVane from '@ali/universal-windvane';
import { alipayCashierLog } from './goc';

/**
 * @value CANCELLED 已取消
 * @value PERIOD 已过期
 * @value SUCCESS 支付成功
 * @value FAIL 支付失败
 */
export type PAY_STATUS = (
  'CANCELLED' |
  'PERIOD' |
  'SUCCESS' |
  'FAIL'
);

export interface AlipayParams {
  // mock
  mock?: boolean;
  // 订单id列表
  signStr?: string;
}

/**
 * @param success 调用成功
 * @param payStatus 支付状态
 * @param message 消息
 * @param code 支付原始CODE
 */
export interface PayResult {
  success: boolean;
  payStatus: PAY_STATUS;
  message?: string;
  code?: string;
}

interface CashierPayResult extends Cashier.PayResult {
  result?: {
    resultStatus: '9000' | '6001' | '6002';
    memo: string;
  };
  ResultStatus?: '9000' | '6001' | '6002';
}

// 埋点
function addPayLog(res: CashierPayResult) {
  const ALIPAY_PAY_SUCCESS = 'alipay-pay-success';
  const ALIPAY_PAY_CANCEL = 'alipay-pay-cancel';
  const ALIPAY_PAY_FAIL = 'alipay-pay-fail';
  try {
    const resultStatus = res?.ResultStatus || res?.result?.resultStatus;
    if (resultStatus === '9000') {
      alipayCashierLog({
        success: true,
        message: ALIPAY_PAY_SUCCESS,
      });
    } else if (resultStatus === '6001') {
      alipayCashierLog({
        success: true,
        message: ALIPAY_PAY_CANCEL,
      });
    } else if (resultStatus === '6002') {
      alipayCashierLog({
        success: false,
        message: ALIPAY_PAY_FAIL,
        res,
      });
    } else {
      alipayCashierLog({
        success: false,
        message: ALIPAY_PAY_FAIL,
      });
    }
  } catch (e) {}
}

function onCashierPaySuccess(res: CashierPayResult): PayResult {
  const resultStatus = res?.ResultStatus || res?.result?.resultStatus;
  addPayLog(res);
  switch (resultStatus) {
    // 支付成功
    case '9000': {
      return {
        success: true,
        payStatus: 'SUCCESS',
        code: resultStatus,
        message: '支付成功',
      };
    }
    // 支付取消
    case '6001': {
      return {
        success: true,
        payStatus: 'CANCELLED',
        code: resultStatus,
        message: res.result?.memo,
      };
    }
    // 支付失败
    case '6002': {
      return {
        success: true,
        payStatus: 'FAIL',
        code: resultStatus,
        message: res.result?.memo,
      };
    }
    default: {
      return {
        success: true,
        payStatus: 'FAIL',
        code: resultStatus,
        message: '支付失败',
      };
    }
  }
}

export const pay = (params: AlipayParams): Promise<PayResult> => {
  const { signStr } = params;
  // signStr未空
  const ALIPAY_PAY_SIGN_STR_NULL = 'alipay-pay-sign-str-null';
  // 拉起支付宝收银台失败
  const ALIPAY_PAY_CALL_ERROR = 'alipay-pay-call-error';
  // 拉起支付宝收银台失败
  const ALIPAY_PAY_CALL_CLICK = 'alipay-pay-call-click';

  return new Promise(async (resolve, reject) => {
    if (!signStr) {
      alipayCashierLog({
        success: false,
        message: ALIPAY_PAY_SIGN_STR_NULL,
      });
      resolve({
        success: false,
        message: '没有signStr',
        payStatus: 'FAIL',
      });
      return;
    }
    try {
      log.addOtherLog(ALIPAY_PAY_CALL_CLICK);
      if (isHarmonyOS()) {
        await Cashier.pay({
          url: signStr,
          onResultCallback: (r: CashierPayResult) => {
            resolve(onCashierPaySuccess(r));
          },
        });
      } else {
        WindVane.call('TBWVPayPasswrdValidateHandler', 'checkPayPasswordAction', {
          url: signStr,
        }, (res: CashierPayResult) => {
          resolve(onCashierPaySuccess(res));
        });
      }
    } catch (e) {
      alipayCashierLog({
        success: false,
        message: ALIPAY_PAY_CALL_ERROR,
        res: e,
      });
      const error = new Error('支付宝收银台唤起失败');
      reject(error);
    }
  });
};
