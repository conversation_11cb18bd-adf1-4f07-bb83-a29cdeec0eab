/**
 * @file 各种jsbridges
 */

import { Device, Account, App } from '@ali/uni-api';
import Windvane from '@ali/universal-windvane';

export function isHarmonyOS() {
  try {
    const deviceInfo = Device.getInfo();
    return deviceInfo?.platform === 'HarmonyOS';
  } catch (e) {
    return false;
  }
}

export function refreshAlipayCookie(data: any) {
  return new Promise((resolve, reject) => {
    try {
      if (isHarmonyOS()) {
        // 将容器对应的支付宝账户cookie同步到客户端
        Account.refreshAlipayCookie({
          data,
          onCallback: resolve,
        });
      } else {
        Windvane.call('aluWVJSBridge', 'refreshAlipayCookie', data, resolve, reject);
      }
    } catch (e) {
      reject(e);
    }
  });
}

export type ENV_TYPE = 'release' | 'pre' | 'daily' | 'unknown';

export interface AppInfoResponse {
  version?: string;
  Environment?: ENV_TYPE;
}

export async function getAppInfo(): Promise<AppInfoResponse | null> {
  try {
    const res = await App.getInfo();
    if (res) {
      return res;
    }
    throw new Error();
  } catch (e) {
    return null;
  }
}

export async function getAlipayApdidToken() {
  return new Promise((resolve, reject) => {
    window.__windvane__.call(
      'AlipayPlugin.getAlipayApdidToken',
      {},
      (response) => {
        resolve(response);
      },
      (e) => {
        reject(e);
      },
    );
  });
}

export async function getAlipayApdidTokenV2() {
  return new Promise((resolve, reject) => {
    Windvane.call2(
      'AlipayPlugin',
      'getAlipayApdidToken',
      {},
      (response) => {
        resolve(response);
      },
      (e) => {
        reject(e);
      },
    );
  });
}

export function getEnv() {
  try {
    return App.getEnv();
  } catch (e) {
    return 'release';
  }
}
