/**
 * @file 还款相关接口
 */

import { MtopGet } from '@/utils/mtop';
import type { REPAY_ORDER_STATUS, PageQueryRes, RepaymentScene } from '@/store/types';

export interface RepayOrderRes {
  repaidInterest: string;
  /** 还款未成功金额 */
  surplusTotalAmount: string;
  repaidPrincipal: string;
  repaidPenalty: string;
  repayOrderId: string;
  failedReason: string;
  repaidTotalAmount: string;
  applyAmount: string;
  applyTime: number;
  status: REPAY_ORDER_STATUS;
  loanOrderId: string;
  paymentParams: any;
  repaymentScene: RepaymentScene;
  /** 还款失败的借据 */
  failedLoanOrderIdList?: string[] | null;
}

export interface RepayOrderDetail {
  applyAmount: string;
  applyTime: number;
  failedReason: any;
  institutionEndTime: number;
  repaidFee: string;
  repaidInterest: string;
  repaidPenalty: string;
  repaidPrincipal: string;
  repaidTotalAmount: string;
  repayOrderId: string;
  repayType: string;
  status: string;
  totalRepayCount: number;
}

// export interface RepayOrder

export interface RepayRecordItem {
  applyAmount: string;
  applyTime: number;
  institutionEndTime: number;
  repaidTotalAmount: string;
  repayOrderId: string;
  repayType: string;
  status: string;
  totalRepayCount: number;
}

export interface RepaySubOrderItem {
  institutionEndTime: number;
  loanContractId: string;
  loanOrderId: string;
  loanedAmount: string;
  loanedTime: number;
  repaidFee: string;
  repaidInterest: string;
  repaidPenalty: string;
  repaidPrincipal: string;
  repaidTotalAmount: string;
  repayOrderId: string;
  repaymentMethod: string;
  status: string;
}

export interface repaySubOrderListRes {
  currentPage: number;
  dataList: RepaySubOrderItem[];
  pageSize: number;
  totalPage: number;
  totalRecord: number;
}

export interface repayOrderListRes {
  currentPage: number;
  dataList: RepayRecordItem[];
  pageSize: number;
  totalPage: number;
  totalRecord: number;
}

export interface installmentBillConsultRes {
  installmentEndDate: number;
  instalmentBillCount: number;
  institution: string;
  loanContractCount: number;
  surplusBillStatus: string;
  surplusTotalAmount: string;
  surplusOverdueTotalAmount: string;
}

export interface InstallmentBillPageRes {
  currentPage: number;
  dataList: Array<{
    installmentBillStatus: string;
    installmentEndDate: number;
    surplusInterest: string;
    surplusPenalty: string;
    surplusPrincipal: string;
    surplusTotalAmount: string;
  }>;
  pageSize: number;
  totalPage: number;
  totalRecord: number;
}

export interface ContractRepaySubOrderSummaryRes {
  repaidFee: string;
  repaidInterest: string;
  repaidPenalty: string;
  repaidPrincipal: string;
}

export interface RepayConsultRes {
  admitted: boolean;
  institution: string;
  needSettleRepay: boolean;
  repayBankCardList: any;
  rejectReason: null;
  repayLoanOrder: any;
  needAgreement: boolean;
  repayTrialResult: any;
}

export interface RepayPreAdmitRes {
  admitted: boolean;
}

export interface RepayTrialDTO {
  admitted: boolean;
  trialFee: string;
  trialInterest: string;
  trialPenalty: string;
  trialPrincipal: string;
  trialTotalAmount: string;
  passThroughInfo: any;
}

// 账单查询
export function queryInstallmentBillConsult(data?): Promise<installmentBillConsultRes> {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.loan.installment.bill.consult',
    data,
  });
}

// 查询还款单
export function queryRepayOrder(data?): Promise<RepayOrderRes> {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.repay.order.get',
    data,
  });
}

// 查询还款计划
export function queryInstallmentBillPage(data?): Promise<InstallmentBillPageRes> {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.loan.installment.bill.page.query',
    data,
  });
}

// 到期/逾期还款咨询
export function repayBatchConsult(data?): Promise<RepayConsultRes> {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.repay.batch.consult',
    data,
  });
}

// 到期/逾期还款试算
export function repayBatchTrial(data?): Promise<RepayTrialDTO> {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.repay.batch.trial',
    data,
  });
}

// 到期/逾期申请
export function repayBatchApply(data?) {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.repay.batch.apply',
    data,
  });
}

// 单借据还款咨询
export function repayConsult(data?): Promise<RepayConsultRes> {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.repay.consult',
    data,
  });
}

// 单借据还款试算
export function repayTrial(data?): Promise<RepayTrialDTO> {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.repay.trial',
    data,
  });
}

// 单借据还款申请
export function repayApply(data?) {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.repay.apply',
    data,
  });
}

// 查询单期还款计划详情
export function queryOneRepayInstallmentLoanList(data?): Promise<PageQueryRes> {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.loan.installment.detail.page.query',
    data,
  });
}

// 查询全部还款记录
export function queryRepayOrderList(data?): Promise<repayOrderListRes> {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.repay.order.page.query',
    data,
  });
}

// 查询还款单详情
export function queryRepayOrderDetail(data?): Promise<RepayOrderDetail> {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.repay.order.detail.get',
    data,
  });
}

// 查询借据下的还款单 或者是根据还款主单查子单
export function queryRepaySubOrderList(data?): Promise<repaySubOrderListRes> {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.repay.sub.order.detail.page.query',
    data,
  });
}

// 查询借据下的全部已还
export function queryContractRepaySubOrderSummary(data?): Promise<ContractRepaySubOrderSummaryRes> {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.repay.sub.order.summary.query',
    data,
  });
}

// 待还合约页面单借据维度的还款准入判断
export function queryRepayPreAdmit(data?): Promise<RepayPreAdmitRes> {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.repay.pre.admit',
    data,
  });
}

// 查账还款页面的还款准入判断
export function queryRepayBatchPreAdmit(data?): Promise<RepayPreAdmitRes> {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.repay.batch.pre.admit',
    data,
  });
}

// ssd 查询待还合约
export function queryLoanContractListAndSummary(data?) {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.loan.contract.list.query',
    data,
  });
}

// ssd 查询还款计划
export function queryInstallmentBillList(data?) {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.loan.installment.bill.list.query',
    data,
  });
}
