import { _, getErrorCodeFromRejectRes, getFailedMsg } from '@/utils';

// 生成主动还款拒绝提示文案, 这里是在查账还款页和待还合约页面上面的通知
export const genRepayRejectNoticeMsg = (res) => {
  let noticeMsgText;
  const rejectCode = getErrorCodeFromRejectRes(res);
  const extMap = _.get(res, 'rejectReason.extMap') || {};
  const { startTime, endTime } = extMap;

  if (
    _.includes(['INSTITUTION_SYSTEM_SHUT_DOWN', 'MANUAL_REPAY_REJECT_WHEN_CUT_OFF'], rejectCode)
  ) {
    noticeMsgText = `机构系统维护中，暂无法主动还款，维护时间：${startTime}~${endTime}`;
  } else {
    noticeMsgText = getFailedMsg(rejectCode);
  }

  return noticeMsgText;
};
