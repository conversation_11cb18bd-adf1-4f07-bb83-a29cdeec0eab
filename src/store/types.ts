/**
 * @file 公共类型
 * <AUTHOR>
 */

export type AGREEMENT_SOURCE = 'INSTITUTION' | 'AGREEMENT_CENTER' | 'MAYI_ZHIXIN';

export type CHANNEL = 'APP' | 'PC';

export type CREDIT_PLATFORM = 'MAYI_ZHIXIN' | 'ALIYUN';

export type CREDIT_TYPE = 'NORMAL_CREDIT' | 'SIMPLE_CREDIT' | 'SIMPLE' | 'STANDARD';

export type CREDIT_TYPE_VERSION = 'SIMPLE_CREDIT_V1' | 'SIMPLE_CREDIT_V2';

export type LOAN_VERSION = 'NORMAL'|
'HOME_LOAN_CONSISTENT' |
'HOME_LOAN_CONSISTENT_V2' |
'HOME_LOAN_CONSISTENT_V3';

export interface Option {
  label: string;
  value: string;
}

export interface BankCardDTO {
  value?: string;
  bankCode?: string;
  bankName?: string;
  bankCardNo?: string;
  bindCardNo?: string;
}

export interface CascadeRepaymentMethod {
  defaultValue?: string;
  options?: RepaymentMethodDTO[];
  disabled?: boolean;
}

export interface LoanTermDTO {
  value?: string;
  loanTerm?: string;
  loanTermUnit?: string;
  label?: string;
  disabled?: boolean;
  cascadeRepaymentMethod?: CascadeRepaymentMethod;
}

export interface LoanCouponDTO {
  label?: string;
  value?: string;
  describe?: string;
  usable?: boolean;
  type?: 'INTEREST_FREE' | 'DISCOUNT';
  expireTime?: number;
  effectiveTime?: number;
  clear?: boolean;
}

export interface CascadeLoanTermOption {
  defaultValue?: LoanTermDTO;
  options?: LoanTermDTO[];
  disabled?: boolean;
}

export interface RepaymentMethodDTO {
  label?: string;
  desc?: string;
  value?: string;
  disabled?: boolean;
  cascadeLoanTerm?: CascadeLoanTermOption;
}

export interface FundSupplierDTO {
  fundSupplierCode?: string;
  fundSupplierName?: string;
}

export interface AgreementDTO extends FundSupplierDTO {
  code?: string;
  name?: string;
  institution?: string;
  minReadTime: number;
  content: string;
  contentType: string;
  bizType?: string;
  agreementNo: string;
  source?: AGREEMENT_SOURCE;
  forceRead?: boolean;
  order: number;
  version?: string;
  agreementStatus?: string;
  contractNo?: string;
}

export interface InstallmentPlanDTO {
  /** 期数 */
  number?: number;
  /** 还款日 */
  endDate?: number;
  /** 还款本金 */
  principal?: string;
  /** 还款利息（优惠后） */
  interest?: string;
  /** 还款总额 */
  totalAmount?: string;
  /** 优惠金额 */
  promotionAmount?: string;
  /** 原始利息 */
  originInterest?: string;
}

export interface RejectReasonDTO {
  rejectCode?:
  | 'C_PERMIT_ALIPAY_ACCOUNT'
  | 'ON_GOING_CONTRACT_MORE_THAN_UPPER_LIMIT'
  | 'NO_AVAILABLE_QUOTA'
  | 'ON_GOING_CONTRACT_MORE_THAN_UPPER_LIMIT'
  | 'QUOTA_STATUS_FROZEN'
  | string;
  rejectDesc?: string;
  extMap?: {
    maxLoanContractCount?: string;
  };
}

export interface Options<D, V> {
  defaultValue?: D;
  minCount?: number;
  options?: V[];
}

export type SupplementDataType =
  | 'profession'
  | 'residenceAddress'
  | 'companyName'
  | 'education'
  | 'monthlyIncome'
  | 'maritalStatus'
  | 'emergencyContact'
  | 'province'
  | 'city'
  | 'county';

export type SupplementDataCollection = {
  [key in SupplementDataType]: Options<Option, Option>;
};

export type SupplementDataApplyData = {
  [key in SupplementDataType]?: string | Option;
};

export interface EmergencyContactDTO {
  name?: string;
  phone?: string;
  relationship?: Option;
}

export interface SupplementDataInitData {
  profession?: Option;
  residenceAddress?: Option;
  companyName?: string;
  education?: Option;
  monthlyIncome?: Option;
  maritalStatus?: Option;
  emergencyContact?: EmergencyContactDTO[];
  province?: Option;
  city?: Option;
  county?: Option;
}

export interface SupplementDataFormData {
  profession?: Option;
  companyName?: string;
  education?: Option;
  monthlyIncome?: Option;
  maritalStatus?: Option;
  emergencyContact?: EmergencyContactDTO[];
  region?: {
    province?: Option;
    city?: Option;
    county?: Option;
  };
  residenceAddress?: string;
}

export interface CREDIT_APPLY_CONSULT_DTO {
  creditContractStatus: CREDIT_CONTRACT_STATUS;
  latestCreditApplyOrder: CREDIT_APPLY_ORDER_DTO;
  applyType: 'SECONDARY' | 'NEW'; // NEW为不可二次授信
  admitted: boolean;
  isPlatformAgreementSigned: boolean;
  creditDefaultFactor: any;
  canCreditApply: boolean;
  canCreditApplyTime: number;
  creditPlatform: 'ALIYUN' | 'MAYI_ZHIXIN';
  hasPendingRepayAmount: boolean;
  creditType?: CREDIT_TYPE;
  platformPromotionOfferList?: PromotionOffer[];
  extension?: string;
  accountStatus?: 'INIT' | 'ENABLE' | 'DISABLE';
}

export interface institutionDecisionResultItem {
  institutionCode: string;
  institutionName: string;
  defaultSelected: boolean;
  order: number;
}

export interface CREDIT_APPLY_ORDER_DTO {
  creditApplyOrderId?: string; // 授信申请单号
  status: CREDIT_APPLY_ORDER_STATUS; // 授信申请单状态
  subStatus?: CREDIT_APPLY_ORDER_SUB_STATUS; // 授信申请单子状态
  applyType?: CREDIT_APPLY_TYPE; // 授信申请单类型
  authenticationToken?: string; // 核身token
  cancelReason?: string; // 取消原因
  failedReason?: string; // 失败原因
  applicationData?: any; // 授信申请单数据
  institutionDecideResultVOList?: institutionDecisionResultItem[]; // 机构决策结果
  extension?: {
    coolOffType?: 'SHORT' | 'LONG'; // 冷静期类型
  };
  creditType: CREDIT_TYPE;
  institutionApplyTime: number;
  creditProcessStatus: 'MANUAL_REVIEW' | 'NORMAL';
}

export type LOAN_STATUS =
  | 'INIT'
  | 'AUTHENTICATING'
  | 'AUTHENTICATED'
  | 'LOANING'
  | 'FAILED'
  | 'SUCCEEDED'
  | 'CANCELLED';
export type CURRENCY = 'CNY';

export type REPAY_ORDER_STATUS =
  | 'INIT' // 初始化
  | 'AUTHENTICATING' // 核身中
  | 'AUTHENTICATED' // 核身完成
  | 'INSTITUTION_REPAYING' // 机构还款中
  | 'INSTITUTION_REPAY_COMPLETE' // 机构还款完成
  | 'INSTITUTION_REPAY_FAIL' // 机构还款失败
  | 'REPAY_WRITING_OFF' // 还款核销中
  | 'SUCCEEDED' // 还款成功
  | 'FAILED' // 还款失败
  | 'CANCELLED' // 还款取消
  | 'PAYING';

export type CREDIT_APPLY_ORDER_SUB_STATUS =
  | 'INIT' // 初始化
  | 'WAIT_TO_FILL_BASIC_INFO' //  待填写基础信息
  | 'WAIT_TO_AUTH' // 待核身
  | 'WAIT_TO_SELECT_INSTITUTION' // 待选择机构
  | 'INSTITUTION_CREDITING' // 机构授信中
  | 'SUCCEEDED' // 授信成功
  | 'FAILED' // 授信失败
  | 'CANCELLED' // 授信取消
  | 'WAIT_TO_SIGN_AGREEMENT' // 待签署协议 只有蚂蚁智信
  | 'WAIT_TO_REAL_NAME_VERIFY'
  | 'REAL_NAME_VERIFIED'
  | 'AUTHENTICATED';

export type CREDIT_CONTRACT_STATUS =
  | 'QUALIFIED' // 授信成功
  | 'FROZEN' // 授信冻结
  | 'CLEARED' // 清退
  | 'CLOSED'; // 已退出 暂未定义

export type CREDIT_APPLY_TYPE =
  | 'NEW' // 首次授信
  | 'SECONDARY'; // 二次授信

export type CREDIT_APPLY_ORDER_STATUS =
  | 'INIT' // 初始化
  | 'PROCESSING' // 进行中
  | 'SUCCEEDED' // 成功
  | 'FAILED' // 失败
  | 'CANCELLED'; // 取消

export interface CapitalInstitutionDTO {
  institutionCode: string;
  institutionName: string;
}

export interface PageQueryRes {
  currentPage: number;
  dataList: any[];
  pageSize: number;
  totalPage: number;
  totalRecord: number;
}

export interface SecureToken {
  apdidToken?: string;
  umidToken?: string;
}

export interface AttachmentVO {
  name?: string;
  suffix?: string;
  fileFactoryNo?: string;
  type?: string;
  path?: string;
  url?: string;
}

export interface CertLicense {
  name?: string;
  licenseNo?: string;
  licenseAddress?: string;
  birthday?: number;
  gender?: string;
  nation?: string;
  licenseAuthDepartment?: string;
  licenseType?: string;
  attachmentVOList?: AttachmentVO[];
  licenseEffectDate?: number;
  licenseLongTermEffective?: boolean;
  licenseExpiredDate?: number;
}

export interface PromotionOffer {
  title?: string;
  code?: string;
  type?: 'TAO_PLATFORM_RED_PACKET';
  amount?: number;
  priority?: number;
  promotionOfferDescriptions: Array<{
    displayPosition?: string;
    description?: string;
    offerSendRule?: string;
  }>;
  offerSendStatus?: 'SEND_SUCCEED' | 'SEND_FAILED' | 'SENDING';
}

export interface LoanModeValue {
  changeName?: 'repaymentMethod' | 'loanTerm';
  loanTermValue?: LoanTermDTO;
  repaymentMethodValue?: string;
}
export interface InstitutionPromotionOffer {
  title?: string;
  code?: string;
  type?: string;
  amount?: number;
  priority?: number;
  promotionOfferDescriptions: Array<{
    displayPosition?: string;
    description?: string;
  }>;
}

/**
 * 还款方式：指定借据还款 | 按期还款
 */
export type RepaymentScene = 'REPAY_BY_LOAN_CONTRACT' | 'REPAY_BY_INSTALLMENT_BILL';
