/**
 * @file 公共服务相关接口
 */

import { MtopGet } from '@/utils/mtop';
import { XFD_ONECONFIG_DATAID } from '@/common/constant';
import { _ } from '@/utils';
import dayjs from 'dayjs';
import { PROFILE_DATA_FIELD_MAP as FIELD_MAP } from '@/store/credit/model';


interface ReadUrlRequest extends BaseRequest {
  fileFactoryNo: string;
  sourceScene: string;
}

interface CreateUrlRequest {
  extension?: string;
  contentType?: string;
  isTemporary?: boolean;
  sourceScene?: string;
  uniqueNo?: string;
}

interface CreateUrlResponse {
  url: string;
  fileFactoryNo: string;
}

interface FileConfirmRequest {
  fileFactoryNo: string;
}

interface FileConfirmResponse {}

interface ReadUrlResponse {
  url?: string;
}

interface EffectConfigResponse {
  configContent: string;
}

export interface OcrBankCardRequest {
  product?: string;
  tenant?: string;
  fileNo?: string;
  withSign?: boolean;
  fileServerFileName?: string;
}

export interface OcrBankCardResponse {
  bank?: string;
  cardNo?: string;
  cardType?: string;
  sign?: string;
}

export interface OcrFrontIdCardDTO {
  address?: string;
  name?: string;
  nationality?: string;
  num?: string;
  sex?: string;
  birth?: string;
  sign?: string;
}

export interface OcrBackIdCardDTO {
  startDate?: string;
  endDate?: string;
  issue?: string;
  sign?: string;
}

export interface checkCifSamePersonRes {
  tbLoginId?: string;
}

// createUrl
export function fileFactoryCreateUrl(data?): Promise<CreateUrlResponse> {
  return MtopGet<CreateUrlRequest, CreateUrlResponse>({
    api: 'mtop.taobao.fin.common.filefactory.createUrl',
    data,
  });
}

// readUrl
export function fileFactoryReadUrl(data: ReadUrlRequest): Promise<ReadUrlResponse> {
  return MtopGet<ReadUrlRequest, ReadUrlResponse>({
    api: 'mtop.taobao.fin.common.filefactory.readUrl',
    data,
  });
}

// 文件Confirm
export function fileFactoryConfirm(data?) {
  return MtopGet<FileConfirmRequest, FileConfirmResponse>({
    api: 'mtop.taobao.fin.common.filefactory.confirm',
    data,
  });
}

// 发送验证码
export function sendSmsCode(data?) {
  return MtopGet({
    api: 'mtop.taobao.fin.common.sms.sendSmsCode',
    data,
  });
}

// 校验验证码
export function checkSmsCode(data?) {
  return MtopGet({
    api: 'mtop.taobao.fin.common.sms.checkSmsCode',
    data,
  });
}

// 查询oneconfig配置
export function queryOneConfig(data?): Promise<any> {
  return MtopGet({
    api: 'mtop.taobao.fin.common.config.queryFromOneConfig',
    data: {
      dataId: XFD_ONECONFIG_DATAID,
      ...data,
    },
  })
    .then((res: EffectConfigResponse) => {
      return JSON.parse(res.configContent);
    })
    .catch(() => {
      return {};
    });
}

export function initOneConfig() {
  return queryOneConfig()
    .then((res) => {
      _.set(window, 'GDATA', res);
      return res;
    })
    .catch(() => {});
}

// 身份证正面ocr
export function ocrIdCardOfFace(data?): Promise<OcrFrontIdCardDTO> {
  return MtopGet({
    api: 'mtop.taobao.fin.common.ocr.ocrIdCardOfFace',
    data,
  });
}

// 身份证反面ocr
export function ocrIdCardOfBack(data?): Promise<OcrBackIdCardDTO> {
  return MtopGet({
    api: 'mtop.taobao.fin.common.ocr.ocrIdCardOfNationalEmblem',
    data,
  });
}

// 银行卡ocr
export function ocrBankCard(data: OcrBankCardRequest) {
  return MtopGet<OcrBankCardRequest, OcrBankCardResponse>({
    api: 'mtop.taobao.fin.common.ocr.ocrBankCard',
    data,
  });
}

// CIF同人校验
export function checkCifSamePerson(data): Promise<checkCifSamePersonRes> {
  return MtopGet({
    api: 'mtop.taobao.fin.common.license.consultSamePersonByIdentityCardId',
    data,
  });
}

interface applicationLicenseData {
  name?: string;
  licenseNo?: string;
  licenseType?: string;
  licenseEffectDate?: string;
  licenseExpiredDate?: string;
  licenseAddress?: string;
  licenseLongTermEffective?: boolean;
  nation?: string;
  birthday?: string;
  gender?: string;
  licenseAuthDepartment?: string;
  attachmentVOList?: any[];
}

interface LicenseConfirmOptions {
  licensePictureFront?: any;
  licensePictureBack?: any;
}

export async function licenseConfirm(options: LicenseConfirmOptions) {
  try {
    const { licensePictureBack, licensePictureFront } = options;
    if (!licensePictureBack?.fileFactoryNo || !licensePictureBack?.fileFactoryNo) {
      throw new Error('FILE_CONFIRM_ERROR');
    }
    await Promise.all([
      fileFactoryConfirm({
        fileFactoryNo: licensePictureFront.fileFactoryNo,
      }),
      fileFactoryConfirm({
        fileFactoryNo: licensePictureBack.fileFactoryNo,
      }),
    ]);
  } catch (e) {
    throw new Error('FILE_CONFIRM_ERROR');
  }
}

// 身份证的照片数据处理成后端的数据结构
export async function processLicensePictureData({
  licensePictureFront,
  licensePictureBack,
  ocrFront,
  ocrBack,
}: {
  licensePictureFront: FileObject;
  licensePictureBack: FileObject;
  ocrFront: OcrFrontIdCardDTO;
  ocrBack: OcrBackIdCardDTO;
}): Promise<applicationLicenseData> {
  await licenseConfirm({
    licensePictureBack,
    licensePictureFront,
  });

  const applicationLicenseData = {};
  try {
    _.set(applicationLicenseData, FIELD_MAP.licenseAddress, ocrFront.address);
    _.set(applicationLicenseData, FIELD_MAP.licenseName, ocrFront.name);
    _.set(applicationLicenseData, FIELD_MAP.licenseNo, ocrFront.num);
    _.set(applicationLicenseData, FIELD_MAP.licenseBirthday, +dayjs(ocrFront.birth));
    _.set(
      applicationLicenseData,
      FIELD_MAP.licenseGender,
      _.get(
        {
          男: 'MALE',
          女: 'FEMALE',
        },
        ocrFront.sex as string,
      ),
    );
    _.set(applicationLicenseData, FIELD_MAP.licenseNation, ocrFront.nationality);
    _.set(applicationLicenseData, FIELD_MAP.licenseAuthDepartment, ocrBack.issue);
    _.set(applicationLicenseData, FIELD_MAP.licenseType, 'IDENTITY_CARD');
    _.set(applicationLicenseData, FIELD_MAP.attachmentVOList, [
      licensePictureFront,
      licensePictureBack,
    ]);

    _.set(applicationLicenseData, FIELD_MAP.licenseEffectDate, +dayjs(ocrBack.startDate));
    if (ocrBack.endDate === '长期') {
      _.set(applicationLicenseData, FIELD_MAP.licenseLongTermEffective, true);
      _.set(applicationLicenseData, FIELD_MAP.licenseExpiredDate, null); // 如果长期这个字段传null
    } else {
      _.set(applicationLicenseData, FIELD_MAP.licenseLongTermEffective, false);
      _.set(applicationLicenseData, FIELD_MAP.licenseExpiredDate, +dayjs(ocrBack.endDate));
    }

    // delete applicationLicenseData.license.pictureFront;
    // delete applicationLicenseData.license.pictureBack;
  } catch (e) {
    throw new Error('PROCESS_LICENSE_PICTURE_DATA_ERROR');
  }

  return applicationLicenseData;
}

interface LicensePreCheckRequest {
  personName?: string;
  personIdCardNo?: string;
}

interface LicensePreCheckResponse {
  isValidLicense?: boolean;
}

// 姓名身份证校验
export function licensePreCheck(data: LicensePreCheckRequest) {
  return MtopGet<LicensePreCheckRequest, LicensePreCheckResponse>({
    api: 'mtop.alibaba.fin.tao.blp.customer.license.pre.check',
    data,
  });
}
