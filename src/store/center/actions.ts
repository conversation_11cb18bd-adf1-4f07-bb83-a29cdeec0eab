import { MtopGet } from '@/utils/mtop';
import type { CREDIT_CONTRACT_STATUS, RejectReasonDTO, PromotionOffer, InstitutionPromotionOffer, LOAN_VERSION } from '@/store/types';

interface CustomerProfileRes {
  result: Array<{
    content: any;
    saveTime: number;
    status: string;
    type: string;
  }>;
}

export type SurplusBillStatus =
  | 'NORMAL'
  | 'DUE'
  | 'OVERDUE_ONLY'
  | 'OVERDUE_AND_DUE'
  | 'SETTLED'
  | 'DUE_TOMORROW';

export type SurplusQuotaStatus = 'NEW' | 'NORMAL' | 'INSUFFICIENT' | 'EXHAUSTED' | 'FROZEN';

export interface RecommendAmount {
  /** 推荐额度 */
  amount: string;
  /** 推荐额度文案展示 */
  amountDisplay: string;
  /** 营销标记 */
  useCoupon?: boolean;
}

export type PreFillAmountType = 'BREAKPOINT' | 'ALGORITHM' | 'DEFAULT';

export interface PreFillAmount {
  /** 支用金额 */
  loanAmount: string;
  /** 支用期限 */
  loanTerm: string;
  /** 支用期限单位 */
  loanTermUnit: string;
  /** 还款方式 */
  repaymentMethod: string;
  /** 预填金额类型 */
  type: PreFillAmountType;
}

export interface LoanSchemaInfoRes {
  interestRate: {
    days: number;
    dailyInterestRatePercent: string; // 日利率
    interestRatePercent: string; // 年利率
    dailyInterestPerThousand: string; // 千元日利息
    tempInterestPerThousand: string; // 临时年利率千元日利息
    tempInterestRatePercent?: string; // 临时年利率
    tempDailyInterestPerThousand?: string; // 临时年利率千元日利息
    tempInterestRateEndTime?: number; // 临时利率到期时间
    tempInterestRateStartTime?: number; // 临时利率开始时间
  };
  institution: string;
  repaymentDay: number;
  installmentEndDate: number; // 最近一个到期日，只有到期和逾期时有值
  quota: {
    surplusQuota: string; // 可用额度（固额+临额）
    totalQuota: string; // 总额度（固额+临额）
  };
  creditContractStatus: CREDIT_CONTRACT_STATUS;
  surplusTotalAmount: string; // 待还金额
  minLoanAmount: string; // 最小支用金额
  maxLoanAmount: string; // 最大支用金额
  /** @deprecated 字段下线 */
  defaultFillAmount?: string; // 默认填充金额，仅自动填充金额版本有值
  overdueDays?: number; // 逾期天数
  surplusPenalty?: string; // 待还罚息
  surplusBillStatus: SurplusBillStatus; // 待还账单状态
  surplusQuotaStatus: SurplusQuotaStatus; // 可用额度状态
  institutionSystemShutDown: boolean;
  loanAdmitted?: boolean;
  loanRejectReason?: RejectReasonDTO;
  creditClosing?: boolean;
  platformPromotionOfferList?: PromotionOffer[];
  institutionPromotionOfferList: InstitutionPromotionOffer[];
  recommendAmountList?: RecommendAmount[]; // 推荐额度
  loanVersion: LOAN_VERSION; // 支用版本
  preFillAmount?: PreFillAmount; // 预填实体
}

export interface announcementItem {
  status: string;
  url: string;
  content: string;
  actionText: any;
}

export interface PopMessageItem {
  content: string;
  popMessageId: number;
  popMessageType: string;
}

export interface QueryAnnouncementRes {
  announcementList: announcementItem[];
}

export interface QueryPopMessageRes {
  popMessageList: PopMessageItem[];
}
export interface ReminderMessageItem {
  content: string;
  url: string;
  reminderMessageId: string;
  reminderStyle: 'BUBBLE';
}

export interface QueryReminderMessageRes {
  reminderMessageList: ReminderMessageItem[];
}

export interface updateCustomerProfileRes {
  changeOrderId: string; // 变更单id
  status: string; // 变更状态(初始化-INIT, 处理中-PROCESSING, 成功-SUCCEEDED, 失败-FAILED)
  rejectReason: any;
}

// 查询客户资料
export function queryCustomerProfile(data?): Promise<CustomerProfileRes> {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.valid.customer.profile.query',
    data,
  });
}

// 查询授信合约 额度/利率
export function queryCreditContract(data?) {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.credit.latest.contract.query',
    data,
  });
}

// 查询首页展示的数据
export function queryLoanSchemaInfo(data?): Promise<LoanSchemaInfoRes> {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.loan.account.query',
    data,
  });
}

// 查询公告
export function queryAnnouncement(data?): Promise<QueryAnnouncementRes> {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.support.announcement.query',
    data,
  });
}

// 查询弹窗消息
export function queryPopMessage(data?): Promise<QueryPopMessageRes> {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.support.pop.message.query',
    data,
  });
}

// 弹窗消息曝光
export function exposePopMessage(data?) {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.support.pop.message.expose',
    data,
  });
}

// 查询提醒消息
export function queryReminderMessage(data?): Promise<QueryReminderMessageRes> {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.support.reminder.message.query',
    data,
  });
}

// 提醒消息曝光上报
export function exposeReminderMessage(data?) {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.support.reminder.message.expose',
    data,
  });
}

// 退出咨询
export function consultReason(data?): Promise<any> {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.credit.close.consult',
    data,
  });
}

// 客户资料资料更新咨询
export function customerProfileConsult(data?) {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.customer.profile.consult',
    data,
  });
}

// 客户资料资料更新咨询
export function updateCustomerProfile(data?): Promise<updateCustomerProfileRes> {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.customer.profile.update',
    data,
  });
}


export function queryCouponList(data?) {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.coupon.page.query',
    data,
  });
}

