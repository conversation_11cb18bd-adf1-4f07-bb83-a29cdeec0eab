/**
 * @file 绑卡相关接口
 * <AUTHOR>
 */

import { MtopGet, MtopPost } from '@/utils/mtop';

export type BindCardConsultRequest = BaseRequest;

export interface BankDTO {
  bankCode: string;
  bankName: string;
}

export interface BindCardConsultResponse {
  name: string;
  licenseNo: string;
  institution: string;
  supportBankList: BankDTO[];
  admitted?: boolean;
  rejectReason?: {
    rejectCode?: string;
    rejectReason?: string;
  };
}

export function consult() {
  return MtopGet<BindCardConsultRequest, BindCardConsultResponse>({
    api: 'mtop.alibaba.fin.tao.blp.customer.bind.card.consult',
    data: {},
  });
}

export interface BindCardCheckRequest extends BaseRequest {
  bankCardNo: string;
  phone: string;
  institution: string;
}

export interface BindCardCheckResponse {
  admitted?: boolean;
  rejectReason: {
    rejectCode: string;
    rejectReason: string;
  };
}

export function check(data: BindCardCheckRequest) {
  return MtopGet<BindCardCheckRequest, BindCardCheckResponse>({
    api: 'mtop.alibaba.fin.tao.blp.customer.bind.card.check',
    data,
  });
}

export interface BindCardApplyRequest extends BaseRequest {
  requestId: string;
  bankCardNo: string;
  bankCode: string;
  bankName: string;
  phone: string;
  institution: string;
  applyTime: number;
}

export interface BindCardApplyResponse {
  admitted?: boolean;
  rejectReason: {
    rejectCode: string;
    rejectReason: string;
  };
  bindCardOrderId: string;
  status: 'INIT' | 'APPLYING' | 'SUCCEEDED' | 'FAILED' | 'CANCELLED';
}

export function apply(data: BindCardApplyRequest) {
  return MtopPost<BindCardApplyRequest, BindCardApplyResponse>({
    api: 'mtop.alibaba.fin.tao.blp.customer.bind.card.apply',
    data,
  });
}

export interface BindCardConfirmRequest extends BaseRequest {
  smsCode: string;
  bindCardOrderId: string;
  requestId: string;
}

export interface BindCardConfirmResponse {
  result?: boolean;
  failedReasonVO?: {
    failedCode?: 'SMS_CODE_TIMEOUT';
    failedReason?: string;
  };
}

export function confirm(data: BindCardConfirmRequest) {
  return MtopPost<BindCardConfirmRequest, BindCardConfirmResponse>({
    api: 'mtop.alibaba.fin.tao.blp.customer.bind.card.confirm',
    data,
  });
}

export interface BindCardChangeMasterCardRequest extends BaseRequest {
  oldMasterCardNo: string;
  newMasterCardNo: string;
  requestId: string;
  institution?: string;
}

export interface BindCardChangeMasterCardResponse {
  result?: boolean;
  rejectReason?: {
    rejectCode?: 'INST_NOT_SUPPORT_CHANGE_MASTER_CARD' | 'CURRENT_CARD_ALREADY_MASTER_CARD' | 'CHANGE_MASTER_CARD_SYSTEM_ERROR';
  };
}

export function changeMasterCard(data: BindCardChangeMasterCardRequest) {
  return MtopPost<BindCardChangeMasterCardRequest, BindCardChangeMasterCardResponse>({
    api: 'mtop.alibaba.fin.tao.blp.customer.bind.card.changeMasterCard',
    data,
  });
}

export interface UnbindCardConsultRequest extends BaseRequest {
  bankCardNo?: string;
  bankCode?: string;
  bankName?: string;
  bindCardNo?: string;
  institution?: string;
}
export interface UnbindCardConsultResponse {
  consultResult?: boolean;
  needAuthentication?: boolean;
  rejectReason?: {
    rejectCode: 'BIND_CARD_CNT_ONE' | 'MASTER_CARD_CANNOT_UNBIND';
    rejectDesc: string;
  };
}

export function unbindConsult(data: UnbindCardConsultRequest) {
  return MtopPost<UnbindCardConsultRequest, UnbindCardConsultResponse>({
    api: 'mtop.alibaba.fin.tao.blp.customer.unbind.card.consult',
    data,
  });
}

export interface UnbindCardCreateRequest extends BaseRequest {
  bizId: string;
  requestId: string;
  origin?: 'APP';
  authType?: 'FACE';
  authScene?: 'UNBIND';
}
export interface UnbindCardCreateResponse {
  taskToken?: string;
}

export function unbindCreate(data: UnbindCardCreateRequest) {
  return MtopPost<UnbindCardCreateRequest, UnbindCardCreateResponse>({
    api: 'mtop.alibaba.fin.tao.blp.authentication.create',
    data,
  });
}

export interface UnBindCardRequest extends BaseRequest {
  requestId: string;
  applyTime: number;
  bankCardNo?: string;
  bankCode?: string;
  bankName?: string;
  bindCardNo?: string;
  institution?: string;
  extension?: {
    authToken?: string;
  };
}

export interface UnBindCardResponse {
  unbindResult?: boolean;
  failReason?: {
    failedCode?: 'BIND_CARD_CNT_ONE' | 'MASTER_CARD_CANNOT_UNBIND' | 'INST_UNBIND_FAILED' | 'CIF_UNBIND_FAILED';
    failedDesc?: string;
  };
  unBindCardOrderId?: string;
  status?: 'INIT' | 'INST_UNBIND' | 'SUCCEEDED' | 'FAILED';
}

export function unBindApply(data: UnBindCardRequest) {
  return MtopPost<UnBindCardRequest, UnBindCardResponse>({
    api: 'mtop.alibaba.fin.tao.blp.customer.unbind.card.apply',
    data,
  });
}

export interface BankCardDTO {
  bankCode?: string;
  bankName?: string;
  bankCardNo?: string;
  bindCardNo?: string;
  masterCard?: boolean;
  bindCardTime?: number;
  value?: string;
  institution?: string;
}

export type BindQueryListRequest = BaseRequest;

export interface BindQueryListResponse {
  result?: BankCardDTO[];
}

export function queryList() {
  return MtopPost<BindQueryListRequest, BindQueryListResponse>({
    api: 'mtop.alibaba.fin.tao.blp.customer.bind.card.query.list',
    data: {},
  });
}

export interface QueryBankCardInfoRequest extends BaseRequest {
  tenant?: string;
  cardNo: string;
}

export interface QueryBankCardInfoResponse {
  binId?: string;
  alias?: string;
  binValue?: string;
  length?: string;
  type?: string;
  insId?: string;
  bankAccountClass?: string;
  bankName?: string;
  bankRegion?: string;
  bankType?: string;
  bankCode?: string;
  bankInsId?: string;
  bankEnname?: string;
  bankTop?: string;
  bankPinyinName?: string;
  bankIconUrl?: string;
}

export function queryBankCardInfo(data: QueryBankCardInfoRequest) {
  return MtopGet<QueryBankCardInfoRequest, QueryBankCardInfoResponse>({
    api: 'mtop.taobao.fin.common.bankcard.querybankcardinfo',
    data,
  });
}
