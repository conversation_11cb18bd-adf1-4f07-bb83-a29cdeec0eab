/**
 * @file 支用格式化方法
 */

import type { LoanStore, ApplyFormData, TrialResponse, ApplyRequest, ApplyExtension, ReceiveBankCard } from '@/store/loan/types';
import type { UnsignedAgreementsExtension, UnsignedAgreementList, QueryUnSignAgreementListResponse } from '@/store/agreement/actions';
import type { LOAN_VERSION, SecureToken, SupplementDataFormData } from '@/store/types';
import { requestId, getApplyTime, _ } from '@/utils';
import type { LoanSchemaInfoRes, PreFillAmount, PreFillAmountType } from '@/store/center/actions';
import { getOriginFromSessionStorage } from '@/utils/session';

interface ExtensionOptions extends SecureToken {
  applyFormData?: ApplyFormData;
  schemeStore?: LoanStore;
  trialStore?: TrialResponse;
  loanAgreements?: QueryUnSignAgreementListResponse;
  envData?: unknown;
  formData?: ApplyFormData;
}

export interface HomeLoanApplyFormData {
  loanApplyAmount?: string;
  loanMode?: {
    repaymentMethod?: string;
    loanTerm?: {
      value?: string;
      loanTermUnit?: string;
    };
  };
  couponCodeList?: string[];
  institution?: string;
  agreementCheck?: boolean;
  receiveBankCard?: ReceiveBankCard;
  supplementData?: SupplementDataFormData;
  loanPurpose?: Option;
  loanVersion?: LOAN_VERSION;
  preFillAmountType?: PreFillAmountType;
}

export interface InitOption {
  umidToken?: string;
  apdidToken?: string;
  loanVersion: LOAN_VERSION;
  preFillAmount?: PreFillAmount;
}

export function getAgreementQueryExtension(options: ExtensionOptions) {
  if (options.trialStore) {
    const { extension, capitalInstitution } = options.trialStore;
    return {
      fundSupplierCode: _.first(capitalInstitution)?.institutionCode,
      extension: {
        authRecommend: extension?.authRecommend,
        isNeedFaceScan: extension?.isNeedFaceScan,
        creditProdCode: extension?.creditProdCode,
        passthroughInfo: {
          lendCalc: extension?.lendCalc,
        },
      },
    };
  }
  return {};
}

export function getAgreementPreviewExtension(
  currentGroup: UnsignedAgreementList, options: ExtensionOptions,
) {
  try {
    if (currentGroup && options?.trialStore && options?.formData) {
      const { extension: trialExtension, capitalInstitution } = options.trialStore;
      const { receiveBankCard } = options.formData;
      const { extension: agreementExtension } = currentGroup;
      return {
        fundSupplierCode: _.first(capitalInstitution)?.institutionCode,
        extension: {
          bankCardId: receiveBankCard?.bindCardNo,
          authRecommend: trialExtension?.authRecommend,
          isNeedFaceScan: trialExtension?.isNeedFaceScan,
          creditProdCode: trialExtension?.creditProdCode,
          passthroughInfo: {
            agreementExposeLend: agreementExtension?.passthroughInfo?.agreementExposeLend,
            lendCalc: trialExtension?.lendCalc,
          },
        },
      };
    }
    return {};
  } catch (e) {
    return {};
  }
}

export function getLoanAgreementsExtensions(
  loanAgreements?: QueryUnSignAgreementListResponse,
): UnsignedAgreementsExtension {
  try {
    if (loanAgreements) {
      const targetList = _.filter(loanAgreements.unSignedAgreementGroupList, {
        source: 'MAYI_ZHIXIN',
      });
      if (targetList?.length === 1) {
        const target = _.first(targetList);
        return target?.extension || {};
      }
    }
    return {};
  } catch (e) {
    return {};
  }
}

export function getApplyExtension(options: ExtensionOptions): ApplyExtension | null {
  if (options) {
    const {
      applyFormData,
      schemeStore,
      trialStore,
      loanAgreements,
      envData,
      umidToken,
      apdidToken,
    } = options;
    const unsignedAgreementExtension = getLoanAgreementsExtensions(loanAgreements);
    const base = {
      envData,
      surplusQuota: trialStore?.extension?.surplusQuota,
      creditQuota: trialStore?.extension?.creditQuota,
      zhiXinApplyExt: {
        isNeedFaceScan: trialStore?.extension?.isNeedFaceScan,
        authRecommend: trialStore?.extension?.authRecommend,
        creditProdCode: trialStore?.extension?.creditProdCode,
        passthroughInfo: {
          lendElements: schemeStore?.extension?.lendElements,
          lendCalc: trialStore?.extension?.lendCalc,
          agreementExposeLend: unsignedAgreementExtension?.passthroughInfo?.agreementExposeLend,
        },
      },
    };
    if (umidToken) {
      _.set(base, 'umidToken', umidToken);
    }
    if (apdidToken) {
      _.set(base, 'apdidToken', apdidToken);
    }

    _.set(base, 'preFillAmountType', applyFormData?.preFillAmountType);

    _.set(base, 'origin', getOriginFromSessionStorage());
    return base;
  }
  // 这里是否要报错
  return null;
}

function formatReceiveBankCard(store: LoanStore) {
  const receiveBankCard = _.first(_.filter(store?.loanBankCard?.options, {
    value: store?.loanBankCard?.defaultValue,
  }));
  if (receiveBankCard?.value) {
    return {
      bindCardNo: receiveBankCard?.value,
      bankCardNo: receiveBankCard?.bankCardNo,
      bankCode: receiveBankCard?.bankCode,
      bankName: receiveBankCard?.bankName,
    };
  }
  return null;
}

export function getInitLoanScheme(
  store: LoanStore,
  loanVersion: LOAN_VERSION,
  preFillAmount?: PreFillAmount,
) {
  const repaymentMethod = preFillAmount?.repaymentMethod || store?.repaymentMethod?.defaultValue;
  const loanTerm = preFillAmount?.loanTerm && preFillAmount?.loanTermUnit ? {
    value: _.toString(preFillAmount?.loanTerm),
    loanTermUnit: preFillAmount?.loanTermUnit,
  } : store?.loanTerm?.defaultValue;
  const receiveBankCard = formatReceiveBankCard(store);
  const institution = store?.institution;
  const loanPurpose = store?.loanPurpose?.defaultValue;
  return {
    loanMode: {
      repaymentMethod,
      loanTerm,
    },
    receiveBankCard,
    institution,
    loanPurpose,
    agreementCheck: false,
    loanVersion,
    preFillAmountType: preFillAmount?.type,
  };
}

export function formatApplyFormData(data: HomeLoanApplyFormData): ApplyFormData {
  const { loanMode = {}, ...other } = data;
  return {
    ...other,
    ...loanMode,
  };
}

export function getTrailScheme(data: ApplyFormData) {
  const {
    loanApplyAmount,
    repaymentMethod,
    loanTerm,
    institution,
    loanVersion,
    preFillAmountType,
    recommendAmount,
  } = data;

  const recommendAmountJSON = recommendAmount ? JSON.stringify(_.omit(recommendAmount, ['class'])) : null;

  return {
    loanApplyAmount,
    repaymentMethod,
    institution,
    loanTerm: loanTerm?.value,
    loanTermUnit: loanTerm?.loanTermUnit,
    loanVersion,
    trialTime: Date.now(),
    extension: {
      preFillAmountType,
      origin: getOriginFromSessionStorage(),
    },
    recommendAmount: recommendAmountJSON,
  };
}

interface GetApplySchemeOptions extends SecureToken {
  applyFormData: ApplyFormData;
  trialStore: TrialResponse;
  schemeStore?: LoanStore;
  loanAgreements?: QueryUnSignAgreementListResponse;
  envData?: unknown;
}

export function getApplyScheme(options: GetApplySchemeOptions): ApplyRequest {
  const { applyFormData, trialStore, schemeStore, loanAgreements, envData, umidToken, apdidToken } = options;
  const {
    loanTerm, loanPurpose, loanApplyAmount, repaymentMethod,
    institution, receiveBankCard, loanVersion,
  } = applyFormData;
  const {
    capitalInstitution, promotionAmount, baseInterestRate,
    interestRate, rateUnit, repaymentDay, interest, couponList,
  } = trialStore;
  const couponCodeList = (couponList || []).map((item) => item.couponCode);
  const extension = getApplyExtension({
    applyFormData,
    schemeStore,
    trialStore,
    loanAgreements,
    envData,
    umidToken,
    apdidToken,
  });
  const baseScheme: any = {
    requestId: requestId(),
    applyTime: getApplyTime(),
    loanApplyAmount,
    repaymentMethod,
    institution,
    receiveBankCard,
    loanTerm: loanTerm?.value,
    loanTermUnit: loanTerm?.loanTermUnit,
    loanPurpose: loanPurpose?.value,
    capitalInstitution,
    promotionAmount,
    baseInterestRate,
    interestRate,
    rateUnit,
    repaymentDay,
    interest,
    channel: 'APP',
    extension,
    loanVersion,
    couponCodeList,
  };
  return baseScheme;
}

export function getConsultOptions(options: InitOption) {
  if (options?.umidToken || options?.apdidToken) {
    const { umidToken, apdidToken, loanVersion, preFillAmount } = options;
    const extension = {};
    if (umidToken) {
      _.set(extension, 'umidToken', umidToken);
    }
    if (apdidToken) {
      _.set(extension, 'apdidToken', apdidToken);
    }

    _.set(extension, 'preFillAmountType', preFillAmount?.type);

    _.set(extension, 'origin', getOriginFromSessionStorage());

    return {
      extension,
      loanVersion,
    };
  }
  return {
    loanVersion: options?.loanVersion,
  };
}

/** 是否已达支用最大笔数 */
export function checkLoanLimit(payload?: LoanSchemaInfoRes) {
  return payload?.loanAdmitted === false &&
    payload?.loanRejectReason?.rejectCode === 'ON_GOING_CONTRACT_MORE_THAN_UPPER_LIMIT';
}
