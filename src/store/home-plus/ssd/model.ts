/**
 * @file 支用页面model
 */

import { useCallback, useRef, useState } from 'react';
import { log } from '@alife/dtao-iec-spm-log';

import {
  getApplyScheme,
  getInitLoanScheme,
  getTrailScheme,
  getAgreementQueryExtension,
  getConsultOptions,
  InitOption,
  checkLoanLimit,
} from './format';
import {
  formatLoanTermList,
  formatRecommendAmountList,
  formatRepaymentMethodList,
  getFormatLoanPurpose,
} from '@/store/loan/format';
import { apply, consultLoanSchema, trial, applyAdmit } from '@/store/loan/actions';
import { queryUnSignAgreementList } from '@/store/agreement/actions';
import { getForceReadAgreements } from '@/store/agreement/format';
import { _, isMtopErrorCreditContractNotExist } from '@/utils';
import { loanApplyAmountValidator, loanTrialValidator, loanApplyValidator } from '@/store/loan/validator';
import { PAGES } from '@/common/constant';
import LinkUtil from '@/utils/link';
import type { QueryUnSignAgreementListResponse } from '@/store/agreement/actions';
import type {
  LoanStore, TrialResponse, ApplyFormData,
  PROCESS_ACTION, ConsultLoanSchemaRequest,
} from '@/store/loan/types';
import type { SecureToken } from '@/store/types';
import { LoanSchemaInfoRes } from '@/store/center/actions';
import { DISABLED_SUBMIT_STATUS } from '@/store/loan';

import { DoUpdateOption } from './type';

interface DoApplyOption extends SecureToken {
  applyFormData: ApplyFormData;
  envData?: unknown;
  exposePlatformPromotionOfferList?: string;
}

interface LoanAgreements {
  payLoad?: QueryUnSignAgreementListResponse;
  forceRead?: boolean;
}

export function useSsdLoan(loanSchemaInfo?: LoanSchemaInfoRes) {
  const [schemeStore, setSchemeStore] = useState<LoanStore>();
  const [trialStore, setTrialStore] = useState<TrialResponse>();
  const [loanAgreements, setLoanAgreements] = useState<LoanAgreements>();
  const [processAction, setProcessAction] = useState<PROCESS_ACTION>();
  const maxLoanAmount = loanSchemaInfo?.maxLoanAmount;

  const trialRequestId = useRef<number>(0);

  const checkLoanApplyAmount = useCallback(async (value?: string) => {
    try {
      if (schemeStore?.loanSchemaQuota && value) {
        const { minStartLoanAmount: min, step } = schemeStore.loanSchemaQuota;
        const max = maxLoanAmount;
        if (max && min && step) {
          await loanApplyAmountValidator(value, {
            max,
            min,
            step,
          });
          return true;
        }
      }
      throw new Error();
    } catch (e) {
      return false;
    }
  }, [schemeStore?.loanSchemaQuota, maxLoanAmount]);

  const doQueryAgreements = useCallback(async (extra: any) => {
    const res = await queryUnSignAgreementList({
      bizType: 'LOAN',
      creditPlatform: 'MAYI_ZHIXIN',
      ...extra || {},
    });
    const forceAgreements = getForceReadAgreements(res);
    setLoanAgreements({
      payLoad: res,
      forceRead: forceAgreements?.length > 0,
    });
  }, []);

  const doTrial = useCallback(async (scheme: ApplyFormData) => {
    const currentRequestId = ++trialRequestId.current;
    setProcessAction('TRIALING');
    try {
      const res = await trial(getTrailScheme(scheme));
      // NOTE: 丢弃过时的试算结果
      if (currentRequestId !== trialRequestId.current) {
        return;
      }
      const checkRes = loanTrialValidator(res);
      if (checkRes?.check) {
        const extra = getAgreementQueryExtension({
          trialStore: res,
        });
        setTrialStore(res);
        await doQueryAgreements({
          institutionList: JSON.stringify([scheme?.institution]),
          ...extra,
        });
        setProcessAction('NULL');
        return res;
      } else {
        setTrialStore({});
        throw res;
      }
    } catch (e) {
      setProcessAction('NULL');
      setTrialStore({});
      throw e;
    }
  }, []);

  const doConsult = useCallback(async (option: ConsultLoanSchemaRequest) => {
    try {
      const res = await consultLoanSchema(option);
      if (res) {
        const { loanTerm, repaymentMethod, loanSchemaQuota, loanPurpose, admitted } = res;
        if (admitted) {
          if (loanTerm?.options && loanTerm?.defaultValue) {
            loanTerm.defaultValue.value = _.toString(loanTerm?.defaultValue?.loanTerm);
            loanTerm.options = formatLoanTermList(loanTerm?.options);
          }
          if (repaymentMethod?.options) {
            repaymentMethod.options = formatRepaymentMethodList(repaymentMethod?.options);
          }
          if (loanPurpose?.defaultValue) {
            // @ts-ignore
            loanPurpose.defaultValue = getFormatLoanPurpose(
              loanPurpose.defaultValue, loanPurpose.options,
            );
          }
          if (loanSchemaQuota?.recommendAmountList) {
            _.set(
              res,
              'loanSchemaQuota.recommendAmountList',
              formatRecommendAmountList(
                loanSchemaQuota.recommendAmountList, maxLoanAmount,
              ),
            );
          }
        }
        return res;
      }
      throw new Error('CONSULT_FETCH_ERROR');
    } catch (e) {
      if (isMtopErrorCreditContractNotExist(e)) {
        LinkUtil.resetToPage(PAGES.SsdCreditLpSimple);
      }
      throw new Error('CONSULT_FETCH_ERROR');
    }
  }, [maxLoanAmount]);

  const addInitLog = (res: any) => {
    try {
      log.addVisitLog(`${PAGES.SsdHomePlus}-loan`, {
        firstLoanFlag: res?.extension?.firstLoanFlag,
        institution: res?.institution,
      });
    } catch (e) {}
  };

  const doInit = useCallback(async (options: InitOption) => {
    setProcessAction('FETCHING');
    try {
      const consultOption = getConsultOptions(options);
      const consultRes = await doConsult(consultOption);
      addInitLog(consultRes);
      setProcessAction('NULL');
      setSchemeStore(consultRes);
      if (consultRes?.admitted) {
        const initLoanScheme = getInitLoanScheme(
          consultRes,
          options?.loanVersion,
          options?.preFillAmount,
        );
        return {
          initLoanScheme,
          maxLoanAmount,
        };
      }
      return {};
    } catch (e: any) {
      setSchemeStore({
        admitted: false,
        rejectReason: {
          rejectDesc: '',
          rejectCode: 'REFRESH',
        },
      });
      setProcessAction('NULL');
      return {};
    }
  }, [doConsult, maxLoanAmount]);

  const doApplyAdmit = useCallback(async (options: DoApplyOption) => {
    setProcessAction('SUBMITTING');
    try {
      // debugger
      if (trialStore) {
        const { applyFormData, envData, umidToken, apdidToken } = options;
        const applyScheme = getApplyScheme({
          applyFormData,
          schemeStore,
          trialStore,
          loanAgreements: loanAgreements?.payLoad,
          envData,
          umidToken,
          apdidToken,
        });
        const checkRes = loanApplyValidator(applyScheme);
        if (!checkRes?.check) {
          throw checkRes;
        }
        const applyAdmitRes = await applyAdmit(applyScheme);
        if (!applyAdmitRes?.admitted) {
          setProcessAction('NULL');
        }
        return applyAdmitRes;
      }
      throw new Error('DO_APPLY_ADMIT_FAILED');
    } catch (e) {
      setProcessAction('NULL');
      return {
        admitted: false,
        extension: {},
        rejectReason: {},
      };
    }
  }, [trialStore, schemeStore, loanAgreements]);

  const doApply = useCallback(async (options: DoApplyOption) => {
    setProcessAction('SUBMITTING');
    try {
      if (trialStore) {
        const { applyFormData, envData, umidToken, apdidToken, exposePlatformPromotionOfferList } = options;
        const applyScheme = getApplyScheme({
          applyFormData,
          schemeStore,
          trialStore,
          loanAgreements: loanAgreements?.payLoad,
          envData,
          umidToken,
          apdidToken,
        });
        _.set(applyScheme, 'exposePlatformPromotionOfferList', exposePlatformPromotionOfferList);
        const applyResult = await apply(applyScheme);
        return applyResult;
      }
      throw new Error('DO_APPLY_FAILED');
    } catch (e) {
      setProcessAction('NULL');
      throw e;
    }
  }, [trialStore, schemeStore, loanAgreements]);

  const doUpdate = useCallback(async (option: DoUpdateOption, noCheck?: boolean) => {
    try {
      const { name, data } = option;
      switch (name) {
        case 'loanApplyAmount': {
          const checkRes = noCheck || await checkLoanApplyAmount(data?.loanApplyAmount);
          if (checkRes) {
            return await doTrial(data);
          } else {
            setTrialStore({});
            return null;
          }
        }
        case 'loanMode':
        case 'couponCodeList':
          return await doTrial(data);
        default: return null;
      }
    } catch (e) {
      throw e;
    }
  }, [doTrial, checkLoanApplyAmount]);

  const checkSubmitDisabled = useCallback(() => {
    if (_.isEmpty(schemeStore)) {
      return true;
    }
    if (checkLoanLimit(loanSchemaInfo)) {
      log.addShowLog('ssd-home-loan-limited');
      return true;
    }
    if (loanSchemaInfo?.surplusQuotaStatus === 'EXHAUSTED') {
      log.addShowLog('ssd-home-exhausted');
      return true;
    }
    if (loanSchemaInfo?.surplusQuotaStatus === 'INSUFFICIENT') {
      log.addShowLog('ssd-home-insufficient');
      return true;
    }
    if (loanSchemaInfo?.surplusQuotaStatus === 'FROZEN') {
      log.addShowLog('ssd-home-frozen');
      return true;
    }
    if (_.includes(DISABLED_SUBMIT_STATUS, processAction)) {
      return true;
    }
    return false;
  }, [loanSchemaInfo, processAction, schemeStore]);

  const changeProcessAction = useCallback((action: PROCESS_ACTION) => {
    setProcessAction(action);
  }, []);

  const doClear = useCallback(() => {
    setSchemeStore(undefined);
    setTrialStore(undefined);
    setLoanAgreements(undefined);
    setProcessAction(undefined);
  }, []);

  return {
    schemeStore,
    trialStore,
    loanAgreements,
    processAction,
    doInit,
    doTrial,
    doApplyAdmit,
    doApply,
    doUpdate,
    checkSubmitDisabled,
    changeProcessAction,
    doClear,
  };
}
