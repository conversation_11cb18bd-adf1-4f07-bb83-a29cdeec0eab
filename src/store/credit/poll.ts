/**
 * @file 授信相关轮询
 */

import { useCallback, useState, useRef } from 'react';
import { log } from '@alife/dtao-iec-spm-log';
import { isNull } from 'lodash-es';

import { queryCreditOrder, QueryCreditOrderRequest } from './actions';
import type { CREDIT_APPLY_ORDER_DTO, CREDIT_APPLY_ORDER_SUB_STATUS } from '@/store/types';
import { _ } from '@/utils';

interface useQueryPollOptions {
  pure?: boolean;
  time?: number;
}

export interface QueryPollOption {
  end: CREDIT_APPLY_ORDER_SUB_STATUS[];
  request: QueryCreditOrderRequest;
  onEnd?: () => void;
  onOver?: () => void;
}

export function useQueryPoll(options?: useQueryPollOptions) {
  const { pure = false, time = 2000 } = options || {};
  const [payload, setPayLoad] = useState<CREDIT_APPLY_ORDER_DTO | null>();
  const timerRef = useRef<any>({
    timer: null,
    times: 99,
  });

  const safeQuery = useCallback(async (request: any) => {
    try {
      const queryRes = await queryCreditOrder(request);
      return queryRes;
    } catch (e) {
      log.addErrorLog('credit-query-poll-error');
      return null;
    }
  }, []);

  // 简易轮询
  const doPoll = async (option: QueryPollOption) => {
    const { request, end } = option;
    const queryRes = await safeQuery(request);
    // 第一次轮询就失败则抛错，非第一次轮询失败，继续查询
    if (isNull(queryRes) && timerRef.current.times === 99) {
      throw new Error('FIRST_POLL_ERROR');
    }
    timerRef.current.times--;
    if (queryRes && JSON.stringify(payload) !== JSON.stringify(queryRes) && !pure) {
      setPayLoad(queryRes);
    }
    if (!timerRef.current.times) {
      clearTimeout(timerRef.current.timer);
      return;
    }
    if (!_.includes(end, queryRes?.status)) {
      timerRef.current.timer = setTimeout(() => {
        doPoll(option);
      }, time);
    } else {
      clearTimeout(timerRef.current.timer);
      option?.onEnd && option.onEnd();
    }
  };

  const doRest = () => {
    setPayLoad(null);
  };

  return {
    doPoll,
    doRest,
    payload,
  };
}
