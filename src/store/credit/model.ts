import { _ } from '@/utils';
import type { CREDIT_APPLY_CONSULT_DTO } from '../types';
import { PAGES } from '@/common/constant';

const splitFn = (str: string) => str.split('.');

export const APPLICATION_DATA_FIELD_MAP = {
  licenseName: splitFn('license.name'),
  licenseNo: splitFn('license.licenseNo'),
  licenseType: splitFn('license.licenseType'),
  licensePictureFront: splitFn('license.pictureFront'),
  licensePictureBack: splitFn('license.pictureBack'),
  licenseAddress: splitFn('license.licenseAddress'),
  phone: splitFn('phone.phoneNo'),
  licenseEffectDate: splitFn('license.licenseEffectDate'),
  licenseExpiredDate: splitFn('license.licenseExpiredDate'),
  licenseLongTermEffective: splitFn('license.licenseLongTermEffective'),
  licenseNation: splitFn('license.nation'),
  licenseBirthday: splitFn('license.birthday'),
  licenseGender: splitFn('license.gender'),
  licenseAuthDepartment: splitFn('license.licenseAuthDepartment'),
  attachmentVOList: splitFn('license.attachmentVOList'),
};

export const PROFILE_DATA_FIELD_MAP = {
  licenseName: 'name',
  licenseNo: 'licenseNo',
  licenseType: 'licenseType',
  licensePictureFront: 'pictureFront',
  licensePictureBack: 'pictureBack',
  licenseEffectDate: 'licenseEffectDate',
  licenseAddress: 'licenseAddress',
  licenseExpiredDate: 'licenseExpiredDate',
  licenseLongTermEffective: 'licenseLongTermEffective',
  licenseNation: 'nation',
  licenseBirthday: 'birthday',
  licenseGender: 'gender',
  licenseAuthDepartment: 'licenseAuthDepartment',
  attachmentVOList: 'attachmentVOList',
};

export function checkAliyunCreditRouter(data: CREDIT_APPLY_CONSULT_DTO) {
  // 默认走云
  if (_.includes(['QUALIFIED', 'FROZEN'], data?.creditContractStatus)) {
    // 已开通
    return PAGES.Home;
  }
  if (!data?.admitted) {
    // 不准入跳到兜底页
    return PAGES.CreditFallback;
  }
  if (!data?.latestCreditApplyOrder && !data?.canCreditApply) {
    // 不准入跳到兜底页
    return PAGES.CreditFallback;
  }
  // 非阿里云
  if (data?.creditPlatform !== 'ALIYUN') {
    return PAGES.Index;
  }

  if (data?.creditType === 'NORMAL_CREDIT') {
    return PAGES.CreditLpSSR;
  }

  return PAGES.CreditSimpleLp;
}

export function checkMayizhixinCreditRouter(data: CREDIT_APPLY_CONSULT_DTO) {
  // 非蚂蚁智信
  if (data?.creditPlatform !== 'MAYI_ZHIXIN') {
    return PAGES.Index;
  }
  // 有授信/冻结/清退 皆路由至首支一体页
  if (_.includes(['QUALIFIED', 'FROZEN', 'CLEARED'], data?.creditContractStatus)) {
    return PAGES.SsdHomePlus;
  }
  if (data?.creditType === 'NORMAL_CREDIT') {
    return PAGES.SsdCreditLpSSR;
  }
  return PAGES.SsdCreditLpSimple;
}
