/**
 * @file 授信相关接口
 */

import { MtopGet, MtopPost } from '@/utils/mtop';
import {
  CREDIT_APPLY_ORDER_DTO,
  CREDIT_APPLY_CONSULT_DTO,
  SupplementDataCollection,
  CREDIT_TYPE,
  CREDIT_TYPE_VERSION,
} from '@/store/types';
import { _, getOrigin, getApplyExtension } from '@/utils';

interface PostCreditApplyRes {
  creditApplyOrderId: string;
  status: string;
  subStatus: string;
  rejectReason: any;
}

function getConsultParams(data?: any) {
  try {
    const origin = getOrigin();

    return {
      extension: {
        origin,
      },
      ...data,
    };
  } catch (e) {
    return data;
  }
}

// 授信咨询
export function queryCreditApplyConsult(data?): Promise<CREDIT_APPLY_CONSULT_DTO> {
  const consultData = getConsultParams(data);
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.credit.apply.consult',
    data: consultData,
  });
}

// 更新基础资料
export function postCreditApplySupplement(data?): Promise<PostCreditApplyRes> {
  return MtopPost({
    api: 'mtop.alibaba.fin.tao.blp.credit.apply.supplement',
    data,
  });
}

export interface PostCreditApplyRequest {
  product?: string;
  tenant?: string;
  requestId?: string;
  applyType?: string;
  applyTime?: number;
  channel?: string;
  creditPlatform?: string;
  extension?: {
    passthroughInfo?: {
      agreementExposeAuth?: any;
      creditInstConsult?: any;
      agreementExposeCredit?: any;
    };
    fundSupplier?: {
      code?: string;
      name?: string;
    };
    creditTypeVersion?: CREDIT_TYPE_VERSION;
    umidToken?: string;
  };
  creditType?: CREDIT_TYPE;
  exposePlatformPromotionOfferList?: string;
}

function getPostApplyParams(data?: any) {
  try {
    // 在extension里面单独处理tracelog
    if (data?.extension) {
      return data;
    }
    // 没有extension字段统一处理
    _.set(data, 'extension', getApplyExtension());
    return data;
  } catch (e) {
    return data;
  }
}

// 授信申请
export function postCreditApply(data?: PostCreditApplyRequest): Promise<PostCreditApplyRes> {
  return MtopPost({
    api: 'mtop.alibaba.fin.tao.blp.credit.apply',
    data: getPostApplyParams(data),
  });
}

export interface QueryCreditOrderRequest {
  creditApplyOrderId?: string;
}

// 查询授信单
export function queryCreditOrder(data?: QueryCreditOrderRequest): Promise<CREDIT_APPLY_ORDER_DTO> {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.credit.apply.order.get',
    data,
  });
}

export async function queryCreditOrderSafe(request?: QueryCreditOrderRequest) {
  try {
    const queryRes = await queryCreditOrder(request);
    return queryRes;
  } catch (e) {
    return null;
  }
}

// 查询待补充资料
export function queryCreditSupplementInfomation(data?): Promise<SupplementDataCollection> {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.credit.pending.supplement.query',
    data,
  });
}


// 查询机构授信单
export function queryInstitutionCreditOrder(data?) {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.credit.order.latest.query',
    data,
  });
}
