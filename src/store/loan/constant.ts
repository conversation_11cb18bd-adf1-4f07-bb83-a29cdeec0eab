/**
 * @file 支用相关的
 */

import type { LOAN_STATUS } from '../types';

// 支用结束状态
export const LOAN_END_STATUS: LOAN_STATUS[] = [
  'SUCCEEDED',
  'FAILED',
  'CANCELLED',
];

// 申请结束状态
export const APPLY_END_STATUS: LOAN_STATUS[] = [
  'AUTHENTICATED',
  'LOANING',
  'SUCCEEDED',
  'FAILED',
  'CANCELLED',
];

// 首支一体支用申请结束状态
export const HOME_PLUS_APPLY_END_STATUS: LOAN_STATUS[] = [
  'SUCCEEDED',
  'FAILED',
  'CANCELLED',
];

export const FIELD_NANE_MAP = {
  loanApplyAmount: 'loanApplyAmount',
  loanTerm: 'loanTerm',
  repaymentMethod: 'repaymentMethod',
  couponCodeList: 'couponCodeList',
  loanTermUnit: 'loanTermUnit',
};

// 不可申请状态
export const DISABLED_SUBMIT_STATUS = [
  'FETCHING',
  'TRIALING',
  'SUBMITTING',
  'AUTENTICATING',
  'CONSULT_LOAN_RESULTING',
];
