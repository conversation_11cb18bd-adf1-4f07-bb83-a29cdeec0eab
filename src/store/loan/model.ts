/**
 * @file 支用页面model
 */

import { useCallback, useState } from 'react';

import { apply, consultLoanSchema, trial, applyAdmit } from './actions';
import {
  formatLoanTermList,
  formatRecommendAmountList,
  formatRepaymentMethodList,
  formatReceiveBankCard,
  getFormatLoanPurpose,
  getApplyScheme,
  getInitLoanScheme,
  getTrailScheme,
  getAgreementQueryExtension,
} from './format';
import type {
  LoanStore, TrialResponse, ApplyFormData, DoUpdateOption,
  PROCESS_ACTION, ConsultLoanSchemaRequest, LoanBankCard,
} from './types';
import { DISABLED_SUBMIT_STATUS } from './constant';
import { _, isMtopErrorCreditContractNotExist } from '@/utils';
import { loanApplyAmountValidator, loanTrialValidator, loanApplyValidator } from './validator';
import { PAGES } from '@/common/constant';
import LinkUtil from '@/utils/link';
import { setAmountValue, getAmountValue } from '@/utils/storage';
import { log } from '@alife/dtao-iec-spm-log';
import { queryUnSignAgreementList, QueryUnSignAgreementListResponse } from '../agreement/actions';
import { LoanModeValue, LoanTermDTO, RepaymentMethodDTO } from '../types';
import { isEmpty } from 'lodash-es';
import { loanAliyunPageLog } from '@/utils/goc';

interface LoanAgreements {
  payLoad?: QueryUnSignAgreementListResponse;
  forceRead?: boolean;
}

export function useLoan() {
  const [schemeStore, setSchemeStore] = useState<LoanStore>();
  const [trialStore, setTrialStore] = useState<TrialResponse>();
  const [processAction, setProcessAction] = useState<PROCESS_ACTION>();
  const [loanBankCardStore, setLoanBankCardStore] = useState<LoanBankCard>();
  const [loanAgreements, setLoanAgreements] = useState<LoanAgreements>();
  const [loanTermOptions, setLoanTermOptions] = useState<LoanTermDTO[]>();
  const [repaymentMethodOptions, setRepaymentMethodOptions] = useState<RepaymentMethodDTO[]>();

  const doQueryAgreements = async (extra: any) => {
    log.addOtherLog('lx-agreement-query');
    const res = await queryUnSignAgreementList({
      bizType: 'LOAN',
      creditPlatform: 'ALIYUN',
      ...extra || {},
    });
    setLoanAgreements({
      payLoad: res,
    });
  };

  const checkLoanApplyAmount = useCallback(async (value?: string) => {
    try {
      if (schemeStore?.loanSchemaQuota && value) {
        const { maxLoanAmount: max, minStartLoanAmount: min, step } = schemeStore.loanSchemaQuota;
        if (max && min && step) {
          await loanApplyAmountValidator(value, {
            max,
            min,
            step,
          });
          return true;
        }
      }
      throw new Error();
    } catch (e) {
      return false;
    }
  }, [schemeStore?.loanSchemaQuota]);

  const getStoreApplyAmount = useCallback(async (loanSchemaQuota: any) => {
    try {
      const storeLoanAmount = getAmountValue();
      if (storeLoanAmount === '') {
        return storeLoanAmount;
      }
      const { maxLoanAmount: max, minStartLoanAmount: min, step } = loanSchemaQuota;
      if (max && min && step) {
        await loanApplyAmountValidator(storeLoanAmount, {
          max,
          min,
          step,
        });
        return storeLoanAmount;
      }
      return null;
    } catch (e) {
      return null;
    }
  }, []);

  const doTrial = useCallback(async (scheme: ApplyFormData) => {
    setProcessAction('TRIALING');
    try {
      if (!scheme?.loanApplyAmount) {
        return;
      }
      const res = await trial(getTrailScheme(scheme));
      const checkRes = loanTrialValidator(res);
      if (checkRes?.check) {
        setTrialStore(res);
        if (scheme?.institution === 'LX') {
          const extra = getAgreementQueryExtension(scheme);
          await doQueryAgreements({
            ...extra,
          });
        }
        setProcessAction('NULL');
        return res;
      } else {
        setTrialStore({});
        throw res;
      }
    } catch (e) {
      setProcessAction('NULL');
      setTrialStore({});
      throw e;
    }
  }, []);

  const doConsult = useCallback(async (option: ConsultLoanSchemaRequest) => {
    try {
      const res = await consultLoanSchema(option);
      if (res) {
        const { loanTerm, repaymentMethod, loanSchemaQuota, loanBankCard, loanPurpose, admitted } = res;
        if (admitted) {
          if (loanTerm?.options && loanTerm?.defaultValue) {
            loanTerm.defaultValue.value = _.toString(loanTerm?.defaultValue?.loanTerm);
            setLoanTermOptions(formatLoanTermList(loanTerm?.options));
          }
          if (repaymentMethod?.options) {
            setRepaymentMethodOptions(formatRepaymentMethodList(repaymentMethod?.options));
          }
          if (loanPurpose?.defaultValue) {
            // @ts-ignore
            loanPurpose.defaultValue = getFormatLoanPurpose(
              loanPurpose.defaultValue, loanPurpose.options,
            );
          }
          setLoanBankCardStore(loanBankCard);
          if (loanSchemaQuota?.recommendAmountList) {
            _.set(
              res,
              'loanSchemaQuota.recommendAmountList',
              formatRecommendAmountList(
                loanSchemaQuota.recommendAmountList, loanSchemaQuota?.maxLoanAmount,
              ),
            );
          }
        }
        return res;
      }
      throw new Error('CONSULT_FETCH_ERROR');
    } catch (e) {
      if (isMtopErrorCreditContractNotExist(e)) {
        LinkUtil.resetToPage(PAGES.CreditLp);
      }
      throw new Error('CONSULT_FETCH_ERROR');
    }
  }, []);

  const addInitLog = (res: any) => {
    try {
      log.addVisitLog(PAGES.LoanApply, {
        firstLoanFlag: res?.extension?.firstLoanFlag,
        institution: res?.institution,
      });
    } catch (e) {}
  };

  const doInit = useCallback(async () => {
    setProcessAction('FETCHING');
    try {
      const consultRes = await doConsult({});
      addInitLog(consultRes);
      setSchemeStore(consultRes);
      if (consultRes?.admitted) {
        const storeAmountValue = await getStoreApplyAmount(consultRes?.loanSchemaQuota);
        const initLoanScheme = getInitLoanScheme(consultRes, storeAmountValue);
        return initLoanScheme;
      }
      setProcessAction('NULL');
      return {};
    } catch (e: any) {
      setSchemeStore({
        admitted: false,
        rejectReason: {
          rejectDesc: '系统开小差，请稍后重试',
          rejectCode: 'REFRESH',
        },
      });
      // 失败的异常在这里
      loanAliyunPageLog({ message: 'init-consult' });
      setProcessAction('NULL');
      return {};
    }
  }, [doConsult]);

  const doApplyAdmit = useCallback(async (data: ApplyFormData) => {
    setProcessAction('SUBMITTING');
    try {
      if (trialStore) {
        const applyScheme = getApplyScheme(data, trialStore);
        const checkRes = loanApplyValidator(applyScheme);
        if (!checkRes?.check) {
          throw checkRes;
        }
        const applyResult = await applyAdmit(applyScheme);
        if (!applyResult?.admitted) {
          setProcessAction('NULL');
        }
        return applyResult;
      }
      throw new Error('DO_APPLY_ADMIT_FAILED');
    } catch (e) {
      setProcessAction('NULL');
      return {
        admitted: false,
        rejectReason: {},
      };
    }
  }, [trialStore]);

  const doApply = useCallback(async (data: ApplyFormData) => {
    setProcessAction('SUBMITTING');
    try {
      if (trialStore) {
        const applyScheme = getApplyScheme(data, trialStore, schemeStore);
        const applyResult = await apply(applyScheme);
        setProcessAction('NULL');
        return applyResult;
      }
      throw new Error('DO_APPLY_FAILED');
    } catch (e) {
      setProcessAction('NULL');
      throw e;
    }
  }, [trialStore]);

  const doLoanModeChange = (option: LoanModeValue) => {
    const cloneOption = _.cloneDeep(option);
    const { changeName, loanTermValue, repaymentMethodValue } = option;
    if (changeName === 'loanTerm') {
      const newLoanTermOption = _.first(_.filter(schemeStore?.loanTerm?.options, {
        value: loanTermValue?.value,
        loanTermUnit: loanTermValue?.loanTermUnit,
      }));
      const cascadeRepaymentMethod = newLoanTermOption?.cascadeRepaymentMethod;
      if (!isEmpty(cascadeRepaymentMethod)) {
        setRepaymentMethodOptions(formatRepaymentMethodList(cascadeRepaymentMethod?.options));
      }
      // 查找是否在还款列表中
      const isRepaymentMethodEnable = !_.isEmpty(_.filter(cascadeRepaymentMethod?.options, {
        value: repaymentMethodValue,
        disabled: false,
      }));
      if (isEmpty(cascadeRepaymentMethod) || !cascadeRepaymentMethod?.defaultValue) {
        log.addErrorLog('aliyun-cascade-empty', {
          type: 'repayment-method',
        });
      }
      if (isRepaymentMethodEnable || isEmpty(cascadeRepaymentMethod)) {
        return cloneOption;
      } else {
        return {
          ...cloneOption,
          repaymentMethodValue: cascadeRepaymentMethod?.defaultValue || option?.repaymentMethodValue,
        };
      }
    } else if (changeName === 'repaymentMethod') {
      const newRepaymentMethodOption = _.first(_.filter(schemeStore?.repaymentMethod?.options, {
        value: repaymentMethodValue,
      }));
      const cascadeLoanTerm = newRepaymentMethodOption?.cascadeLoanTerm;
      if (!isEmpty(cascadeLoanTerm)) {
        setLoanTermOptions(formatLoanTermList(cascadeLoanTerm?.options));
      }
      // 查找是否在还款列表中
      const isloanTermEnable = !_.isEmpty(_.filter(cascadeLoanTerm?.options, {
        value: loanTermValue?.value,
        loanTermUnit: loanTermValue?.loanTermUnit,
        disabled: false,
      }));
      if (isEmpty(cascadeLoanTerm) || !cascadeLoanTerm?.defaultValue) {
        log.addErrorLog('aliyun-cascade-empty', {
          type: 'loan-term',
        });
      }
      if (isloanTermEnable || isEmpty(cascadeLoanTerm)) {
        return cloneOption;
      } else {
        return {
          ...cloneOption,
          loanTermValue: cascadeLoanTerm?.defaultValue || option?.loanTermValue,
        };
      }
    }
    return {};
  };

  const doUpdate = useCallback(async (option: DoUpdateOption) => {
    try {
      const { name, data } = option;
      switch (name) {
        case 'loanApplyAmount': {
          const checkRes = await checkLoanApplyAmount(data?.loanApplyAmount);
          setAmountValue(data?.loanApplyAmount);
          if (checkRes) {
            await doTrial(data);
          } else {
            setTrialStore({});
          }
          break;
        }
        case 'loanMode':
        case 'couponCodeList':
          await doTrial(data);
          break;
        default: break;
      }
    } catch (e) {
      throw e;
    }
  }, [doTrial, checkLoanApplyAmount]);

  const doUpdateLoanBankCard = async () => {
    try {
      setProcessAction('FETCHING');
      const res = await consultLoanSchema({});
      setProcessAction('NULL');
      setLoanBankCardStore(res?.loanBankCard);
      if (!loanBankCardStore?.defaultValue) {
        return formatReceiveBankCard(res);
      }
      return null;
    } catch (e) {
      setProcessAction('NULL');
      return null;
    }
  };

  const checkSubmitDisabled = useCallback(() => {
    if (_.includes(DISABLED_SUBMIT_STATUS, processAction)) {
      return true;
    }
    if (_.isEmpty(schemeStore) || _.isEmpty(trialStore)) {
      return true;
    }
    return false;
  }, [processAction, schemeStore, trialStore]);

  const changeProcessAction = useCallback((action: PROCESS_ACTION) => {
    setProcessAction(action);
  }, []);

  return {
    schemeStore,
    trialStore,
    loanBankCardStore,
    processAction,
    loanAgreements,
    loanTermOptions,
    repaymentMethodOptions,
    doInit,
    doTrial,
    doApplyAdmit,
    doApply,
    doUpdate,
    checkSubmitDisabled,
    doUpdateLoanBankCard,
    doLoanModeChange,
    changeProcessAction,
  };
}
