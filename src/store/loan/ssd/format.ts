/**
 * @file 支用格式化方法
 */

import type { LoanStore, ApplyFormData, TrialResponse, ApplyRequest, ApplyExtension } from '@/store/loan/types';
import type { UnsignedAgreementsExtension, UnsignedAgreementList, QueryUnSignAgreementListResponse } from '@/store/agreement/actions';
import type { SecureToken } from '@/store/types';
import { requestId, getApplyTime, _ } from '@/utils';

interface ExtensionOptions extends SecureToken {
  schemeStore?: LoanStore;
  trialStore?: TrialResponse;
  loanAgreements?: QueryUnSignAgreementListResponse;
  envData?: unknown;
  formData?: ApplyFormData;
}

export interface InitOption {
  umidToken?: string;
  apdidToken?: string;
}


export function getAgreementQueryExtension(options: ExtensionOptions) {
  if (options.trialStore) {
    const { extension, capitalInstitution } = options.trialStore;
    return {
      fundSupplierCode: _.first(capitalInstitution)?.institutionCode,
      extension: {
        authRecommend: extension?.authRecommend,
        isNeedFaceScan: extension?.isNeedFaceScan,
        creditProdCode: extension?.creditProdCode,
        passthroughInfo: {
          lendCalc: extension?.lendCalc,
        },
      },
    };
  }
  return {};
}

export function getAgreementPreviewExtension(
  currentGroup: UnsignedAgreementList, options: ExtensionOptions,
) {
  try {
    if (currentGroup && options?.trialStore && options?.formData) {
      const { extension: trialExtension, capitalInstitution } = options.trialStore;
      const { receiveBankCard } = options.formData;
      const { extension: agreementExtension } = currentGroup;
      return {
        fundSupplierCode: _.first(capitalInstitution)?.institutionCode,
        extension: {
          bankCardId: receiveBankCard?.bindCardNo,
          authRecommend: trialExtension?.authRecommend,
          isNeedFaceScan: trialExtension?.isNeedFaceScan,
          creditProdCode: trialExtension?.creditProdCode,
          passthroughInfo: {
            agreementExposeLend: agreementExtension?.passthroughInfo?.agreementExposeLend,
            lendCalc: trialExtension?.lendCalc,
          },
        },
      };
    }
    return {};
  } catch (e) {
    return {};
  }
}

export function getLoanAgreementsExtensions(
  loanAgreements?: QueryUnSignAgreementListResponse,
): UnsignedAgreementsExtension {
  try {
    if (loanAgreements) {
      const targetList = _.filter(loanAgreements.unSignedAgreementGroupList, {
        source: 'MAYI_ZHIXIN',
      });
      if (targetList?.length === 1) {
        const target = _.first(targetList);
        return target?.extension || {};
      }
    }
    return {};
  } catch (e) {
    return {};
  }
}

export function getApplyExtension(options: ExtensionOptions): ApplyExtension | null {
  if (options) {
    const { schemeStore, trialStore, loanAgreements, envData, umidToken, apdidToken } = options;
    const unsignedAgreementExtension = getLoanAgreementsExtensions(loanAgreements);
    const base = {
      envData,
      surplusQuota: trialStore?.extension?.surplusQuota,
      creditQuota: trialStore?.extension?.creditQuota,
      zhiXinApplyExt: {
        isNeedFaceScan: trialStore?.extension?.isNeedFaceScan,
        authRecommend: trialStore?.extension?.authRecommend,
        creditProdCode: trialStore?.extension?.creditProdCode,
        passthroughInfo: {
          lendElements: schemeStore?.extension?.lendElements,
          lendCalc: trialStore?.extension?.lendCalc,
          agreementExposeLend: unsignedAgreementExtension?.passthroughInfo?.agreementExposeLend,
        },
      },
    };
    if (umidToken) {
      _.set(base, 'umidToken', umidToken);
    }
    if (apdidToken) {
      _.set(base, 'apdidToken', apdidToken);
    }
    return base;
  }
  // 这里是否要报错
  return null;
}

function formatReceiveBankCard(store: LoanStore) {
  const receiveBankCard = _.first(_.filter(store?.loanBankCard?.options, {
    value: store?.loanBankCard?.defaultValue,
  }));
  if (receiveBankCard?.value) {
    return {
      bindCardNo: receiveBankCard?.value,
      bankCardNo: receiveBankCard?.bankCardNo,
      bankCode: receiveBankCard?.bankCode,
      bankName: receiveBankCard?.bankName,
    };
  }
  return null;
}

export function getInitLoanScheme(store: LoanStore) {
  const repaymentMethod = store?.repaymentMethod?.defaultValue;
  const loanTerm = store?.loanTerm?.defaultValue;
  const receiveBankCard = formatReceiveBankCard(store);
  const institution = store?.institution;
  const loanPurpose = store?.loanPurpose?.defaultValue;
  return {
    repaymentMethod,
    loanTerm,
    receiveBankCard,
    institution,
    loanPurpose,
    agreementCheck: false,
  };
}

export function getTrailScheme(data: ApplyFormData) {
  const { loanApplyAmount, repaymentMethod, loanTerm, institution } = data;
  return {
    loanApplyAmount,
    repaymentMethod,
    institution,
    loanTerm: loanTerm?.value,
    loanTermUnit: loanTerm?.loanTermUnit,
  };
}

interface GetApplySchemeOptions extends SecureToken {
  applyFormData: ApplyFormData;
  trialStore: TrialResponse;
  schemeStore?: LoanStore;
  loanAgreements?: QueryUnSignAgreementListResponse;
  envData?: unknown;
}

export function getApplyScheme(options: GetApplySchemeOptions): ApplyRequest {
  const { applyFormData, trialStore, schemeStore, loanAgreements, envData, umidToken, apdidToken } = options;
  const {
    loanTerm, loanPurpose, loanApplyAmount, repaymentMethod,
    institution, receiveBankCard,
  } = applyFormData;
  const {
    capitalInstitution, promotionAmount, baseInterestRate,
    interestRate, rateUnit, repaymentDay, interest,
  } = trialStore;
  const extension = getApplyExtension({
    schemeStore,
    trialStore,
    loanAgreements,
    envData,
    umidToken,
    apdidToken,
  });
  const baseScheme: any = {
    requestId: requestId(),
    applyTime: getApplyTime(),
    loanApplyAmount,
    repaymentMethod,
    institution,
    receiveBankCard,
    loanTerm: loanTerm?.value,
    loanTermUnit: loanTerm?.loanTermUnit,
    loanPurpose: loanPurpose?.value,
    capitalInstitution,
    promotionAmount,
    baseInterestRate,
    interestRate,
    rateUnit,
    repaymentDay,
    interest,
    channel: 'APP',
    extension,
  };
  return baseScheme;
}

export function getConsultOptions(options: InitOption) {
  if (options?.umidToken || options?.apdidToken) {
    const { umidToken, apdidToken } = options;
    const extension = {};
    if (umidToken) {
      _.set(extension, 'umidToken', umidToken);
    }
    if (apdidToken) {
      _.set(extension, 'apdidToken', apdidToken);
    }
    return {
      extension,
    };
  }
  return {};
}
