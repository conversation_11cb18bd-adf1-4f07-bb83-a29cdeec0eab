/**
 * @file 支用相关的各种方法
 */

import { MtopGet, MtopPost } from '@/utils/mtop';

import type {
  ConsultLoanSchemaRequest, ConsultLoanSchemaResponse, TrialRequest, TrialResponse, ApplyRequest,
  ApplyResponse, GetLoanOrderDetailRequest, GetLoanOrderDetailResponse, QueryRequest, QueryResponse,
} from './types';

export function consultLoanSchema(data: ConsultLoanSchemaRequest) {
  return MtopGet<ConsultLoanSchemaRequest, ConsultLoanSchemaResponse>({
    api: 'mtop.alibaba.fin.tao.blp.loan.consultLoanSchema',
    data,
  });
}

export function trial(data: TrialRequest) {
  return MtopPost<TrialRequest, TrialResponse>({
    api: 'mtop.alibaba.fin.tao.blp.loan.trial',
    data,
  });
}

export function applyAdmit(data: ApplyRequest) {
  return MtopPost<ApplyRequest, ApplyResponse>({
    api: 'mtop.alibaba.fin.tao.blp.loan.applyAdmit',
    data,
  });
}

export function apply(data: ApplyRequest) {
  return MtopPost<ApplyRequest, ApplyResponse>({
    api: 'mtop.alibaba.fin.tao.blp.loan.apply',
    data,
  });
}


export function query(data: QueryRequest) {
  return MtopGet<QueryRequest, QueryResponse>({
    api: 'mtop.alibaba.fin.tao.blp.loan.order.query',
    data,
  });
}

export function getLoanOrderDetail(data: GetLoanOrderDetailRequest): Promise<GetLoanOrderDetailResponse> {
  return MtopGet<GetLoanOrderDetailRequest, GetLoanOrderDetailResponse>({
    api: 'mtop.alibaba.fin.tao.blp.loan.order.getLoanOrderDetail',
    data,
  });
}

// 分页查询借据
export function queryLoanOrderPage(data: any) {
  return MtopGet<any, any>({
    api: 'mtop.alibaba.fin.tao.blp.loan.order.queryLoanOrderPage',
    data,
  });
}


// 分页查询待还借据列表
export function queryLoanContractList(data?) {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.loan.contract.page.query',
    data,
  });
}


// 查询全部待还
export function queryLoanContractSummary(data?) {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.loan.contract.summary.query',
    data,
  });
}

// 同步合约和还款计划
export async function contractAsync() {
  try {
    await MtopGet({
      api: 'mtop.alibaba.fin.tao.blp.loan.contract.sync',
      data: {},
    });
  } catch (e) {}
}
