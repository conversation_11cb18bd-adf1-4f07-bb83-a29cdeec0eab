/**
 * @file 支用格式化方法
 */

import { moment, number } from '@ali/iec-dtao-utils';
import { first, last, set, isEmpty, keys } from 'lodash-es';

import { formatLoanTerm, formatRepaymentMethod, formatRepaymentMethodDesc, getFormatContacts, genApplyExposePlatformPromotionOfferStr } from '@/store/lib/format';
import type { LoanTermDTO, RepaymentMethodDTO, Option, SupplementDataCollection, SupplementDataFormData, LoanModeValue } from '../types';
import type { LoanStore, ApplyFormData, TrialResponse, ApplyRequest } from './types';
import { requestId, getApplyTime, _ } from '@/utils';
import { getOriginFromSessionStorage } from '@/utils/session';

function getEmergencyContact(emergencyContact: any) {
  if (emergencyContact) {
    const { defaultValue, minCount = 0 } = emergencyContact;
    if (!defaultValue || isEmpty(defaultValue)) {
      return getFormatContacts(minCount);
    } else if (defaultValue?.length > 0) {
      if (defaultValue.length < minCount) {
        return getFormatContacts(minCount - defaultValue.length, defaultValue);
      } else if (defaultValue.length === minCount) {
        return defaultValue;
      } else {
        return getFormatContacts(minCount);
      }
    } else {
      return getFormatContacts(minCount);
    }
  }
  return [];
}

// 格式化支用补充数据
export function getSupplementInitData(collections?: SupplementDataCollection) {
  const initData = {};
  if (collections) {
    const {
      profession, education, maritalStatus, monthlyIncome, companyName, emergencyContact,
      residenceAddress, city, county, province,
    } = collections;
    if (profession?.defaultValue) {
      set(initData, 'profession', profession.defaultValue);
    }
    if (education?.defaultValue) {
      set(initData, 'profession', education?.defaultValue);
    }
    if (maritalStatus?.defaultValue) {
      set(initData, 'maritalStatus', maritalStatus?.defaultValue);
    }
    if (monthlyIncome?.defaultValue) {
      set(initData, 'monthlyIncome', monthlyIncome?.defaultValue);
    }
    if (companyName?.defaultValue) {
      set(initData, 'companyName', companyName?.defaultValue);
    }
    if (emergencyContact) {
      set(initData, 'emergencyContact', getEmergencyContact(emergencyContact));
    }
    if (city || county || province || residenceAddress) {
      set(initData, 'region', {
        city,
        county,
        province,
      });
    }
    if (residenceAddress) {
      set(initData, 'residenceAddress', residenceAddress?.defaultValue);
    }
  }
  return initData;
}

// 检查是否为空
export function checkSupplementDataEmpty(data: any) {
  if (!data || isEmpty(data)) {
    return true;
  }
  if (data) {
    const dataKeys = keys(data);
    if (dataKeys?.length === 1 && dataKeys[0] === 'emergencyContact') {
      const emergencyContact = data?.emergencyContact;
      const noEmptyList = _.filter(emergencyContact, (item) => !_.isEmpty(item));
      return noEmptyList?.length === 0;
    }
  }
  return false;
}

export function formatReceiveBankCard(store: LoanStore) {
  const receiveBankCard = _.first(_.filter(store?.loanBankCard?.options, {
    value: store?.loanBankCard?.defaultValue,
  }));
  if (receiveBankCard?.value) {
    return {
      bindCardNo: receiveBankCard?.value,
      bankCardNo: receiveBankCard?.bankCardNo,
      bankCode: receiveBankCard?.bankCode,
      bankName: receiveBankCard?.bankName,
    };
  }
  return undefined;
}

export function getInitLoanScheme(store: LoanStore, storeAmountValue?: string) {
  let loanApplyAmount = store?.loanSchemaQuota?.maxLoanAmount;
  if (storeAmountValue || storeAmountValue === '') {
    loanApplyAmount = storeAmountValue;
  }
  const repaymentMethod = store?.repaymentMethod?.defaultValue;
  const loanTerm = store?.loanTerm?.defaultValue;
  const couponCodeList = store?.coupon?.defaultValue;
  const receiveBankCard = formatReceiveBankCard(store);
  const institution = store?.institution;
  const loanPurpose = store?.loanPurpose?.defaultValue;
  const supplementData = getSupplementInitData(store?.supplementData);
  const license = store?.license?.defaultValue;
  const loanMode = {
    loanTermValue: loanTerm,
    repaymentMethodValue: repaymentMethod,
  };
  return {
    loanApplyAmount,
    couponCodeList,
    receiveBankCard,
    institution,
    loanPurpose,
    supplementData,
    license,
    agreementCheck: false,
    loanMode,
  };
}

export function getTrailScheme(data: ApplyFormData) {
  const { loanApplyAmount, couponCodeList, institution, loanMode } = data;
  return {
    loanApplyAmount,
    repaymentMethod: loanMode?.repaymentMethodValue,
    couponCodeList,
    institution,
    loanTerm: loanMode?.loanTermValue?.value,
    loanTermUnit: loanMode?.loanTermValue?.loanTermUnit,
  };
}

export function getFormatSupplementData(supplementData?: SupplementDataFormData) {
  if (supplementData?.region) {
    const { region, ...supplementDataOther } = supplementData;
    return {
      ...region,
      ...supplementDataOther || {},
    };
  }
  return supplementData;
}

export function getFormatLoanPurpose(value?: string, options?: any) {
  if (value && options?.length) {
    const targets = _.filter(options, {
      value,
    });
    if (targets?.length === 1 && first(targets)?.label) {
      return {
        value,
        label: first(targets)?.label,
      };
    }
  }
  return value;
}

export function getApplyScheme(formData: ApplyFormData, trialStore: TrialResponse, schemeStore?: LoanStore): ApplyRequest {
  const {
    loanPurpose, loanApplyAmount, institution, receiveBankCard, supplementData,
    license, umidToken, loanMode,
  } = formData;
  const {
    capitalInstitution, promotionAmount, baseInterestRate,
    interestRate, rateUnit, repaymentDay, interest, couponList,
  } = trialStore;
  const applySupplementData = getFormatSupplementData(supplementData);
  const baseScheme: any = {
    requestId: requestId(),
    applyTime: getApplyTime(),
    loanApplyAmount,
    institution,
    receiveBankCard,
    loanTerm: loanMode?.loanTermValue?.value,
    loanTermUnit: loanMode?.loanTermValue?.loanTermUnit,
    repaymentMethod: loanMode?.repaymentMethodValue,
    loanPurpose: loanPurpose?.value,
    capitalInstitution,
    promotionAmount,
    baseInterestRate,
    interestRate,
    rateUnit,
    repaymentDay,
    interest,
    channel: 'APP',
    exposePlatformPromotionOfferList: genApplyExposePlatformPromotionOfferStr(schemeStore?.platformPromotionOfferList),
    extension: {
      umidToken,
      origin: getOriginFromSessionStorage(),
    },
    couponCodeList: _.map(couponList, (item) => item?.couponCode),
  };
  if (license && !isEmpty(license)) {
    _.set(baseScheme, 'license', license);
  }
  if (applySupplementData) {
    return {
      ...baseScheme,
      supplementData: applySupplementData,
    };
  }
  return baseScheme;
}

export function formatLoanTermList(loanTerm?: LoanTermDTO[]) {
  const loanTermList = [] as LoanTermDTO[];
  if (loanTerm?.length) {
    _.forEach(loanTerm, (term) => {
      const label = formatLoanTerm(term);
      if (label) {
        loanTermList.push({
          value: term?.value,
          loanTermUnit: term?.loanTermUnit,
          label,
          disabled: term?.disabled,
        });
      }
    });
  }
  return loanTermList;
}

export function formatRepaymentMethodList(repaymentMethod?: RepaymentMethodDTO[]) {
  const repaymentMethodList = [] as RepaymentMethodDTO[];
  if (repaymentMethod?.length) {
    _.forEach(repaymentMethod, (item) => {
      const label = formatRepaymentMethod(item);
      const desc = formatRepaymentMethodDesc(item);
      if (label) {
        repaymentMethodList.push({
          value: item?.value,
          label,
          desc,
          disabled: item?.disabled,
        });
      }
    });
  }
  return repaymentMethodList;
}

export function formatRecommendAmountList(
  recommendAmountList: string[], maxLoanAmount?: string,
) {
  const recommendAmountListList = [] as Option[];
  if (recommendAmountList?.length) {
    _.forEach(recommendAmountList, (amount) => {
      if (amount) {
        let label = `¥${amount}`;
        if (maxLoanAmount && amount && maxLoanAmount === amount) {
          label = '借全部';
        }
        recommendAmountListList.push({
          value: amount,
          label,
        });
      }
    });
  }
  return recommendAmountListList;
}

export function formatSupplementData(data?: any) {
  if (!data) {
    return data;
  }
  const { region, ...other } = data;
  return {
    ...region,
    ...other,
  };
}

// function checkLprRate(lprRate?: string) {
//   try {
//     if (!lprRate) {
//       return '';
//     }
//     const lprRateValue = number.getNumber(lprRate);
//     if (lprRateValue === '--') {
//       return '';
//     }
//     if (lprRateValue > 0) {
//       return '加';
//     }
//     if (lprRateValue < 0) {
//       return '减';
//     }
//     return '';
//   } catch (e) {
//     return '';
//   }
// }

export function formatAgreementPreviewParams(formData?: ApplyFormData, trialData?: TrialResponse) {
  if (formData && trialData) {
    const { loanApplyAmount, loanPurpose, receiveBankCard, license, loanMode } = formData;
    const { interestRate, interestRatePercent, installmentPlanList, repaymentDay, extension } = trialData;
    const repaymentMethod = loanMode?.repaymentMethodValue;
    const loanTerm = loanMode?.loanTermValue?.value;
    const loanTermUnit = loanMode?.loanTermValue?.loanTermUnit;
    if (loanApplyAmount && repaymentMethod && receiveBankCard?.bankCode) {
      const borrowAmountUpper = number.digitUppercase(loanApplyAmount);
      const borrowAmountLower = loanApplyAmount;
      const annualInterest = `${interestRatePercent || '--'}%`;
      const period = loanTerm;
      const endTime = last(installmentPlanList)?.endDate;
      const consumeTypeName = loanPurpose?.label;
      const repaymentDayPerMonth = repaymentDay;
      const repaymentTypeName = formatRepaymentMethod({
        value: repaymentMethod,
      });
      const borrowerBankCardId = receiveBankCard?.bankCardNo;
      const bankName = receiveBankCard?.bankName;
      return {
        agreementCenter: {
          borrowAmountUpper,
          borrowAmountLower,
          annualInterest,
          period,
          endTime: endTime ? moment.YMD(endTime) : '',
          consumeTypeName: consumeTypeName || '',
          repaymentDayPerMonth,
          repaymentTypeName,
          borrowerBankCardId,
          bankName: bankName || '',
          pricingDate: extension?.lprTener ? `${extension?.lprTener}年` : '',
          // TODO: 这里需要根据lprRate判断（加/减）
          baseInterestRate: extension?.lprRateDesc || '',
          interestRatePlus: extension?.floatRate || '',
          contactAddr: license?.licenseAddress || '',
        },
        institution: {
          loanAmount: loanApplyAmount,
          interestRate,
          loanTerm,
          loanTermUnit,
          repaymentMethod,
          repaymentDay,
          bindCardNo: receiveBankCard.bindCardNo,
          loanPurpose: loanPurpose?.value || '',
        },
      };
    }
  }
  return {};
}

export function checkLoanModel(loanMode?: LoanModeValue) {
  if (
    loanMode?.loanTermValue?.value &&
    loanMode?.loanTermValue?.loanTermUnit &&
    loanMode?.repaymentMethodValue
  ) {
    return true;
  }
  return false;
}

export function getAgreementQueryExtension(scheme: ApplyFormData) {
  // 等支用关联后要改
  const { loanApplyAmount, institution, loanMode } = scheme;
  if (!checkLoanModel(loanMode)) {
    throw new Error('agreement-query-extension-error');
  }
  return {
    institutionList: [institution],
    extension: {
      loanTerm: loanMode?.loanTermValue?.value,
      loanTermUnit: loanMode?.loanTermValue?.loanTermUnit,
      repaymentMethod: loanMode?.repaymentMethodValue,
      loanAmount: loanApplyAmount,
    },
  };
}
