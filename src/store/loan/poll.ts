/**
 * @file 支用相关轮询
 */

import { useCallback, useState, useRef } from 'react';
import { log } from '@alife/dtao-iec-spm-log';
import { _ } from '@/utils';

import { query } from './actions';
import type { QueryPollOption, QueryResponse } from './types';
import { isNull } from 'lodash-es';

interface useQueryPollOptions {
  pure?: boolean;
  time?: number;
  times?: number;
}

export function useQueryPoll(options?: useQueryPollOptions) {
  const { pure = false, time = 2000, times = 99 } = options || {};
  const [payload, setPayLoad] = useState<QueryResponse>({});
  const timerRef = useRef<{
    timer: NodeJS.Timeout | undefined;
    times: number;
  }>({
    timer: undefined,
    times,
  });

  const safeQuery = useCallback(async (request: any) => {
    try {
      const queryRes = await query(request);
      return queryRes;
    } catch (e) {
      log.addErrorLog('loan-query-poll-error');
      return null;
    }
  }, []);

  const resetTimer = () => {
    timerRef.current.times = times;
    clearTimeout(timerRef.current.timer);
  };

  // 简易轮询
  const doPoll = useCallback(async (option: QueryPollOption) => {
    const { request, end } = option;
    const queryRes = await safeQuery(request);

    // 第一次轮询就失败则抛错，非第一次轮询失败，继续查询
    if (isNull(queryRes) && timerRef.current.times === times) {
      throw new Error('FIRST_POLL_ERROR');
    }
    timerRef.current.times--;
    if (queryRes && JSON.stringify(payload) !== JSON.stringify(queryRes) && !pure) {
      setPayLoad(queryRes);
    }
    if (!timerRef.current.times) {
      resetTimer();
      return;
    }
    if (!_.includes(end, queryRes?.status)) {
      timerRef.current.timer = setTimeout(() => {
        doPoll(option);
      }, time);
    } else {
      resetTimer();
      option?.onEnd && option.onEnd(queryRes);
    }
  }, [payload, pure]);

  return {
    doPoll,
    payload,
  };
}
