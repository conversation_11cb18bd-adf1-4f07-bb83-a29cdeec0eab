/**
 * @file 校验方法
 */

import { number } from '@ali/iec-dtao-utils';
import { filter, map } from 'lodash-es';
import { ApplyRequestPre, TrialResponse } from '@/store/loan/types';

export function loanApplyAmountValidator(value: string, option: any) {
  const { min, max, step } = option;
  const { getNumber } = number;
  if (!min || !max || !step) {
    return Promise.reject(new Error('金额校验异常，请重试'));
  }
  const minNumber = getNumber(min);
  const maxNumber = getNumber(max);
  const stepNumber = getNumber(step);
  const numValue = getNumber(value);
  if (minNumber === '--' || maxNumber === '--' || max === '--' || stepNumber === '--') {
    return Promise.reject(new Error(''));
  }
  if (numValue !== '--' && numValue >= minNumber && numValue % stepNumber === 0 && numValue <= maxNumber) {
    return Promise.resolve();
  }
  if (numValue === '--') {
    return Promise.reject(new Error(''));
  }
  if (numValue <= 0) {
    return Promise.reject(new Error('借款金额不能小于0元'));
  }
  if (numValue > maxNumber) {
    return Promise.reject(new Error(`借款金额不能超过${maxNumber}元`));
  }
  if (numValue < minNumber) {
    return Promise.reject(new Error(`借款金额不能小于${minNumber}元`));
  }
  if (numValue % stepNumber !== 0) {
    return Promise.reject(new Error(`借款金额必须是${step}的倍数`));
  }
  return Promise.reject(new Error(''));
}

function checkAllValue(data: any, list: string[]) {
  const empty: any = [];
  let check = false;
  if (data && list?.length) {
    const resList = map(list, (name) => {
      if (data[name]) {
        return 1;
      }
      empty.push({
        name,
        value: data[name],
      });
      return 0;
    });
    const checkedList = filter(resList, (i) => i === 1);
    if (checkedList?.length > 0 && list?.length > 0) {
      check = checkedList.length === list.length;
    }
  }
  return {
    check,
    empty,
  };
}

export function loanTrialValidator(trialStore: TrialResponse) {
  const checkList = [
    'admitted', 'capitalInstitution', 'baseInterestRate', 'baseInterestRate',
    'interestRate', 'rateUnit', 'interest', 'principal',
    'principal', 'totalAmount', 'installmentPlanList', 'repaymentDay',
  ];
  return checkAllValue(trialStore, checkList);
}

export function loanApplyValidator(applyData: ApplyRequestPre) {
  const checkList = [
    'loanApplyAmount', 'repaymentMethod', 'loanTerm', 'loanTermUnit', 'institution',
    'requestId', 'applyTime', 'capitalInstitution', 'receiveBankCard',
    'baseInterestRate', 'interestRate', 'interest', 'rateUnit', 'repaymentDay', 'loanPurpose',
    'channel',
  ];
  return checkAllValue(applyData, checkList);
}
