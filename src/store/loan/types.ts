/**
 * @file 类型
 * <AUTHOR>
 */

import type {
  BankCardDTO, LoanTermDTO, Options, LoanCouponDTO, InstallmentPlanDTO,
  RejectReasonDTO, RepaymentMethodDTO, SupplementDataCollection, LOAN_STATUS,
  CURRENCY, CHANNEL, CapitalInstitutionDTO,
  SupplementDataFormData, CertLicense,
  PromotionOffer,
  LOAN_VERSION,
  LoanModeValue,
} from '@/store/types';
import type { PreFillAmountType } from '../center/actions';

export type FIELD_NAME = 'loanApplyAmount' | 'loanTerm' | 'repaymentMethod' | 'couponCodeList' | 'loanMode';

export type LOAN_ADMIT_REJECT_CODE = 'INSTITUTION_SHUT_DOWN' | 'IDENTITY_CARD_EXPIRED' | 'APPLY_QUOTA_EXCEPTION' | 'LOAN_DISABLED' | 'INSTITUTION_SERVICE_SHUT_DOWN' | 'HAS_OVERDUE' | 'HAS_ON_GOING_CREDIT_CLOSE_ORDER' | 'SUPPLEMENT_DATA_VALID_EXCEPTION' | 'SUPPLEMENT_DATA_IS_NULL' | 'CONTRACT_PERSON_CAN_NOT_BE_SELF' | 'ON_GOING_CONTRACT_MORE_THAN_UPPER_LIMIT' | 'QUOTA_STATUS_FROZEN' | 'NO_AVAILABLE_QUOTA' | 'LOAN_SCHEMA_VALID_EXCEPTION';

export interface ConsultLoanSchemaRequest extends BaseRequest {
  loanApplyAmount?: string;
  repaymentMethod?: string;
  loanTerm?: string;
  loanTermUnit?: string;
  extension?: {
    umidToken?: string;
  };
}

export interface ReceiveBankCard {
  receivedBindCardNo?: string;
  bankCardNo?: string;
  bankCode?: string;
  bindCardNo?: string;
  bankName?: string;
}

export interface ConsultLoanSchemaSSDExtension {
  lendElements?: unknown;
  unLimitUrl?: string;
  maxLoanContractCount?: number;
  firstLoanFlag?: boolean;
}

export type LoanBankCard = Options<string, BankCardDTO>;

export interface ConsultLoanSchemaResponse {
  admitted?: boolean;
  rejectReason?: RejectReasonDTO;
  institution?: string;
  loanSchemaQuota?: {
    minStartLoanAmount?: string;
    maxLoanAmount?: string;
    recommendAmountList?: string[];
    step?: string;
  };
  loanBankCard?: LoanBankCard;
  loanTerm?: Options<LoanTermDTO, LoanTermDTO>;
  repaymentMethod?: Options<string, RepaymentMethodDTO>;
  coupon?: Options<string[], LoanCouponDTO>;
  supplementData?: SupplementDataCollection;
  license?: {
    defaultValue?: CertLicense;
    disabled?: boolean;
  };
  agreementCheck?: boolean;
  loanPurpose?: Options<string, Option>;
  // SSD支用consult的额外字段
  extension?: ConsultLoanSchemaSSDExtension;
  platformPromotionOfferList?: PromotionOffer[];
}

export interface ApplyScheme {
  loanApplyAmount?: string;
  repaymentMethod?: string;
  loanTerm?: string;
  loanTermUnit?: string;
  couponCodeList?: string[];
  institution?: string;
}

export interface ApplyFormData {
  loanApplyAmount?: string;
  repaymentMethod?: string;
  loanTerm?: {
    value?: string;
    loanTermUnit?: string;
  };
  couponCodeList?: string[];
  institution?: string;
  agreementCheck?: boolean;
  receiveBankCard?: ReceiveBankCard;
  supplementData?: SupplementDataFormData;
  license?: CertLicense;
  loanPurpose?: Option;
  umidToken?: string;
  loanVersion?: LOAN_VERSION;
  preFillAmountType?: PreFillAmountType;
  loanMode?: LoanModeValue;
}

export interface TrialRequest extends ApplyScheme, BaseRequest {
}

export interface TrialExtension {
  /* SSD */
  authRecommend?: string;
  isNeedFaceScan?: unknown;
  creditProdCode?: string;
  lendCalc?: unknown;
  surplusQuota?: string;
  creditQuota?: string;
  /* SSD */
  /* ALIYUN */
  lprRate?: string;
  floatRate?: string;
  lprTener?: string;
  lprRateDesc?: '加' | '减' | '';
  /* ALIYUN */
}

export interface TrialResponse {
  admitted?: boolean;
  capitalInstitution?: CapitalInstitutionDTO[];
  baseInterestRate?: string;
  baseInterestRatePercent?: string;
  baseDailyInterestRatePercent?: string;
  dailyInterestRatePercent?: string; // 日
  /** 执行日利息 */
  dailyInterest?: string;
  interestRate?: string;
  interestRatePercent?: string; // 年
  rateUnit?: string;
  /* 优惠前利息 */
  originInterest?: string;
  /** 应还利息 */
  interest?: string;
  /** 应还本金 */
  principal?: string;
  /** 应还总额 */
  totalAmount?: string;
  /** 总优惠金额 */
  promotionAmount?: string;
  /** 还款计划 */
  installmentPlanList?: InstallmentPlanDTO[];
  /** 还款日 */
  repaymentDay?: string;
  /** 优惠列表 */
  couponList?: Array<{
    /** 金银岛卡券编码 */
    couponCode: string;
    /** 金银岛卡券类型 */
    type: 'INTEREST_FREE' | string;
    /** 金银岛卡券优惠额度 */
    freeQuota: string;
    /** 卡券使用规则 */
    couponUsableRule: {
      /** 金银岛卡券有效期数 */
      limitTerms: number;
      /** 金银岛卡券有效期单位 */
      limitTermUnit: 'DAY' | 'MONTH' | 'YEAR';
    };
  }>;
  extension?: TrialExtension;
}

export interface ApplyRequestPre extends ApplyScheme, BaseRequest {
  requestId: string;
  applyTime: number;
  capitalInstitution?: CapitalInstitutionDTO[];
  receiveBankCard?: ReceiveBankCard;
  promotionAmount?: string;
  baseInterestRate?: string;
  interestRate?: string;
  interest?: string;
  rateUnit?: string;
  repaymentDay?: string;
  loanPurpose?: string;
  supplementData?: any;
  channel: string;
}

export interface ApplyExtension {
  envData?: unknown;
  umidToken?: string;
  zhiXinApplyExt?: {
    // 环境数据
    envData?: unknown;
    // 是否需要人脸，试算来的
    isNeedFaceScan?: unknown;
    // 是否需要签署代扣协议
    isDeductAlipayAgreementExist?: boolean;
    surplusQuota?: string;
    creditQuota?: string;
    // 核身偏好，试算来的
    authRecommend?: unknown;
    // TODO，不知道从哪里来
    ctuInfo?: unknown;
    // consult来的
    verifyInfo?: unknown;
    // 其他透传参数
    passthroughInfo?: {
      // TODO: 不确定有没有consult透传参数
      lendElements?: unknown;
      // trial透传参数
      lendCalc?: unknown;
      // 未签署协议接口透传参数
      agreementExposeLend?: unknown;
    };
  };
}

export interface ApplyRequest {
  requestId: string;
  applyTime: number;
  loanApplyAmount: string;
  repaymentMethod: string;
  loanTerm: string;
  loanTermUnit: string;
  couponCodeList?: string[];
  institution: string;
  capitalInstitution: CapitalInstitutionDTO[];
  receiveBankCard: ReceiveBankCard;
  promotionAmount: string;
  baseInterestRate: string;
  interestRate: string;
  interest: string;
  rateUnit: string;
  repaymentDay: string;
  loanPurpose: string;
  supplementData?: any;
  channel: string;
  extension?: ApplyExtension;
  license?: CertLicense;
  exposePlatformPromotionOfferList?: string;
}

export interface LoanApplyRejectReason {
  rejectCode?: LOAN_ADMIT_REJECT_CODE;
  rejectDesc?: string;
}

export type ApplyAdmitRequest = ApplyRequest;

export interface ApplyAdmitResponse {
  admitted?: boolean;
  extension?: {
    unLimitUrl?: string; // 身份证解权链接
  };
  rejectReason?: LoanApplyRejectReason;
}

export interface ApplyResponse {
  admitted?: boolean;
  extension?: {
    unLimitUrl?: string; // 身份证解权链接
  };
  rejectReason?: LoanApplyRejectReason;
  loanOrderId: string;
  status: LOAN_STATUS;
  authenticationToken: string;
}

export interface QueryRequest extends BaseRequest {
  loanOrderId: string;
  needLoanAuditStatus?: boolean;
}

export interface QueryResponse {
  authenticationToken?: string;
  channel?: string;
  creditContractId?: string;
  currency?: CURRENCY;
  failedReason?: {
    failedCode?: string;
    failedDesc?: string;
  };
  fipCreditOrderId?: string;
  institution?: string;
  loanApplyAmount?: string;
  loanedAmount?: string;
  loanOrderId?: string;
  loanPurpose?: string;
  loanTerm?: number;
  loanTermUnit?: string;
  receiveBankCard?: ReceiveBankCard;
  repaymentDay?: number;
  repaymentMethod?: string;
  status?: LOAN_STATUS;
  loanProcessStatus?: 'MANUAL_REVIEW';
  platformPromotionOfferList?: PromotionOffer[];
}

export interface GetLoanOrderDetailRequest extends BaseRequest {
  loanOrderId: string;
  customerId?: string;
}

export interface GetLoanOrderDetailResponse {
  status?: LOAN_STATUS;
  loanApplyAmount?: string;
  loanedAmount?: string;
  receiveBankCard?: ReceiveBankCard;
  capitalInstitutionCode?: string;
  totalTerm?: string;
  repaymentStartDay?: string;
  repaymentEndDay?: string;
  repaymentMethod?: string;
  loanContractAgreement?: [];
  installmentPlanList?: InstallmentPlanDTO[];
  institution?: string;
  extension?: {
    zhiXinSignedAgreementQueryDisable?: boolean;
  };
}

export type LoanStore = ConsultLoanSchemaResponse;

export type FieldObj = {
  [key in FIELD_NAME]: string;
};

export type DoUpdateField = (name: FIELD_NAME, value: any) => void;

export interface DoUpdateOption {
  name: FIELD_NAME;
  data: ApplyFormData;
  updateField: DoUpdateField;
}

export interface QueryPollOption {
  end: LOAN_STATUS[];
  request: QueryRequest;
  onEnd?: (order?: QueryResponse | null) => void;
  onOver?: () => void;
}

export interface DoInitOption {
  channel: CHANNEL;
}

export type PROCESS_ACTION = 'FETCHING' | 'TRIALING' | 'SUBMITTING' | 'AUTENTICATING' | 'CONSULT_LOAN_RESULTING' | 'NULL';
