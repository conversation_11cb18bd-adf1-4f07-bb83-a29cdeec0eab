/**
 * @file 公共格式化函数
 */

import type { LoanTermDTO, RepaymentMethodDTO, CapitalInstitutionDTO, PromotionOffer, InstitutionPromotionOffer } from '@/store/types';
import { REPAYMENT_METHOD, LOAN_TERM_UNIT, REPAYMENT_METHOD_DESC } from '@/common/constant';
import { map, join, cloneDeep, get, pick, find } from 'lodash-es';
import type { PopMessageItem } from '../center/actions';

export function formatLoanTerm(loanTermItem?: LoanTermDTO) {
  if (loanTermItem) {
    const { value, loanTermUnit } = loanTermItem;
    if (value && loanTermUnit) {
      return `${value}${LOAN_TERM_UNIT[loanTermUnit]}`;
    }
  }
  return '';
}

export function formatRepaymentMethod(repaymentMethod?: RepaymentMethodDTO) {
  if (repaymentMethod) {
    const { value } = repaymentMethod;
    if (value) {
      return REPAYMENT_METHOD[value];
    }
  }
  return '';
}

export function formatRepaymentMethodDesc(repaymentMethod?: RepaymentMethodDTO) {
  if (repaymentMethod) {
    const { value } = repaymentMethod;
    if (value) {
      return REPAYMENT_METHOD_DESC[value];
    }
  }
  return '';
}

export function formatCapitalInstitution(capitalInstitutions?: CapitalInstitutionDTO[]) {
  if (!capitalInstitutions?.length) {
    return '';
  }
  const names = map(capitalInstitutions, (item) => {
    return item?.institutionName;
  });
  return join(names, ',');
}

export function getFormatContacts(count: number, defaultValue?: any) {
  const list: any = cloneDeep(defaultValue) || [];
  for (let i = 0; i < count; i++) {
    list.push({});
  }
  return list;
}

export function getInstitutionCouponData(institutionPromotionOfferList?: InstitutionPromotionOffer[]) {
  if (!institutionPromotionOfferList?.length) {
    return null;
  }
  const ret = institutionPromotionOfferList[0];
  return {
    ...ret,
    displayPosition: get(ret.promotionOfferDescriptions, '[0].displayPosition'),
    promotionOfferDescription: get(ret.promotionOfferDescriptions, '[0].description'),
  };
}

export function getPopMessageData(popMessageList: PopMessageItem[]) {
  if (!popMessageList?.length) {
    return null;
  }

  let content = {};
  try {
    content = JSON.parse(popMessageList[0]?.content);
  } catch (e) {
  }

  return {
    ...popMessageList[0],
    ...content,
  };
}


export function getPromotionOfferData(
  promotionOfferList: PromotionOffer[],
  descriptionDisplayPosition: string,
): (PromotionOffer & {
    descriptionDisplayPosition?: string;
    descData?: {
      displayPosition?: string;
      description?: string;
      offerSendRule?: string;
    };
    promotionOfferDescription?: string;
    offerSendRule?: string;
    url?: string;
  }) | null {
  if (!promotionOfferList?.length) {
    return null;
  }

  const ret = promotionOfferList[0];
  const promotionOfferDescription = find(ret.promotionOfferDescriptions, { displayPosition: descriptionDisplayPosition });

  if (!promotionOfferDescription) {
    return {} as unknown as PromotionOffer;
  }

  return {
    ...ret,
    descriptionDisplayPosition,
    descData: promotionOfferDescription,
    promotionOfferDescription: get(promotionOfferDescription, 'description'),
    offerSendRule: get(promotionOfferDescription, 'offerSendRule'),
    url: get(promotionOfferDescription, 'url'),
  };
}

export function genApplyExposePlatformPromotionOfferStr(
  promotionOfferList?: PromotionOffer[],
): string | undefined {
  if (!promotionOfferList?.length) {
    return;
  }

  // 示例： "[{\"title\":\"平台营销现金红包\",\"code\":\"营销活动唯一id\",\"type\":\"TAO_PLATFORM_RED_PACKET\",\"amount\":10}]"
  return JSON.stringify([pick(promotionOfferList[0], ['title', 'code', 'type', 'amount'])]);
}
