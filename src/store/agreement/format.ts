import { forEach } from 'lodash-es';
import type {
  QueryUnSignAgreementListResponse,
} from '@/store/agreement/actions';
import type { AgreementDTO } from '@/store/types';

export function getForceReadAgreements(queryResponse?: QueryUnSignAgreementListResponse) {
  const forceAgreements: AgreementDTO[] = [];
  const unSignedAgreementGroupList = queryResponse?.unSignedAgreementGroupList;
  if (unSignedAgreementGroupList?.length) {
    forEach(unSignedAgreementGroupList, (group) => {
      const unSignedAgreementList = group?.unSignedAgreementList;
      if (unSignedAgreementList?.length) {
        forEach(unSignedAgreementList, (agreement) => {
          if (agreement?.forceRead) {
            forceAgreements.push(agreement);
          }
        });
      }
    });
  }
  return forceAgreements;
}
