/**
 * @file 协议相关的各种方法
 */

import { MtopGet, MtopPost } from '@/utils/mtop';
import type {
  AgreementDTO,
  AGREEMENT_SOURCE,
  CREDIT_PLATFORM,
  CREDIT_TYPE,
  FundSupplierDTO,
} from '@/store/types';

export interface QueryUnSignAgreementListRequest extends BaseRequest {
  bizType?: string;
  creditPlatform?: CREDIT_PLATFORM;
  institutionList?: string | string[];
  extension?: unknown;
  creditType?: CREDIT_TYPE;
}

/**
 * @case 支用协议列表，需要passthroughInfo/lendCalc
 * @case 支用协议预览，需要passthroughInfo/agreementExposeLend、passthroughInfo/lendCalc
 * @description [extends FundSupplierDTO] 主要是为了兼容原单机构授信链路
 */

export interface UnsignedAgreementsExtension extends FundSupplierDTO {
  isDeductAlipayAgreementExist?: boolean;
  // 多机构
  fundSupplierList?: FundSupplierDTO[];
  passthroughInfo?: {
    agreementExposeLend?: unknown;
  };
}

export interface UnsignedAgreementList {
  institution?: string;
  order?: number;
  source?: AGREEMENT_SOURCE;
  bizType?: string;
  unSignedAgreementList?: AgreementDTO[];
  extension?: UnsignedAgreementsExtension;
}

export interface QueryUnSignAgreementListResponse {
  unSignedAgreementGroupList?: UnsignedAgreementList[];
}

export function queryUnSignAgreementList(request: QueryUnSignAgreementListRequest) {
  return MtopPost<QueryUnSignAgreementListRequest, QueryUnSignAgreementListResponse>({
    api: 'mtop.alibaba.fin.tao.blp.unsign.agreement.query',
    data: request,
  });
}

export interface PreviewRequest extends BaseRequest {
  bizType?: string;
  code?: string;
  source?: string;
  institution?: string;
  name?: string;
  params?: any;
  extension?: unknown;
  version?: string;
  fundSupplierCode?: string;
  agreementStatus?: string;
  contractNo?: string;
}

export interface PreviewResponse {
  minReadTime?: number;
  previewAgreementList?: AgreementDTO[];
}

export function preview(request: PreviewRequest) {
  return MtopPost<PreviewRequest, PreviewResponse>({
    api: 'mtop.alibaba.fin.tao.blp.agreement.preview',
    data: request,
    noResponse: true,
  });
}

export interface QuerySignedAgreementRequest extends BaseRequest {
  bizType: string; // 'PLATFORM' | 'CREDIT' | 'LOAN' | 'BIND_CARD';
  sortType?: string;
  pageStart: number;
  pageSize: number;
  bizId?: string;
}

export interface SignedAgreementDTO {
  code?: string;
  name?: string;
  institution?: string;
  status?: string;
  fileFactoryNo?: string;

}

export interface QuerySignedAgreementResponse {
  dataList?: SignedAgreementDTO[];
  currentPage?: number;
  pageSize?: number;
  totalPage?: number;
  totalRecord?: number;
}


export function querySignedAgreementPage(request: QuerySignedAgreementRequest) {
  return MtopGet<QuerySignedAgreementRequest, QuerySignedAgreementResponse>({
    api: 'mtop.alibaba.fin.tao.blp.valid.signed.agreement.page.query',
    data: request,
  });
}

export interface QuerySsdSignedAgreementRequest extends BaseRequest {
  bizType: string; // 'PLATFORM' | 'CREDIT' | 'LOAN' | 'BIND_CARD';
  sortType?: string;
  creditPlatform?: string;
}

export interface SsdSignedAgreementDTO {
  code?: string;
  name?: string;
  institution?: string;
  status?: string;
  fileFactoryNo?: string;
  startTime?: string;
  type?: string;
  signTime?: number;
}

export interface QuerySsdSignedAgreementResponse {
  signedAgreementList?: SsdSignedAgreementDTO[];
  currentPage?: number;
  pageSize?: number;
  totalPage?: number;
  totalRecord?: number;
}
export function querySsdSignedAgreementPage(request: QuerySsdSignedAgreementRequest) {
  return MtopGet<QuerySsdSignedAgreementRequest, QuerySsdSignedAgreementResponse>({
    api: 'mtop.alibaba.fin.tao.blp.signed.agreement.list.query',
    data: request,
  });
}


export type GetExtensionParams = (currentGroup: UnsignedAgreementList) => any;
