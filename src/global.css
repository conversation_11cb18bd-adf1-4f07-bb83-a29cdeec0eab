@import './assets/styles/reset.css';
@import './assets/styles/font.css';
@import 'antd-mobile/es/global/global.css';
@import 'https://at.alicdn.com/t/a/font_4494081_ke8bgtjuf3h.css';

body {
  margin: 0;
  font-size: 24rpx;

  --primary: #1677ff;
  --color: #1677ff;
  --adm-color-primary: #1677ff;
  --adm-font-size-9: 26rpx;
  --adm-button-border-radius: 49rpx;
  --adm-font-size-main: 24rpx;
  --adm-color-text: #111;
  --adm-color-text-secondary: #111;
  --adm-color-light: #7c889c;
  --agreement-name-font-size: 20rpx;

  /* antd-form样式覆盖 */
  .adm-form {
    --prefix-width: auto;
  }
  .adm-notice-bar.adm-notice-bar-info {
    --background-color: rgba(255, 255, 255, 0.4);
    border: none;
  }
  .adm-form-item.adm-form-item-horizontal {
    &:not(:last-child) {
      margin-bottom: 16rpx;
    }
  }
  .adm-form-item.adm-form-item-horizontal .adm-list-item-content-prefix {
    padding: 0;
  }
  .adm-form-item.adm-form-item-horizontal .adm-form-item-label {
    display: inline-block;
    line-height: 3.5;
    padding-right: 24rpx;
  }
  .adm-list-item-content-main {
    padding: 0;
  }
  .adm-form-item-label .adm-form-item-required-asterisk {
    position: static;
    padding-left: 6rpx;
  }
  .adm-list-default .adm-list-body {
    border: none;
    background-color: transparent;
  }
  .adm-list-default .adm-list-body .adm-list-item-content {
    --padding-right: 0;
    border: none;
  }
  .adm-list-default .adm-list-body .adm-list-item {
    --padding-left: 0;
    border: none;
    background-color: transparent;
  }
  .adm-list-item-content {
    --padding-left: 0;
    border: none;
  }

  /* antd-form样式覆盖 */

  /* antd-button 样式覆盖 */
  .adm-button {
    padding: 24rpx 17rpx;
    --adm-font-size-9: 30rpx;
  }
  .adm-button-large {
    height: 98rpx;
  }
  .adm-button.cancel-button {
    --border-color: #f3f6f8;
    --background-color: #f3f6f8;
    --text-color: #111;
  }
  .adm-button.bordered-button {
    --border-color: #7c889c!important;
    --background-color: #fff!important;
    --text-color: #111!important;
    font-weight: 500!important;
  }
  .adm-button.disabled-btn {
    --background-color: #e5e8ec;
    --border-color: #e5e8ec;
    color: #cacfd7;
    &.adm-button-disabled {
      opacity: 1;
    }
  }
  .adm-button-loading-wrapper {
    flex-direction: row-reverse;
  }
  /* antd-button 样式覆盖 */

  /* antd-input 样式覆盖 */
  .adm-input {
    --background-color: #f3f6f8;
    --placeholder-color: #7c889c;
    padding: 24rpx;
    border-radius: 12rpx;
  }
  .adm-text-area {
    padding: 12rpx 24rpx;
    background: #f3f6f8;
    border-radius: 12rpx;
  }
  /* antd-input 样式覆盖 */

  .adm-center-popup-wrap {
    width: 594rpx;
    .adm-modal-body:not(.adm-modal-with-image) {
      padding: 48rpx 40rpx 40rpx;
    }
    .adm-modal-title {
      line-height: 36rpx;
      font-size: 36rpx;
      margin-bottom: 30rpx;
    }
    .adm-modal-content {
      font-size: 30rpx;
      color: #50607a;
      padding: 0 0 36rpx;
    }
    .adm-modal-footer {
      padding: 0;
    }
  }
  .normal-input-field {
    line-height: 3.5;
    border-radius: 12rpx;
    padding: 0 24rpx;
    background: #f3f6f8;
    --field-panel-text-color: #111;
    --field-panel-placeholder-color: #7c889c;
  }

  /* toast样式 */
  .adm-toast-loading {
    --size: 48rpx !important;
  }
  .adm-toast-mask .adm-toast-main-icon {
    padding: 24rpx;
    min-width: unset;
  }
  /* toast样式 */
}

.layout-ssd {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: rgba(240, 243, 247, 1);
  position: relative;
  z-index: 0;
  .page-container {
    flex: 1 1 auto;
    position: relative;
  }
}

.common-page-ssd {
  position: relative;
  background-image: url('https://gw.alicdn.com/imgextra/i3/O1CN01dtxEY31u9J1UiAjrO_!!6000000005994-2-tps-750-800.png');
  background-size: contain;
  background-repeat: no-repeat;
  &::before {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 800rpx;
    z-index: -1;
  }
}

.layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  .page-container {
    flex: 1 1 auto;
    position: relative;
    display: flex;
    flex-direction: column;
    > *:first-child {
      flex: 1 1 auto;
      padding: 32rpx 32rpx 165rpx;
    }
  }
}

.common-page-bg-1 {
  position: relative;
  /* background: linear-gradient(180deg, #cdd7ff 0%, #f3f6f8 69%);
  background-size: contain;
  background-repeat: no-repeat; */
  &::before {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: #f3f6f8;
  }
  &::after {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 1100rpx;
    z-index: -1;
    background: linear-gradient(180deg, #cdd7ff 0%, #f3f6f8 69%);
    background-size: contain;
    background-repeat: no-repeat;
  }
}

.yun-new-lp-bg {
  position: relative;
  &::before {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: #f3f6f8;
  }
  &::after {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 800rpx;
    z-index: -1;
    background-image: url('https://gw.alicdn.com/imgextra/i1/O1CN01B3saSq1GG6Ir27wVb_!!6000000000594-2-tps-750-800.png');
    background-size: contain;
    background-repeat: no-repeat;
  }
}

.common-page-bg-3 {
  background: linear-gradient(180deg, #fff 0%, #f3f6f8 100%);
}

.common-page-bg-gray {
  background-color: #f3f6f8;
}

.common-page-bg-white {
  background-color: #fff;
}

.common-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.common-card-2 {
  background: #f3f6f8;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.common-divide-line {
  height: 2rpx;
  background-color: #e5e8ec;
  transform: scaleY(0.5);
}
.common-divide-line-vertical {
  display: inline-block;
  margin: 0 8rpx;
  width: 2rpx;
  height: 30rpx;
  background-color: #e5e8ec;
  transform: scaleX(0.5);
}

.arrow-icon {
  width: 22rpx;
  height: 22rpx;
  margin-left: 8rpx;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-align-center {
  display: flex;
  align-items: center;
}

.flex-space-between {
  display: flex;
  justify-content: space-between;
}

.page-title-1 {
  font-size: 40rpx;
  font-weight: 500;
  line-height: 56rpx;
}

.page-title-2 {
  font-size: 30rpx;
  font-weight: 500;
  margin: 24rpx 0;
}

.page-bottom-bar {
  position: fixed;
  width: 100%;
  left: 0;
  bottom: 0;
  z-index: 100;
  padding: 16rpx 32rpx 42rpx;
  background: #fff;
  .main-btn {
    margin-top: 16rpx;
  }
  .agreement-radio-line {
    font-size: 20rpx;
    color: #7c889c;
  }
}

.page-bottom-text {
  position: fixed;
  width: 100%;
  left: 0;
  bottom: 130rpx;
  z-index: 100;
  text-align: center;
  color: #111;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.color-text-1 {
  color: #50607a;
}

.with-asterisk {
  position: relative;
  &::after {
    display: inline-block;
    content: '*';
    position: absolute;
    right: -20rpx;
    top: -4rpx;
    color: #ff6200;
    font-size: 26rpx;
    /* line-height: 26rpx; */
    font-family: PingFang SC;
  }
}

.sms-code-btn {
  font-size: 26rpx;
  &.disabled {
    color: #cacfd7;
  }
}

.common-tag {
  display: inline-flex;
  height: 36rpx;
  padding: 0 12rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  align-items: center;
  justify-content: center;
  font-weight: 400;
  margin: 0 8rpx;
  &.red {
    color: #f00;
    background: rgba(255, 0, 0, 0.06);
  }
  &.white {
    color: #f00;
    background: #fff;
  }
  &.primary {
    color: var(--primary);
    background: #ffefe5;
  }
}

.alibaba-font-md {
  font-family: 'ALIBABA NUMBER FONT MD';
}

.alibaba-font-rg {
  font-family: 'ALIBABA NUMBER FONT RG';
}

.fixed-bottom {
  display: flex;
  flex-direction: column;
  padding: 16rpx 32rpx;
}

.common-institution-icon {
  width: 28rpx;
  height: 28rpx;
  margin: 0 8rpx;
}

.safe-area-inset-top {
  height: var(--safe-area-inset-top);
}
.safe-area-inset-bottom {
  height: var(--safe-area-inset-bottom);
}
.navbar-height {
  height: var(--navbar-height);
  min-height: 88rpx;
}
