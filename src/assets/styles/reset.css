* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

body {
  padding: 0;
  margin: 0;
  -webkit-overflow-scrolling: touch;
}

img {
  border: 0;
  vertical-align: middle;
  max-width: 100%;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td,
th {
  padding: 0;
  vertical-align: middle;
}

h1,
h2,
h3,
h4,
h5,
h6,
p,
figure,
form,
blockquote {
  margin: 0;
}

ul,
ol,
li,
dl,
dd {
  margin: 0;
  padding: 0;
}

ul,
ol {
  list-style: none outside none;
}

h1,
h2,
h3 {
  line-height: 2;
  font-weight: normal;
}

i {
  font-style: normal;
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: "";
  content: none;
}

/* custom */
a {
  outline: none;
  text-decoration: none;
  -webkit-backface-visibility: hidden;
}

a:focus {
  outline: none;
}

input {
  outline: none;
  appearance: none;
}

input:focus,
select:focus,
textarea:focus {
  outline: none;
}

iframe {
  border: none;
  width: 100%;
}
