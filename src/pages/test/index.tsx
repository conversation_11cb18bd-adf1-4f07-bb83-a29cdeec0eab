/**
 * @file 组件测试页面
 * <AUTHOR>
 */
import { useCallback, useRef, useState } from 'react';
import { definePageConfig } from 'ice';
// import { urlQuery } from '@/utils';
import { Button, Toast } from 'antd-mobile';
import { UnsignedAgreementsSSD, UnsignedAgreementsSSDRef, AddressSelector } from '@/components';
import styles from './index.module.scss';
import { toIndex } from '@/utils/link';

export const pageConfig = definePageConfig(() => ({
  title: '测试',
}));

export default function TestPage() {
  const loanAgreementRef = useRef<UnsignedAgreementsSSDRef>();
  const [payload, setPayload] = useState({
    institutionList: ['XW_BANK', 'AI_BANK'],
  });

  const handleClick = useCallback(() => {
    setPayload({
      institutionList: ['ZYXJ'],
    });
    loanAgreementRef.current?.show({
      action: 'compulsory',
      popupProps: {
        title: ' ',
      },
    });
  }, []);

  const handleCompleted = () => {
  };

  const getInstitutionList = () => {
    return payload?.institutionList;
  };

  const handleClear = () => {
    localStorage.clear();
    Toast.show('缓存清空完毕');
    toIndex();
  };

  return (
    <div className={styles.test}>
      <UnsignedAgreementsSSD
        bizType="LOAN"
        institutionList={getInstitutionList()}
        ref={loanAgreementRef}
        popupTitle="授信机构相关协议"
        creditPlatform="MAYI_ZHIXIN"
        onCompleted={handleCompleted}
      />
      <Button block color="primary" onClick={handleClick} className={styles.button}>强制阅读</Button>
      <AddressSelector />
      <Button block color="primary" onClick={handleClear}>
        清空缓存并重新访问
      </Button>
    </div>
  );
}
