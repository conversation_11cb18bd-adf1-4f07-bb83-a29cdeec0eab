/**
 * @file 核身页面
 */

import { useCallback, useEffect, useState } from 'react';
import { definePageConfig } from 'ice';
import { Modal, Toast } from 'antd-mobile';
import Authentication from '@/components/Authentication';
import { _, requestId, getQueryParams } from '@/utils';
import { PAGES } from '@/common/constant';
import { authenticationCreate } from '@/store/authentication/actions';
import LinkUtil from '@/utils/link';
import { log } from '@alife/dtao-iec-spm-log';
import FullLoading from '@/components/FullLoading';
import styles from './index.module.scss';

enum SignScene {
  LICENSE = 'LICENSE',
  PHONE = 'PHONE',
  UNBIND = 'UNBIND',
  CLOSE = 'CLOSE',
}

const SIGN_SCENE_CONFIG = {
  LICENSE: {
    createParams: {
      authType: 'FACE',
      authScene: 'MODIFY_LICENSE',
    },
    successDirectPage: PAGES.CenterCertUpdate,
  },
  PHONE: {
    createParams: {
      authType: 'FACE',
      authScene: 'MODIFY_PHONE',
    },
    successDirectPage: PAGES.CenterPhoneUpdate,
  },
  UNBIND: {
    successDirectPage: 'popPage',
  },
  CLOSE: {
    successDirectPage: 'popPage',
  },
};

const queryParams: any = getQueryParams();

export default function ProfileSign() {
  const [loading, setLoading] = useState(false);
  const [authenticationToken, setAuthenticationToken] = useState();
  const signScene: SignScene = _.get(queryParams, 'scene');
  const signConfig = _.get(SIGN_SCENE_CONFIG, signScene);

  const pageInit = useCallback(async () => {
    setLoading(true);
    if (_.get(queryParams, 'authenticationToken')) {
      setAuthenticationToken(_.get(queryParams, 'authenticationToken'));
      setLoading(false);
      return;
    }
    if (!signConfig) {
      setLoading(false);
      Modal.alert({
        title: '服务器开小差',
        content: '请稍后重试',
      });
      return;
    }
    const res: any = await authenticationCreate({
      product: 'XFD',
      bizId: requestId(), // 保证唯一
      requestId: requestId(), // 幂等号
      origin: 'APP',
      ..._.get(signConfig, 'createParams'),
    });
    setLoading(false);
    setAuthenticationToken(res.taskToken);
  }, []);

  useEffect(() => {
    log.addVisitLog(PAGES.CenterProfileSign);
    pageInit();
  }, []);

  const handleSuccess = useCallback(() => {
    log.addSuccessLog('profile-sign-auth');
    Toast.show({
      icon: 'success',
      content: '核身成功',
    });
    if (signConfig?.successDirectPage === 'popPage') {
      LinkUtil.popPage();
    } else {
      setTimeout(() => {
        LinkUtil.locationReplace(signConfig.successDirectPage, { authToken: authenticationToken });
      }, 1000);
    }
  }, [authenticationToken, signConfig.successDirectPage]);

  const handleFailed = useCallback(() => {
    log.addErrorLog('profile-sign-auth');
    Toast.show({
      icon: 'fail',
      content: '核身失败',
    });
  }, []);

  if (!signConfig) {
    Modal.alert({
      title: '服务器开小差',
      content: '请稍后重试',
    });
    return;
  }

  return (
    <div className={styles.ProfileSignPage}>
      <FullLoading visible={loading} />
      <Authentication
        authenticationToken={authenticationToken}
        onSuccess={handleSuccess}
        onFail={handleFailed}
      />
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '人脸认证',
  window: {
    navBarImmersive: false,
  },
}));
