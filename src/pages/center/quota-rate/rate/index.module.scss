.rate {
  border-radius: 12rpx;
  padding: 24rpx;
  background: linear-gradient(180deg, #dde4ff 0%, #fff 100%);
  box-sizing: border-box;
  border: 2rpx solid #fff;
  .title {
    margin-bottom: 12rpx;
    display: flex;
    align-items: center;
    height: 36rpx;
    line-height: 36rpx;
  }
  .value {
    font-size: 60rpx;
    font-weight: 500;
    color: #111;
    font-family: "ALIBABA NUMBER FONT MD";
  }
  .baseRate {
    color: #7c889c;
    font-size: 28rpx;
    font-family: "ALIBABA NUMBER FONT RG";
    margin-left: 12rpx;
    text-decoration: line-through;
  }
  .rateTag {
    display: inline-flex;
    height: 32rpx;
    padding: 0 12rpx;
    border-radius: 8rpx;
    font-size: 20rpx;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    margin: 8rpx;
    color: var(--primary);
    background: #ffefe5;
    margin-right: 8rpx;
  }
  .tempQuotaWrap {
    border: 1rpx solid #e5e8ec;
    font-size: 26rpx;
    background: #fff;
    border-radius: 12rpx;
    padding: 15rpx 18rpx;
    position: relative;
    &::before {
      content: "";
      display: block;
      position: absolute;
      left: 24rpx;
      background: #fff;
      border: 1rpx solid #e5e8ec;
      border-width: 1rpx 0 0 1rpx;
      transform: rotate(45deg);
      width: 16rpx;
      height: 16rpx;
      top: -8rpx;
    }
    .tempQuotaDate {
      color: #111;
      color: #7c889c;
      font-family: "ALIBABA NUMBER FONT RG";
    }
  }
}
