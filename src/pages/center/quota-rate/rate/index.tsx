import { Fragment } from 'react';
import styles from './index.module.scss';
import { number } from '@ali/iec-dtao-utils';
import { isGreaterThan0, _, isNowBefore, isNowAfter, dayjsFormat } from '@/utils';

export default function Rate({ creditContract }) {
  const { interestRateInfo } = creditContract;
  const {
    tempInterestRatePercent,
    interestRatePercent,
    tempInterestRateStartTime,
    tempInterestRateEndTime,
  } = interestRateInfo;
  const { amountFormat } = number;

  const hasDiscount =
    tempInterestRatePercent &&
    isGreaterThan0(tempInterestRatePercent) &&
    interestRatePercent !== tempInterestRatePercent &&
    isNowBefore(tempInterestRateEndTime) &&
    isNowAfter(tempInterestRateStartTime);

  return (
    <div className={styles.rate}>
      <div className={styles.title}>
        当前利率
      </div>
      <div className={styles.value}>
        {hasDiscount ? (
          <Fragment>
            {amountFormat(_.get(interestRateInfo, 'tempInterestRatePercent'))}%
            <span className={styles.baseRate}>
              {amountFormat(_.get(interestRateInfo, 'interestRatePercent'))}%
            </span>
          </Fragment>
        ) : (
          ` ${amountFormat(_.get(interestRateInfo, 'interestRatePercent'))}%`
        )}
      </div>
      {hasDiscount && (
        <div className={styles.tempQuotaWrap}>
          <span className={styles.tempQuotaDate}>有效期至{dayjsFormat(_.get(interestRateInfo, 'tempInterestRateEndTime'))}</span>
        </div>
      )}
    </div>
  );
}
