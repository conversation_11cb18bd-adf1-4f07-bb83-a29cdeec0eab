.quotaRate {
  padding: 0 32rpx!important;
  .commonTabsWrap {
    margin-bottom: 32rpx;
  }
}

.serviceManageBtn {
  position: fixed;
  bottom: 92rpx;
  left: 50%;
  transform: translateX(-50%);
  border: 2rpx solid #e5e8ec;
  border-radius: 48rpx;
  height: 56rpx;
  font-size: 28rpx;
  z-index: 999;
  font-weight: 600;

  border-radius: 12rpx;
  box-sizing: border-box;
  /* Fill 填充/风灰蓝 #CACFD7 */
  /* 样式描述：弱提示图形模块填充 **Fill normal2** */
  border: 2rpx solid #cacfd7;
  min-width: 80rpx;
  padding: 15rpx 24rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #50607a;
  cursor: pointer;
}

.closePopup {
  :global(.adm-popup-body) {
    border-radius: 12rpx 12rpx 0 0;
    min-height: 96rpx;
  }
  .closePopupBody {
    padding: 32rpx 32rpx 68rpx;
  }
  .blockItem {
    color: #111;
    font-size: 30rpx;
    margin-bottom: 56rpx;
    text-align: center;
  }
}