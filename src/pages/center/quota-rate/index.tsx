import { useState, useEffect } from 'react';
import { definePageConfig } from 'ice';
import styles from './index.module.scss';
import CommonTabs from '@/components/CommonTabs';
import FullLoading from '@/components/FullLoading';
import Quota from './quota';
import Rate from './rate';
import { queryCreditContract } from '@/store/center/actions';
import { log } from '@alife/dtao-iec-spm-log';
import { PAGES } from '@/common/constant';

const defaultTabKey = 'QUOTA';

export default function QuotaRate() {
  const [activeKey, setActiveKey] = useState(defaultTabKey);
  const [creditContract, setCreditContract] = useState<any>(null);

  useEffect(() => {
    log.addVisitLog(PAGES.CenterQuotaRate);
    queryCreditContract({
      needQuerySurplusQuota: true, // 传true才能拿到可用额度
    }).then((res) => {
      setCreditContract(res);
    });
  }, []);

  if (!creditContract) {
    return <FullLoading visible />;
  }
  return (
    <div className={styles.quotaRate}>
      <div className={styles.commonTabsWrap}>
        <CommonTabs
          tabConfig={[
            {
              key: 'QUOTA',
              title: '额度',
            },
            {
              key: 'RATE',
              title: '利率',
            },
          ]}
          onTabClick={(key) => {
            setActiveKey(key);
          }}
          defaultActiveKey={defaultTabKey}
        />
      </div>


      {activeKey === 'QUOTA' && <Quota creditContract={creditContract} />}
      {activeKey === 'RATE' && <Rate creditContract={creditContract} />}
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '额度与利率',
}));
