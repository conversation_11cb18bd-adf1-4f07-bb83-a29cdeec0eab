import { useState, useEffect } from 'react';
import { definePageConfig } from 'ice';
import styles from './index.module.scss';
import CommonTabs from '@/components/CommonTabs';
import FullLoading from '@/components/FullLoading';
import Quota from './quota';
import Rate from './rate';
import { queryCreditContract } from '@/store/center/actions';
import { log } from '@alife/dtao-iec-spm-log';
import { PAGES } from '@/common/constant';
import { Popup, Button } from 'antd-mobile';
import LinkUtil from '@/utils/link';

const defaultTabKey = 'QUOTA';

export default function QuotaRate() {
  const [activeKey, setActiveKey] = useState(defaultTabKey);
  const [creditContract, setCreditContract] = useState<any>(null);
  const [closePopupVisible, setClosePopupVisible] = useState(false);

  const handleServiceManageClick = () => {
    log.addClickLog('service-manage-btn');
    setClosePopupVisible(true);
  };

  const handleClose = () => {
    setClosePopupVisible(false);
  };

  const handleCloseServiceBtnClick = async () => {
    log.addClickLog('service-manage-close-btn');
    LinkUtil.pushPage(PAGES.CenterClose);
  };

  useEffect(() => {
    log.addVisitLog(PAGES.CenterQuotaRate);
    queryCreditContract({
      needQuerySurplusQuota: true, // 传true才能拿到可用额度
    }).then((res) => {
      setCreditContract(res);
    });
  }, []);

  if (!creditContract) {
    return <FullLoading visible />;
  }
  return (
    <>
      <div className={styles.quotaRate}>
        <div className={styles.commonTabsWrap}>
          <CommonTabs
            tabConfig={[
              {
                key: 'QUOTA',
                title: '额度',
              },
              {
                key: 'RATE',
                title: '利率',
              },
            ]}
            onTabClick={(key) => {
              setActiveKey(key);
            }}
            defaultActiveKey={defaultTabKey}
          />
        </div>


        {activeKey === 'QUOTA' && <Quota creditContract={creditContract} />}
        {activeKey === 'RATE' && <Rate creditContract={creditContract} />}
      </div>
      <div onClick={handleServiceManageClick} className={styles.serviceManageBtn}>
        服务管理
      </div>
      <Popup
        className={styles.closePopup}
        onClose={handleClose}
        onMaskClick={handleClose}
        visible={closePopupVisible}
      >
        <div className={styles.closePopupBody}>
          <div onClick={handleCloseServiceBtnClick} className={styles.blockItem}>
            关闭服务
          </div>
          <div className={styles.middleEmpty} />
          <Button onClick={handleClose} block className="cancel-button">
            取消
          </Button>
        </div>
      </Popup>
    </>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '我的额度及更多',
}));
