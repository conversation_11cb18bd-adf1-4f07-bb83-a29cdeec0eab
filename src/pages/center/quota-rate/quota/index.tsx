import styles from './index.module.scss';
import { numberFormat, _, isGreaterThan0, dayjsFormat } from '@/utils';

export default function Quota({ creditContract }) {
  const { creditQuotaInfo, tempQuotaInfo } = creditContract || {};
  const hasTempQuota = isGreaterThan0(_.get(tempQuotaInfo, 'totalQuota'));

  return (
    <div className={styles.quota}>
      <div className={styles.quotaTitle}>总额度 (元)</div>
      <div className={styles.quotaAmount}>{numberFormat(_.get(creditQuotaInfo, 'totalQuota'))}</div>
      {hasTempQuota && (
        <div className={styles.tempQuotaWrap}>
          <div className={styles.tempQuotaTitle}>含临时额度 (元)</div>
          <div className="flex-align-center">
            <span className={styles.tempQuotaAmount}>
              {numberFormat(_.get(tempQuotaInfo, 'totalQuota'))}
            </span>
            <span className={styles.tempQuotaDate}>有效期至{dayjsFormat(_.get(tempQuotaInfo, 'quotaEndTime'))}</span>
          </div>
        </div>
      )}
      <div className={styles.quotaTypes}>
        <div className={styles.quotaTypeItem}>
          可用额度
          {creditContract.status === 'FROZEN' ? (
            <span className={styles.frozenText}>额度冻结</span>
          ) : (
            <span className={styles.quotaValue}>
              {numberFormat(_.get(creditQuotaInfo, 'surplusQuota'))}
            </span>
          )}
        </div>
        <div className={styles.quotaTypeItem}>
          已用额度
          <span className={styles.quotaValue}>
            {numberFormat(_.get(creditQuotaInfo, 'occupiedQuota'))}
          </span>
        </div>
      </div>
    </div>
  );
}
