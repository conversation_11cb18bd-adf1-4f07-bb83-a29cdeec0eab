/**
 * @file 已签署的协议
 */

import { useState, useCallback, useEffect } from 'react';
import { definePageConfig } from 'ice';
import { InfiniteScroll, Toast } from 'antd-mobile';
import { url } from '@ali/iec-dtao-utils';
import { log } from '@alife/dtao-iec-spm-log';
import { includes } from 'lodash-es';

import { CommonTabs, ArrowIcon } from '@/components';
import {
  querySignedAgreementPage,
  QuerySignedAgreementRequest,
  QuerySignedAgreementResponse,
  SignedAgreementDTO,
} from '@/store/agreement/actions';
import { useListModal } from '@/hooks/useListModal';
import LinkUtil from '@/utils/link';
import { _, getQueryParams } from '@/utils';
import { PAGES, AGREEMENT_PDF } from '@/common/constant';
import classnames from 'classnames';

import styles from './index.module.scss';

const defaultBizType = 'PLATFORM';

export default function SignedAgreements() {
  const [curBizType, setCurBizType] = useState(defaultBizType);
  const [signedAgreementsHasMore, setSignedAgreementsHasMore] = useState<boolean>(false);
  const pageFrom = _.get(getQueryParams(), 'from');
  const isSSD = pageFrom === 'ssd';
  const AGREEMENT_CENTER_CODE = ['XFD_SERVICE_AGREEMENT', 'XFD_INDIVIDUAL_INFO_AUTH'];

  const renderItem = useCallback((item: SignedAgreementDTO) => {
    return (
      <div
        onClick={() => handleAgreementClick(item)}
        key={item.code}
        className={styles.agreementItem}
      >
        <p className={styles.name}>{item.name}</p>
        <ArrowIcon />
      </div>
    );
  }, []);

  const renderEmpty = useCallback(() => {
    return (
      <div className={styles.empty}>
        <img
          className={styles.emptyImg}
          src={
            isSSD
              ? 'https://gw.alicdn.com/imgextra/i1/O1CN01k0MIIk1Uum53BWQrs_!!6000000002578-2-tps-560-400.png'
              : 'https://img.alicdn.com/imgextra/i2/O1CN017JIPCc1oZVzehYXTx_!!6000000005239-2-tps-560-400.png'
          }
        />
        <p className={styles.emptyText}>{isSSD ? '请联系客服获取相关协议' : '暂无协议'}</p>
      </div>
    );
  }, [pageFrom]);

  const loadMore = async () => {
    const { hasMore } = await doFetch();
    setSignedAgreementsHasMore(hasMore);
  };

  const { doFetch, doInit, renderList } = useListModal<
  QuerySignedAgreementRequest,
  QuerySignedAgreementResponse,
  SignedAgreementDTO
  >({
    fetch: querySignedAgreementPage,
    renderItem,
    renderEmpty,
    listKey: 'dataList',
    totalKey: 'totalRecord',
    pageParam: {
      bizType: curBizType,
      sortType: 'SIGN_TIME_DESC',
      pageStart: 1,
      pageSize: 6,
    },
  });

  const handleAgreementClick = (item: any) => {
    log.addClickLog('signed-agreement-click');
    if (includes(AGREEMENT_CENTER_CODE, item?.code)) {
      LinkUtil.pushPage(PAGES.SignedAgreementPreview, {
        title: item?.name,
        code: item?.code,
      });
      return;
    }
    const targetUrl = url.add(AGREEMENT_PDF, {
      title: item?.name,
      fileFactoryNo: item?.fileFactoryNo,
      code: item?.code,
    });
    LinkUtil.navigatorOpenURL(targetUrl);
  };

  const handleTabClick = (key) => {
    setCurBizType(key);
  };

  const init = async () => {
    try {
      const loading = Toast.show({
        icon: 'loading',
        content: '加载中...',
        duration: 0,
      });
      const res = await doInit();
      setSignedAgreementsHasMore(res?.hasMore);
      log.addSuccessLog('signed-agreements-fetch');
      loading.close();
    } catch (e) {
      log.addErrorLog('signed-agreements-fetch');
    }
  };

  useEffect(() => {
    log.addVisitLog(PAGES.CenterSignedAgreements);
    init();
  }, [curBizType]);

  useEffect(() => {
    log.addVisitLog(PAGES.CenterSignedAgreements);
  }, []);

  return (
    <div className={classnames([styles.signedAgreements])}>
      <div className={styles.commonTabsWrap}>
        <CommonTabs
          onTabClick={(key) => {
            handleTabClick(key);
          }}
          defaultActiveKey={curBizType}
          tabConfig={[
            {
              key: 'PLATFORM',
              title: '服务协议',
            },
            {
              key: 'CREDIT',
              title: '授信协议',
            },
          ]}
        />
      </div>
      <div className={styles.agreementItems}>{renderList()}</div>
      <InfiniteScroll loadMore={loadMore} hasMore={signedAgreementsHasMore}>
        {null}
      </InfiniteScroll>
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '相关协议',
  spm: {
    spmB: PAGES.CenterSignedAgreements,
  },
}));
