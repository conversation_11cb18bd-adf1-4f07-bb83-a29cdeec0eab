import { Fragment, useState, useEffect, useCallback } from 'react';
import { definePageConfig } from 'ice';
import { Modal, NoticeBar } from 'antd-mobile';
import styles from './index.module.scss';
import { dayjsFormat, _, getMsgByErrorCode, getErrorCodeFromRejectRes } from '@/utils';
import FullLoading from '@/components/FullLoading';
import { DetailItem } from '@/components';
import { useDebounceFn } from 'ahooks';
import { queryCustomerProfile, customerProfileConsult } from '@/store/center/actions';
import { useVisibilityChangeRefresh } from '@/hooks';
import LinkUtil from '@/utils/link';
import { log } from '@alife/dtao-iec-spm-log';
import { PAGES } from '@/common/constant';

export default function CertificateManage() {
  const [identityData, setIdentityData] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const pageInit = async () => {
    try {
      const res = await queryCustomerProfile({
        typeList: JSON.stringify(['IDENTITY_CARD']),
      });
      const data = _.find(res.result, (item) => item.type === 'IDENTITY_CARD');
      if (!data) {
        Modal.alert({
          title: '证件查询失败',
          onConfirm: () => {
            LinkUtil.popPage();
          },
        });
        return;
      }
      setIdentityData(data.content);
      log.addVisitLog(PAGES.CenterCertManage, { status: data.content?.status });
    } catch (error) {
      setLoading(false);
    }
  };

  useEffect(() => {
    pageInit();
  }, []);

  useVisibilityChangeRefresh(pageInit);

  const { run: handleUpdateBtnClick } = useDebounceFn(
    async () => {
      try {
        const consultRes: any = await customerProfileConsult({
          type: 'IDENTITY_CARD',
        });
        if (consultRes.admitted) {
          if (consultRes.needAuthentication) {
            LinkUtil.pushPage(PAGES.CenterProfileSign, { scene: 'LICENSE' });
          } else {
            LinkUtil.pushPage(PAGES.CenterCertUpdate);
          }
        } else {
          throw new Error(getErrorCodeFromRejectRes(consultRes));
        }
      } catch (e) {
        Modal.alert({
          title: '无法更新证件',
          content: getMsgByErrorCode(e.message),
        });
      }
    },
    {
      wait: 500,
      leading: true,
      trailing: false,
    },
  );

  const renderNoticeBar = useCallback(() => {
    if (identityData?.status === 'PENDING_ID_PHOTO') {
      return <NoticeBar content={<span className={styles.noticeContent}>为了给您提供更好的服务，请您补充身份证照片</span>} color="alert" />;
    }
    if (identityData?.status === 'EXPIRED') {
      return <NoticeBar content={<span className={styles.noticeContent}>身份证已过期，请尽快更新，以免影响借款</span>} color="alert" />;
    }
    if (identityData?.status === 'APPROACHING_EXPIRATION') {
      return (
        <NoticeBar
          content={<span className={styles.noticeContent}>{`身份证有效期剩余${identityData.remainEffectiveDays}天，请尽快更新，以免影响借款`}</span>}
          color="alert"
        />
      );
    }

    return null;
  }, [identityData]);

  const renderStartEndDate = (data) => {
    if (data.status === 'PENDING_ID_PHOTO') {
      return '上传身份证照片后查看';
    }
    const endDate = _.get(data, 'licenseLongTermEffective')
      ? '长期'
      : dayjsFormat(_.get(data, 'licenseExpiredDate'));
    return `${dayjsFormat(_.get(data, 'licenseEffectDate'))}至${endDate}`;
  };

  const showUpdateBtn = _.includes(
    ['EXPIRED', 'APPROACHING_EXPIRATION', 'PENDING_ID_PHOTO'],
    identityData?.status,
  );

  const isPendingIdPhoto = identityData?.status === 'PENDING_ID_PHOTO';
  const frontImgSrc = isPendingIdPhoto ? 'https://gw.alicdn.com/imgextra/i3/O1CN01Anqpju1TUq374iwtC_!!6000000002386-2-tps-602-372.png' : 'https://gw.alicdn.com/imgextra/i2/O1CN01MlEPyU1LqLivy75o4_!!6000000001350-2-tps-596-366.png';
  const backImgSrc = isPendingIdPhoto ? 'https://gw.alicdn.com/imgextra/i1/O1CN01YkMiPw1yzIThRq9po_!!6000000006649-2-tps-602-372.png' : 'https://gw.alicdn.com/imgextra/i2/O1CN01xGa7sv1zXBmh9rltW_!!6000000006723-2-tps-598-368.png';

  return (
    <div className={styles.certificateManage}>
      {renderNoticeBar()}
      <div className={styles.certificateManageContainer}>
        {
          <div
            className={styles.certImageWrap}
            style={{ visibility: identityData ? 'visible' : 'hidden' }}
          >
            <div className={styles.certImageItem}>
              <img src={frontImgSrc} />
            </div>
            <div className={styles.certImageItem}>
              <img src={backImgSrc} />
            </div>
          </div>
        }
        {!identityData ? (
          <FullLoading visible={loading} />
        ) : (
          <Fragment>
            <div>
              <DetailItem label="姓名" value={_.get(identityData, 'name')} />
              <DetailItem label="身份证号" value={_.get(identityData, 'licenseNo')} />
              <DetailItem label="有效期" value={renderStartEndDate(identityData)} />
              {/* <div className={classnames([styles.cardItem])}>
                <div>姓名</div>
                <div className={styles.value}>{_.get(identityData, 'name')}</div>
              </div>
              <div className={classnames([styles.cardItem])}>
                <div>身份证号</div>
                <div className={styles.value}>{_.get(identityData, 'licenseNo')}</div>
              </div>
              <div className={classnames([styles.cardItem])}>
                <div>有效期</div>
                <div className={styles.value}>
                  {dayjsFormat(_.get(identityData, 'licenseEffectDate'))}至
                  {renderLicenseExpiredDate(identityData)}
                </div>
              </div> */}
            </div>
          </Fragment>
        )}
      </div>
      {showUpdateBtn && (
        <div className={styles.expiredTip}>
          我要
          <a onClick={handleUpdateBtnClick}>{isPendingIdPhoto ? '上传' : '更新'}身份证</a>
        </div>
      )}
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '证件管理',
  spm: {
    spmB: PAGES.CenterCertManage,
  },
}));
