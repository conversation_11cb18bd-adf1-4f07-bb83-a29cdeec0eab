import { useEffect, useState } from 'react';
import { definePageConfig } from 'ice';
import styles from './index.module.scss';
import { queryCustomerProfile, customerProfileConsult } from '@/store/center/actions';
import { Modal, Button } from 'antd-mobile';
import FullLoading from '@/components/FullLoading';
import { useVisibilityChangeRefresh } from '@/hooks';
import { useDebounceFn } from 'ahooks';
import { _, getErrorCodeFromRejectRes, getMsgByErrorCode } from '@/utils';
import LinkUtil from '@/utils/link';
import { PAGES } from '@/common/constant';
import { log } from '@alife/dtao-iec-spm-log';

export default function PhoneManage() {
  const [customerPhone, setCustomerPhone] = useState({});

  const pageInit = async () => {
    const res = await queryCustomerProfile({
      typeList: JSON.stringify(['PHONE']),
    });

    const phoneData = _.find(res.result, (item) => item.type === 'PHONE');
    if (!phoneData) {
      Modal.alert({
        title: '查询手机号失败',
        onConfirm: () => {
          LinkUtil.popPage();
        },
      });
      return;
    }
    setCustomerPhone(phoneData.content);
  };

  useVisibilityChangeRefresh(pageInit);

  useEffect(() => {
    log.addVisitLog(PAGES.CenterPhoneManage);
    pageInit();
  }, []);

  const { run: handleUpdateBtnClick } = useDebounceFn(
    async () => {
      try {
        const consultRes: any = await customerProfileConsult({
          type: 'PHONE',
        });

        if (consultRes.admitted) {
          if (consultRes.needAuthentication) {
            LinkUtil.pushPage(PAGES.CenterProfileSign, { scene: 'PHONE' });
          } else {
            LinkUtil.pushPage(PAGES.CenterPhoneUpdate);
          }
        } else {
          throw new Error(getErrorCodeFromRejectRes(consultRes));
        }
      } catch (e) {
        Modal.alert({
          title: '暂无法更换',
          content: getMsgByErrorCode(e.message),
        });
      }
    },
    {
      wait: 500,
      leading: true,
      trailing: false,
    },
  );

  const customerPhoneNo = _.get(customerPhone, 'phoneNo');

  if (!customerPhoneNo) {
    return <FullLoading visible />;
  }

  return (
    <div className={styles.phoneManage}>
      <div className={styles.phoneManageValue}>{customerPhoneNo}</div>
      <div className={styles.safeTip}>
        <img src="https://gw.alicdn.com/imgextra/i3/O1CN01tARv8J28tTZC2crJt_!!6000000007990-2-tps-48-48.png" />
        信息安全保障中
      </div>
      <div className={styles.bottomTip}>
        该手机号用于接收通知短信，包括借款、还款、优惠权益、短信验证码等
      </div>
      <div className={styles.bottomBtnWrap}>
        <Button className={styles.bottomBtn} onClick={handleUpdateBtnClick} color="primary">
          更换手机号
        </Button>
      </div>
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '手机号管理',
}));
