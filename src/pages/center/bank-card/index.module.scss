.bankCardPage {
  padding: 32rpx;
  .bindC {
    width: 100%;
    padding: 32rpx;
    background-color: #f3f6f8;
  }
  .bindCFixed {
    position: fixed;
    bottom: 0;
    left: 0;
  }
  .bind {
    border-radius: 12rpx;
    opacity: 1;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    width: 100%;
    padding: 47rpx 0;
    .addText {
      font-size: 26rpx;
      line-height: 26rpx;
      color: #111;
      padding-left: 13rpx;
    }
    .addIcon {
      display: block;
      height: 35rpx;
      width: 35rpx;
      background-image: url('https://gw.alicdn.com/imgextra/i2/O1CN01okwvGb1PLtqVJUkJR_!!*************-2-tps-200-200.png');
      background-position: 50% 50%;
      background-repeat: no-repeat;
      background-size: cover;
    }
  }
  .item {
    border-radius: 12rpx;
    background-color: #fff;
    padding: 24rpx;
    margin-bottom: 16rpx;
    .hd {
      display: flex;
      flex-direction: row;
      align-items: center;
      .bankIcon {
        font-size: 72rpx;
      }
      .info {
        padding-left: 10rpx;
        .title {
          display: flex;
          flex-direction: row;
          .name {
            font-size: 30rpx;
            font-weight: 500;
            line-height: 30rpx;
            color: #111;
          }
          .tag {
            padding: 6rpx 8rpx;
            text-align: center;
            background: rgba(64, 102, 255, 0.1);
            font-size: 20rpx;
            font-weight: 600;
            line-height: 20rpx;
            color: var(--primary);
            border-radius: 6rpx;
            margin-left: 6rpx;
          }
        }
        .desc {
          font-size: 24rpx;
          line-height: 24rpx;
          color: #50607a;
          padding-top: 13rpx;
        }
      }
    }
    .bd {
      padding-top: 42rpx;
      .no {
        font-size: 36rpx;
        font-weight: 600;
        line-height: 36rpx;
        color: #111;
        text-align: right;
      }
    }
  }
}

.popup {
  :global(.adm-popup-body) {
    border-radius: 32rpx 32rpx 0 0;
    min-height: 96rpx;
  }
}

.action {
  text-align: center;
  padding: 16rpx 0;
  font-size: 30rpx;
  line-height: 45rpx;
  color: #111;
  margin: 0 32rpx;
  margin-bottom: 24rpx;
  &:first-child {
    margin-top: 24rpx;
  }
}

.cancel {
  padding: 25rpx 36rpx;
  background-color: #f3f6f8;
  font-size: 30rpx;
  font-weight: 600;
  line-height: 30rpx;
  text-align: center;
  color: #111;
  margin: 0 32rpx;
  margin-bottom: 68rpx;
  border-radius: 12rpx;
}

.confirmTitle {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  .confirmName {
    font-size: 36rpx;
    line-height: 36rpx;
    color: #111;
    font-weight: 500;
  }
  .iconSmall {
    font-size: 36rpx;
    margin-right: 6rpx;
  }
}

.confirmContent {
  font-size: 30rpx;
  line-height: 45rpx;
  text-align: center;
  color: #50607a;
  padding-top: 24rpx;
}

.button1 {
  background-color: #000;
}
