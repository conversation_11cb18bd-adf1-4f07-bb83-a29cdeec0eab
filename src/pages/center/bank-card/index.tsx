/**
 * @file 银行卡管理页面
 * <AUTHOR>
 */

import { definePageConfig } from 'ice';
import { useCallback, useEffect, useState, useRef } from 'react';
import { Dialog, Popup, Toast } from 'antd-mobile';
import { filter, first, map, get } from 'lodash-es';
import classNames from 'classnames';
import { log } from '@alife/dtao-iec-spm-log';
import { useDebounceFn } from 'ahooks';

import { useLoading, IconInstitution, ModalLight, ModalLightRef, FullLoading } from '@/components';
import {
  queryList, BindQueryListResponse, BankCardDTO,
  unbindConsult, unbindCreate, unBindApply, changeMasterCard,
} from '@/store/bankcard/actions';
import { query as AuthQuery } from '@/store/authentication/actions';
import { popPage, pushPage } from '@/utils/link';
import { ErrorMessageMap, PAGES, PRODUCT, TENANT, UNBIND_APPLY_ERROR_CODE } from '@/common/constant';
import { getTimeStamp, requestId, getBankCardNo, _ } from '@/utils';
import { useVisibilityChangeRefresh } from '@/hooks';

import styles from './index.module.scss';

interface BindRef {
  taskToken?: string;
  bankCard?: BankCardDTO;
}

export const pageConfig = definePageConfig(() => ({
  title: '银行卡管理',
  spm: {
    spmB: PAGES.CenterBankCard,
  },
}));


export default function BankCardPage() {
  const [payload, setPayLoad] = useState<BindQueryListResponse>();
  const { showLoading, hideLoading } = useLoading();
  const [visible, setVisible] = useState(false);
  const [current, setCurrent] = useState<BankCardDTO>();
  const [loadingVisible, setLoadingVisible] = useState(false);
  const bindRef = useRef<BindRef>({
    taskToken: '',
    bankCard: undefined,
  });
  const unbindErrorRef = useRef<ModalLightRef>();
  const unbindRef = useRef<ModalLightRef>();
  const changeRef = useRef<ModalLightRef>();

  const doInit = async () => {
    setLoadingVisible(true);
    try {
      const res = await queryList();
      setPayLoad(res);
      hideLoading();
      log.addSuccessLog('bank-card-init');
    } catch (e) {
      hideLoading();
      Dialog.show({
        content: ErrorMessageMap.BUSY_DEFUALT,
        closeOnAction: true,
        actions: [[{
          key: 'cancel',
          text: '知道了',
          onClick: popPage,
        }]],
      });
      log.addErrorLog('bank-card-init');
    } finally {
      setLoadingVisible(false);
    }
  };

  const doClear = () => {
    bindRef.current = {};
  };

  const handleAuthenticationFail = () => {
    Toast.show({
      icon: 'fail',
      content: '核身失败，请重试',
    });
    log.addErrorLog('bank-card-unbind-auth');
    doClear();
  };

  const handleAuthenticationSuccess = async (authToken?: string) => {
    try {
      showLoading('解绑申请中...');
      log.addSuccessLog('bank-card-unbind-auth');
      if (bindRef?.current?.bankCard) {
        const { bankCardNo, bankCode, bankName, bindCardNo, institution } = bindRef.current.bankCard;
        const baseParams = {
          requestId: requestId(),
          applyTime: getTimeStamp(),
          bankCardNo,
          bankCode,
          bankName,
          bindCardNo,
          institution,
        };
        if (authToken) {
          _.set(baseParams, 'extension', {
            authToken,
          });
        }
        const applyRes = await unBindApply(baseParams);
        if (applyRes?.unbindResult) {
          Toast.show({
            icon: 'success',
            content: '解绑成功',
          });
          doClear();
          doInit();
          log.addSuccessLog('bank-card-unbind');
        } else {
          const unBindFailedCode = applyRes?.failReason?.failedCode || '';
          const unBindErrorMessage = get(UNBIND_APPLY_ERROR_CODE, unBindFailedCode, ErrorMessageMap.BUSY_DEFUALT);
          log.addErrorLog('bank-card-unbind', {
            unBindFailedCode,
          });
          throw new Error(unBindErrorMessage);
        }
      } else {
        log.addErrorLog('bank-card-unbind');
        throw new Error();
      }
    } catch (e) {
      hideLoading();
      Toast.show({
        content: e?.message || ErrorMessageMap.BUSY_DEFUALT,
      });
      log.addErrorLog('bank-card-unbind');
    }
  };

  const handleCallback = async () => {
    try {
      log.addSuccessLog('bank-card-auth-callback-new');
      const taskToken = bindRef?.current?.taskToken;
      if (taskToken) {
        const queryRes = await AuthQuery({
          product: PRODUCT,
          tenant: TENANT,
          authenticationToken: taskToken,
        });
        if (queryRes?.status === 'SUCCESS') {
          handleAuthenticationSuccess(taskToken);
        } else {
          handleAuthenticationFail();
        }
      } else {
        doInit();
      }
    } catch (e) {
      log.addErrorLog('bank-card-auth-callback');
    }
  };

  const doUnbindCardImpl = async () => {
    try {
      unbindRef.current?.toggle(false);
      showLoading('解绑申请中...');
      log.addClickLog('bank-card-unbind');
      if (current) {
        const { bankCardNo, bankCode, bankName, bindCardNo, institution } = current;
        const consultRes = await unbindConsult({
          bankCardNo,
          bankCode,
          bankName,
          bindCardNo,
          institution,
        });
        if (consultRes?.consultResult === true) {
          log.addSuccessLog('bank-card-unbind-consult');
          if (consultRes?.needAuthentication === true) {
            const createRes = await unbindCreate({
              bizId: requestId(),
              requestId: requestId(),
              origin: 'APP',
              authType: 'FACE',
              authScene: 'UNBIND',
            });
            if (createRes?.taskToken) {
              log.addSuccessLog('bank-card-unbind-consult-auth');
              bindRef.current.taskToken = createRes.taskToken;
              pushPage(PAGES.CenterProfileSign, {
                authenticationToken: createRes.taskToken,
                scene: 'UNBIND',
              });
            } else {
              log.addErrorLog('bank-card-unbind-consult-auth');
              throw new Error('UNIBIND_CREATE_ERROR');
            }
          } else {
            log.addSuccessLog('bank-card-unbind-consult-no-auth');
            handleAuthenticationSuccess();
          }
        } else if (consultRes?.consultResult === false) {
          const unbindConsultRejectCode = consultRes?.rejectReason?.rejectCode || '';
          const unbindConsultDesc = get(
            ErrorMessageMap,
            unbindConsultRejectCode,
            ErrorMessageMap.BUSY_DEFUALT,
          );
          Toast.show({
            content: unbindConsultDesc,
          });
          log.addOtherLog('bank-card-unbind-reject', {
            unbindConsultRejectCode,
          });
        } else {
          throw new Error();
        }
      } else {
        throw new Error('UNIBIND_CREATE_PARAMS');
      }
    } catch (e) {
      hideLoading();
      Toast.show({
        icon: 'fail',
        content: e?.message || ErrorMessageMap.BUSY_DEFUALT,
      });
      log.addErrorLog('bank-card-unbind-consult');
    }
  };

  const doUnbindCard = useDebounceFn(doUnbindCardImpl, {
    wait: 1000,
    leading: true,
    trailing: false,
  }).run;

  const handleUnbindCard = () => {
    setVisible(false);
    if (payload?.result?.length && payload?.result?.length > 1) {
      unbindRef.current?.toggle(true);
    } else {
      unbindErrorRef.current?.toggle(true);
    }
  };

  const handleBindClick = async () => {
    log.addClickLog('bank-card-bind-card');
    pushPage(PAGES.BindCard);
  };

  const handleCancelClick = () => {
    log.addClickLog('bank-card-cancel');
    setVisible(false);
  };

  const handleBankCardClick = useCallback((bankCard: BankCardDTO) => () => {
    log.addClickLog('bank-card-select');
    setCurrent(bankCard);
    bindRef.current.bankCard = bankCard;
    setVisible(true);
  }, []);

  const handlePopupClose = () => {
    setCurrent(undefined);
    doClear();
  };

  const doChangeMasterCard = async () => {
    try {
      log.addClickLog('bank-card-change-master-card');
      changeRef.current?.toggle(false);
      const masterCardList = filter(payload?.result, {
        masterCard: true,
      });
      if (!masterCardList?.length) {
        Toast.show({
          content: '没有主卡，不能切换',
        });
        return;
      }
      if (masterCardList?.length > 1) {
        Toast.show({
          content: '多主卡，不能切换',
        });
        return;
      }
      const masterCard = first(masterCardList);
      if (!masterCard?.bindCardNo || !current?.bindCardNo) {
        log.addErrorLog('bank-card-change-master-card');
        Toast.show({
          content: '参数异常',
        });
        return;
      }
      log.addOtherLog('bank-card-change-master-card-apply');
      const res = await changeMasterCard({
        oldMasterCardNo: masterCard?.bindCardNo,
        newMasterCardNo: current?.bindCardNo,
        requestId: requestId(),
        institution: current?.institution,
      });
      if (res?.result === true) {
        setVisible(false);
        Toast.show({
          icon: 'success',
          content: '切换成功',
        });
        doInit();
        log.addSuccessLog('bank-card-change-master-card');
        return;
      }
      const changeRejectCode = res?.rejectReason?.rejectCode || '';
      const changeErrorMsg = get(ErrorMessageMap, changeRejectCode, ErrorMessageMap.BUSY_DEFUALT);
      throw new Error(changeErrorMsg);
    } catch (e) {
      hideLoading();
      Toast.show({
        content: e?.message || ErrorMessageMap.BUSY_DEFUALT,
      });
      log.addErrorLog('bank-card-change-master-card');
    }
  };

  const handleChangeMasterCard = () => {
    setVisible(false);
    if (current?.masterCard) {
      Toast.show({
        content: '该银行卡已是主卡',
      });
      return;
    }
    changeRef.current?.toggle(true);
  };

  const renderItem = (item: BankCardDTO, index: number) => {
    return (
      <div className={styles.item} key={`bank-item-${index}`} onClick={handleBankCardClick(item)}>
        <div className={styles.hd}>
          <IconInstitution className={styles.bankIcon} type={item?.bankCode} />
          <div className={styles.info}>
            <div className={styles.title}>
              <p className={styles.name}>{item?.bankName}</p>
              {item?.masterCard ? <span className={styles.tag}>主卡</span> : null}
            </div>
            <p className={styles.desc}>储蓄卡</p>
          </div>
        </div>
        <div className={styles.bd}>
          <p className={styles.no}>
            ****&nbsp;****&nbsp;****&nbsp;****&nbsp;{getBankCardNo(item?.bankCardNo)}
          </p>
        </div>
      </div>
    );
  };

  const renderList = () => {
    if (payload?.result?.length) {
      return map(payload?.result, renderItem);
    }
    return null;
  };

  useEffect(() => {
    log.addVisitLog(PAGES.CenterBankCard);
    doInit();
  }, []);

  useVisibilityChangeRefresh(handleCallback);

  return (
    <div className={styles.bankCardPage}>
      <FullLoading visible={loadingVisible} />
      {renderList()}
      <div className={classNames(styles.bindC, payload?.result?.length && styles.bindCFixed)}>
        <div className={styles.bind} onClick={handleBindClick}>
          <i className={styles.addIcon} />
          <p className={styles.addText}>添加银行卡</p>
        </div>
      </div>
      <Popup className={styles.popup} visible={visible} onMaskClick={handleCancelClick} onClose={handlePopupClose}>
        <div className={styles.action} onClick={handleChangeMasterCard}>
          <p className={styles.text}>设为主银行卡</p>
        </div>
        <div className={styles.action} onClick={handleUnbindCard}>
          <p className={styles.text}>解绑银行卡</p>
        </div>
        <div className={styles.cancel} onClick={handleCancelClick}>
          <p className={styles.text}>取消</p>
        </div>
      </Popup>
      <ModalLight
        ref={changeRef}
        actions={[{
          children: '我再想想',
          key: 'cancel',
          color: 'default',
        }, {
          children: '我已确认',
          key: 'confirm',
          color: 'primary',
          onClick: doChangeMasterCard,
        }]}
      >
        <div className={styles.content}>
          <div className={styles.confirmTitle}>
            <IconInstitution type={current?.bankCode} className={styles.iconSmall} />
            <p className={styles.confirmName}>{current?.bankName}({current?.bankCardNo})</p>
          </div>
          <p className={styles.confirmContent}>确认将该卡设为主银行卡？主银行卡将优先用于还款</p>
        </div>
      </ModalLight>
      <ModalLight
        ref={unbindRef}
        actions={[{
          children: '我再想想',
          key: 'cancel',
          color: 'default',
        }, {
          children: '我已确认',
          key: 'confirm',
          color: 'primary',
          onClick: doUnbindCard,
        }]}
      >
        <div className={styles.content}>
          <div className={styles.confirmTitle}>
            <IconInstitution type={current?.bankCode} className={styles.iconSmall} />
            <p className={styles.confirmName}>{current?.bankName}({current?.bankCardNo})</p>
          </div>
          <p className={styles.confirmContent}>确认解除绑定该银行卡？</p>
        </div>
      </ModalLight>
      <ModalLight
        ref={unbindErrorRef}
        actions={[{
          children: '我知道了',
          key: 'cancel',
          color: 'default',
        }, {
          children: '添加新卡',
          key: 'confirm',
          color: 'primary',
          onClick: handleBindClick,
        }]}
      >
        <div className={styles.content}>
          <div className={styles.confirmTitle}>
            <p className={styles.confirmName}>无法解绑</p>
          </div>
          <p className={styles.confirmContent}>您当前仅绑定了一张银行卡无法解绑，添加新的银行卡后可解绑</p>
        </div>
      </ModalLight>
    </div>
  );
}
