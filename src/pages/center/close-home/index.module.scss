.closeCard {
  padding: 24rpx 32rpx 186rpx!important;
  .topBg {
    border-radius: 16rpx;
    padding: 24rpx;
    background: #fff;
  }
  .cardTitle {
    font-weight: 500;
    font-size: 32rpx;
    color: #333;
  }
  .list {
    margin-top: 52rpx;
    display: flex;
  }
  .listTitle {
    font-size: 28rpx;
    color: #333;
    flex: 1;
  }
  .listIcon {
    width: 44rpx;
    font-size: 28rpx;
    color: #333;
  }

  .textArea {
    margin-top: 26rpx;
  }

  .bottomWrap {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 186rpx;
    background: #fff;
  }
  .bottomInner {
    margin: 24rpx 20rpx;
  }
}


.backButtonWrap {
  margin-top: 50rpx;
  text-align: center;
}

.resultWaiting {
  background: none;
  :global(.adm-result-icon .antd-mobile-icon) {
    color: #1677ff;
  }
}

.resultPage {
  padding-top: 20vh!important;
  background-color: #fff;
}

.resultSuccess {
  background: none;
}

.button {
  width: 388rpx;
}

.resultImg {
  width: 280rpx;
  height: 200rpx;
}