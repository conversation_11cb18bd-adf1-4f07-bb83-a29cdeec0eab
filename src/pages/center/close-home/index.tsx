import { useEffect, useRef, useState } from 'react';
import { Button, Radio, TextArea } from 'antd-mobile';
import useCloseModel, { CloseReasonEnum } from './model';
import { isEmpty } from 'lodash-es';
import Dialog from '@ali/dialog';
import { definePageConfig } from 'ice';
import { PAGES } from '@/common/constant';
import { log } from '@alife/dtao-iec-spm-log';
import { CommonResult } from '@/components';
import { ActionDescriptor } from '@ali/dialog/esm/modules/Typings';
import { resetToPage } from '@/utils/link';
import { urlQuery } from '@/utils';

import styles from './index.module.scss';

export const pageConfig = definePageConfig(() => ({
  title: '关闭服务',
  spm: {
    spmB: PAGES.CenterClose,
  },
}));

export function CloseDoingPage() {
  const handleClick = () => {
    resetToPage(PAGES.Home);
  };

  return (
    <div className={styles.resultPage}>
      <CommonResult
        status="PROCESSING"
        title="服务关闭处理中"
        description="如需借钱，可再次申请"
      />
      {urlQuery('from') !== 'home' ? (
        <div className={styles.backButtonWrap}>
          <Button
            color="primary"
            fill="solid"
            size="large"
            className={styles.button}
            onClick={handleClick}
          >
            我知道了
          </Button>
        </div>
      ) : null}
    </div>
  );
}

export function CloseDonePage() {
  const handleClick = () => {
    resetToPage('index');
  };

  return (
    <div className={styles.resultPage}>
      <CommonResult
        customImg={
          <img
            className={styles.resultImg}
            src="https://gw.alicdn.com/imgextra/i3/O1CN011kSUVM1Zot3XVmqpN_!!6000000003242-2-tps-560-400.png"
          />
        }
        title="服务已关闭"
        description="如需借钱，可再次申请"
      />
      <div className={styles.backButtonWrap}>
        <Button
          color="primary"
          fill="solid"
          size="large"
          className={styles.button}
          onClick={handleClick}
        >
          我知道了
        </Button>
      </div>
    </div>
  );
}

export default function CloseHomePage() {
  const { submit, reasons, pageStatus } = useCloseModel();
  const [showInputArea, setShowInputArea] = useState(false);
  const [reasonCode, setReasonCode] = useState<CloseReasonEnum>(CloseReasonEnum.none);
  const reasonText = useRef<string>('');
  const [canSubmit, setCanSubmit] = useState(false);

  useEffect(() => {
    log.addVisitLog(PAGES.CenterClose);
  }, []);

  const checkCanSubmit = (code) => {
    if (code === CloseReasonEnum.other) {
      if (isEmpty(reasonText.current)) {
        setCanSubmit(false);
      } else {
        setCanSubmit(true);
      }
    } else {
      setCanSubmit(true);
    }
  };

  const handleReasonSelect = (code: CloseReasonEnum) => {
    setReasonCode(code);
    if (code === CloseReasonEnum.other) {
      // 显示输入框
      setShowInputArea(true);
    } else {
      setShowInputArea(false);
      reasonText.current = '';
    }
    checkCanSubmit(code);
  };

  const handleTextAreaChange = (val: string) => {
    reasonText.current = val;
    checkCanSubmit(reasonCode);
  };

  const handleSubmit = () => {
    log.addSubmitLog('close-yun-submit');
    if (!canSubmit) {
      return;
    }
    Dialog.show({
      contentStyle: {
        width: '560rpx',
      },
      title: '确认关闭服务吗',
      content:
        '关闭后将无法借钱。若再次申请，将为你重新评估，可能无法开通借钱服务，或额度利率发生变化。',
      closeOnAction: true,
      actions: [
        [
          {
            key: 'confirm',
            text: '确认',
          },
          {
            key: 'cancel',
            text: '我再想想',
            bold: true,
          },
        ],
      ],
      async onAction(action: ActionDescriptor) {
        if (action.key === 'confirm') {
          log.addClickLog('close-yun-submit-confirm');
          await submit(reasonCode, reasonText.current);
        } else if (action.key === 'cancel') {
          log.addClickLog('close-yun-submit-cancel');
        }
      },
    });
  };

  if (pageStatus === 'SUCCEEDED') {
    return <CloseDonePage />;
  }

  if (pageStatus === 'INIT' || pageStatus === 'PROCESSING') {
    return <CloseDoingPage />;
  }

  if (!reasons?.length) {
    return null;
  }

  return (
    <div className={styles.closeCard}>
      <div className={styles.topBg}>
        <div className={styles.cardTitle}>为什么想要关闭借款服务呢？</div>
        <Radio.Group value={reasonCode} onChange={handleReasonSelect}>
          {reasons.map((item) => {
            return (
              <div
                className={styles.list}
                key={item.desc}
                onClick={() => handleReasonSelect(item.code)}
              >
                <div className={styles.listTitle}>{item.desc}</div>
                <div className={styles.listIcon}>
                  <Radio value={item.code} />
                </div>
              </div>
            );
          })}
        </Radio.Group>
        {showInputArea && (
          <div className={styles.textArea}>
            <TextArea onChange={handleTextAreaChange} defaultValue="" showCount maxLength={50} />
          </div>
        )}
      </div>

      <div className={styles.bottomWrap}>
        <div className={styles.bottomInner}>
          <Button
            color="primary"
            fill="solid"
            size="large"
            disabled={!canSubmit}
            block
            onClick={handleSubmit}
          >
            确认关闭
          </Button>
        </div>
      </div>
    </div>
  );
}
