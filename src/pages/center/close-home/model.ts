import { useState, useRef, useEffect, useCallback } from 'react';
import { Toast, Dialog, Modal } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';

import { closeOrder, closeOrderQuery, authenticationCreate } from './service';
import {
  requestId,
  getErrorCodeFromRejectRes,
  _,
  getQueryParams,
  getMsgByErrorCode,
  getTimeStamp,
} from '@/utils';
import { queryCreditContract, consultReason } from '@/store/center/actions';
import { query as AuthQuery } from '@/store/authentication/actions';
import { useLoading } from '@/components/Loading';
import LinkUtil from '@/utils/link';
import { PAGES, TENANT } from '@/common/constant';
import { useVisibilityChangeRefresh } from '@/hooks';
import { useDebounceFn } from 'ahooks';


export type PAGE_STATUS = 'SUCCEEDED' | 'FAILED' | 'PROCESSING' | 'INIT';

export enum CloseReasonEnum {
  none = 'none',
  interest = 'HIGH_RATE',
  amount = 'LOW_QUOTA',
  zhengxin = 'CREDIT',
  promo = 'MARKETING',
  security = 'SECURITY',
  usage = 'DEMAND',
  other = 'OTHER',
}

const REASONS = [
  {
    desc: '利息太贵，不划算',
    code: CloseReasonEnum.interest,
    index: Math.random() * 100,
  },
  {
    desc: '额度太低，不够用',
    code: CloseReasonEnum.amount,
    index: Math.random() * 100,
  },
  {
    desc: '担心征信受到影响',
    code: CloseReasonEnum.zhengxin,
    index: Math.random() * 100,
  },
  {
    desc: '当前不需要借钱',
    code: CloseReasonEnum.usage,
    index: Math.random() * 100,
  },
  {
    desc: '担心安全问题',
    code: CloseReasonEnum.security,
    index: Math.random() * 100,
  },
  {
    desc: '营销打扰太多了',
    code: CloseReasonEnum.promo,
    index: Math.random() * 100,
  },
  {
    desc: '其他原因',
    code: CloseReasonEnum.other,
    index: 0,
  },
];

export default function useCloseModel() {
  const [pageStatus, setPageStatus] = useState<PAGE_STATUS>();
  const [reasons, setReasons] = useState<any>([]);
  const curCreditOrder = useRef<any>();
  const { showLoading, hideLoading } = useLoading();
  const closeApplyType = _.get(getQueryParams(), 'quitType') || 'MANUAL';
  const closeRef = useRef<any>({
    taskToken: '',
    closeParams: {},
  });

  const handleFailed = () => {
    Dialog.alert({
      title: '关闭失败，请重试',
      confirmText: '我知道了',
    });
  };

  const loopCloseOrderQuery = async (firstTime?) => {
    const { creditContractId } = curCreditOrder.current || {};
    if (!creditContractId) return;
    try {
      const rst = await closeOrderQuery({
        product: 'XFD',
        creditContractId,
      });
      // 4.0 处理各种状态
      const { status } = rst as any;
      if (status === 'SUCCEEDED') {
        // 关闭成功
        log.addSuccessLog('close-ssd-submit');
        hideLoading();
      } else if (status === 'FAILED') {
        // fail
        log.addErrorLog('close-ssd-submit');
        !firstTime && handleFailed(); // 不是初次进来才弹窗失败，刚进页面不弹
        hideLoading();
      } else if (status === 'PROCESSING' || status === 'INIT') {
        log.addOtherLog('close-ssd-submit-doing');
        if (status === 'PROCESSING') {
          hideLoading();
        }
        setTimeout(() => {
          loopCloseOrderQuery();
        }, 2000);
      }
      setPageStatus(status);
      return {
        status,
      };
    } catch (e) {
      Modal.alert({
        title: '服务器开小差',
        content: '请稍后重试',
      });
      log.addErrorLog('close-ssd-submit', { code: e.message });
    }
  };

  const doInit = async () => {
    try {
      curCreditOrder.current = await queryCreditContract({
        needQuerySurplusQuota: false,
      });
      await loopCloseOrderQuery(true);
      setReasons(
        REASONS.sort((a, b) => {
          return b.index - a.index;
        }),
      );
      hideLoading();
    } catch (e) {
      hideLoading();
      Dialog.show({
        content: '啊呀开了个小差，请重试',
        closeOnAction: true,
        actions: [
          [
            {
              key: 'cancel',
              onClick: LinkUtil.popPage,
              text: '返回',
            },
            {
              key: 'retry',
              onClick: LinkUtil.reload,
              bold: true,
              text: '点击重试',
            },
          ],
        ],
      });
    }
  };

  const handleAuthenticationSuccess = async (authToken?: string) => {
    try {
      showLoading('退出中...');
      const closeRes = await closeOrder({
        product: 'XFD',
        requestId: requestId(),
        applyType: closeApplyType,
        applyTime: getTimeStamp(),
        channel: 'APP',
        taskToken: authToken,
        ...closeRef.current.closeParams,
      });
      const rejectCode = getErrorCodeFromRejectRes(closeRes);
      if (rejectCode) {
        throw new Error(rejectCode);
      }

      loopCloseOrderQuery();
    } catch (e) {
      hideLoading();
      Dialog.alert({
        content: getMsgByErrorCode(e.message),
        onConfirm: () => {
          LinkUtil.popPage();
        },
      });
    }
  };

  const handleValuesChangeFn = useCallback(async () => {
    try {
      log.addSuccessLog('close-home-re-visible-new');
      const taskToken = closeRef?.current?.taskToken;
      if (taskToken) {
        const queryRes = await AuthQuery({
          tenant: TENANT,
          authenticationToken: taskToken,
        });
        if (queryRes?.status === 'SUCCESS') {
          handleAuthenticationSuccess(taskToken);
        } else {
          handleAuthenticationFail();
        }
      } else {
        doInit();
      }
    } catch (e) {}
  }, []);


  const { run: handleCallback } = useDebounceFn(handleValuesChangeFn, {
    wait: 1000,
    leading: true,
    trailing: false,
  });

  const doClear = () => {
    closeRef.current = {};
  };

  const handleAuthenticationFail = () => {
    Toast.show({
      icon: 'fail',
      content: '核身失败，请重试',
    });
    doClear();
  };

  const submit = async (code: CloseReasonEnum, reasonText: string) => {
    if (code === CloseReasonEnum.none) {
      return;
    }

    let reason;
    if (code === CloseReasonEnum.other) {
      reason = reasonText;
    } else {
      const matchReason = reasons.find((item) => item.code === code);
      reason = matchReason?.desc;
    }

    try {
      const consultRes = await consultReason({
        applyType: closeApplyType,
      });
      if (!consultRes.admitted) {
        throw new Error(getErrorCodeFromRejectRes(consultRes));
      } else {
        closeRef.current.closeParams = {
          closedCode: code, // 退出code
          closedDesc: reason, // 退出原因
        };
        if (consultRes.needAuthentication) {
          const res: any = await authenticationCreate({
            product: 'XFD',
            bizId: requestId(), // 保证唯一
            requestId: requestId(), // 幂等号
            point: 'QUIT',
            origin: 'APP',
            authType: 'FACE',
            authScene: 'QUIT',
          });
          const authenticationToken = res?.taskToken;
          closeRef.current.taskToken = authenticationToken;
          LinkUtil.pushPage(PAGES.CenterProfileSign, { scene: 'CLOSE', authenticationToken });
        } else {
          handleAuthenticationSuccess();
        }
      }
    } catch (e) {
      Dialog.alert({
        content: getMsgByErrorCode(e.message),
      });
      log.addErrorLog('close-submit', { code: e.message });
    }
  };

  useEffect(() => {
    doInit();
  }, []);

  useVisibilityChangeRefresh(handleCallback);

  return {
    pageStatus,
    reasons,
    loopCloseOrderQuery,
    submit,
  };
}
