/**
 * @file 绑卡
 */

import { useCallback, useEffect, useState, useRef } from 'react';
import { definePageConfig } from 'ice';
import { Button, Form, Toast, Modal } from 'antd-mobile';
import { useDebounceFn } from 'ahooks';
import { mask } from '@ali/iec-dtao-utils';
import { filter, get, isEmpty } from 'lodash-es';
import { log } from '@alife/dtao-iec-spm-log';

import {
  Tips,
  BankCardInput,
  BankSuportList,
  BankInfoField,
  UnsignedAgreementsAliyun,
  UnsignedAgreementsAliyunRef,
  SmsCodeConfirm,
  SmsCodeConfirmRef,
  FixedBottom,
  FullLoading,
  PhoneNumberInput,
} from '@/components';
import { BindCardConsultResponse, consult, check, apply, confirm, queryBankCardInfo } from '@/store/bankcard/actions';
import { getTimeStamp, requestId, isMtopErrorCreditContractNotExist } from '@/utils';
import { PHONE_REGX, BANK_REGX, PAGES, TENANT, ErrorMessageMap, BIND_CARD_APPLY_ERROR_CODE, BIND_CARD_CONFIRM_ERROR_CODE } from '@/common/constant';
import { BankCardDTO } from '@/store/types';
import LinkUtil, { popPage } from '@/utils/link';

import styles from './index.module.scss';
import classNames from 'classnames';
import { bindCardPageLog } from '@/utils/goc';

export const pageConfig = definePageConfig(() => ({
  title: '添加银行卡',
  spm: {
    spmB: PAGES.BindCard,
  },
  window: {
    navBarImmersive: false,
  },
}));

interface BindCardForm {
  bankCardInfo: BankCardDTO;
  bankCardNo: string;
  phone: string;
  agreementCheck?: boolean;
}

interface ConfirmOption {
  smsCode: string;
}

type ProcessAction = 'FETCHING' | 'QUERYING' | 'SUBMITTING' | '';

export default function BindCardPage() {
  const [payLoad, setPayLoad] = useState<BindCardConsultResponse>();
  const [processAction, setProcessAction] = useState<ProcessAction>();
  const [form] = Form.useForm<BindCardForm>();
  const bankCardInfoValue = Form.useWatch('bankCardInfo', form);
  const bankCardNoValue = Form.useWatch('bankCardNo', form);
  const agreementRef = useRef<UnsignedAgreementsAliyunRef>();
  const smsCodeConfirmRef = useRef<SmsCodeConfirmRef>();
  const smsCodeProcessRef = useRef(false);
  const applyRef = useRef({
    bindCardOrderId: '',
  });

  const doInit = useCallback(async () => {
    try {
      setProcessAction('FETCHING');
      const res = await consult();
      if (res) {
        bindCardPageLog({
          success: true,
          message: 'bind-card-consult-fetch',
        });
        setProcessAction('');
        setPayLoad(res);
      } else {
        throw new Error();
      }
    } catch (e) {
      bindCardPageLog({
        success: false,
        message: 'bind-card-consult-fetch',
      });
      if (isMtopErrorCreditContractNotExist(e)) {
        LinkUtil.resetToPage(PAGES.CreditLp);
      } else {
        Modal.show({
          content: ErrorMessageMap.BUSY_DEFUALT,
          actions: [{
            key: 'retry',
            text: '我知道了',
            primary: true,
            onClick: popPage,
          }],
        });
      }

      setProcessAction('');
    }
  }, []);

  const resetAgreementCheck = useCallback(() => {
    form.setFieldValue('agreementCheck', false);
    log.addOtherLog('rest-agreement-check');
  }, [form]);

  const handleFail = useCallback(() => {
    log.addErrorLog('bind-card-ocr-fail');
    form.setFields([{
      name: 'bankCardNo',
      errors: ['银行卡识别失败'],
    }, {
      name: 'bankCardInfo',
      value: null,
    }]);
  }, [form]);

  const handleSuccess = useCallback(() => {
    form.setFields([{
      name: 'bankCardNo',
      errors: [],
    }]);
  }, [form]);

  const checkInSupportBankList = useCallback((list: any, bankCode: string) => {
    if (payLoad?.supportBankList) {
      const target = filter(list, {
        bankCode,
      });
      if (target?.length) {
        return true;
      }
    }
    return false;
  }, [payLoad?.supportBankList]);

  const doFetchBankCardInfo = useCallback(async (val: string) => {
    try {
      setProcessAction('QUERYING');
      const result = await queryBankCardInfo({
        tenant: TENANT,
        cardNo: val,
      });
      setProcessAction('');
      if (result && result?.bankEnname && result?.bankName) {
        if (
          payLoad?.supportBankList
          && checkInSupportBankList(payLoad?.supportBankList, result?.bankEnname)
          && result?.type === 'DC'
        ) {
          log.addSuccessLog('bind-card-query');
          handleSuccess();
          form.setFields([{
            name: 'bankCardInfo',
            errors: [],
            value: {
              bankCode: result.bankEnname,
              bankName: result.bankName,
            },
          }]);
        } else if (result?.type !== 'DC') {
          log.addErrorLog('bind-card-query-no-dc');
          form.setFields([{
            name: 'bankCardNo',
            errors: ['请绑定个人一类储蓄卡'],
          }, {
            name: 'bankCardInfo',
            value: null,
          }]);
        } else {
          log.addErrorLog('bind-card-query-no-support');
          form.setFields([{
            name: 'bankCardNo',
            errors: ['抱歉，暂不支持该银行卡'],
          }, {
            name: 'bankCardInfo',
            value: null,
          }]);
        }
      }
    } catch (e) {
      setProcessAction('');
      log.addErrorLog('bind-card-query-failed');
      form.setFields([{
        name: 'bankCardNo',
        errors: ['银行卡号识别失败'],
      }, {
        name: 'bankCardInfo',
        value: null,
      }, {
        name: 'phone',
        errors: [],
      }]);
    }
  }, [form, payLoad?.supportBankList, checkInSupportBankList, handleSuccess]);

  const handleValuesChangeFn = useCallback(async (fieldObj: any) => {
    if (fieldObj?.bankCardNo && BANK_REGX.test(fieldObj?.bankCardNo)) {
      doFetchBankCardInfo(fieldObj?.bankCardNo);
    }
  }, [doFetchBankCardInfo]);

  const { run: handleValuesChange } = useDebounceFn(handleValuesChangeFn, {
    wait: 1000,
    leading: false,
    trailing: true,
  });

  const handleDoConfirm = useCallback(async (option?: ConfirmOption) => {
    setProcessAction('FETCHING');
    log.addOtherLog('bind-card-confirm-start');
    try {
      const { bindCardOrderId } = applyRef.current;
      if (smsCodeProcessRef.current) {
        return;
      }
      if (!bindCardOrderId || !option?.smsCode) {
        throw new Error();
      }
      smsCodeProcessRef.current = true;
      const confirmRes = await confirm({
        smsCode: option.smsCode,
        bindCardOrderId,
        requestId: requestId(),
      });
      setProcessAction('');
      if (!confirmRes?.result) {
        const failedCode = confirmRes?.failedReasonVO?.failedCode || '';
        smsCodeProcessRef.current = false;
        Toast.show({
          content: get(
            BIND_CARD_CONFIRM_ERROR_CODE,
            failedCode,
            '短信验证失败，请重试',
          ),
        });
        return {
          success: false,
        };
      }
      if (confirmRes?.result === true) {
        smsCodeProcessRef.current = false;
        log.addSuccessLog('bind-card-confirm-new');
        Toast.show({
          content: '添加成功',
        });
        LinkUtil.popPage();
        resetAgreementCheck();
        return {
          success: true,
        };
      }
      throw new Error();
    } catch (e) {
      resetAgreementCheck();
      log.addErrorLog('bind-card-confirm');
      setProcessAction('');
      smsCodeProcessRef.current = false;
      Toast.show({
        // 需要改文案
        content: '短信验证失败，请重试',
      });
      return {
        success: false,
      };
    }
  }, [resetAgreementCheck]);

  const getFormData = useCallback(() => {
    const formData = form.getFieldsValue();
    return {
      agreementCenter: {
        borrowerBankCardId: formData?.bankCardNo,
        bankName: formData?.bankCardInfo?.bankName,
        bankCardNo: formData?.bankCardNo,
        phone: formData?.phone || '',
      },
      institution: {
        borrowerBankCardId: formData?.bankCardNo,
        bankName: formData?.bankCardInfo?.bankName,
        bankCardNo: formData?.bankCardNo,
        phone: formData?.phone || '',
      },
    };
  }, [form]);

  const handleDoSend = useCallback(async () => {
    setProcessAction('SUBMITTING');
    log.addClickLog('bind-card-send');
    log.addSuccessLog('bind-card-sms-send');
    const request = form.getFieldsValue();
    const applyTime = getTimeStamp();
    const { phone, bankCardInfo, bankCardNo } = request || {};
    const { bankCode, bankName } = bankCardInfo || {};
    if (bankCode && bankName && payLoad?.institution) {
      // 发送验证短信
      const applyRes = await apply({
        phone,
        bankCardNo,
        requestId: requestId(),
        bankCode,
        bankName,
        institution: payLoad?.institution,
        applyTime,
      });
      resetAgreementCheck();
      setProcessAction('');
      if (applyRes?.admitted === true && applyRes?.bindCardOrderId && applyRes?.status === 'APPLYING') {
        log.addSuccessLog('bind-card-send-sms');
        applyRef.current.bindCardOrderId = applyRes.bindCardOrderId;
        smsCodeConfirmRef.current?.exec();
        return {
          success: true,
          bindCardOrderId: applyRes.bindCardOrderId,
        };
      }
      Toast.show({
        content: get(
          BIND_CARD_APPLY_ERROR_CODE,
          applyRes?.rejectReason?.rejectCode,
          ErrorMessageMap.BUSY_DEFUALT,
        ),
      });
      return {
        success: false,
      };
    } else {
      resetAgreementCheck();
      setProcessAction('');
      log.addErrorLog('bind-card-send-sms');
      Toast.show({
        content: ErrorMessageMap.BUSY_DEFUALT,
      });
      return {
        success: false,
      };
    }
  }, [payLoad?.institution, form, resetAgreementCheck]);

  const handleDoSendSafe = useCallback(async () => {
    try {
      const sendRes = await handleDoSend();
      return sendRes;
    } catch (e) {
      resetAgreementCheck();
      setProcessAction('');
      log.addErrorLog('bind-card-send-sms');
      Toast.show({
        content: ErrorMessageMap.BUSY_DEFUALT,
      });
      return {
        success: false,
      };
    }
  }, [handleDoSend, resetAgreementCheck]);

  const handleSubmit = useCallback(async (data: BindCardForm) => {
    log.addSubmitLog('bind-card-apply');
    setProcessAction('SUBMITTING');
    try {
      const { phone, bankCardNo, bankCardInfo } = data;
      const { bankCode, bankName } = bankCardInfo;
      if (!bankCode || !bankName || !bankCardNo || !payLoad?.institution) {
        throw new Error('BANK_SUBMIT_INFO_ERROR');
      }
      // 先进行四要素验证
      const checkRes = await check({
        phone,
        bankCardNo,
        institution: payLoad?.institution,
      });
      setProcessAction('');
      if (checkRes?.admitted !== true) {
        const rejectCode = checkRes?.rejectReason?.rejectCode;
        log.addErrorLog('bind-card-check-failed', {
          rejectCode,
        });
        if (rejectCode === 'BANK_CARD_VERIFICATION_FAILED') {
          form.setFields([
            {
              name: 'bankCardNo',
              errors: [ErrorMessageMap.BANK_CARD_VERIFICATION_FAILED],
            },
            {
              name: 'phone',
              errors: [ErrorMessageMap.BANK_CARD_VERIFICATION_FAILED],
            },
          ]);
        } else {
          Toast.show({
            content: get(ErrorMessageMap, rejectCode, ErrorMessageMap.BUSY_DEFUALT),
          });
        }
        return;
      }
      if (checkRes?.admitted === true) {
        if (!data?.agreementCheck) {
          agreementRef.current?.show({
            action: 'compulsory',
            params: getFormData(),
          });
          return;
        }
        log.addSuccessLog('bind-card-force');
        log.addSuccessLog('bind-card-check');
        setProcessAction('SUBMITTING');
        return;
      }
      throw new Error('BANK_SMS_CODE_SEND_ERROR');
    } catch (e) {
      log.addErrorLog('bind-card-check-sms');
      resetAgreementCheck();
      setProcessAction('');
      Toast.show({
        content: ErrorMessageMap.BUSY_DEFUALT,
      });
    }
  }, [payLoad?.institution, getFormData, resetAgreementCheck, form]);

  const handleFinishFailed = useCallback((errorInfo: any) => {
    log.addErrorLog('bind-card-invalid', errorInfo);
  }, []);

  const handleAgreementError = () => {
    setProcessAction('');
  };

  const renderFooter = useCallback(() => {
    if (payLoad?.institution) {
      return (
        <FixedBottom className={styles.submitFooter} useResize>
          <>
            <Form.Item className={styles.agreement} name="agreementCheck">
              {bankCardInfoValue ? (
                <UnsignedAgreementsAliyun
                  bizType="BIND_CARD"
                  institutionList={[payLoad?.institution]}
                  ref={agreementRef}
                  prefix="查阅"
                  title="绑卡相关协议"
                  getParams={getFormData}
                  onCompleted={handleDoSendSafe}
                  onError={handleAgreementError}
                />
              ) : null}
            </Form.Item>
            <Button
              block
              type="submit"
              color="primary"
              disabled={!!processAction || isEmpty(bankCardInfoValue)}
              loading={processAction === 'SUBMITTING'}
              loadingText="添加中"
            >
              确认添加
            </Button>
          </>
        </FixedBottom>
      );
    }
    return null;
  }, [processAction, bankCardInfoValue, getFormData, handleDoSendSafe, payLoad?.institution]);

  const renderAppend = () => {
    return (
      <div className={classNames(styles.append, !bankCardInfoValue?.bankCode && styles.displayNo)}>
        <Form.Item
          label="开户银行"
          className={styles.formItem}
          name="bankCardInfo"
          rules={[{
            required: true,
            message: '请选择开户银行',
          }]}
        >
          <BankInfoField
            className="normal-input-field"
            options={payLoad?.supportBankList}
          />
        </Form.Item>
        <Form.Item
          label="银行预留手机号"
          name="phone"
          className={styles.formItem}
          rules={[{
            required: true,
            message: '请输入银行预留手机号',
          }, {
            pattern: PHONE_REGX,
            message: '请输入格式正确手机号',
          }]}
        >
          <PhoneNumberInput placeholder="请输入" logKey="bind-card-phone" />
        </Form.Item>
      </div>
    );
  };

  const renderTitle = useCallback(() => {
    const phone = form.getFieldValue('phone') || '--';
    return <p className={styles.confirmTitle}>请完成银行预留手机号<b>{mask.phone(phone)}</b>的验证</p>;
  }, [form]);

  const pageInit = async () => {
    doInit();
  };

  useEffect(() => {
    pageInit();
  }, []);

  useEffect(() => {
    if (!bankCardNoValue || !BANK_REGX.test(bankCardNoValue)) {
      form.setFields([{
        name: 'bankCardInfo',
        value: null,
      }, {
        name: 'phone',
        errors: [],
      }]);
    }
  }, [bankCardNoValue, form]);

  if (!payLoad || isEmpty(payLoad)) {
    return null;
  }

  return (
    <div className={styles.bindCardPage}>
      <FullLoading visible={processAction === 'FETCHING'} />
      <h5 className={styles.title}>
        请添加借款人本人一类储蓄卡
      </h5>
      <Tips
        type="safe"
        content="根据法律法规规定，我们将收集您的银行账户信息，提供给您后续申请授信/借款的金融机构，用于提供信贷服务"
      />
      <div className={styles.panel}>
        <div className={styles.item}>
          <label className={styles.label}>
            姓名
          </label>
          <span className={styles.value}>
            {payLoad?.name}
          </span>
        </div>
        <div className={styles.item}>
          <label className={styles.label}>
            身份证号
          </label>
          <span className={styles.value}>
            {payLoad?.licenseNo}
          </span>
        </div>
      </div>
      <Form
        className={styles.form}
        footer={renderFooter()}
        onFinish={handleSubmit}
        onFinishFailed={handleFinishFailed}
        onValuesChange={handleValuesChange}
        form={form}
      >
        <div className={styles.formItemC}>
          <BankSuportList
            className={styles.support}
            options={payLoad?.supportBankList}
          />
          <Form.Item
            label="银行卡号"
            name="bankCardNo"
            className={styles.formItem}
            rules={[{
              required: true,
              message: '请输入银行卡号',
            }, {
              pattern: BANK_REGX,
              message: '请输入格式正确的银行卡号',
            }]}
          >
            <BankCardInput
              onFail={handleFail}
              onSuccess={handleSuccess}
            />
          </Form.Item>
        </div>
        {renderAppend()}
      </Form>
      <SmsCodeConfirm
        ref={smsCodeConfirmRef}
        title={renderTitle}
        // @ts-ignore
        doSend={handleDoSendSafe}
        // @ts-ignore
        doConfirm={handleDoConfirm}
        disabled={processAction !== ''}
      />
    </div>
  );
}
