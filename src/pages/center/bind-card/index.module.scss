@import '../../../assets/styles/font.css';

.bindCardPage {
  :global(.adm-form .adm-form-footer) {
    padding: 0;
  }
  .title {
    font-size: 40rpx;
    font-weight: 500;
    padding-bottom: 16rpx;
    color: #111;
  }
  .panel {
    padding-top: 68rpx;
    .item {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      padding-bottom: 24rpx;
      &:last-child {
        padding-bottom: 0;
      }
      .label, .value {
        font-size: 26rpx;
        line-height: 39rpx;
        color: #7c889c;
        font-family: 'ALIBABA NUMBER FONT RG';
      }
    }
  }
  .form {
    padding-top: 24rpx;
    .formItem {
      padding-top: 40rpx;
    }
    .agreement {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding-bottom: 16rpx;
    }
  }
  .displayNo {
    display: none;
  }
}

.confirmTitle {
  font-size: 26rpx;
  line-height: 39rpx;
  text-align: center;
  color: #7c889c;
  padding-top: 32rpx;
  b {
    font-size: 30rpx;
    font-weight: 500;
    color: #111;
    padding: 0 12rpx;
    font-family: 'ALIBABA NUMBER FONT MD';
  }
}

.formItemC {
  position: relative;
  .support {
    position: absolute;
    right: 0;
    z-index: 999;
    top: 32rpx;
  }
}

.submitFooter {
  --fix-bottom-bg: #fff;
}
