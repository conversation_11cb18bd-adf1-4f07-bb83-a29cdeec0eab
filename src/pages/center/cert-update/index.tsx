import { useEffect, useRef, useState, useCallback } from 'react';
import { definePageConfig } from 'ice';
import { Button, Form, Toast, Modal } from 'antd-mobile';
import classnames from 'classnames';
import CommonPopup from '@/components/CommonPopup';
import UploadImage from '@/components/UploadImage';
import FullLoading from '@/components/FullLoading';
import {
  dayjsFormat,
  _,
  addAntdFormErrorLog,
  requestId,
  getQueryParams,
  getErrorCodeFromRejectRes,
  isMtopCodeTimeout,
} from '@/utils';
import { LicenseUpload, DetailItem } from '@/components';
import { useDebounceFn } from 'ahooks';
import { PROFILE_DATA_FIELD_MAP as FIELD_MAP } from '@/store/credit/model';
import { queryCustomerProfile, updateCustomerProfile } from '@/store/center/actions';
import {
  OcrFrontIdCardDTO,
  OcrBackIdCardDTO,
  processLicensePictureData,
} from '@/store/common/actions';
import { ErrorMessageMap, PAGES, CUSTOMER_UPDATE_PROFILE_ERROR_CODE } from '@/common/constant';
import LinkUtil from '@/utils/link';
import styles from './index.module.scss';
import { log } from '@alife/dtao-iec-spm-log';

interface OcrData {
  front: OcrFrontIdCardDTO;
  back: OcrBackIdCardDTO;
}

export default function CreditIdentity() {
  const commonPopupRef = useRef<any>(null);
  const [loadingVisible, setLoadingVisible] = useState(false);
  const [identityCardCustomerProfile, setIdentityCardCustomerProfile] = useState<any>({});
  const [idcardErrorStatus, setIdcardErrorStatus] = useState({
    front: '',
    back: '',
  });
  const [ocrData, setOcrData] = useState<OcrData>({
    front: {},
    back: {},
  });
  const [identityCardData, setIdentityCardData] = useState<any>({});
  const [form] = Form.useForm();
  const authToken = _.get(getQueryParams(), 'authToken');

  const handleShowCommonPopup = () => {
    log.addClickLog('idcard-not-around');
    commonPopupRef.current.toggleVisible(true);
  };

  const { run: handleSubmit } = useDebounceFn(
    async () => {
      log.addClickLog('cert-update-submit');
      // 先判断ocr是否成功
      if (!_.isEmpty(idcardErrorStatus.front) || !_.isEmpty(idcardErrorStatus.back)) {
        Toast.show(idcardErrorStatus.front) || _.isEmpty(idcardErrorStatus.back);
        return;
      }

      // 表单校验
      const values = await executeFormValidate();
      if (!values) return;

      setLoadingVisible(true);

      const licensePictureFront = _.get(values, FIELD_MAP.licensePictureFront);
      const licensePictureBack = _.get(values, FIELD_MAP.licensePictureBack);

      let licenseData;
      try {
        licenseData = await processLicensePictureData({
          licensePictureFront,
          licensePictureBack,
          ocrFront: ocrData.front,
          ocrBack: ocrData.back,
        });
      } catch (error) {
        if (error.message === 'FILE_CONFIRM_ERROR') {
          log.addErrorLog('idcard-image-confirm-error', { error });
          Modal.alert({
            title: '照片处理失败',
            content: ErrorMessageMap.BUSY_DEFUALT,
          });
          setLoadingVisible(false);
        }
      }

      // 字段处理
      try {
        const res = await updateCustomerProfile({
          requestId: requestId(),
          updateItem: {
            content: { ...licenseData },
            type: 'IDENTITY_CARD',
          },
          taskToken: authToken,
        });

        if (res.status === 'SUCCEEDED') {
          Toast.show('更新成功');
          setTimeout(() => {
            LinkUtil.popPage();
          }, 1000);
        } else if (res.status === 'PROCESSING') {
          Modal.alert({
            title: '更新中',
            content: '身份证更新中，请耐心等待',
            onConfirm: LinkUtil.popPage,
          });
        } else {
          throw new Error(getErrorCodeFromRejectRes(res));
        }
      } catch (e) {
        log.addLog('cert-profile-update-error', 'error', { error: e });
        if (isMtopCodeTimeout(_.get(e, 'errorList[0].code'))) {
          Modal.alert({
            title: '更新中',
            content: '身份证更新中，请耐心等待',
            onConfirm: LinkUtil.popPage,
          });
        } else {
          Modal.alert({
            title: '更新失败',
            content:
              _.get(CUSTOMER_UPDATE_PROFILE_ERROR_CODE, e.message) || ErrorMessageMap.BUSY_DEFUALT,
            onConfirm: LinkUtil.popPage,
          });
        }
      } finally {
        setLoadingVisible(false);
      }
    },
    {
      wait: 500,
      leading: true,
      trailing: false,
    },
  );

  const executeFormValidate = async (name?) => {
    let validateResult;
    try {
      validateResult = await form.validateFields(name ? [name] : undefined);
      return validateResult;
    } catch (error) {
      addAntdFormErrorLog(error.errorFields);
    }
  };

  const handleOcrSuccess = (ocrResult, type) => {
    setOcrData((prev) => {
      return {
        ...prev,
        [type]: ocrResult,
      };
    });

    changeIdCardErrorStatus(type, null);
  };

  const handleOcrFailed = async (e, type) => {
    log.addLog('ocr-failed', 'error', { type, e });
    if (e?.code === 'UNDER_18') {
      changeIdCardErrorStatus(type, e?.msg);
      Toast.show(e?.msg);
    } else if (e?.code === 'SAME_PERSON') {
      changeIdCardErrorStatus(type, e?.msg);
      Toast.show('您上传的身份证已有申请记录');
    } else if (e?.code === 'EXPIRE') {
      changeIdCardErrorStatus(type, e?.msg);
      Toast.show(e?.msg);
    } else {
      changeIdCardErrorStatus(type, e?.msg);
    }

    setOcrData((prev) => {
      return {
        ...prev,
        [type]: null,
      };
    });
  };

  const changeIdCardErrorStatus = (type, errorMsg) => {
    setIdcardErrorStatus((prev) => {
      return {
        ...prev,
        [type]: errorMsg,
      };
    });
  };

  const idcardUploadItemValitor = useCallback(
    ({ type, value }) => {
      if (_.isEmpty(value)) {
        changeIdCardErrorStatus(type, '请上传身份证');
        return Promise.reject(new Error('请上传身份证'));
      }

      changeIdCardErrorStatus(type, null);

      return Promise.resolve();
    },
    [ocrData],
  );

  const UploadFormItemComponentRender = useCallback(
    ({ field, type }) => {
      return (
        <Form.Item
          noStyle
          name={field}
          rules={[
            {
              validator: (rules, value) => idcardUploadItemValitor({ type, value }),
            },
          ]}
        >
          <UploadImage
            className={styles.idcardUploadItem}
            ocrConfig={{
              ocrType: type,
              preLicenseCheck: true,
            }}
            emptyRender={LicenseUpload.emptyRender}
            uploadedImgRender={LicenseUpload.uploadedImgRender}
            onOcrSuccess={(res) => handleOcrSuccess(res, type)}
            onOcrFailed={(e) => handleOcrFailed(e, type)}
          />
        </Form.Item>
      );
    },
    [ocrData.front, ocrData.back],
  );

  useEffect(() => {
    if (!_.isEmpty(ocrData.front)) {
      executeFormValidate([FIELD_MAP.licensePictureFront]);
    }
  }, [ocrData.front]);

  useEffect(() => {
    if (!_.isEmpty(ocrData.back)) {
      executeFormValidate([FIELD_MAP.licensePictureBack]);
    }
  }, [ocrData.back]);

  useEffect(() => {
    queryCustomerProfile({
      typeList: JSON.stringify(['IDENTITY_CARD']),
    }).then((res) => {
      const identityData = _.find(res.result, (item) => item.type === 'IDENTITY_CARD');

      setIdentityCardCustomerProfile(identityData?.content || {});
      setIdentityCardData({
        ...(identityData?.content || {}),
      });
    });
    log.addVisitLog(PAGES.CreditIdentity);
  }, []);

  const licenseName = _.get(ocrData, 'front.name') || identityCardData.name;
  const licenseNo = _.get(ocrData, 'front.num') || identityCardData.licenseNo;
  const startDate = _.get(ocrData, 'back.startDate') || identityCardData.startDate;
  const endDate = _.get(ocrData, 'back.endDate') || identityCardData.endDate;

  return (
    <div className={styles.creditIdentity}>
      <FullLoading visible={loadingVisible} />
      {/* <div className={classnames([styles.creditIdentityTitle1, 'page-title-1'])}>
        请完善个人信息
      </div> */}
      {/* <div className={styles.creditIdentityTips}>
        <img
          className={styles.creditIdentityTipsIcon}
          src="https://gw.alicdn.com/imgextra/i2/O1CN01C5ECWo1kem43yU528_!!6000000004709-2-tps-28-36.png"
        />
        <div className={styles.creditIdentityTipsText}>
          根据法律法规规定，我们将收集您的银行账户信息，提供给您后续申请授信/借款的金融机构，用于提供信贷服务
        </div>
      </div> */}
      <div className={classnames([styles.creditIdentityTitle2, 'page-title-2'])}>
        <span className="with-asterisk">请上传身份证</span>
      </div>
      <div className={styles.creditIdentityIdcardTip}>
        请确保身份证照片<span>清晰完整</span>，属于<span>{identityCardCustomerProfile.name}</span>
        本人
      </div>
      <Form form={form}>
        <div className={styles.creditIdentityIdcardUpload}>
          <div className={styles.idcardUploadWrap}>
            {UploadFormItemComponentRender({
              field: FIELD_MAP.licensePictureFront,
              type: 'front',
            })}
            {UploadFormItemComponentRender({
              field: FIELD_MAP.licensePictureBack,
              type: 'back',
            })}
          </div>
          <div className={styles.idcardErrorMessage}>
            {_.get(idcardErrorStatus, 'front') || _.get(idcardErrorStatus, 'back')}
          </div>
          <div className={styles.idcardInfo}>
            {licenseName && <DetailItem label="姓名" value={licenseName} />}
            {licenseNo && <DetailItem label="身份证号" value={licenseNo} />}
            {startDate && (
              <DetailItem
                label="有效期"
                value={`${dayjsFormat(startDate)}至${
                  endDate === '长期' ? '长期' : dayjsFormat(endDate)
                }`}
              />
            )}
          </div>
          <div className={styles.idcardBottomTip}>
            <a onClick={handleShowCommonPopup}>身份证不在身边？</a>
          </div>
        </div>
      </Form>
      <div className="page-bottom-bar">
        <Button
          disabled={_.isEmpty(ocrData.front) || _.isEmpty(ocrData.front)}
          onClick={handleSubmit}
          block
          color="primary"
        >
          提交
        </Button>
      </div>
      <CommonPopup forceRender ref={commonPopupRef} title="身份证照片下载路径">
        <img src="https://gw.alicdn.com/imgextra/i3/O1CN01d0vfqE1fQy3nF4iOi_!!6000000004002-2-tps-1500-2272.png" />
      </CommonPopup>
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '更新身份证',
  spm: {
    spmB: PAGES.CenterCertUpdate,
  },
  window: {
    navBarImmersive: false,
  },
}));
