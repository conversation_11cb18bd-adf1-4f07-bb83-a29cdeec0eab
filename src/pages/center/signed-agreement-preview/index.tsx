/**
 * @file pdf
 */

import { useCallback, useEffect, useState } from 'react';
import { definePageConfig } from 'ice';
import { url } from '@ali/iec-dtao-utils';
import { log } from '@alife/dtao-iec-spm-log';
import { first, get } from 'lodash-es';
import { Button, DotLoading } from 'antd-mobile';

import { popPage } from '@/utils/link';
import { preview } from '@/store/agreement/actions';

import styles from './index.module.scss';
import { PAGES } from '@/common/constant';

export const pageConfig = definePageConfig(() => ({
  spm: {
    spmB: PAGES.SignedAgreementPreview,
  },
  title: '协议内容',
  window: {
    navBarImmersive: false,
  },
}));

export default function SignedAgreementPreview() {
  const [empty, setEmpty] = useState<boolean>();
  const [content, setContent] = useState<any>();

  const getPreview = useCallback(async (code: string) => {
    try {
      const res = await preview({
        code,
        source: 'AGREEMENT_CENTER',
        product: 'XFD',
        bizType: 'PLATFORM',
      });
      const previewContent = get(first(res?.previewAgreementList), 'content');
      if (previewContent) {
        log.addSuccessLog('platform-signed-agreements-pdf-success');
        setContent(previewContent);
      } else {
        throw new Error();
      }
    } catch (e) {
      log.addErrorLog('platform-signed-agreements-pdf-error');
      setEmpty(true);
    }
  }, []);

  useEffect(() => {
    log.addVisitLog('signed-agreement-detail');
    const code = url.query('code');
    getPreview(code);
  }, []);

  if (empty) {
    return (
      <div className={styles.empty}>
        <img src="https://gw.alicdn.com/imgextra/i1/O1CN01EBNrhL20DmaYk0Sxe_!!6000000006816-2-tps-560-400.png" />
        <p className={styles.title}>
          抱歉，协议暂无法预览，请稍后再试
        </p>
        <Button onClick={popPage} className={styles.back} color="primary">
          我知道了
        </Button>
      </div>
    );
  }

  if (!content) {
    return (
      <div className={styles.loading}>
        <DotLoading />
      </div>
    );
  }

  return (
    <div className={styles.agreement}>
      <div
        className={styles.content}
        dangerouslySetInnerHTML={{ __html: content }}
      />
    </div>
  );
}
