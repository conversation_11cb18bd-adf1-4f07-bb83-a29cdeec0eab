import { useEffect, useState } from 'react';
import { definePageConfig } from 'ice';
import styles from './index.module.scss';
import CouponItem from '@/components/CouponItem';
import { InfiniteScroll } from 'antd-mobile';
import { CommonResult } from '@/components';
import { PAGES } from '@/common/constant';
import { log } from '@alife/dtao-iec-spm-log';
import FullLoading from '@/components/FullLoading';
import { useListModal } from '@/hooks/useListModal';
import LinkUtil from '@/utils/link';
import { queryCouponList } from '@/store/center/actions';

export default function Coupon() {
  const [listHasMore, setListHasMore] = useState<boolean>(false);
  const { doFetch, doInit, renderList, isFetching } = useListModal({
    fetch: queryCouponList,
    renderItem: (coupon) => {
      return <CouponItem scene="yun" key={coupon?.code} data={coupon} />;
    },
    renderEmpty: () => {
      return (
        <CommonResult
          title="暂无优惠券"
          customImg={
            <img
              className={styles.customImg}
              src="https://gw.alicdn.com/imgextra/i3/O1CN01M9orSz22jVIG1Qj2g_!!6000000007156-2-tps-510-282.png"
            />
          }
        />
      );
    },
    listKey: 'dataList',
    totalKey: 'totalRecord',
    pageParam: {
      pageStart: 1,
      pageSize: 10,
      status: 'EFFECTIVE',
    },
  });

  const loadMore = async () => {
    const { hasMore } = await doFetch();
    setListHasMore(hasMore);
  };
  const handleInvalidCouponClick = () => {
    log.addClickLog('center-invalid-coupon-click');
    LinkUtil.pushPage(PAGES.CenterInvalidCoupon);
  };

  useEffect(() => {
    doInit().then(({ hasMore }) => {
      setListHasMore(hasMore);
    });
  }, []);
  useEffect(() => {
    log.addVisitLog(PAGES.CenterDiscountCoupon);
  }, []);

  return (
    <div className={styles.couponListWrap}>
      <FullLoading visible={isFetching} />
      <div>{renderList()}</div>
      <InfiniteScroll loadMore={loadMore} hasMore={listHasMore}>
        <>{null}</>
      </InfiniteScroll>
      <div className={styles.bottomInvalid}>
        <span onClick={handleInvalidCouponClick} className={styles.invalidCoupon}>
          已失效的券
          <img
            className={styles.knowArrowIcon}
            src="https://gw.alicdn.com/imgextra/i3/O1CN01ZaDvpb1e3K0uiI7Q1_!!6000000003815-2-tps-48-48.png"
          />
        </span>
      </div>
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '优惠券',
  spm: {
    spmB: PAGES.CenterDiscountCoupon,
  },
  window: {
    navBarImmersive: false,
    navBarBgColor: '#f3f6f8',
    pageBgColor: '#f3f6f8',
  },
}));
