import { Fragment, useEffect, useState, useRef, useCallback } from 'react';
import { definePageConfig } from 'ice';
import { Button, NoticeBar, Modal } from 'antd-mobile';
import styles from './index.module.scss';
import {
  dayjsFormat,
  amountFormat,
  numberFormat,
  isGreaterThan0,
  isEqual0,
  isGreaterThan,
  _,
  getQueryParams,
} from '@/utils';
import { useCreditRouter, useVisibilityChangeRefresh } from '@/hooks';
import classnames from 'classnames';
import ArrowIcon from '@/components/ArrowIcon';
import RateExplanationPopup from '@/components/RateExplanationPopup';
import RedPacketOffer from '@/components/Yun/RedPacketOffer';
import CouponPopover from '@/components/Yun/CouponPopover';
import BottomBarWrap from '@/components/BottomBar';
import {
  queryLoanSchemaInfo,
  queryAnnouncement,
  LoanSchemaInfoRes,
  announcementItem,
} from '@/store/center/actions';
import { contractAsync } from '@/store/loan/actions';
import { PAGES, INSTITUTION_NAME_MAP_FOR_HOME } from '@/common/constant';
import LinkUtil from '@/utils/link';
import { log } from '@alife/dtao-iec-spm-log';
import { homeAliyunPageLog } from '@/utils/goc';

const YunRedirectPageMap = {
  recordlist: PAGES.RecordList,
  loanapply: PAGES.LoanApply,
  repaybill: PAGES.RepayBill,
  coupon: PAGES.CenterDiscountCoupon,
};


const Home = (props) => {
  const { creditConsultData, routerPage, queryConsult } = useCreditRouter();
  const [schemaData, setSchemaData] = useState<LoanSchemaInfoRes | null>(null);
  const [announcementList, setAnnouncementList] = useState<announcementItem[]>([]);
  const { onShowBottomBar, customStyle: homeStyle, onUpdateLoanSchema } = props;
  const rateDeclarePopupRef = useRef<any>(null);

  const pageInit = async () => {
    try {
      const shemaRes = await queryLoanSchemaInfo();
      homeAliyunPageLog({
        shemaRes,
      });
      if (shemaRes.creditClosing) {
        LinkUtil.replacePage(PAGES.CenterClose, { from: 'home' });
        return;
      }

      log.setGlobalExtra({ institution: shemaRes?.institution });

      setSchemaData(shemaRes);
    } catch (e) {
      homeAliyunPageLog({
        message: 'home-query-loan-schema-info',
      });
      Modal.alert({
        title: '服务器开小差',
        content: '请稍后重试',
      });
    }
    try {
      const announcementRes = await queryAnnouncement();
      setAnnouncementList(announcementRes.announcementList);
    } catch (e) {}
  };

  const visibilityChangeRefreshFn = () => {
    pageInit();
    queryConsult();
  };

  useVisibilityChangeRefresh(visibilityChangeRefreshFn);

  const doContractAsync = async () => {
    await contractAsync();
  };

  useEffect(() => {
    // 处理到了home页去指定页面，就是url上的to参数
    const toParam = _.get(getQueryParams(), 'to');
    const targetPage = _.get(YunRedirectPageMap, `${toParam}`);

    onShowBottomBar(true);
    pageInit();
    doContractAsync();

    if (targetPage) {
      LinkUtil.pushPage(targetPage);
    }
  }, []);

  useEffect(() => {
    onUpdateLoanSchema(schemaData);
  }, [schemaData]);

  const renderNoticeBar = useCallback(() => {
    const firstAnnouncementItem: announcementItem = _.get(announcementList, '0');
    if (!firstAnnouncementItem) return null;

    const handleNoticeClick = () => {
      if (!firstAnnouncementItem.url) return;
      if (firstAnnouncementItem.url === 'LICENSE_MANAGEMENT') {
        LinkUtil.pushPage(PAGES.CenterCertManage);
      } else {
        LinkUtil.navigatorOpenURL(firstAnnouncementItem.url);
      }
    };
    return (
      <NoticeBar
        content={
          <div onClick={handleNoticeClick} className={styles.noticeContent}>
            {firstAnnouncementItem.content}
            {firstAnnouncementItem.actionText && (
              <a className={styles.noticeAction}>{firstAnnouncementItem?.actionText}</a>
            )}
          </div>
        }
        color="alert"
      />
    );
  }, [announcementList]);

  if (creditConsultData && routerPage !== PAGES.Home) {
    LinkUtil.locationReplace(routerPage);
    return;
  }

  if (!schemaData) return null;

  const handleMainBtnClick = () => {
    log.addClickLog('home-main-btn-click');
    LinkUtil.pushPage(PAGES.LoanApply);
  };

  const handleRepayInstalmentPanelClick = () => {
    log.addClickLog('home-repay-instalment-panel-click');
    LinkUtil.pushPage(PAGES.RepayBill);
  };

  const handleRateQuestionClick = () => {
    log.addClickLog('home-rate-question-click');
    rateDeclarePopupRef.current.toggleVisible(true);
  };

  const genMainContent = ({
    institutionSystemShutDown,
    surplusBillStatus,
    surplusTotalAmount,
    surplusQuotaStatus,
    interestRate,
    minLoanAmount,
    installmentEndDate,
    quota,
  }) => {
    const isDue = surplusBillStatus === 'DUE';
    const isOverDueOnly = surplusBillStatus === 'OVERDUE_ONLY';
    const isOverdueAndDue = surplusBillStatus === 'OVERDUE_AND_DUE';
    // const isSettled = surplusBillStatus === 'SETTLED';
    const isQuotaFrozen = surplusQuotaStatus === 'FROZEN';

    const isSurplusTotalAmountGreaterThan0 = isGreaterThan0(surplusTotalAmount);
    // 展示额度卡片： 有余额就展示

    // 展示查账还款的卡片：两种情况， 1、有余额且不是逾期, 2、到期但是已结清
    let showRepayInstalmentPanel = false;

    const renderInterestRate = () => {
      const {
        interestRatePercent,
        dailyInterestPerThousand,
        tempInterestRatePercent,
        tempDailyInterestPerThousand,
      } = interestRate;
      // 判断是否展示优惠
      const hasDiscount =
        tempInterestRatePercent &&
        isGreaterThan0(tempInterestRatePercent) &&
        interestRatePercent !== tempInterestRatePercent;
      const showInterestPerThousand = hasDiscount
        ? tempDailyInterestPerThousand
        : dailyInterestPerThousand;
      return (
        <Fragment>
          <div className={styles.cardYearRate}>
            {/* {hasDiscount && <span className="common-tag primary">优惠</span>} */}
            年利率(单利)
            {hasDiscount ? (
              <span className={styles.discountRateWrap}>
                {amountFormat(tempInterestRatePercent)}%
                <span className={styles.baseRate}>{amountFormat(interestRatePercent)}%</span>
              </span>
            ) : (
              `${amountFormat(interestRatePercent)}%`
            )}
            <img
              className={styles.rateQuestionIcon}
              onClick={handleRateQuestionClick}
              src="https://gw.alicdn.com/imgextra/i2/O1CN01ADiSPK28mbco0tpfO_!!6000000007975-2-tps-48-48.png"
            />
          </div>
          <div className={styles.cardDailyInterest}>
            借1千元用一天利息约{amountFormat(showInterestPerThousand)}元
          </div>
        </Fragment>
      );
    };

    const renderRepayInstalmentPanel = () => {
      let panelTitle;
      let panelAmount;
      if (isOverDueOnly || isOverdueAndDue) {
        panelTitle = '逾期应还(元)';
        panelAmount = <span className={styles.redText}>{amountFormat(surplusTotalAmount)}</span>;
      } else if (isDue) {
        panelTitle = '今日应还(元)';
        panelAmount = isSurplusTotalAmountGreaterThan0 ? (
          `¥${amountFormat(surplusTotalAmount)}`
        ) : (
          <span className={styles.clearText}>本期已结清</span>
        );
      } else {
        panelTitle = `${dayjsFormat(installmentEndDate, 'MM月DD日')}应还 (元)`;
        panelAmount = `¥${amountFormat(surplusTotalAmount)}`;
      }

      return (
        <div
          onClick={handleRepayInstalmentPanelClick}
          className={classnames([styles.repayInstalmentWrap, 'common-card'])}
        >
          <div className={styles.repayInstalmentTitle}>查账还款</div>
          <div className={styles.repayInstalmentDateTitle}>
            {panelTitle}
            <ArrowIcon />
          </div>
          <div className={styles.repayInstalmentAmountValue}>{panelAmount}</div>
        </div>
      );
    };

    // 是否停机维护
    if (institutionSystemShutDown) {
      if (isGreaterThan0(surplusTotalAmount)) {
        showRepayInstalmentPanel = true;
      }
      return {
        mainTitle: '当前可借(元)',
        mainValue: (
          <span className={styles.mainValueDisabled}>
            {isEqual0(quota.surplusQuota) ? (
              <span className={styles.noQuotaText}>额度已用完</span>
            ) : (
              numberFormat(quota.surplusQuota)
            )}
          </span>
        ),
        mainTips: null,
        mainActions: (
          <Button disabled block color="primary">
            暂无法借钱
          </Button>
        ),
        showRepayInstalmentPanel,
        renderRepayInstalmentPanel,
      };
    }

    // 额度冻结
    if (isQuotaFrozen) {
      if (isGreaterThan0(surplusTotalAmount)) {
        showRepayInstalmentPanel = true;
      }
      return {
        mainTitle: '当前可借(元)',
        mainValue: <span className={classnames([styles.mainValueDisabled])}>额度冻结</span>,
        mainTips: null,
        mainActions: (
          <Button disabled block color="primary">
            暂无法借钱
          </Button>
        ),
        showRepayInstalmentPanel,
        renderRepayInstalmentPanel,
      };
    }

    // 逾期时， 非冻结和非停机
    if (isOverDueOnly || isOverdueAndDue) {
      showRepayInstalmentPanel = false;
      return {
        mainTitle: '逾期应还(元)',
        mainValue: (
          <span className={classnames([styles.redText])}>{numberFormat(surplusTotalAmount)}</span>
        ),
        mainTips: <span className={styles.redText}>逾期将影响信用，请尽快还清</span>,
        mainActions: (
          <Fragment>
            <Button onClick={() => LinkUtil.pushPage(PAGES.RepayBill)} block color="primary">
              去还款
            </Button>
            <div
              onClick={() => LinkUtil.pushPage(PAGES.LoanContract)}
              className={styles.loanDetailBtn}
            >
              借款详情
            </div>
          </Fragment>
        ),
        showRepayInstalmentPanel,
        renderRepayInstalmentPanel,
      };
    }

    // 额度用完的情况，非冻结和非停机非逾期
    if (!isGreaterThan0(quota.surplusQuota)) {
      showRepayInstalmentPanel = true;
      return {
        mainTitle: '当前可借(元)',
        mainValue: isEqual0(quota.surplusQuota) ? (
          <span className={styles.noQuotaText}>额度已用完</span>
        ) : (
          amountFormat(quota.surplusQuota)
        ),
        mainTips: renderInterestRate(),
        mainActions: (
          <Button disabled block color="primary">
            暂无法借钱
          </Button>
        ),
        showRepayInstalmentPanel,
        renderRepayInstalmentPanel,
      };
    }

    // 这里只剩下到期或者是未到期的情况
    showRepayInstalmentPanel = isDue || isGreaterThan0(surplusTotalAmount);

    return {
      mainTitle: '当前可借(元)',
      mainValue: <span>{numberFormat(quota.surplusQuota)}</span>,
      mainTips: renderInterestRate(),
      mainActions: isGreaterThan(minLoanAmount, quota.surplusQuota) ? ( // 判断一下最小支用金额大于剩余可用额度
        <Fragment>
          <div className={styles.loanDetailBtn}>小于最小借款金额</div>
          <Button disabled block color="primary">
            暂无法借钱
          </Button>
        </Fragment>
      ) : (
        <CouponPopover offerList={schemaData?.institutionPromotionOfferList}>
          <Button onClick={handleMainBtnClick} block color="primary">
            去借钱
          </Button>
        </CouponPopover>
      ),
      showRepayInstalmentPanel,
      renderRepayInstalmentPanel,
    };
  };


  const {
    mainTitle,
    mainValue,
    mainTips,
    mainActions,
    showRepayInstalmentPanel,
    renderRepayInstalmentPanel,
  } = genMainContent({
    ...schemaData,
  });

  return (
    <div style={homeStyle} className={classnames([styles.homeWrap])}>
      {renderNoticeBar()}
      <div className={classnames([styles.homeContainer])}>
        <div className={styles.mainCard}>
          <div className={styles.institutionInfo}>
            <div className={styles.institutionDesc}>
              贷款由
              <span className={styles.institutionName}>
                {INSTITUTION_NAME_MAP_FOR_HOME[schemaData.institution]}
              </span>
              提供
            </div>
          </div>
          <Fragment>
            <div className={styles.mainTitle}>{mainTitle}</div>
            <div className={styles.mainValue}>{mainValue}</div>
            <div className={styles.mainTips}>{mainTips}</div>
            {!_.isEmpty(schemaData?.platformPromotionOfferList) && <RedPacketOffer offerList={schemaData?.platformPromotionOfferList} />}
            <div className={styles.mainActions}>{mainActions}</div>
          </Fragment>
        </div>

        {showRepayInstalmentPanel && renderRepayInstalmentPanel()}

        <div className={styles.bottomTipText}>
          <img src="https://gw.alicdn.com/imgextra/i2/O1CN010xuP9G1LtYSib7oE3_!!6000000001357-2-tps-48-48.png" />
          谨防电信诈骗，警惕陌生来电
        </div>
      </div>
      <RateExplanationPopup interestRate={schemaData.interestRate} ref={rateDeclarePopupRef} />
    </div>
  );
};

export default () => <BottomBarWrap HomeComp={Home} />;

export const pageConfig = definePageConfig(() => ({
  title: '',
  spm: {
    spmB: PAGES.Home,
  },
}));
