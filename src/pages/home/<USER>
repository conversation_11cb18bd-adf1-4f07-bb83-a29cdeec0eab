.homeWrap {
  padding: 0 !important;
  .homeContainer {
    padding: 32rpx 16rpx 210rpx;
  }
  .mainCard {
    background: #fff;
    padding: 80rpx 48rpx 100rpx;
    text-align: center;
    border-radius: 24rpx;
    margin-bottom: 24rpx;
    .institutionInfo {
      background: linear-gradient(
        90deg,
        rgba(0, 94, 255, 0) 0%,
        rgba(0, 94, 255, 0.08) 53%,
        rgba(0, 94, 255, 0) 100%
      );
      border-radius: 50rpx;
      text-align: center;
      padding: 12rpx 0;
      margin-bottom: 50rpx;
      .institutionName {
        color: #111;
        font-size: 24rpx;
        margin: 0 4rpx;
      }
      .institutionDesc {
        font-size: 24rpx;
        color: #7c889c;
      }
    }
    .mainTitle {
      color: #000;
      font-size: 32rpx;
      margin-bottom: 24rpx;
    }
    .rateQuestionIcon {
      margin-left: 8rpx;
      width: 24rpx;
      height: 24rpx;
    }
    .baseRate {
      color: #7c889c;
      font-size: 28rpx;
      font-family: "ALIBABA NUMBER FONT RG";
      margin-left: 12rpx;
      text-decoration: line-through;
    }
    .mainValue {
      font-family: "ALIBABA NUMBER FONT MD";
      font-size: 108rpx;
      margin-bottom: 16rpx;
      line-height: 144rpx;
      // height: 84rpx;
      font-weight: 500;
      .mainValueDisabled {
        color: #cacfd7;
      }
      .noQuotaText {
        font-size: 60rpx;
      }
    }
    .loanDetailBtn {
      color: var(--primary);
      font-weight: 500;
      margin-top: 16rpx;
    }
    .discountRateWrap {
      display: inline-flex;
      align-items: center;
    }
    .cardYearRate {
      margin-bottom: 8rpx;
      font-size: 26rpx;
      color: #000;
      display: inline-flex;
      align-items: center;
    }
    .cardDailyInterest {
      margin-bottom: 16rpx;
      font-size: 26rpx;
      color: #999;
    }
    .cardPromotion {
      margin: 60rpx 0;
      background-image: url("https://gw.alicdn.com/imgextra/i1/O1CN01IbYdE31LR9taHkcLh_!!6000000001295-2-tps-590-160.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      height: 160rpx;
      display: flex;
      .cardPromotionLeft {
        flex: 0 0 180rpx;
        display: flex;
        align-items: center;
        align-items: flex-end;
        justify-content: center;
        font-weight: 500;
        color: #ff6a5b;
        font-size: 28rpx;
        padding-bottom: 52rpx;
        .promotionValue {
          font-size: 48rpx;
          line-height: 48rpx;
          margin-right: 6rpx;
        }
      }
      .cardPromotionRight {
        display: flex;
        flex-direction: column;
        padding: 44rpx 24rpx;
        font-size: 26rpx;
        text-align: left;
        .promotionName {
          font-weight: 500;
          margin-bottom: 8rpx;
        }
        .promotionDesc {
          color: #7c889c;
        }
      }
    }
    .interestDes {
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 28rpx;
        height: 28rpx;
        margin-right: 12rpx;
      }
      font-size: 24rpx;
      color: #7c889c;
    }
    .mainActions {
      margin-top: 100rpx;
      > *:not(:last-child) {
        margin-bottom: 16rpx;
      }
    }
    .cardAgreements {
      margin-top: 16rpx;
      font-size: 20rpx;
      text-align: left;
      color: #7c889c;
    }
  }
  .repayInstalmentWrap {
    position: relative;
    z-index: 9;
    .repayInstalmentTitle {
      font-size: 30rpx;
      font-weight: 600;
      margin-bottom: 12rpx;
    }
    .repayInstalmentDateTitle {
      font-size: 26rpx;
      color: #111;
      margin-bottom: 12rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .repayInstalmentAmountValue {
      font-family: "ALIBABA NUMBER FONT MD";
      font-size: 42rpx;
      font-weight: 500;
    }
    .repayInstalmentTip {
      color: #7c889c;
    }
  }
  .myQuotaWrap {
    .myQuotaTitle {
      font-size: 30rpx;
      color: #111;
      margin-bottom: 12rpx;
      font-weight: 600;
    }
    .myQuotaTitle2 {
      font-size: 26rpx;
      color: #111;
      margin-bottom: 12rpx;
      display: flex;
      justify-content: space-between;
    }
    .myQuotaValue {
      font-size: 42rpx;
      font-weight: 500;
      font-family: "ALIBABA NUMBER FONT MD";
    }
  }
}

.redText {
  color: #f00;
}

.clearText {
  font-size: 26rpx;
  font-weight: 600;
  line-height: 26rpx;
  letter-spacing: 0;
  color: #111;
}

.announcementItem {
  margin-right: 50rpx;
}

.noticeContent {
  font-size: 26rpx;
  color: #ff8000;
}

.noticeAction {
  margin-left: 12rpx;
  color: #4066ff;
}




.bottomTipText {
  position: fixed;
  z-index: 5;
  bottom: 165rpx;
  left: 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #7c889c;
  img {
    width: 24rpx;
    height: 24rpx;
    margin-right: 8rpx;
  }
}

.redPacketSendingPopupContent {
  font-size: 28rpx;
  color: #50607a;
  text-align: center;
  line-height: 45rpx;
}