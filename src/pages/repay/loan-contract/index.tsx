import { useState, useEffect, Fragment } from 'react';
import { definePageConfig } from 'ice';
import FullLoading from '@/components/FullLoading';
import { Button, NoticeBar, Modal } from 'antd-mobile';
import { dayjsYYMD, amountFormat } from '@/utils';
import styles from './index.module.scss';
import classnames from 'classnames';
import { queryLoanContractSummary, queryLoanContractList } from '@/store/loan/actions';
import { queryRepayPreAdmit } from '@/store/repay/actions';
import { PAGES, REPAYMENT_METHOD } from '@/common/constant';
import { genRepayRejectNoticeMsg } from '@/store/repay/helper';
import { useVisibilityChangeRefresh } from '@/hooks';
import LinkUtil from '@/utils/link';
import { log } from '@alife/dtao-iec-spm-log';
import { loanContractPageLog } from '@/utils/goc';

export default function LoanContract() {
  const [loanContractSummary, setLoanContractSummary] = useState<any>(null);
  const [loanContractListData, setLoanContractListData] = useState<any>(null);
  const [repayAdmit, setRepayAdmit] = useState<any>({});
  const [noticeMsg, setNoticeMsg] = useState<any>(null);

  const pageInit = async () => {
    try {
      queryLoanContractSummary({
        statusQueryType: 'NOT_FINISHED',
      }).then((res) => {
        setLoanContractSummary(res);
      });
      queryLoanContractList({
        statusQueryType: 'NOT_FINISHED',
        orderByType: 'ORDER_BY_DESC',
        orderTypeKey: 'LOAN_START_DATE',
        pageStart: 1,
        pageSize: 50,
      }).then((res) => {
        setLoanContractListData(res);
      });

      const admitRes = await queryRepayPreAdmit();
      loanContractPageLog({
        success: true,
        creditPlatform: 'ALIYUN',
      });
      setRepayAdmit(admitRes);
      if (admitRes.admitted === false) {
        setNoticeMsg(genRepayRejectNoticeMsg(admitRes));
      }
    } catch (error) {
      loanContractPageLog({
        success: false,
        message: error?.message,
        creditPlatform: 'ALIYUN',
      });
      Modal.alert({
        title: '服务器开小差',
        content: '请稍后重试',
      });
    }
  };

  useVisibilityChangeRefresh(pageInit);

  useEffect(() => {
    log.addVisitLog(PAGES.LoanContract);
    pageInit();
  }, []);

  if (!loanContractSummary || !loanContractListData) {
    return <FullLoading visible />;
  }

  return (
    <div className={classnames([styles.loanContract])}>
      {noticeMsg && <NoticeBar content={noticeMsg} color="alert" />}

      <div className={classnames([styles.loanContractContainer])}>
        <div className={classnames([styles.surplusPrincipal, 'common-card'])}>
          <div>剩余未还本金 (元)</div>
          <div className={styles.surplusPrincipalAmount}>
            {amountFormat(loanContractSummary.surplusPrincipal)}
          </div>
        </div>
        <div className={styles.loanContractCount}>
          共含
          <span>{loanContractListData?.totalRecord}笔</span>借款
        </div>
        <Fragment>
          {loanContractListData.dataList.map((item) => {
            const isOverdue = item.status === 'OVERDUE';
            return (
              <div
                key={item.loanOrderId}
                className={classnames([styles.contractItem, 'common-card'])}
              >
                <div>
                  <span className={styles.contractItemDateAndAmount}>
                    {dayjsYYMD(item.loanStartDate)}借{amountFormat(item.loanedAmount)}元
                    <span className="common-divide-line-vertical" />
                    {REPAYMENT_METHOD[item.repaymentMethod]}
                    {isOverdue && <div className={classnames(['common-tag red'])}>逾期</div>}
                  </span>
                </div>
                <div className={styles.contractItemSurplusPrincipalTitle}>
                  未还本金
                  <span className={styles.contractItemTerm}>
                    (剩余{item.surplusInstallmentCount}期)
                  </span>
                </div>
                <div className={styles.contractItemSurplusPrincipalAmount}>
                  ¥{amountFormat(item.surplusPrincipal)}
                </div>
                <div className="common-divide-line" />
                <div className={styles.contractItemBtns}>
                  <Button
                    onClick={() => {
                      log.addClickLog('loan-detail');
                      LinkUtil.pushPage(PAGES.LoanDetail, { loanOrderId: item.loanOrderId });
                    }}
                    className={classnames(['cancel-button', styles.contractItemBtn])}
                  >
                    借款详情
                  </Button>
                  <Button
                    onClick={() => {
                      log.addClickLog('repay-early-apply');
                      LinkUtil.pushPage(PAGES.RepayEarlyApply, {
                        loanOrderId: item.loanOrderId,
                        loanContractId: item.loanContractId,
                      });
                    }}
                    className={styles.contractItemBtn}
                    color="default"
                    disabled={!repayAdmit.admitted}
                  >
                    去还款
                  </Button>
                </div>
              </div>
            );
          })}
        </Fragment>
      </div>
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '使用中的借款',
  spm: {
    spmB: PAGES.LoanContract,
  },
}));
