.repayApply {
  padding-top: 16rpx!important;
  background: linear-gradient(180deg, #fff 0%, #f3f6f8 100%);
  .loanContractInfoSurplus {
    font-size: 26rpx;
    margin: 12rpx 0;
  }
  .loanContractInfoAmount {
    color: #111;
    font-family: "ALIBABA NUMBER FONT MD";
    font-size: 36rpx;
    font-weight: 500;
    margin-right: 8rpx;
    line-height: 36rpx;
  }
  .dynamicTrialResult {
    margin-top: 24rpx;
  }
  .settleRepayBtn {
    display: inline-block;
    color: var(--primary);
    font-size: 26rpx;
    align-self: center;
    white-space: nowrap;
    min-width: 100rpx;
    text-align: right;
  }
  .repayApplyTitle {
    font-size: 24rpx;
    color: #111;
    text-align: center;
    margin-bottom: 24rpx;
  }
  .repayApplyAmount {
    font-size: 60rpx;
    font-weight: 500;
    font-family: "ALIBABA NUMBER FONT MD";
    text-align: center;
    margin-bottom: 24rpx;
  }
  .infoItem {
    padding: 16rpx 0;
    display: flex;
    justify-content: space-between;
    line-height: 1.5;
    .infoItemLabel {
      font-size: 26rpx;
    }
    .infoItemValue {
      font-size: 26rpx;
      color: #7c889c;
      font-family: "ALIBABA NUMBER FONT RG";
      display: flex;
      align-items: center;
    }
    .infoItemArrow {
      position: relative;
      top: 2rpx;
    }
  }
}
