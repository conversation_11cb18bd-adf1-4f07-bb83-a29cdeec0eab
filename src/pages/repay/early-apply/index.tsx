import { definePageConfig } from 'ice';
import { useState, useEffect, Fragment, useCallback, useRef } from 'react';
import { Button, Modal, Form, Toast } from 'antd-mobile';
import styles from './index.module.scss';
import classnames from 'classnames';
import {
  dayjsYYMD,
  amountFormat,
  getQueryParams,
  _,
  isGreaterThan0,
  isGreaterThan,
  idempotentId,
  getMsgByErrorCode,
  getErrorCodeFromRejectRes,
  getTimeStamp,
  isMtopErrorCreditContractNotExist,
} from '@/utils';
import {
  Authentication,
  FullLoading,
  BankInfoTip,
  CommonPopup,
  BankCardList,
  ArrowIcon,
  NewAmountInput,
  DetailItemList,
} from '@/components';
import {
  repayTrial,
  repayConsult,
  repayApply,
  RepayConsultRes,
  RepayTrialDTO,
  queryRepayOrder,
} from '@/store/repay/actions';
import { useDebounceFn } from 'ahooks';
import { BankCardDTO } from '@/store/types';
import { useVisibilityChangeRefresh } from '@/hooks';
import { log } from '@alife/dtao-iec-spm-log';
import { PAGES } from '@/common/constant';
import LinkUtil from '@/utils/link';


export default function RepayApply() {
  const [initialTrialResult, setInitialTrialResult] = useState<RepayTrialDTO | any>({});
  const [dynamicTrialResult, setDynamicTrialResult] = useState<RepayTrialDTO | any>({});
  const [repayConsultData, setRepayConsultData] = useState<RepayConsultRes | any>(null);
  const [repayBankCard, setRepayBankCard] = useState<BankCardDTO>({});
  const loanOrderId = _.get(getQueryParams(), 'loanOrderId');
  const defaultAmount = _.get(getQueryParams(), 'defaultAmount');
  const [applyResult, setApplyResult] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [trailing, setTrailing] = useState(false);
  const [form] = Form.useForm();
  const repayOrderId = applyResult?.repayOrderId;
  const latestTrialResult = useRef<any>(null);
  const repayAccountCommonPopupRef = useRef<any>(null);

  const handleRepayAccountClick = () => {
    repayAccountCommonPopupRef.current?.toggleVisible(true);
  };

  const { run: handleSubmit } = useDebounceFn(
    async () => {
      log.addClickLog('submit-click');
      if (_.isEmpty(dynamicTrialResult)) return;
      setLoading(true);
      repayApply({
        institution: repayConsultData?.institution,
        channel: 'APP',
        applyTime: Date.now(),
        repayBindCardNo: repayBankCard?.bindCardNo,
        requestId: idempotentId(),
        loanOrderId,
        repayTrialResult: dynamicTrialResult,
      })
        .then((res: any) => {
          if (res.repayOrderId && res.authenticationToken) {
            setApplyResult(res);
          } else {
            throw new Error(getErrorCodeFromRejectRes(res));
          }
        })
        .catch((e) => {
          log.addErrorLog('repay-apply-error', { code: e.message });
          Modal.alert({
            title: '还款失败',
            content: getMsgByErrorCode(e.message),
          });
        })
        .finally(() => {
          setLoading(false);
        });
    },
    {
      wait: 800,
      leading: true,
      trailing: false,
    },
  );

  const clearSimulateResult = () => {
    // 执行一下试算 拦掉之前的执行
    setTrailing(false);
    latestTrialResult.current = null;
    setDynamicTrialResult(null);
  };

  const handleBankCardChange = (item) => {
    setRepayBankCard(item);
    repayAccountCommonPopupRef.current?.toggleVisible(false);
  };

  const handleTotalRepayBtnClick = () => {
    if (_.isEmpty(initialTrialResult)) return;

    form.setFieldsValue({
      applyAmount: initialTrialResult.trialTotalAmount,
    });
    const key = getTimeStamp();
    latestTrialResult.current = key;
    doRepayTrial(initialTrialResult.trialTotalAmount, key);
  };

  const { run: debounceRepayTrial } = useDebounceFn(
    useCallback(
      (applyAmount, trialKey?) => {
        repayTrial({
          loanOrderId,
          institution: repayConsultData.institution,
          applyAmount,
          repayPhase: 'UNDUE',
          settleRepayTrialResult: initialTrialResult,
        })
          .then((res) => {
            if (!res.admitted) {
              throw new Error(getErrorCodeFromRejectRes(res));
            }
            if (!latestTrialResult.current) return;
            if (trialKey === latestTrialResult.current) {
              setTrailing(false);
              setDynamicTrialResult(res);
            }
          })
          .catch((e) => {
            log.addErrorLog('repay-trial-error', { code: e.message });
            if (trialKey === latestTrialResult.current) {
              setTrailing(false);
              setDynamicTrialResult(null);
              Modal.alert({
                title: '还款计算失败',
                content: getMsgByErrorCode(e.message),
              });
            }
          });
      },
      [repayConsultData],
    ),
    {
      wait: 300,
      leading: true,
      trailing: true,
    },
  );

  const doRepayTrial = useCallback(
    (applyAmount, trialKey?) => {
      if (!applyAmount) return;
      setDynamicTrialResult(null);
      setTrailing(true);
      debounceRepayTrial(applyAmount, trialKey);
    },
    [repayConsultData],
  );

  const checkAmountValidator = (rules, value) => {
    if (!value) {
      clearSimulateResult();
      return Promise.reject();
    }

    // 是否大于最小还款金额
    if (
      initialTrialResult.minApplyAmount &&
      isGreaterThan(initialTrialResult.minApplyAmount, value)
    ) {
      clearSimulateResult();
      return Promise.reject(new Error(`单笔最小需还${initialTrialResult.minApplyAmount}元`));
    }

    const key = getTimeStamp();
    latestTrialResult.current = key;
    doRepayTrial(String(Number(value)), key);

    return Promise.resolve();
  };

  const renderTrialResult = useCallback(
    (trialResult) => {
      const repayBankCardCompCfg = {
        customRender: () => {
          if (_.isEmpty(repayBankCard)) return null;
          return (
            <div onClick={handleRepayAccountClick} className={styles.infoItem}>
              <div className={styles.infoItemLabel}>还款账户</div>
              <div className={styles.infoItemValue}>
                <BankInfoTip
                  cardNo={repayBankCard.bankCardNo}
                  code={repayBankCard.bankCode}
                  name={repayBankCard.bankName}
                />
                <ArrowIcon className={styles.infoItemArrow} />
              </div>
            </div>
          );
        },
      };
      // 不是全额还，切如果最近一次没有触发试算 || 没有在试算中且没有试算结果，这种情况一般是试算异常
      if (!repayConsultData.needSettleRepay && (!latestTrialResult.current || (!trailing && !trialResult))) {
        return repayBankCard ? (
          <div className={styles.dynamicTrialResult}>
            <DetailItemList data={[repayBankCardCompCfg]} />
          </div>
        ) : null;
      }

      return (
        <div className={styles.dynamicTrialResult}>
          <DetailItemList
            data={_.filter(
              [
                {
                  label: '本金',
                  value: trailing ? '计算中...' : `¥${trialResult?.trialPrincipal}`,
                },
                {
                  label: '利息',
                  value: trailing ? '计算中...' : `¥${trialResult?.trialInterest}`,
                },
                isGreaterThan0(trialResult?.trialPenalty) && {
                  label: '罚息',
                  value: `¥${trialResult?.trialPenalty}`,
                },
                repayBankCard && repayBankCardCompCfg,
              ],
              (item) => !!item,
            )}
          />
        </div>
      );
    },
    [initialTrialResult, dynamicTrialResult, repayBankCard, latestTrialResult.current, trailing],
  );

  const pageInit = async () => {
    try {
      const res = await repayConsult({
        loanOrderId,
      });
      if (!res.admitted) {
        throw new Error(getErrorCodeFromRejectRes(res));
      } else {
        const bankCard =
          _.find(res?.repayBankCardList, (item) => item.masterCard) ||
          _.first(res?.repayBankCardList) ||
          {};
        if (!bankCard) {
          throw new Error('REPAY_BANK_CARD_EMPTY');
        }
        setRepayConsultData(res);
        setInitialTrialResult(res.repayTrialResult);

        // 需要全部还款
        if (res.needSettleRepay) {
          setDynamicTrialResult(res.repayTrialResult);
        }
        setRepayBankCard(bankCard);
      }
    } catch (e) {
      if (isMtopErrorCreditContractNotExist(e)) {
        LinkUtil.resetToPage(PAGES.CreditLp);
      } else {
        Modal.alert({
          title: '暂无法还款',
          content: getMsgByErrorCode(e.message),
          onConfirm: () => {
            LinkUtil.popPage();
          },
        });
      }
    } finally {
      setLoading(false);
    }
  };

  useVisibilityChangeRefresh(pageInit);

  useEffect(() => {
    log.addVisitLog(PAGES.RepayEarlyApply);
    pageInit();
  }, []);

  const queryOrder = useCallback(async () => {
    const res = await queryRepayOrder({
      repayOrderId,
    });
    if (res.status !== 'AUTHENTICATING') {
      LinkUtil.locationReplace(PAGES.RepayResult, {
        repayOrderId,
      });
    } else {
      setTimeout(queryOrder, 2000);
    }
  }, [repayOrderId]);

  const handleSuccess = () => {
    log.addSuccessLog('repay-sign-auth-new');
    setLoading(true);
    Toast.show({
      icon: 'success',
      content: '核身成功',
    });
    queryOrder();
  };

  useEffect(() => {
    if (defaultAmount) {
      form.setFieldValue('applyAmount', defaultAmount);
      form.validateFields();
    }
  }, [initialTrialResult]);

  if (!repayConsultData) {
    return <FullLoading visible={loading} />;
  }

  const { repayLoanOrder, needSettleRepay } = repayConsultData || {};

  return (
    <div className={styles.repayApply}>
      <FullLoading visible={loading} />
      {!_.isEmpty(repayConsultData) && (
        <div className={classnames([styles.loanContractInfo, 'common-card-2'])}>
          <div className="alibaba-font-rg color-text-1">
            {dayjsYYMD(repayLoanOrder.loanedTime)}借{amountFormat(repayLoanOrder.loanedAmount)}元
          </div>
          <div className={styles.loanContractInfoSurplus}>未还总额</div>
          <div className="flex-align-center">
            <span className={styles.loanContractInfoAmount}>
              ¥{amountFormat(initialTrialResult.trialTotalAmount)}
            </span>
            <span className="alibaba-font-rg color-text-1">
              含本金{amountFormat(initialTrialResult.trialPrincipal)}元，利息
              {amountFormat(initialTrialResult.trialInterest)}元
              {isGreaterThan0(initialTrialResult.trialPenalty)
                ? `，罚息${amountFormat(initialTrialResult.trialPenalty)}元`
                : ''}
            </span>
          </div>
        </div>
      )}
      {needSettleRepay ? (
        <Fragment>
          <div className={styles.repayApplyTitle}>应还金额 (元)</div>
          <div className={styles.repayApplyAmount}>
            {amountFormat(initialTrialResult.trialTotalAmount)}
          </div>
        </Fragment>
      ) : (
        <Form form={form}>
          <Form.Item
            rules={[
              {
                validator: checkAmountValidator,
              },
            ]}
            name="applyAmount"
          >
            <NewAmountInput
              renderAppend={() => (
                <span onClick={handleTotalRepayBtnClick} className={styles.settleRepayBtn}>
                  全额还
                </span>
              )}
              autoFocus
              disabled={_.isEmpty(initialTrialResult)}
              max={initialTrialResult.trialTotalAmount}
            />
          </Form.Item>
        </Form>
      )}

      {renderTrialResult(dynamicTrialResult)}
      <div className="page-bottom-bar">
        <Button
          disabled={
            _.isEmpty(initialTrialResult) ||
            _.isEmpty(dynamicTrialResult) ||
            _.isEmpty(repayBankCard) ||
            trailing ||
            loading
          }
          onClick={handleSubmit}
          block
          color="primary"
        >
          确认还款
        </Button>
      </div>
      <Authentication
        directly
        authenticationToken={_.get(applyResult, 'authenticationToken')}
        onSuccess={handleSuccess}
        onFail={() => {
          setLoading(false);
        }}
      />
      <CommonPopup ref={repayAccountCommonPopupRef} title="还款账户">
        <BankCardList
          onChange={handleBankCardChange}
          value={repayBankCard}
          options={repayConsultData?.repayBankCardList}
        />
      </CommonPopup>
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '还款',
  spm: {
    spmB: PAGES.RepayEarlyApply,
  },
  window: {
    navBarImmersive: false,
  },
}));
