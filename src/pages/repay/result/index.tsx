import { useState, useEffect, useRef } from 'react';
import { definePageConfig } from 'ice';
import CommonResult from '@/components/CommonResult';
import { Button, Space, Toast, Modal } from 'antd-mobile';
import classnames from 'classnames';
import styles from './index.module.scss';
import { queryRepayOrder, RepayOrderRes } from '@/store/repay/actions';
import { DetailItem } from '@/components/DetailItemList';
import {
  getQueryParams,
  amountFormat,
  _,
  isGreaterThan0,
  getErrorCodeFromRejectRes,
  getFailedCodeFromOrder,
  getFailedMsg,
} from '@/utils';
import { PAGES } from '@/common/constant';
import LinkUtil from '@/utils/link';
import { log } from '@alife/dtao-iec-spm-log';

let pageVisitTime = 0;

export default function RepayResult() {
  const [repayOrderData, setRepayOrderData] = useState<RepayOrderRes | null>(null);
  const _timer = useRef<any>();
  const urlRepayOrderId = _.get(getQueryParams(), 'repayOrderId');

  const handleRepayAgain = () => {
    // 判断是否带支用单号
    if (repayOrderData?.loanOrderId) {
      LinkUtil.replacePage(PAGES.RepayEarlyApply, {
        loanOrderId: repayOrderData.loanOrderId,
        defaultAmount: repayOrderData.surplusTotalAmount,
      });
    } else {
      LinkUtil.replacePage(PAGES.RepayBatchApply);
    }
  };

  const queryFn = async (repayOrderId) => {
    try {
      const res = await queryRepayOrder({
        repayOrderId,
      });
      if (!_.includes(['SUCCEEDED', 'INSTITUTION_REPAY_FAIL'], res.status)) {
        log.addOtherLog('final-repay-result', {
          status: res.status,
        });
        _timer.current = setTimeout(() => {
          queryFn(repayOrderId);
        }, 3000);
      } else {
        log.addOtherLog('final-repay-result', {
          status: res.status,
          code: getErrorCodeFromRejectRes(res),
          waitingTime: Date.now() - pageVisitTime,
        });
      }

      setRepayOrderData(res);
    } catch (error) {
      Modal.alert({
        title: '还款单查询失败',
        onConfirm: () => {
          LinkUtil.popPage();
        },
      });
    }
  };

  const pageInit = async () => {
    log.addVisitLog(PAGES.RepayResult);
    pageVisitTime = Date.now();
    if (urlRepayOrderId) {
      queryFn(urlRepayOrderId);
    } else {
      Toast.show('还款单号为空');
    }
  };

  useEffect(() => {
    pageInit();
  }, []);

  if (!repayOrderData?.status) return null;

  const { repaidPrincipal, repaidInterest, repaidPenalty, surplusTotalAmount } = repayOrderData;

  if (repayOrderData?.status === 'SUCCEEDED') {
    return (
      <div className={styles.repayResult}>
        <CommonResult
          status="SUCCESS"
          title={`成功还款${amountFormat(repayOrderData.repaidTotalAmount)}元`}
        />
        <div className="flex-center">
          <div className={styles.resultSucceeded}>
            <DetailItem label="本金" value={`¥${repaidPrincipal}`} />
            <DetailItem label="利息" value={`¥${repaidInterest}`} />
            {isGreaterThan0(repaidPenalty) && (
              <DetailItem label="罚息" value={`¥${repaidPenalty}`} />
            )}
            {isGreaterThan0(surplusTotalAmount) ? (
              <div className={classnames([styles.twiceRepayment, 'common-card-2'])}>
                <div className={styles.twiceRepaymentLeft}>
                  <div className={styles.twiceRepaymentLeftTitle}>抱歉本次部分还款未成功</div>
                  <div>剩余{surplusTotalAmount}元待还</div>
                </div>
                <div className={styles.twiceRepaymentRight}>
                  <Button
                    onClick={handleRepayAgain}
                    className={styles.twiceRepaymentBtn}
                    color="primary"
                  >
                    继续还款
                  </Button>
                </div>
              </div>
            ) : (
              <div className="flex-center">
                <Button
                  onClick={() => LinkUtil.resetToPage(PAGES.Home)}
                  className={styles.actionBtn}
                  color="primary"
                >
                  返回首页
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  if (repayOrderData?.status === 'INSTITUTION_REPAY_FAIL') {
    const desc =
    getFailedMsg(getFailedCodeFromOrder(repayOrderData)) ||
      '您的还款未完成，请稍后重试';
    return (
      <div className={classnames([styles.repayResult, styles.hasPaddingTop])}>
        <CommonResult status="FAILED" description={desc} title="还款失败" />
        <div className="flex-center">
          <Space>
            <Button
              onClick={() => LinkUtil.resetToPage(PAGES.Home)}
              className={styles.actionBtn}
              color="default"
            >
              返回首页
            </Button>
          </Space>
        </div>
      </div>
    );
  }

  return (
    <div className={classnames([styles.repayResult, styles.hasPaddingTop])}>
      <CommonResult status="PROCESSING" title="你的还款正在处理中，请稍候" />
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '还款',
  spm: {
    spmB: PAGES.RepayResult,
  },
}));
