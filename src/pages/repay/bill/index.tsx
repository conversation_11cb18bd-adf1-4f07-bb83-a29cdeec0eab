import { definePageConfig } from 'ice';
import { useState, useEffect, useCallback } from 'react';
import { Button, Modal, NoticeBar } from 'antd-mobile';
import styles from './index.module.scss';
import classnames from 'classnames';
import {
  dayjsFormat,
  amountFormat,
  _,
  isGreaterThan0,
  isEqual0,
  getMsgByErrorCode,
  isMtopErrorCreditContractNotExist,
} from '@/utils';
import EmptyResult from '@/components/EmptyResult';
import {
  queryInstallmentBillConsult,
  queryRepayBatchPreAdmit,
  queryInstallmentBillPage,
} from '@/store/repay/actions';
import ArrowIcon from '@/components/ArrowIcon';
import FullLoading from '@/components/FullLoading';
import { useVisibilityChangeRefresh } from '@/hooks';
import { PAGES } from '@/common/constant';
import LinkUtil from '@/utils/link';
import { genRepayRejectNoticeMsg } from '@/store/repay/helper';
import { log } from '@alife/dtao-iec-spm-log';
import { repayBillPageLog } from '@/utils/goc';

export default function RepayBill() {
  const [installmentBillConsult, setInstallmentBillConsult] = useState<any>(null);
  const [installmentBillPageData, setInstallmentBillPageData] = useState<any>({});
  const [repayBatchAdmit, setRepayBatchAdmit] = useState<any>({});
  const [inited, setInited] = useState(false);
  const [noticeMsg, setNoticeMsg] = useState<any>(null);

  const handleRightBtnClick = () => {
    log.addClickLog('repay-batch-apply');
    LinkUtil.pushPage(PAGES.RepayBatchApply);
  };

  const pageInit = async () => {
    queryInstallmentBillConsult()
      .then((consultRes) => {
        setInstallmentBillConsult(consultRes);
        // 如果是未支用过或者已结清，不继续查
        repayBillPageLog({
          success: true,
          surplusBillStatus: consultRes?.surplusBillStatus,
          creditPlatform: 'ALIYUN',
        });
        if (_.includes(['NEW', 'SETTLED'], consultRes.surplusBillStatus)) return;

        queryInstallmentBillPage({
          statusQueryType: 'NOT_FINISHED',
          pageStart: 1,
          pageSize: 10,
        }).then((res) => {
          repayBillPageLog({
            success: true,
            surplusBillStatus: consultRes?.surplusBillStatus,
            creditPlatform: 'ALIYUN',
            message: 'repay-bill-consult',
          });
          setInstallmentBillPageData(res);
        });
      }).catch((e) => {
        repayBillPageLog({
          success: false,
          creditPlatform: 'ALIYUN',
          message: e?.message,
        });
        if (isMtopErrorCreditContractNotExist(e)) {
          LinkUtil.resetToPage(PAGES.CreditLp);
        } else {
          Modal.alert({
            title: '查询失败',
            content: getMsgByErrorCode(e.message),
            onConfirm: LinkUtil.popPage,
          });
        }
      }).finally(() => {
        setInited(true);
      });
    try {
      const admitRes = await queryRepayBatchPreAdmit({
        repaymentScene: 'REPAY_BY_INSTALLMENT_BILL',
      });
      setRepayBatchAdmit(admitRes);
      if (admitRes.admitted === false) {
        setNoticeMsg(genRepayRejectNoticeMsg(admitRes));
      }
    } catch (e) {
      log.addErrorLog('repay-batch-pre-admit', {
        code: e.message,
      });
    }
  };

  useEffect(() => {
    pageInit();
  }, []);

  const handleLoanContractBtnClick = useCallback(() => {
    log.addClickLog('loan-contract');
    LinkUtil.pushPage(PAGES.LoanContract);
  }, []);
  const handleLoanRecordBtnClick = useCallback(() => {
    log.addClickLog('loan-record');
    LinkUtil.pushPage(PAGES.RecordList, { tabKey: 'LOAN' });
  }, []);
  const handleRepayRecordBtnClick = useCallback(() => {
    log.addClickLog('repay-record');
    LinkUtil.pushPage(PAGES.RecordList, { tabKey: 'REPAY' });
  }, []);
  const handleRepayPlanBtnClick = useCallback(() => {
    log.addClickLog('repay-plan');
    LinkUtil.pushPage(PAGES.RepayPlan);
  }, []);

  useVisibilityChangeRefresh(pageInit);

  if (!installmentBillConsult && !inited) {
    return <FullLoading visible />;
  }

  const {
    surplusBillStatus,
    surplusTotalAmount,
    surplusOverdueTotalAmount,
    installmentEndDate,
    loanContractCount,
  } = installmentBillConsult || {};

  const isNew = surplusBillStatus === 'NEW';
  const isDue = surplusBillStatus === 'DUE';
  const isNormal = surplusBillStatus === 'NORMAL'; // 未到期
  const isOverDueOnly = surplusBillStatus === 'OVERDUE_ONLY';
  const isOverDueAndDue = surplusBillStatus === 'OVERDUE_AND_DUE';
  const isSettled = surplusBillStatus === 'SETTLED';

  const RepayIndexPageWraper = (props) => {
    return (
      <div className={styles.repayIndex}>
        {props.children}
        <div className="page-bottom-text">
          <span onClick={handleLoanRecordBtnClick}>借款记录</span>
          <span className="common-divide-line-vertical" />
          <span onClick={handleRepayRecordBtnClick}>还款记录</span>
        </div>
      </div>
    );
  };


  if (isNew || isSettled) {
    return (
      <RepayIndexPageWraper>
        <div className={styles.withMarginTop}>
          <EmptyResult title="当前无需还款" />
        </div>
      </RepayIndexPageWraper>
    );
  }

  const renderCardTitle = () => {
    if (isDue || isOverDueAndDue) {
      return '今日应还 (元)';
    }
    if (isOverDueOnly) {
      return '逾期应还 (元)';
    }
    return `${dayjsFormat(installmentEndDate, 'MM月DD日')}应还 (元)`;
  };

  const renderAmount = () => {
    if (isEqual0(surplusTotalAmount)) {
      return '当期已还清';
    }
    return amountFormat(surplusTotalAmount);
  };

  const renderAmountTips = () => {
    if (isOverDueOnly) {
      return <span className={styles.repayTipError}>您已逾期，请尽快还款，以免影响信用</span>;
    }
    if (isOverDueAndDue) {
      return (
        <span className={styles.repayTipError}>
          {`含已逾期${amountFormat(surplusOverdueTotalAmount)}元，请尽快还款，以免影响信用`}
        </span>
      );
    }

    return isGreaterThan0(surplusTotalAmount) ? '还款日会自动扣款，当天也可主动还' : null;
  };


  return (
    <RepayIndexPageWraper>
      {noticeMsg && <NoticeBar content={noticeMsg} color="alert" />}
      <div className={styles.repayBillWrap}>
        <div className="common-card">
          <div className={styles.repayIndexCardTitle}>{renderCardTitle()}</div>
          <div className={styles.repayAmountBlock}>
            <div className={styles.repayIndexCardLeft}>
              <div className={styles.repayAmountValue}>{renderAmount()}</div>
              <div className={styles.repayTip}>{renderAmountTips()}</div>
            </div>
            {isGreaterThan0(surplusTotalAmount) && !isNormal && !isNew && !_.isEmpty(repayBatchAdmit) && (
              <div className={styles.repayIndexCardRight}>
                <Button
                  onClick={handleRightBtnClick}
                  className={styles.repayIndexCardRightBtn}
                  color="primary"
                  disabled={!repayBatchAdmit.admitted}
                >
                  去还款
                </Button>
              </div>
            )}
          </div>
          <div className="common-divide-line" />
          <div onClick={handleRepayPlanBtnClick} className={styles.repayIndexCardRepayPlan}>
            <div className={styles.repayIndexCardRepayPlanLeft}>还款计划</div>
            <div className={classnames([styles.repayIndexCardRepayPlanRight, 'flex-align-center'])}>
              {installmentBillPageData.totalRecord &&
                `剩余${installmentBillPageData.totalRecord}期`}
              <ArrowIcon />
            </div>
          </div>
        </div>
        <div onClick={handleLoanContractBtnClick} className="common-card flex-space-between">
          <div className={styles.loanCountText}>{loanContractCount}笔借款使用中</div>
          <div className={classnames([styles.loanTip, 'flex-align-center'])}>
            可查看借款详情 {!isDue && !isOverDueAndDue && !isOverDueOnly ? '，也可提前还款' : ''}
            <ArrowIcon />
          </div>
        </div>
      </div>
    </RepayIndexPageWraper>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '查账还款',
  spm: {
    spmB: PAGES.RepayBill,
  },
}));
