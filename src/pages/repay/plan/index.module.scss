.repayPlan {
  .billListWrapHasOverDue {
    height: 170rpx !important;
  }
  .billListWrap {
    display: flex;
    overflow-x: scroll;
    overflow-y: visible;
    height: 126rpx;
    margin-bottom: 20rpx;
    &::-webkit-scrollbar {
      display: none;
    }
    .billItemOverdue {
      color: #f00;
    }
    .billItemActive {
      background-color: var(--primary) !important;
      color: #fff !important;
      &::after {
        content: '';
        display: block;
        position: absolute;
        bottom: -12rpx;
        left: 50%;
        transform: rotate(45deg) translateX(-50%);
        width: 16rpx;
        height: 16rpx;
        background: var(--primary);
        border-radius: 2rpx;
        z-index: 100;
      }
    }
    .billItemHasOverDue {
      height: 156rpx !important;
    }
    .billItem {
      background: #fff;
      text-align: center;
      border-radius: 12rpx;
      padding: 24rpx 0;
      margin: 0 8rpx;
      font-weight: 500;
      display: inline-block;
      flex: 0 0 190rpx;
      height: 112rpx;
      position: relative;
      .billEndDate {
        height: 26rpx;
        line-height: 26rpx;
      }

      &.overdue {
        color: #f00;
      }
      .billAmount {
        margin-top: 8rpx;
        font-size: 30rpx;
        margin-bottom: 8rpx;
        height: 30rpx;
        line-height: 30rpx;
        font-family: 'ALIBABA NUMBER FONT RG';
      }
    }
  }

  .activeBillInfo {
    text-align: center;
    margin-top: 0;
    .surplusTotalAmount {
      margin-top: 12rpx;
      font-size: 60rpx;
      font-weight: 500;
      margin-bottom: 12rpx;
      font-family: 'ALIBABA NUMBER FONT MD';
    }
    .surplusTotalAmountDetail {
      margin-bottom: 38rpx;
      color: #7c889c;
    }
  }
  .loanContractInstallmentList {
    margin-top: 40rpx;
  }
  .loanContractInstallmentDetailWrap {
    &:not(:last-child) {
      margin-bottom: 40rpx;
    }
    &:last-child {
      .installmentDetailBottom {
        margin-bottom: 0;
      }
    }
    .installmentDetailTop {
      margin-bottom: 12rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 40rpx;
      .installmentDetailDateAndAmount {
        font-size: 26rpx;
        line-height: 26rpx;
        color: #50607a;
        font-family: 'ALIBABA NUMBER FONT RG';
        display: flex;
        align-items: center;
      }
    }
    .installmentDetailMid {
      margin: 12rpx 0;
      font-size: 30rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .installmentDetailBottom {
      > * {
        height: 40rpx;
        color: #7c889c;
        font-size: 26rpx;
        &:not(:last-child) {
          margin-bottom: 24rpx;
        }
      }
    }
  }
}
