import { useEffect, useState, useCallback, useRef } from 'react';
import { definePageConfig } from 'ice';
import { Button, Modal, Toast } from 'antd-mobile';
import styles from './index.module.scss';
import {
  amountFormat,
  isGreaterThan0,
  idempotentId,
  _,
  getMsgByErrorCode,
  getErrorCodeFromRejectRes,
  isMtopErrorCreditContractNotExist,
} from '@/utils';
import {
  repayBatchConsult,
  repayBatchApply,
  RepayConsultRes,
  queryRepayOrder,
} from '@/store/repay/actions';
import {
  Authentication,
  BankInfoTip,
  FullLoading,
  CommonPopup,
  BankCardList,
  ArrowIcon,
} from '@/components';
import { useDebounceFn } from 'ahooks';
import { BankCardDTO } from '@/store/bankcard/actions';
import { useVisibilityChangeRefresh } from '@/hooks';
import { PAGES } from '@/common/constant';
import { log } from '@alife/dtao-iec-spm-log';
import LinkUtil from '@/utils/link';

export default function RepayApply() {
  const [batchConsultData, setBatchConsultData] = useState<null | RepayConsultRes>(null);
  const [repayBankCard, setRepayBankCard] = useState<BankCardDTO>({});
  const [applyResult, setApplyResult] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const repayAccountCommonPopupRef = useRef<any>(null);

  const repayOrderId = applyResult?.repayOrderId;

  const pageInit = async () => {
    try {
      const res = await repayBatchConsult();
      if (!res.admitted) {
        throw new Error(getErrorCodeFromRejectRes(res));
      } else {
        setBatchConsultData(res);

        setRepayBankCard(
          _.find(res?.repayBankCardList, (item) => item.masterCard) ||
            _.first(res?.repayBankCardList) ||
            {},
        );
      }
    } catch (e) {
      log.addErrorLog('repay-consult-error', { code: e.message });
      if (isMtopErrorCreditContractNotExist(e)) {
        LinkUtil.resetToPage(PAGES.CreditLp);
      } else {
        Modal.alert({
          title: '暂无法还款',
          content: getMsgByErrorCode(e.message),
          onConfirm: () => {
            LinkUtil.popPage();
          },
        });
      }
    } finally {
      setLoading(false);
    }
  };

  useVisibilityChangeRefresh(pageInit);

  const queryOrder = useCallback(async () => {
    const res = await queryRepayOrder({
      repayOrderId,
    });
    if (res.status !== 'AUTHENTICATING') {
      LinkUtil.locationReplace(PAGES.RepayResult, {
        repayOrderId,
      });
    } else {
      setTimeout(queryOrder, 2000);
    }
  }, [repayOrderId]);

  const handleBankCardChange = (item) => {
    setRepayBankCard(item);
    repayAccountCommonPopupRef.current?.toggleVisible(false);
  };

  const handleSuccess = () => {
    log.addSuccessLog('repay-sign-auth');
    setLoading(true);
    Toast.show({
      icon: 'success',
      content: '核身成功',
    });
    queryOrder();
  };

  const handleRepayAccountClick = () => {
    repayAccountCommonPopupRef.current?.toggleVisible(true);
  };

  const { run: handleSubmit } = useDebounceFn(
    () => {
      log.addClickLog('submit-click');
      setLoading(true);
      repayBatchApply({
        institution,
        channel: 'APP',
        applyTime: Date.now(),
        repayBindCardNo: repayBankCard.bindCardNo,
        requestId: idempotentId(),
        repayTrialResult: JSON.stringify(repayTrialResult),
      })
        .then((res: any) => {
          if (res.repayOrderId && res.authenticationToken) {
            setApplyResult(res);
          } else {
            throw new Error(getErrorCodeFromRejectRes(res));
          }
        })
        .catch((e) => {
          log.addErrorLog('repay-apply-error', { code: e.message });
          Modal.alert({
            title: '还款失败',
            content: getMsgByErrorCode(e.message),
          });
        })
        .finally(() => {
          setLoading(false);
        });
    },
    {
      wait: 500,
      leading: true,
      trailing: false,
    },
  );

  useEffect(() => {
    log.addVisitLog(PAGES.RepayBatchApply);
    pageInit();
  }, []);

  if (!batchConsultData) {
    return (
      <div className={styles.repayApply}>
        <FullLoading visible={loading} />
      </div>
    );
  }

  const { repayTrialResult, institution } = batchConsultData;

  return (
    <div className={styles.repayApply}>
      <FullLoading visible={loading} />
      <div className={styles.repayApplyTitle}>应还金额 (元)</div>
      <div className={styles.repayApplyAmount}>
        {amountFormat(repayTrialResult.trialTotalAmount)}
      </div>
      <div className={styles.repaymentInfoCard}>
        <div className={styles.infoItem}>
          <div className={styles.infoItemLabel}>本金</div>
          <div className={styles.infoItemValue}>
            ¥{amountFormat(repayTrialResult.trialPrincipal)}
          </div>
        </div>
        <div className={styles.infoItem}>
          <div className={styles.infoItemLabel}>利息</div>
          <div className={styles.infoItemValue}>
            ¥{amountFormat(repayTrialResult.trialInterest)}
          </div>
        </div>
        {isGreaterThan0(repayTrialResult.trialPenalty) && (
          <div className={styles.infoItem}>
            <div className={styles.infoItemLabel}>罚息</div>
            <div className={styles.infoItemValue}>
              ¥{amountFormat(repayTrialResult.trialPenalty)}
            </div>
          </div>
        )}
        <div onClick={handleRepayAccountClick} className={styles.infoItem}>
          <div className={styles.infoItemLabel}>还款账户</div>
          <div className={styles.infoItemValue}>
            <BankInfoTip
              cardNo={repayBankCard.bankCardNo}
              code={repayBankCard.bankCode}
              name={repayBankCard.bankName}
            />
            <ArrowIcon className={styles.infoItemArrow} />
          </div>
        </div>
      </div>
      <div className="page-bottom-bar">
        <Button
          disabled={_.isEmpty(repayTrialResult) || _.isEmpty(repayBankCard) || loading}
          onClick={handleSubmit}
          block
          color="primary"
        >
          确认还款
        </Button>
      </div>
      <Authentication
        directly
        authenticationToken={_.get(applyResult, 'authenticationToken')}
        onSuccess={handleSuccess}
        onFail={() => {
          setLoading(false);
        }}
      />
      <CommonPopup ref={repayAccountCommonPopupRef} title="还款账户">
        <BankCardList
          onChange={handleBankCardChange}
          value={repayBankCard}
          options={batchConsultData.repayBankCardList}
        />
      </CommonPopup>
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '还款',
  spm: {
    spmB: PAGES.RepayBatchApply,
  },
  window: {
    navBarImmersive: false,
    navBarBgColor: '#f3f6f8',
    pageBgColor: '#f3f6f8',
  },
}));
