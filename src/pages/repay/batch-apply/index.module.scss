.repayApply {
  padding-top: 16rpx!important;
  background: #f3f6f8;
  .loanContractInfoSurplus {
    font-size: 26rpx;
    margin: 12rpx 0;
  }
  .loanContractInfoAmount {
    color: #111;
    font-family: "ALIBABA NUMBER FONT MD";
    font-size: 36rpx;
    font-weight: 500;
    margin-right: 8rpx;
    line-height: 36rpx;
  }
  .repayApplyTitle {
    font-size: 24rpx;
    color: #111;
    text-align: center;
    margin-bottom: 24rpx;
  }
  .repayApplyAmount {
    font-size: 60rpx;
    font-weight: 500;
    font-family: "ALIBABA NUMBER FONT MD";
    text-align: center;
    margin-bottom: 24rpx;
  }
  .repaymentInfoCard {
    background: #fff;
    border-radius: 18rpx;
    padding: 0 24rpx;
    .infoItem {
      padding: 16rpx 0;
      display: flex;
      justify-content: space-between;
      line-height: 1.5;
      .infoItemLabel {
        font-size: 26rpx;
      }
      .infoItemValue {
        font-size: 26rpx;
        color: #7c889c;
        font-family: "ALIBABA NUMBER FONT RG";
        display: flex;
        align-items: center;
      }
      .infoItemArrow {
        position: relative;
        top: 2rpx;
      }
    }
  }
}
