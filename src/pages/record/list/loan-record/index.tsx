import classnames from 'classnames';
import { _, amountFormat, dayjsYYMD } from '@/utils';
import { InfiniteScroll } from 'antd-mobile';
import styles from './index.module.scss';
import { PAGES, REPAYMENT_METHOD } from '@/common/constant';
import EmptyResult from '@/components/EmptyResult';
import SsdEmpty from '@/components/SSD/Empty';
import ArrowIcon from '@/components/ArrowIcon';
import { actions } from '@/store/loan';
import FullLoading from '@/components/FullLoading';
import { useListModal } from '@/hooks/useListModal';
import { useEffect, useState } from 'react';
import LinkUtil from '@/utils/link';
import { recordListPageLog } from '@/utils/goc';

const StatusTextMap = {
  LOANING: '放款中',
  USING: '使用中',
  OVERDUE: '已逾期',
  FAILED: '放款失败',
  CLEARED: '已结清',
};

const StatusTextClassNameMap = {
  OVERDUE: styles.statusRed,
  FAILED: styles.statusFailed,
  CLEARED: styles.statusFailed,
};

export default function LoanRecordList({ isSSD }) {
  const [listHasMore, setListHasMore] = useState<boolean>(false);
  const loadMore = async () => {
    const { hasMore } = await doFetch();
    setListHasMore(hasMore);
  };

  const handleSuccess = () => {
    recordListPageLog({
      success: true,
      isSSD,
    });
  };

  const handleError = () => {
    recordListPageLog({
      success: false,
      isSSD,
    });
  };

  const renderEmpty = () => {
    return (
      <div className={styles.emptyWrap}>{isSSD ? <SsdEmpty title="暂无借款记录" /> : <EmptyResult title="暂无借款记录" />}</div>
    );
  };

  const renderItem = (record, index) => {
    const loanTime = _.includes(['LOANING', 'FAILED'], record.status)
      ? record.applyTime
      : record.loanedTime;

    const loanAmount = _.includes(['LOANING', 'FAILED'], record.status)
      ? amountFormat(record.loanApplyAmount)
      : amountFormat(record.loanedAmount);

    return (
      <div
        onClick={() => {
          LinkUtil.pushPage(PAGES.LoanDetail, { loanOrderId: record.loanOrderId });
        }}
        key={index}
        className={styles.recordItem}
      >
        <div className={styles.left}>
          <div className={styles.recordAmount}>
            借款 <span className={styles.amountValue}>¥{amountFormat(loanAmount)}</span>
          </div>
          <div className={styles.recordDate}>
            {dayjsYYMD(loanTime)}借款&nbsp;
            {REPAYMENT_METHOD[record.repaymentMethod]}
          </div>
        </div>
        <div className={classnames([styles.right, 'flex-align-center'])}>
          <span className={classnames([styles.statusText, StatusTextClassNameMap[record.status]])}>
            {StatusTextMap[record.status]}
          </span>
          <ArrowIcon />
        </div>
      </div>
    );
  };

  const { doFetch, doInit, renderList, isFetching } = useListModal({
    fetch: actions.queryLoanOrderPage,
    renderItem,
    renderEmpty,
    listKey: 'dataList',
    totalKey: 'totalRecord',
    onSuccess: handleSuccess,
    onError: handleError,
    pageParam: {
      pageStart: 1,
      pageSize: 15,
    },
  });

  useEffect(() => {
    doInit().then(({ hasMore }) => {
      setListHasMore(hasMore);
    });
  }, []);

  return (
    <div>
      <FullLoading visible={isFetching} />
      <div className={styles.loanRecordList}>{renderList()}</div>
      <InfiniteScroll loadMore={loadMore} hasMore={listHasMore}>
        <>{null}</>
      </InfiniteScroll>
    </div>
  );
}
