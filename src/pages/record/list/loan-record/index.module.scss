.loanRecordList {
  margin: 32rpx 0;
  .emptyWrap {
    margin-top: 180rpx;
  }
  .recordItem {
    height: 132rpx;
    padding: 24rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    background-color: #fff;
    &:only-child {
      border-radius: 12rpx 12rpx 12rpx 12rpx!important;
    }
    &:first-child {
      border-radius: 12rpx 12rpx 0 0;
    }
    &:last-child {
      border-radius: 0 0 12rpx 12rpx;
    }
    .left {
      .recordAmount {
        display: flex;
        align-items: center;
        font-size: 26rpx;
        color: #111;
        line-height: 44rpx;
        .amountValue {
          line-height: 44rpx;
          position: relative;
          margin-left: 8rpx;
          font-size: 26rpx;
          font-family: "ALIBABA NUMBER FONT RG";
        }
      }
      .recordDate {
        font-size: 26rpx;
        color: #999;
        font-family: "ALIBABA NUMBER FONT RG";
      }
    }
    .right {
      .statusText {
        font-size: 26rpx;
        color: #111;
        &.statusRed {
          color: #fe413d;
        }
        &.statusFailed {
          color: #999;
        }
      }
      .arrowIcon {
        width: 32rpx;
        height: 32rpx;
        margin-left: 2rpx;
        position: relative;
        top: -2rpx;
        transform: rotate(-90deg);
      }
    }
  }
}
