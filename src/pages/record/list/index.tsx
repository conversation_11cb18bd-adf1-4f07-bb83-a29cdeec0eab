import { definePageConfig } from 'ice';
import { useState, useEffect } from 'react';
import styles from './index.module.scss';
import RepayRecordList from './repay-record';
import LoanRecordList from './loan-record';
import CommonTabs from '@/components/CommonTabs';
import classnames from 'classnames';
import { getQueryParams, _ } from '@/utils';
import { PAGES } from '@/common/constant';
import { log } from '@alife/dtao-iec-spm-log';

const TabConfig = [
  {
    key: 'LOAN',
    title: '借款记录',
  },
  {
    key: 'REPAY',
    title: '还款记录',
  },
];

const defaultTabKey = 'LOAN';


export default function RecordList() {
  const queryParams: any = getQueryParams();
  const [activeKey, setActiveKey] = useState(_.get(queryParams, 'tabKey', defaultTabKey));

  useEffect(() => {
    log.addVisitLog(PAGES.RecordList);
  }, []);

  return (
    <div className={classnames([styles.loanRepayRecord, queryParams.isSSD && styles.isSSD])}>
      <div className={styles.recordListTabs}>
        <CommonTabs
          tabConfig={TabConfig}
          defaultActiveKey={activeKey}
          onTabClick={(key) => {
            setActiveKey(key);
          }}
        />
      </div>

      {activeKey === 'LOAN' && <LoanRecordList isSSD={queryParams.isSSD} />}
      {activeKey === 'REPAY' && <RepayRecordList isSSD={queryParams.isSSD} />}
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '借还记录',
  spm: {
    spmB: PAGES.RecordList,
  },
  window: {
    navBarImmersive: false,
    navBarBgColor: '#f3f6f8',
    pageBgColor: '#f3f6f8',
  },
}));
