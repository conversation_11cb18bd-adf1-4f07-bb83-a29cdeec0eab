import { useEffect, useState } from 'react';
import { InfiniteScroll } from 'antd-mobile';
import EmptyResult from '@/components/EmptyResult';
import classnames from 'classnames';
import { dayjsYYMD, _, amountFormat } from '@/utils';
import styles from './index.module.scss';
import SsdEmpty from '@/components/SSD/Empty';
import { PAGES } from '@/common/constant';
import ArrowIcon from '@/components/ArrowIcon';
import { queryRepayOrderList } from '@/store/repay/actions';
import { useListModal } from '@/hooks/useListModal';
import FullLoading from '@/components/FullLoading';
import LinkUtil from '@/utils/link';

export default function RepayRecordList({ isSSD }) {
  const [listHasMore, setListHasMore] = useState<boolean>(false);
  const loadMore = async () => {
    const { hasMore } = await doFetch();
    setListHasMore(hasMore);
  };
  const renderEmpty = () => {
    return (
      <div className={styles.emptyWrap}>{isSSD ? <SsdEmpty title="暂无还款记录" /> : <EmptyResult title="暂无还款记录" />}</div>
    );
  };

  const renderItem = (record, index) => {
    let statusText;
    if (record.status === 'INSTITUTION_REPAYING') {
      statusText = '还款中';
    } else if (record.status === 'INSTITUTION_REPAY_FAIL') {
      statusText = '还款失败';
    } else if (record.repayType === 'SYSTEM_REPAY') {
      statusText = record.repayChannel === 'OFFLINE' ? '线下还款' : '系统扣款';
    } else {
      statusText = _.get(
        {
          MANUAL_REPAY: '主动还款',
          // SYSTEM_REPAY: '系统扣款',
          // OFFLINE_REPAY: '线下还款',
        },
        record.repayType,
      );
    }
    return (
      <div
        onClick={() => {
          LinkUtil.pushPage(PAGES.RepayDetail, { repayOrderId: record.repayOrderId, isSSD });
        }}
        key={index}
        className={styles.recordItem}
      >
        <div className={styles.left}>
          <div className={styles.recordAmount}>
            还款
            <span className={styles.amountValue}>
              ¥
              {record.status === 'INSTITUTION_REPAYING'
                ? amountFormat(record.applyAmount)
                : amountFormat(record.repaidTotalAmount)}
            </span>
          </div>
          <div className={styles.recordDate}>
            {dayjsYYMD(
              record.status === 'SUCCEEDED' ? record.institutionEndTime : record.applyTime,
            )}
          </div>
        </div>
        <div className={classnames([styles.right, 'flex-align-center'])}>
          <span
            className={classnames([
              styles.statusText,
              record.status === 'INSTITUTION_REPAY_FAIL' && styles.failedStatusText,
            ])}
          >
            {statusText}
          </span>
          <ArrowIcon />
        </div>
      </div>
    );
  };

  const { doInit, doFetch, renderList, isFetching } = useListModal({
    fetch: queryRepayOrderList,
    renderItem,
    renderEmpty,
    listKey: 'dataList',
    totalKey: 'totalRecord',
    pageParam: {
      sortType: 'APPLY_TIME_DESC',
      pageStart: 1,
      pageSize: 15,
    },
  });

  useEffect(() => {
    doInit().then(({ hasMore }) => {
      setListHasMore(hasMore);
    });
  }, []);

  return (
    <div>
      <FullLoading visible={isFetching} />
      <div className={styles.repayRecordList}>{renderList()}</div>
      <InfiniteScroll loadMore={loadMore} hasMore={listHasMore}>
        <>{null}</>
      </InfiniteScroll>
    </div>
  );
}
