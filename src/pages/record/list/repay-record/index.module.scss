.repayRecordList {
  margin: 32rpx 0;
  .emptyWrap {
    margin-top: 180rpx;
  }
  .recordItem {
    height: 132rpx;
    padding: 24rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    background-color: #fff;
    &:only-child {
      border-radius: 12rpx 12rpx 12rpx 12rpx!important;
    }
    &:first-child {
      border-radius: 12rpx 12rpx 0 0;
    }
    &:last-child {
      border-radius: 0 0 12rpx 12rpx;
    }
    .left {
      .recordAmount {
        font-size: 26rpx;
        color: #111;
        line-height: 44rpx;
        display: flex;
        align-items: center;
        .amountValue {
          font-size: 26rpx;
          line-height: 44rpx;
          position: relative;
          margin-left: 8rpx;
          font-family: "ALIBABA NUMBER FONT RG";
        }
      }
      .recordDate {
        font-size: 26rpx;
        color: #999;
        font-family: "ALIBABA NUMBER FONT RG";
      }
    }
    .right {
      .statusText {
        color: #666;
        font-size: 26rpx;
        margin-right: 8rpx;
      }
      .failedStatusText {
        color: #7c889c;
      }
      .arrowIcon {
        width: 32rpx;
        height: 32rpx;
        margin-left: 2rpx;
        position: relative;
        top: -2rpx;
        transform: rotate(-90deg);
      }
    }
  }
}
