import { useState, useEffect, Fragment, useCallback } from 'react';
import { definePageConfig } from 'ice';
import { Button, Modal } from 'antd-mobile';
import classnames from 'classnames';
import CommonTabs from '@/components/CommonTabs';
import CommonResult from '@/components/CommonResult';
import { DetailItemList } from '@/components/DetailItemList';
import { actions } from '@/store/loan';
import { formatCapitalInstitution } from '@/store/lib/format';
import styles from './index.module.scss';
import { amountFormat, dayjsFormat, getQueryParams, _ } from '@/utils';
import FullLoading from '@/components/FullLoading';
import BankInfoTip from '@/components/BankInfoTip';
import ArrowIcon from '@/components/ArrowIcon';
import { queryRepayBatchPreAdmit, queryRepayPreAdmit } from '@/store/repay/actions';
import { REPAYMENT_METHOD, PAGES, AGREEMENT_PDF } from '@/common/constant';
import CommonFixedNavBar from '@/components/CommonFixedNavBar';
import { useVisibilityChangeRefresh } from '@/hooks';
import RepaidDetail from './repaid-detail';
import RepayPlan from './repay-plan';
import LinkUtil from '@/utils/link';
import { log } from '@alife/dtao-iec-spm-log';
import { url } from '@ali/iec-dtao-utils';
import { checkHmRejectAlert } from '@/components';

export default function LoanDetail() {
  const [loanOrderDetail, setLoanOrderDetail] = useState<any>(null);
  const [activeKey, setActiveKey] = useState('REPAY_PLAN');
  const [repayAdmit, setRepayAdmit] = useState<any>(false);
  const loanOrderId = _.get(getQueryParams(), 'loanOrderId');
  const [inited, setInited] = useState(false);

  const isSSD = loanOrderDetail?.institution === 'MAYI_ZHIXIN';

  const handleSubmit = useCallback(() => {
    if (isSSD) {
      if (checkHmRejectAlert(PAGES.LoanDetail)) {
        return;
      }
      LinkUtil.pushPage(PAGES.SsdRepayEarlyApply, {
        loanOrderIdList: encodeURIComponent(JSON.stringify([loanOrderDetail.loanOrderId])),
      });
      return;
    }
    LinkUtil.pushPage(PAGES.RepayEarlyApply, { loanOrderId: loanOrderDetail.loanOrderId });
  }, [loanOrderDetail, isSSD]);

  const handlePreviewClick = (detail) => {
    if (isSSD) {
      log.addClickLog('ssd-signed-agreement-click');
      LinkUtil.pushPage(PAGES.SsdSignedAgreementsPreview, {
        bizId: detail.loanOrderId,
        fipBizOrderId: detail?.fipLoanOrderid,
        bizType: 'LOAN',
        institution: 'MAYI_ZHIXIN',
      });
    } else {
      log.addClickLog('signed-agreement-click');
      const targetUrl = url.add(AGREEMENT_PDF, {
        title: '支用协议',
        bizType: 'LOAN',
        bizId: detail.loanOrderId,
      });
      LinkUtil.navigatorOpenURL(targetUrl);
    }
  };

  const pageInit = async () => {
    log.addVisitLog(PAGES.LoanDetail);

    try {
      if (!loanOrderId) {
        throw new Error('loanOrderId Empty');
      }
      const res = await actions.getLoanOrderDetail({
        loanOrderId,
      });
      setLoanOrderDetail(res);

      // 如果已结清和失败  不查询是否还款准入
      if (_.includes(['CLEARED', 'FAILED', 'LOANING'], res.status)) return;

      let admitRes;

      if (res.institution === 'MAYI_ZHIXIN') {
        admitRes = await queryRepayBatchPreAdmit({
          repaymentScene: 'REPAY_BY_LOAN_CONTRACT',
          loanOrderIdList: [loanOrderId],
        });
      } else {
        admitRes = await queryRepayPreAdmit({
          loanOrderId,
        });
      }

      setRepayAdmit(admitRes.admitted);
    } catch (error) {
      Modal.alert({
        title: '查询失败',
        onConfirm: () => {
          LinkUtil.popPage();
        },
      });
    } finally {
      setInited(true);
    }
  };

  useVisibilityChangeRefresh(pageInit);

  useEffect(() => {
    pageInit();
  }, []);

  if (!loanOrderDetail) {
    return !inited ? <FullLoading visible={!inited} /> : null;
  }

  const {
    status,
    receiveBankCard = {},
    loanApplyAmount,
    loanedAmount,
    surplusPrincipal,
    capitalInstitution,
  } = loanOrderDetail;

  const loanDetailInfoRender = () => {
    const BankInfoTipComp = (
      <BankInfoTip
        cardNo={receiveBankCard.bankCardNo}
        code={receiveBankCard.bankCode}
        name={receiveBankCard.bankName}
        source={isSSD ? 'mycdn' : ''}
      />
    );

    const formatZHIXINInstitution = () => {
      return `随身贷·${_.get(capitalInstitution, '[0].institutionName')}`;
    };

    const institutionNames = isSSD
      ? formatZHIXINInstitution()
      : formatCapitalInstitution(capitalInstitution);

    if (status === 'FAILED') {
      return (
        <DetailItemList
          noPadding
          data={[
            {
              label: '收款账户',
              value: BankInfoTipComp,
            },
            {
              label: '放款机构',
              value: institutionNames,
            },
          ]}
        />
      );
    }
    const amount = status === 'LOANING' ? loanApplyAmount : loanedAmount;

    return (
      <DetailItemList
        noPadding={status !== 'LOANING'}
        data={[
          {
            label: '借款金额',
            value: `¥${amountFormat(amount)}`,
          },
          {
            label: '收款账户',
            value: BankInfoTipComp,
          },
          {
            label: '放款机构',
            value: institutionNames,
          },
          {
            customRender: () => {
              if (
                !loanOrderDetail.loanTerm ||
                !loanOrderDetail.repaymentStartDay ||
                !loanOrderDetail.repaymentEndDay
              ) {
                return null;
              }
              return (
                <div className={classnames([styles.loanDetailItem, styles.loanTerm])}>
                  <div className={styles.loanDetailItemLabel}>合同期限</div>
                  <div className={styles.loanDetailItemValue}>
                    <div className={styles.agreementTermTitle}>{loanOrderDetail.loanTerm}期</div>
                    <div>
                      {dayjsFormat(loanOrderDetail.repaymentStartDay)}至
                      {dayjsFormat(loanOrderDetail.repaymentEndDay)}
                    </div>
                  </div>
                </div>
              );
            },
          },
          {
            label: '还款方式',
            value: REPAYMENT_METHOD[loanOrderDetail.repaymentMethod],
          },
          {
            customRender: () => {
              if (loanOrderDetail?.extension?.zhiXinSignedAgreementQueryDisable) {
                return null;
              }
              return (
                status !== 'LOANING' &&
                status !== 'FAILED' && (
                  <div className={classnames([styles.loanDetailItem])}>
                    <div className={styles.loanDetailItemLabel}>借款合同</div>
                    <div
                      onClick={() => handlePreviewClick(loanOrderDetail)}
                      className={styles.loanDetailItemValue}
                    >
                      <div className="flex-align-center">
                        查看 <ArrowIcon />
                      </div>
                    </div>
                  </div>
                )
              );
            },
          },
        ]}
      />
    );
  };

  let mainTitle;
  let mainValue;
  let statusImgSrc;
  let bottomContent;

  if (status === 'LOANING') {
    return (
      <div className={styles.loanDetail}>
        <CommonResult isSSD={isSSD} title="您的借款正在处理中，请稍候" />
        {loanDetailInfoRender()}
      </div>
    );
  }

  if (status === 'FAILED') {
    mainTitle = '申请金额 (元)';
    mainValue = amountFormat(loanApplyAmount);
    statusImgSrc =
      'https://gw.alicdn.com/imgextra/i2/O1CN0199fD2E1WwGZe81OEG_!!6000000002852-2-tps-240-240.png';
  }

  if (status === 'USING' || status === 'OVERDUE') {
    mainTitle = '使用中金额 (元)';
    mainValue = status === 'USING' ? amountFormat(surplusPrincipal) : amountFormat(loanedAmount);
    statusImgSrc =
      status === 'OVERDUE'
        ? 'https://gw.alicdn.com/imgextra/i4/O1CN01GNFxmG1HHlJJgfbty_!!6000000000733-2-tps-240-240.png'
        : null;
    bottomContent = (
      <Fragment>
        <CommonTabs
          defaultActiveKey="REPAY_PLAN"
          onTabClick={(key) => setActiveKey(key)}
          tabConfig={[
            {
              title: '还款计划',
              key: 'REPAY_PLAN',
            },
            {
              title: '已还明细',
              key: 'REPAID_DETAIL',
            },
          ]}
        />
        {activeKey === 'REPAY_PLAN' && <RepayPlan loanOrderDetail={loanOrderDetail} />}
        {activeKey === 'REPAID_DETAIL' && (
          <RepaidDetail isSSD={isSSD} loanOrderDetail={loanOrderDetail} />
        )}
        {repayAdmit && (
          <div className="page-bottom-bar">
            {isSSD ? (
              <Button
                className={styles.ssdRepayBtn}
                onClick={handleSubmit}
                shape="rounded"
                color="primary"
                block
                size="large"
              >
                提前还款
              </Button>
            ) : (
              <Button onClick={handleSubmit} className="bordered-button" block>
                提前还款
              </Button>
            )}
          </div>
        )}
      </Fragment>
    );
  }

  if (status === 'CLEARED') {
    mainTitle = '已结清金额 (元)';
    mainValue = amountFormat(loanedAmount);
    statusImgSrc =
      'https://gw.alicdn.com/imgextra/i4/O1CN01Xwqw3Q1z0fcmwcG1k_!!6000000006652-2-tps-240-240.png';
    bottomContent = (
      <Fragment>
        <div className={styles.repaidDetailtitle}>已还明细</div>
        <RepaidDetail isSSD={isSSD} loanOrderDetail={loanOrderDetail} />
      </Fragment>
    );
  }

  return (
    <div className={classnames([styles.loanDetail, isSSD && styles.isSSD])}>
      <CommonFixedNavBar bgColor="#f3f6f8" />
      <div className={classnames([styles.mainCard, 'common-card'])}>
        {statusImgSrc && <img className={styles.statusImg} src={statusImgSrc} />}
        <div className={styles.mainCardTitle}>{mainTitle}</div>
        <div className={styles.mainCardAmount}>{mainValue}</div>
        <div className="common-divide-line" />
        <div className={styles.loanDetailInfoContent}>{loanDetailInfoRender()}</div>
      </div>
      {bottomContent}
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '借款详情',
  spm: {
    spmB: PAGES.LoanDetail,
  },
}));
