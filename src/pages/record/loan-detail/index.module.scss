.field {
  line-height: 3.5;
  border-radius: 12rpx;
  padding: 0 24rpx;
  background: #f3f6f8;
  --field-text-color: #7c889c;
  --field-placeholder-color: #7c889c;
}

.agreement {
  --agreement-name-font-size: 20rpx;
}

.prefix {
  font-size: 20rpx;
}

.ssdRepayBtn {
  --background-color: #1677ff!important;
  --primary: #1677ff!important;
}
.loanDetail {
  &.isSSD {
    --primary: #1677ff;
  }
  .repaidDetailtitle {
    font-size: 30rpx;
    font-weight: 500;
    margin-bottom: 24rpx;
  }
  .mainCard {
    text-align: center;
    position: relative;
    .statusImg {
      position: absolute;
      top: 10rpx;
      right: 10rpx;
      width: 120rpx;
      height: 120rpx;
    }
    .mainCardTitle {
      font-size: 26rpx;
      &.failedMainCardTitle {
        color: #cacfd7;
      }
    }
    .mainCardAmount {
      margin-top: 12rpx;
      margin-bottom: 12rpx;
      font-size: 60rpx;
      font-weight: 500;
      font-family: "ALIBABA NUMBER FONT MD";
      &.failedMainCardAmount {
        color: #cacfd7;
      }
    }
  }
  .loanDetailItem {
    height: 72rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 26rpx;
    .loanDetailItemValue {
      font-family: "ALIBABA NUMBER FONT RG";
      font-size: 26rpx;
      color: #7c889c;
      text-align: right;
      .previewBtn {
        height: 56rpx;
        padding: 0 24rpx;
        font-size: 24rpx;
        font-weight: normal;
      }
    }
  }
  .loanTerm {
    height: 116rpx;
    padding: 16rpx 0;
    align-items: flex-start;
    .agreementTermTitle {
      margin-bottom: 6rpx;
    }
  }
  .loanDetailInfoContent {
    margin-top: 12rpx;
  }
}
