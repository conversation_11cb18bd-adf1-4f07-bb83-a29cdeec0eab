import { Fragment, useEffect, useState } from 'react';
import styles from './index.module.scss';
import EmptyResult from '@/components/EmptyResult';
import { amountFormat, isGreaterThan0, _, dayjsYYMD } from '@/utils';
import classnames from 'classnames';
import { DetailItem } from '@/components/DetailItemList';
import SsdEmpty from '@/components/SSD/Empty';
import {
  queryContractRepaySubOrderSummary,
  queryRepaySubOrderList,
  ContractRepaySubOrderSummaryRes,
  repaySubOrderListRes,
} from '@/store/repay/actions';

export default function RepaidDetail({ loanOrderDetail, isSSD }) {
  const [contranctRepaySubOrderSummary, setContranctRepaySubOrderSummary] =
    useState<null | ContractRepaySubOrderSummaryRes>(null);
  const [repaySubOrderData, setRepaySubOrderData] = useState<null | repaySubOrderListRes>(null);
  const [inited, setInited] = useState(false);

  const queryData = async () => {
    const [summaryRes, listRes] = await Promise.all([
      queryContractRepaySubOrderSummary({
        loanContractId: loanOrderDetail.loanContractId,
        statusList: ['SUCCEEDED'],
      }),
      queryRepaySubOrderList({
        cascadeLoanContract: false,
        loanContractId: loanOrderDetail.loanContractId,
        pageSize: 50,
        statusList: ['SUCCEEDED'],
        sortType: 'APPLY_TIME_DESC',
        pageStart: 1,
      }),
    ]);

    setContranctRepaySubOrderSummary(summaryRes);
    setRepaySubOrderData(listRes);
    setInited(true);
  };

  useEffect(() => {
    if (loanOrderDetail) {
      queryData();
    }
  }, [loanOrderDetail]);

  if (!inited || !contranctRepaySubOrderSummary) return null;

  const { repaidInterest, repaidPenalty, repaidPrincipal } = contranctRepaySubOrderSummary;

  return (
    <div className={classnames([styles.repaidDetail])}>
      {inited && _.isEmpty(contranctRepaySubOrderSummary) ? (
        <div className={classnames(['common-card'])}>
          {isSSD ? <SsdEmpty title="暂无已还明细" /> : <EmptyResult title="暂无已还明细" />}
        </div>
      ) : (
        <Fragment>
          <div className={classnames([styles.repaidDetailContent, 'common-card'])}>
            <div className={styles.repaidDetailItem}>
              <div>已还总本金</div>
              <div className={styles.repaidDetailItemAmount}>¥{amountFormat(repaidPrincipal)}</div>
            </div>
            <div className={styles.repaidDetailItem}>
              <div>已还总利息</div>
              <div className={styles.repaidDetailItemAmount}>¥{amountFormat(repaidInterest)}</div>
            </div>
            {isGreaterThan0(repaidPenalty) && (
              <div className={styles.repaidDetailItem}>
                <div>已还总罚息</div>
                <div className={styles.repaidDetailItemAmount}>¥{amountFormat(repaidPenalty)}</div>
              </div>
            )}
          </div>
          {_.map(repaySubOrderData?.dataList, (item) => {
            return (
              <div className={classnames([styles.repayOrderItem])}>
                <div className={styles.repayOrderItemAmountAndDate}>
                  {dayjsYYMD(item.institutionEndTime)}还款{amountFormat(item.repaidTotalAmount)}元
                </div>
                {isGreaterThan0(item.repaidPrincipal) && (
                  <DetailItem label="已还本金" value={`¥${amountFormat(item.repaidPrincipal)}`} />
                )}
                {isGreaterThan0(item.repaidInterest) && (
                  <DetailItem label="已还利息" value={`¥${amountFormat(item.repaidInterest)}`} />
                )}
                {isGreaterThan0(item.repaidPenalty) && (
                  <DetailItem label="已还罚息" value={`¥${amountFormat(item.repaidPenalty)}`} />
                )}
              </div>
            );
          })}
        </Fragment>
      )}
    </div>
  );
}
