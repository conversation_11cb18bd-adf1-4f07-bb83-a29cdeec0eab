import styles from './index.module.scss';
import { dayjsFormat, amountFormat, isGreaterThan0, _ } from '@/utils';
import classnames from 'classnames';

export default function RepayPlan({ loanOrderDetail }) {
  return (
    <div className={classnames([styles.repayPlan, 'common-card'])}>
      {_.map(loanOrderDetail.installmentPlanVOList, (instalment) => {
        if (instalment.status === 'SETTLED') return null;
        return (
          <div key={instalment.number} className={styles.rpInstalmentItem}>
            <div className={styles.left}>
              <div className={styles.instalmentText}>第{instalment.number}期</div>
              <div className={styles.instalmentDate}>
                {dayjsFormat(instalment.endDate, 'YYYY/MM/DD')}
              </div>
            </div>
            <div className={styles.mid}>
              <div className={styles.dot} />
            </div>
            <div className={styles.right}>
              <div className={styles.rightTotalAmount}>
                ¥{amountFormat(instalment.totalAmount)}
                {instalment.status === 'OVERDUE' && <div className="common-tag red">逾期</div>}
              </div>
              <div className={styles.rightDesc}>
                本金<span>¥{amountFormat(instalment.principal)}</span> + 利息
                <span>¥{amountFormat(instalment.interest)}</span>
                {isGreaterThan0(instalment.penalty) && (
                  <span> + 罚息¥{amountFormat(instalment.penalty)}元</span>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}
