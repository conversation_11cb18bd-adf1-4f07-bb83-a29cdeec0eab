
.repayPlan {
  margin-top: 24rpx;
  .rpInstalmentItem {
    display: flex;
    &:not(:last-child) {
      margin-bottom: 50rpx;
      .mid {
        &::after {
          content: "";
          position: absolute;
          height: 72rpx;
          width: 1px;
          background-color: #c2c2c2;
          top: 42rpx;
          left: 0;
          transform: translate(8rpx);
        }
        &.overdue {
          &::after {
            height: 346rpx;
          }
        }
      }
    }
    .mid {
      flex: 0 0 56rpx;
      margin-right: 0;
      position: relative;
      .dot {
        border-radius: 50%;
        width: 18rpx;
        height: 18rpx;
        border: 3rpx solid var(--primary);
        margin-top: 8rpx;
        img {
          width: 18rpx;
          height: 18rpx;
        }
      }
    }
    .left {
      margin-right: 34rpx;
      text-align: right;
      .instalmentText {
        font-weight: 500;
        color: #111;
        line-height: 42rpx;
        font-size: 26rpx;
      }

      .instalmentDate {
        font-size: 24rpx;
        color: #7c889c;
        font-family: "ALIBABA NUMBER FONT RG";
      }
    }
    .right {
      flex: 1 1 auto;
      position: relative;
      .rightTotalAmount {
        font-family: "ALIBABA NUMBER FONT MD";
        font-size: 26rpx;
        font-weight: bold;
        margin-bottom: 6rpx;
      }

      .rightDesc {
        position: absolute;
        left: 0;
        top: 40rpx;
        font-size: 24rpx;
        color: #7c889c;
        font-family: "ALIBABA NUMBER FONT RG";
      }
    }
  }
}
