import { useState, useEffect, Fragment } from 'react';
import { definePageConfig } from 'ice';
import classnames from 'classnames';
import { Modal } from 'antd-mobile';
import CommonResult from '@/components/CommonResult';
import {
  dayjsFormat,
  getQueryParams,
  isGreaterThan0,
  amountFormat,
  _,
  dayjsYYMD,
  getFailedCodeFromOrder,
  getFailedMsg,
} from '@/utils';
import styles from './index.module.scss';
import FullLoading from '@/components/FullLoading';
import ArrowIcon from '@/components/ArrowIcon';
import { DetailItemList, DetailItem } from '@/components/DetailItemList';
import CommonFixedNavBar from '@/components/CommonFixedNavBar';
import { PAGES, REPAYMENT_METHOD } from '@/common/constant';
import {
  queryRepaySubOrderList,
  queryRepayOrderDetail,
  repaySubOrderListRes,
} from '@/store/repay/actions';
import LinkUtil from '@/utils/link';
import { log } from '@alife/dtao-iec-spm-log';

export default function RepayDetail() {
  const [repayOrderDetail, setRepayDetail] = useState<any>(null);
  const [repaySubOrderData, setRepaySubOrderData] = useState<null | repaySubOrderListRes>(null);
  const [inited, setInited] = useState(false);

  const repayOrderId = _.get(getQueryParams(), 'repayOrderId');
  const isSSD = _.get(getQueryParams(), 'isSSD');

  const queryRepaySubOrderListFn = () => {
    queryRepaySubOrderList({
      parentId: repayOrderId, // 主单号
      cascadeLoanContract: true,
      sortType: 'APPLY_TIME_DESC', // 按发起还款时间倒序
      statusList: ['SUCCEEDED'],
      pageStart: 1,
      pageSize: 50,
    }).then((res) => {
      setRepaySubOrderData(res);
    });
  };

  useEffect(() => {
    log.addVisitLog(PAGES.RepayDetail);
    queryRepayOrderDetail({
      repayOrderId,
    })
      .then((res) => {
        if (res.status === 'SUCCEEDED') {
          queryRepaySubOrderListFn();
        }
        setRepayDetail(res);
      })
      .catch(() => {
        Modal.alert({
          title: '还款详情查询失败',
          onConfirm: () => {
            LinkUtil.popPage();
          },
        });
      })
      .finally(() => {
        setInited(true);
      });
  }, []);

  if (!inited) {
    return <FullLoading visible />;
  }

  if (!repayOrderDetail) return null;

  // const
  if (repayOrderDetail.status === 'INSTITUTION_REPAYING') {
    return (
      <div className={styles.repayOrderDetail}>
        <CommonResult isSSD={isSSD} title="还款正在处理中，请稍候" />
        <DetailItemList
          data={[
            {
              label: '申请金额',
              value: amountFormat(repayOrderDetail.applyAmount),
            },
            {
              label: '申请时间',
              value: dayjsFormat(repayOrderDetail.applyTime),
            },
          ]}
        />
      </div>
    );
  }

  if (repayOrderDetail.status === 'INSTITUTION_REPAY_FAIL') {
    const desc = getFailedMsg(getFailedCodeFromOrder(repayOrderDetail));
    return (
      <div className={styles.repayOrderDetail}>
        <div className={classnames([styles.failedCard, 'common-card'])}>
          <div className={styles.mainCardTitle}>还款金额 (元)</div>
          <div className={styles.mainCardAmount}>
            {amountFormat(repayOrderDetail.repaidTotalAmount)}
          </div>
          <div className={styles.faildReason}>{desc}</div>
          <div className="common-divide-line" />
          <div className={styles.repayDetailInfoContent}>
            <DetailItem
              label="申请金额"
              value={<span>¥{amountFormat(repayOrderDetail.applyAmount)}</span>}
            />
            <DetailItem label="申请时间" value={dayjsFormat(repayOrderDetail.applyTime)} />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={classnames([styles.repayOrderDetail, isSSD && styles.isSSD])}>
      <CommonFixedNavBar bgColor="#f3f6f8" />
      <div className={classnames([styles.mainCard, 'common-card'])}>
        <div className={styles.title}>还款金额 (元)</div>
        <div className={styles.repayAmount}>{amountFormat(repayOrderDetail.repaidTotalAmount)}</div>
        <div className="common-divide-line" />
        <div className={styles.detailItemWrap}>
          <DetailItem
            label="已还本金"
            value={<span>¥{amountFormat(repayOrderDetail.repaidPrincipal)}</span>}
          />
          <DetailItem
            label="已还利息"
            value={<span>¥{amountFormat(repayOrderDetail.repaidInterest)}</span>}
          />
          {isGreaterThan0(repayOrderDetail.repaidPenalty) && (
            <DetailItem
              label="已还罚息"
              value={<span>¥{amountFormat(repayOrderDetail.repaidPenalty)}</span>}
            />
          )}
          <DetailItem label="还款时间" value={dayjsFormat(repayOrderDetail.institutionEndTime)} />
        </div>
      </div>
      {!_.isEmpty(repaySubOrderData?.dataList) && (
        <Fragment>
          <div className={styles.repayOrderDetailTitle2}>
            该还款对应以下<span>{repaySubOrderData?.totalRecord}笔</span>借款
          </div>
          {_.map(repaySubOrderData?.dataList, (item) => {
            return (
              <div
                key={item.loanContractId}
                className={classnames([styles.loanContractItem, 'common-card'])}
              >
                <div className={styles.loanContractItemTitle}>
                  本次还款
                  <span className={styles.loanContractItemAmount}>
                    ¥{amountFormat(item.repaidTotalAmount)}
                  </span>
                </div>
                <div className="common-divide-line" />
                <div className={styles.installmentDetailTop}>
                  <span className={styles.installmentDetailDateAndAmount}>
                    {dayjsYYMD(item.institutionEndTime)}借{amountFormat(item.loanedAmount)}元
                    <span className="common-divide-line-vertical" />
                    {REPAYMENT_METHOD[item.repaymentMethod]}
                  </span>
                  <span className="flex-align-center">
                    <a
                      className={isSSD && styles.ssdBtn}
                      onClick={() => {
                        LinkUtil.pushPage(PAGES.LoanDetail, { loanOrderId: item.loanOrderId });
                      }}
                    >
                      借款详情
                    </a>
                    <ArrowIcon />
                  </span>
                </div>
              </div>
            );
          })}
        </Fragment>
      )}
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '还款详情',
  spm: {
    spmB: PAGES.RepayDetail,
  },
}));
