.isSSD {
  --primary: #1677ff;
}
.ssdBtn {
  color: #1677ff;
}
.repayOrderDetail {
  .failedCard {
    text-align: center;
    .faildReason {
      color: #7c889c;
      margin-top: 12rpx;
      margin-bottom: 12rpx;
    }
    .repayDetailInfoContent {
      margin-top: 12rpx;
    }
    .mainCardTitle {
      font-size: 26rpx;
    }
    .mainCardAmount {
      margin-top: 12rpx;
      margin-bottom: 12rpx;
      font-size: 60rpx;
      font-weight: 500;
      font-family: "ALIBABA NUMBER FONT MD";
    }
  }
  .mainCard {
    text-align: center;
    .repayAmount {
      margin-top: 12rpx;
      font-size: 60rpx;
      font-weight: 500;
      font-family: "ALIBABA NUMBER FONT MD";
    }
    .detailItemWrap {
      margin-top: 12rpx;
    }
  }
  .repayOrderDetailTitle2 {
    color: #7c889c;
    font-weight: 500;
    margin-bottom: 24rpx;
    span {
      color: #111;
    }
  }
  .loanContractItem {
    .loanContractItemTitle {
      margin-bottom: 24rpx;
      display: flex;
      align-items: center;
      line-height: 26rpx;
      font-size: 26rpx;
    }
    .loanContractItemAmount {
      font-size: 36rpx;
      font-weight: 500;
      font-family: "ALIBABA NUMBER FONT MD";
      margin-top: -4rpx;
      margin-left: 6rpx;
    }
    .installmentDetailTop {
      margin-top: 24rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 40rpx;
      .installmentDetailDateAndAmount {
        font-size: 26rpx;
        line-height: 26rpx;
        color: #50607a;
        font-family: "ALIBABA NUMBER FONT RG";
        display: flex;
        align-items: center;
      }
    }
  }
}
