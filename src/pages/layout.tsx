import { Fragment, useCallback, useEffect } from 'react';
import { Outlet, useData, defineDataLoader } from 'ice';
import { NavBar } from '@ali/uni-api';
import CommonNavBar from '@/components/CommonNavBar';
import { PAGES } from '@/common/constant';
import { getPageName, _, isThemis, isTbApp } from '@/utils';
import { initAes } from '@/utils/aes';
import classnames from 'classnames';

import '@/assets/styles/aliyun.css';

const PageClassMap = {
  [PAGES.Index]: 'common-page-bg-white',
  [PAGES.Home]: 'common-page-bg-1',
  [PAGES.CreditLp]: 'common-page-bg-1',
  [PAGES.CreditSign]: 'common-page-identify',
  [PAGES.LoanSign]: 'common-page-identify',
  [PAGES.CenterSignedAgreements]: 'common-page-bg-white',
  [PAGES.CenterPhoneManage]: 'common-page-bg-white',
  [PAGES.CenterPhoneUpdate]: 'common-page-bg-white',
  [PAGES.CreditFallback]: 'common-page-bg-1',
  [PAGES.CreditResult]: 'common-page-bg-white',
  // [PAGES.LoanResult]: 'common-page-bg-white',
  [PAGES.RepayResult]: 'common-page-bg-white',
  [PAGES.BindCard]: 'common-page-bg-white',
  [PAGES.LoanApply]: 'common-page-bg-3',
};

const NoSafeAreaPages = [
  PAGES.BindCard,
  PAGES.CreditInstitution,
  PAGES.RepayBatchApply,
  PAGES.RecordList,
  PAGES.RepayEarlyApply,
  PAGES.CenterPhoneUpdate,
  PAGES.CenterCertUpdate,
  PAGES.InstList,
  PAGES.LoanApply,
  PAGES.CreditIdentity,
  PAGES.CreditNewIdentity,
  PAGES.CenterProfileSign,
  PAGES.CreditSign,
  PAGES.LoanSign,
  PAGES.SignedAgreementPreview,
  PAGES.CenterDiscountCoupon,
  PAGES.CenterInvalidCoupon,
];

export default function Layout() {
  const data = useData() || {};

  const handleSetPageTitleImg = useCallback(async () => {
    if (
      _.includes([PAGES.Home, PAGES.CreditLp, PAGES.CreditFallback], getPageName(data.pathname))
    ) {
      try {
        await NavBar.setTitleImage({
          url: 'https://gw.alicdn.com/imgextra/i3/O1CN01o1lBeN1M5SlR1r7Fl_!!6000000001383-2-tps-138-66.png',
        });
      } catch (error) {}
    }
  }, []);

  useEffect(() => {
    handleSetPageTitleImg();
    // 初始化 AES 埋点
    initAes();
  }, []);

  if (!isTbApp()) {
    return null;
  }

  return (
    <div
      className={classnames(
        'layout',
        PageClassMap[getPageName(data.pathname)] || 'common-page-bg-gray',
      )}
    >
      {isThemis() && !_.includes(NoSafeAreaPages, getPageName(data.pathname)) && (
        <Fragment>
          <div className="safe-area-inset-top" />
          <div className="navbar-height" />
          {data.pathname && <CommonNavBar pathname={data.pathname} />}
        </Fragment>
      )}
      <div className="page-container">
        <Outlet />
      </div>
    </div>
  );
}

export const dataLoader = defineDataLoader(async (ctx) => {
  return ctx;
});
