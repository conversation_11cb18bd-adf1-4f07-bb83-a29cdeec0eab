/**
 * @file 404页面
 */

import { Button } from 'antd-mobile';
import classNames from 'classnames';

import styles from './index.module.scss';

export default function NotFound() {
  return (
    <div className={styles.notFound}>
      <img
        className={styles.img}
        src="https://gw.alicdn.com/imgextra/i1/O1CN01zOnG411ydJR9Y0DSB_!!6000000006601-2-tps-862-506.png"
      />
      <p className={styles.title}>网络无法连接</p>
      <p className={styles.desc}>请刷新页面或检查网络设置</p>
      <Button color="primary" className={styles.button}>
        刷新重试
      </Button>
      <Button color="default" className={classNames(styles.button, styles.default)}>
        返回
      </Button>
    </div>
  );
}
