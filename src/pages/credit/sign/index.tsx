/**
 * @file 授信核身页面
 */

import { useCallback, useEffect, useState } from 'react';
import { definePageConfig } from 'ice';
import { Modal, Toast } from 'antd-mobile';
import Authentication from '@/components/Authentication';
import { useCreditSubRouter } from '@/hooks';
import { _ } from '@/utils';
import { PAGES } from '@/common/constant';
import LinkUtil from '@/utils/link';
import { log } from '@alife/dtao-iec-spm-log';
import FullLoading from '@/components/FullLoading';
import { queryCreditOrder } from '@/store/credit/actions';
import { decideRouterPage } from '@/hooks/useCreditSubRouter';
import styles from './index.module.scss';
import { creditSignPageLog } from '@/utils/goc';

export default function CreditSign() {
  const { creditApplyOrderData, inited: subRouterInited } = useCreditSubRouter();
  const [loading, setLoading] = useState(false);
  const creditApplyOrderId = _.get(creditApplyOrderData, 'creditApplyOrderId');
  const authenticationToken = _.get(creditApplyOrderData, 'applicationData.authentication.token');

  if (subRouterInited && !authenticationToken) {
    Modal.alert({
      title: '服务器开小差',
      content: '请稍后重试',
    });
  }

  const handleSuccess = useCallback(() => {
    log.addSuccessLog('credit-sign-auth');
    // 这里可以不用再setLoading了
    setLoading(true);
    queryOrder();
  }, [creditApplyOrderData]);

  const queryOrder = useCallback(async () => {
    const res = await queryCreditOrder({
      creditApplyOrderId,
    });
    const targetPage = decideRouterPage(res);
    if (targetPage === PAGES.CreditInstitution || targetPage === PAGES.CreditResult) {
      LinkUtil.locationReplace(targetPage, {
        creditApplyOrderId,
      });
    } else {
      setTimeout(queryOrder, 2000);
    }
  }, [creditApplyOrderId]);

  const handleFailed = useCallback(() => {
    log.addErrorLog('credit-sign-auth');
    Toast.show({
      icon: 'fail',
      content: '核身失败',
    });
  }, []);

  useEffect(() => {
    creditSignPageLog({
      creditApplyOrderId,
      authenticationToken,
    });
  }, []);

  return (
    <div className={styles.creditSignPage}>
      <FullLoading visible={loading} />
      <div className={styles.bgTop} />
      <Authentication
        authenticationToken={authenticationToken}
        onSuccess={handleSuccess}
        onFail={handleFailed}
      />
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '实人认证',
  spm: {
    spmB: PAGES.CreditSign,
  },
  window: {
    navBarImmersive: false,
  },
}));
