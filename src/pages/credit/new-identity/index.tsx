
import { useEffect, useRef, useState, useCallback } from 'react';
import { definePageConfig } from 'ice';
import { Button, Form, Toast, Modal } from 'antd-mobile';

import { useSmsCodeCount, useVisibilityChangeRefresh } from '@/hooks';
import FullLoading from '@/components/FullLoading';
import Authentication from '@/components/Authentication';
import classnames from 'classnames';

import { _, getMsgByErrorCode, addAntdFormErrorLog, getErrorCodeFromRejectRes } from '@/utils';
import { useDebounceFn } from 'ahooks';
import useQueryCreditOrder from '@/hooks/useQueryCreditOrder';
import { APPLICATION_DATA_FIELD_MAP as FIELD_MAP } from '@/store/credit/model';
import { sendSmsCode, checkSmsCode } from '@/store/common/actions';
import { postCreditApplySupplement } from '@/store/credit/actions';
import { getInsititutionCodeList } from '@/store/credit/lib';
import {
  PAGES,
  SMSCODE_REG,
  SMS_BASE_PARAMS,
  CREDIT_SUPPLEMENT_ERROR_CODE,
} from '@/common/constant';
import LinkUtil from '@/utils/link';
import Verified from './components/verified';
import WaitVerify from './components/wait-verify';
import {
  TbPlatformRealPersonIdentifyAgreementPopup,
  UnsignedAgreementsAliyunRef,
  UnsignedAgreementsAliyun,
} from '@/components';
import styles from './index.module.scss';
import { log } from '@alife/dtao-iec-spm-log';

export default function CreditIdentityIdentityVerified() {
  const { creditApplyOrderData, queryCreditOrderFn, inited } = useQueryCreditOrder();
  const [loadingVisible, setLoadingVisible] = useState(false);
  const [curSmsCodeData, setCurSmsCodeData] = useState<any>(null);
  const [formBtnDisabled, setFormBtnDisabled] = useState(true);
  const [showSmsCodeItem, setShowSmsCodeItem] = useState(true);
  const [isVerified, setIsVerified] = useState<null | boolean>(null);
  const agreementRef = useRef<UnsignedAgreementsAliyunRef>();
  const tbPlatformRealPersonIdentifyAgreementPopupRef = useRef<any>(null);

  const [form] = Form.useForm();
  const { startCount, ...countData } = useSmsCodeCount({
    reSendTxt: '发送验证码',
    countingTextRender: (count) => `${count}s后重试`,
  });

  const authenticationToken = _.get(creditApplyOrderData, 'applicationData.authentication.token');
  const creditApplyOrderId = _.get(creditApplyOrderData, 'creditApplyOrderId');

  const { run: handleSubmit } = useDebounceFn(
    async () => {
      log.addClickLog('identity-submit', { isVerified });

      // 表单校验
      const values = await executeFormValidate();
      if (!values) return;

      const applicationData = {
        ...values,
      };

      _.set(applicationData, FIELD_MAP.licenseType, 'IDENTITY_CARD');

      setLoadingVisible(true);

      if (showSmsCodeItem) {
        log.addOtherLog('smscode-check', { isVerified });
        try {
          const smsCodeCheckRes: any = await checkSmsCode({
            ...curSmsCodeData,
            code: values.smscode,
            phone: _.get(values, FIELD_MAP.phone),
          });
          if (!smsCodeCheckRes.result) {
            throw new Error('SMSCODE_CHECK_FAILED');
          }
        } catch (error) {
          log.addErrorLog('smscode-check', { error: error.message, isVerified });
          Toast.show('验证码错误');
          setLoadingVisible(false);
          return;
        }
      }

      // 字段处理，身份证转大写
      const licenseNo = String(_.get(applicationData, FIELD_MAP.licenseNo)).trim();
      const licenseName = String(_.get(applicationData, FIELD_MAP.licenseName)).replace(/\s/g, '');

      _.set(applicationData, FIELD_MAP.licenseNo, licenseNo.toUpperCase());
      _.set(applicationData, FIELD_MAP.licenseName, licenseName);

      try {
        const supplementRes: any = await postCreditApplySupplement({
          applicationData: JSON.stringify(applicationData),
          currentSubStatus: _.get(creditApplyOrderData, 'subStatus'),
          creditApplyOrderId: _.get(creditApplyOrderData, 'creditApplyOrderId'),
          channel: _.get(creditApplyOrderData, 'channel'),
        });

        // 这里可能会跳过核身， 直接到WAIT_TO_SELECT_INSTITUTION
        if (_.includes(['WAIT_TO_AUTH', 'WAIT_TO_SELECT_INSTITUTION'], supplementRes.subStatus)) {
          const res: any = await queryCreditOrderFn();
          if (res.subStatus === 'WAIT_TO_SELECT_INSTITUTION') {
            agreementRef.current?.show({
              action: 'compulsory',
            });
            setLoadingVisible(false);
          }

          if (res.subStatus === 'WAIT_TO_AUTH') {
            // 会自动更新authenticationToken，这里不需要处理
          }
          log.addSuccessLog('post-credit-supplement', { isVerified });
        } else if (supplementRes.subStatus === 'FAILED') {
          handleSupplementFailed();
        } else {
          throw new Error(getErrorCodeFromRejectRes(supplementRes));
        }
      } catch (e) {
        handleSupplementError(e);
      } finally {
        setLoadingVisible(false);
      }
    },
    {
      wait: 1000,
      leading: true,
      trailing: false,
    },
  );

  const handlePhoneModifyBtnClick = () => {
    log.addClickLog('phone-modify-btn-click', { isVerified });
    setShowSmsCodeItem(true);
    setFormBtnDisabled(true);
    form.setFieldValue(FIELD_MAP.phone, '');
  };

  const executeFormValidate = async (name?) => {
    let validateResult;
    try {
      validateResult = await form.validateFields(name ? [name] : undefined);
      return validateResult;
    } catch (error) {
      addAntdFormErrorLog(error.errorFields);
    }
  };

  const { run: handleSendSmsCodeBtnClick } = useDebounceFn(
    async () => {
      log.addClickLog('send-sms-code-btn-click', { isVerified });
      if (countData.smsBtnDisabled) {
        return;
      }

      const validateResult = await executeFormValidate(FIELD_MAP.phone);
      if (!validateResult) {
        Toast.show('请先输入手机号');
        return;
      }
      const phoneValue = _.get(validateResult, FIELD_MAP.phone);

      try {
        const sendRes: any = await sendSmsCode({
          phone: phoneValue,
          ...SMS_BASE_PARAMS,
        });
        if (sendRes.result) {
          setCurSmsCodeData({
            ...SMS_BASE_PARAMS,
            requestId: sendRes.requestId,
          });
          Toast.show('短信发送成功');
          startCount();
        } else {
          throw new Error();
        }
      } catch (error) {
        log.addErrorLog('send-smscode-error', { error, isVerified });
        Toast.show('短信发送失败');
      }
    },
    {
      wait: 1000,
      leading: true,
      trailing: false,
    },
  );

  const handleValuesChange = (fieldObj, allValues) => {
    const smsCodeValue = _.get(allValues, 'smscode');
    if (SMSCODE_REG.test(smsCodeValue)) {
      setFormBtnDisabled(false);
    }
  };

  const handleSupplementFailed = () => {
    setTimeout(() => {
      LinkUtil.popPage();
    }, 500);
  };

  const handleSupplementError = (e) => {
    const code = _.get(e, 'errorList[0].code') || e.message;
    log.addErrorLog('credit-new-identity-supplement-error', { code, isVerified });
    Modal.alert({
      title: '提交失败',
      content: getMsgByErrorCode(code, CREDIT_SUPPLEMENT_ERROR_CODE),
    });
  };

  const handleAgreementsCompleted = useCallback(
    async ({ institutionList }) => {
      log.addOtherLog('credit-agreements-completed', { isVerified });
      setLoadingVisible(true);

      try {
        const res = await postCreditApplySupplement({
          currentSubStatus: creditApplyOrderData?.subStatus,
          creditApplyOrderId: creditApplyOrderData?.creditApplyOrderId,
          selectedInstitutionList: institutionList,
        });
        if (res.subStatus === 'INSTITUTION_CREDITING') {
          Toast.show('提交成功');
          setTimeout(() => {
            LinkUtil.popPage();
          }, 500);
        } else if (res.subStatus === 'FAILED') {
          handleSupplementFailed();
        } else {
          throw new Error(getErrorCodeFromRejectRes(res));
        }
      } catch (e) {
        handleSupplementError(e);
      } finally {
        setLoadingVisible(false);
      }
    },
    [creditApplyOrderData, isVerified],
  );

  useEffect(() => {
    if (!inited) return;
    const isVerifiedStatus = _.includes(
      ['REAL_NAME_VERIFIED', 'WAIT_TO_AUTH'],
      _.get(creditApplyOrderData, 'subStatus'),
    );
    log.addVisitLog(PAGES.CreditNewIdentity, {
      subStatus: _.get(creditApplyOrderData, 'subStatus'),
      isVerified: isVerifiedStatus,
    });

    setIsVerified(isVerifiedStatus);
    const applicationData = _.get(creditApplyOrderData, 'applicationData');
    if (applicationData) {
      const phoneNo = _.get(applicationData, 'phone.phoneNo');
      const licenseName = _.get(applicationData, 'license.name');
      const licenseNo = _.get(applicationData, 'license.licenseNo');
      form.setFieldValue(FIELD_MAP.phone, phoneNo);
      form.setFieldValue(FIELD_MAP.licenseName, licenseName);
      form.setFieldValue(FIELD_MAP.licenseNo, licenseNo);
      setFormBtnDisabled(!phoneNo);
      // setShowSmsCodeItem(!phoneNo);
    }
  }, [inited]);

  const handleAuthFailed = () => {
    log.addErrorLog('credit-sign-auth', { isVerified });
    Toast.show({
      icon: 'fail',
      content: '核身失败',
    });
  };

  const pollingQueryOrder = useCallback(async () => {
    const res = await queryCreditOrderFn();
    if (res?.subStatus === 'WAIT_TO_SELECT_INSTITUTION') {
      log.addOtherLog('credit-new-identity-wait-to-select-institution', { isVerified });
      agreementRef.current?.show({
        action: 'compulsory',
      });
      setLoadingVisible(false);
      return;
    }
    if (res?.subStatus === 'INSTITUTION_CREDITING' || res?.subStatus === 'FAILED') {
      LinkUtil.popPage();
      return;
    }

    setTimeout(pollingQueryOrder, 2000);
  }, [creditApplyOrderId, isVerified]);

  const handleAuthSuccess = () => {
    log.addSuccessLog('sign-auth', { isVerified });
    setLoadingVisible(true);
    pollingQueryOrder();
  };

  useVisibilityChangeRefresh(() => {
    queryCreditOrderFn();
  });

  if (!creditApplyOrderData) {
    if (!inited) {
      return <FullLoading visible />;
    } else {
      setTimeout(LinkUtil.popPage, 1500);
      return null;
    }
  }

  if (isVerified === null) {
    return <FullLoading visible />;
  }

  return (
    <div className={classnames([styles.creditIdentity, isVerified && styles.isVerified])}>
      <FullLoading visible={loadingVisible} />
      {isVerified ? (
        <Verified
          creditApplyOrderData={creditApplyOrderData}
          countData={countData}
          onSendSmsCodeBtnClick={handleSendSmsCodeBtnClick}
          onPhoneModifyBtnClick={handlePhoneModifyBtnClick}
          form={form}
          onValuesChange={handleValuesChange}
          showSmsCodeItem={showSmsCodeItem}
        />
      ) : (
        <WaitVerify
          countData={countData}
          onSendSmsCodeBtnClick={handleSendSmsCodeBtnClick}
          onPhoneModifyBtnClick={handlePhoneModifyBtnClick}
          form={form}
          onValuesChange={handleValuesChange}
          showSmsCodeItem={showSmsCodeItem}
        />
      )}

      <div className="page-bottom-bar">
        <Button disabled={formBtnDisabled} onClick={handleSubmit} block color="primary">
          确认信息并扫脸
        </Button>
      </div>
      <Authentication
        directly
        authenticationToken={
          creditApplyOrderData.subStatus === 'WAIT_TO_AUTH' ? authenticationToken : null
        }
        onSuccess={handleAuthSuccess}
        onFail={handleAuthFailed}
      />
      <UnsignedAgreementsAliyun
        bizType="CREDIT"
        institutionList={getInsititutionCodeList(
          _.get(creditApplyOrderData, 'institutionDecideResultVOList'),
        )}
        ref={agreementRef}
        renderTitle={() => null}
        popupTitle="授信机构相关协议"
        onCompleted={handleAgreementsCompleted}
        onClose={() => LinkUtil.popPage()}
      />
      <TbPlatformRealPersonIdentifyAgreementPopup
        ref={tbPlatformRealPersonIdentifyAgreementPopupRef}
      />
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '实名信息',
  spm: {
    spmB: PAGES.CreditNewIdentity,
  },
  window: {
    navBarImmersive: false,
    navBarBgColor: '#f3f6f8',
    pageBgColor: '#f3f6f8',
  },
}));
