import { useCallback, useState } from 'react';
import { Form } from 'antd-mobile';
import classnames from 'classnames';
import { _, fieldDesensitization } from '@/utils';
import { APPLICATION_DATA_FIELD_MAP as FIELD_MAP } from '@/store/credit/model';
import { SMSCODE_REG, PHONE_REG } from '@/common/constant';
import { PhoneNumberInput, SmsCodeInput } from '@/components';
import styles from './index.module.scss';

export default function ({
  creditApplyOrderData,
  countData,
  onSendSmsCodeBtnClick,
  onPhoneModifyBtnClick,
  form,
  onValuesChange,
  showSmsCodeItem,
}) {
  const [smsCodeDisabled, setSmsCodeDisabled] = useState(true);

  const licenseName = _.get(creditApplyOrderData?.applicationData, FIELD_MAP.licenseName);
  const licenseNo = _.get(creditApplyOrderData?.applicationData, FIELD_MAP.licenseNo);
  const taobaoNick = _.get(creditApplyOrderData, 'extension.taobaoNick');

  const handleValuesChange = (fieldObj, allValues) => {
    const phoneValue = _.get(allValues, FIELD_MAP.phone);
    const smsCodeValue = _.get(allValues, 'smscode');

    if (PHONE_REG.test(phoneValue)) {
      setSmsCodeDisabled(false);
      if (SMSCODE_REG.test(smsCodeValue)) {
        onValuesChange(fieldObj, allValues);
      }
    } else {
      setSmsCodeDisabled(true);
    }
  };
  const PhoneNumberInputRender = useCallback((props) => {
    return (
      <div className={styles.PhoneNumberInputWrap}>
        {showSmsCodeItem ? (
          <PhoneNumberInput {...props} disabled={!showSmsCodeItem} placeholder="请填写手机号" />
        ) : (
          <span className={styles.phoneNoValue}>
            {_.get(creditApplyOrderData?.applicationData, 'phone.phoneNo')}
          </span>
        )}
        {!showSmsCodeItem && (
          <div>
            <a onClick={onPhoneModifyBtnClick}>修改</a>
          </div>
        )}
      </div>
    );
  }, [showSmsCodeItem]);

  return (
    <div className={styles.verifiedFormCard}>
      <div className={styles.taobaoAvatar}>
        <img src="https://gw.alicdn.com/imgextra/i3/O1CN01vYWzZ81uIvN0NJgfF_!!6000000006015-2-tps-160-160.png" />
      </div>
      <div className={styles.verifiedFormCardTitle}>
        您淘宝账号{taobaoNick}的实名认证信息如下，将用于申请借钱额度
      </div>
      <Form className={styles.newIndentityForm} form={form} onValuesChange={handleValuesChange}>
        <div className={styles.infoForm}>
          <div className={styles.infoFormItem}>
            <div className={styles.infoFormItemLabel}>姓名</div>
            <div className={styles.infoFormItemValue}>
              <Form.Item noStyle name={FIELD_MAP.licenseName}>
                {licenseName && fieldDesensitization(licenseName, 0, 1)}
              </Form.Item>
            </div>
          </div>
          <div className={styles.infoFormItem}>
            <div className={styles.infoFormItemLabel}>身份证号</div>
            <div className={styles.infoFormItemValue}>
              <Form.Item noStyle name={FIELD_MAP.licenseNo}>
                {licenseNo && fieldDesensitization(licenseNo, 1, 2)}
              </Form.Item>
            </div>
          </div>
          <div className={styles.infoFormItem}>
            <div className={styles.infoFormItemLabel}>手机号</div>
            <div className={styles.infoFormItemValue}>
              <Form.Item
                noStyle
                name={FIELD_MAP.phone}
                rules={[
                  {
                    required: true,
                    message: '请输入手机号',
                  },
                  {
                    pattern: PHONE_REG,
                    message: '手机号码格式错误',
                  },
                ]}
                className={styles.smsCodeFormItem}
              >
                <PhoneNumberInputRender />
              </Form.Item>
            </div>
          </div>
          {showSmsCodeItem && (
            <div className={styles.infoFormItem}>
              <div className={styles.infoFormItemLabel}>验证码</div>
              <div className={styles.infoFormItemValue}>
                <div className={styles.smsCodeInputWrap}>
                  <Form.Item
                    name={'smscode'}
                    rules={[
                      {
                        required: true,
                        message: '请输入验证码',
                      },
                      {
                        pattern: SMSCODE_REG,
                        validateTrigger: 'onBlur',
                        message: '验证码格式错误',
                      },
                    ]}
                  >
                    <SmsCodeInput placeholder="请填写验证码" />
                  </Form.Item>
                  <span
                    onClick={onSendSmsCodeBtnClick}
                    className={classnames({
                      [styles.smsCodeBtn]: true,
                      [styles.smsCodeBtnDisabled]: smsCodeDisabled || countData.smsBtnDisabled,
                    })}
                  >
                    {countData.smsBtnTxt}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>
      </Form>
    </div>
  );
}
