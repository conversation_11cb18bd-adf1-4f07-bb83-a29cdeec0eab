.verifiedFormCardTitle {
  font-size: 30rpx;
  margin-bottom: 24rpx;
  color: #11192d;
}
.verifiedFormCard {
  background: #fff;
  padding: 48rpx;
  border-radius: 24rpx;
  position: relative;
  z-index: 100;
}
.taobaoAvatar {
  text-align: center;
  margin-bottom: 42rpx;
  img {
    width: 80rpx;
    height: 80rpx;
  }
}

.infoForm {
  border-radius: 12rpx;
  border: 1rpx solid #e5e8ec;
  padding: 0 32rpx;
  margin-top: 2rpx;
  .infoFormItem {
    padding: 20rpx 0;
    display: flex;
    position: relative;
    height: 80rpx;
    line-height: 40rpx;
    &:not(:last-child) {
      &::after {
        content: "";
        position: absolute;
        display: block;
        width: 100%;
        height: 1rpx;
        bottom: 0;
        left: 0;
        background: #e5e8ec;
      }
    }
    .infoFormItemLabel {
      font-size: 26rpx;
      color: #11192d;
      flex: 0 0 128rpx;
    }
    .infoFormItemValue {
      color: #7c889c;
      font-size: 26rpx;
      display: flex;
      align-items: center;
      flex: 1 1 auto;
      > * {
        width: 100%;
      }
      .smsCodeInputWrap {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .smsCodeBtn {
        color: #11192d;
        font-size: 26rpx;
        white-space: nowrap;
      }
      .smsCodeBtnDisabled {
        color: #7c889c;
      }
      :global {
        .adm-input {
          padding: 0;
          background-color: #fff;
        }
        .adm-list-item-description {
          display: none;
        }
      }
    }
  }
}

.PhoneNumberInputWrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  white-space: nowrap;
}

.phoneNoValue {
  color: #11192d;
}
