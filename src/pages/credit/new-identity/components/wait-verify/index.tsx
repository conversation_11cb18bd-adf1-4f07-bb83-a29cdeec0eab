import { useState } from 'react';
import { Form, Input } from 'antd-mobile';
import classnames from 'classnames';
import { _ } from '@/utils';
import { APPLICATION_DATA_FIELD_MAP as FIELD_MAP } from '@/store/credit/model';
import { SMSCODE_REG, PHONE_REG } from '@/common/constant';
import { regex } from '@ali/iec-dtao-utils';
import { PhoneNumberInput, SmsCodeInput } from '@/components';
import styles from './index.module.scss';

export default function ({
  countData,
  onSendSmsCodeBtnClick,
  onPhoneModifyBtnClick,
  form,
  onValuesChange,
  showSmsCodeItem,
}) {
  const [smsCodeDisabled, setSmsCodeDisabled] = useState(true);

  const handleValuesChange = (fieldObj, allValues) => {
    const phoneValue = _.get(allValues, FIELD_MAP.phone);
    const smsCodeValue = _.get(allValues, 'smscode');

    if (PHONE_REG.test(phoneValue)) {
      setSmsCodeDisabled(false);
      if (SMSCODE_REG.test(smsCodeValue)) {
        onValuesChange(fieldObj, allValues);
      }
    } else {
      setSmsCodeDisabled(true);
    }
  };
  return (
    <>
      <div className={classnames([styles.creditIdentityTitle1, 'page-title-1'])}>
        请完善个人信息
      </div>
      <div className={styles.creditIdentityTips}>
        <img
          className={styles.creditIdentityTipsIcon}
          src="https://gw.alicdn.com/imgextra/i2/O1CN01IbtYzO1w2YRZovGuV_!!6000000006250-2-tps-64-64.png"
        />
        <div className={styles.creditIdentityTipsText}>
          根据法律法规规定，您需要进一步完善个人基本信息，
          <span className={styles.creditIdentityTipsTextBold}>
            该信息将作为您的账号认证信息，请务必谨慎填写。
          </span>
        </div>
      </div>
      <Form className={styles.newIndentityForm} form={form} onValuesChange={handleValuesChange}>
        <div className="page-title-2">
          <span>姓名</span>
        </div>
        <Form.Item
          name={FIELD_MAP.licenseName}
          rules={[
            {
              required: true,
              message: '请输入姓名',
            },
          ]}
        >
          <Input placeholder="请输入姓名" />
        </Form.Item>
        <div className="page-title-2">
          <span>身份证号</span>
        </div>
        <Form.Item
          name={FIELD_MAP.licenseNo}
          rules={[
            {
              required: true,
              message: '请输入身份证号',
            },
            {
              pattern: regex.ID_CARD_IN_LAND,
              message: '身份证号格式错误',
            },
          ]}
        >
          <Input placeholder="请输入身份证号" />
        </Form.Item>
        <div className="page-title-2">
          <span>手机号</span>
        </div>
        <Form.Item
          name={FIELD_MAP.phone}
          rules={[
            {
              required: true,
              message: '请输入手机号',
            },
            {
              pattern: PHONE_REG,
              message: '手机号码格式错误',
            },
          ]}
          className={styles.smsCodeFormItem}
          extra={
            !showSmsCodeItem && (
              <a onClick={onPhoneModifyBtnClick} className={styles.phoneNoModifyBtn}>
                修改
              </a>
            )
          }
        >
          <PhoneNumberInput disabled={!showSmsCodeItem} placeholder="请填写手机号" />
        </Form.Item>
        {showSmsCodeItem && (
          <>
            <div className="page-title-2">
              <span>验证码</span>
            </div>
            <Form.Item
              name={'smscode'}
              rules={[
                {
                  required: true,
                  message: '请输入验证码',
                },
                {
                  pattern: SMSCODE_REG,
                  validateTrigger: 'onBlur',
                  message: '验证码格式错误',
                },
              ]}
              className={styles.smsCodeFormItem}
              extra={
                <span
                  onClick={onSendSmsCodeBtnClick}
                  className={classnames({
                    'sms-code-btn': true,
                    disabled: smsCodeDisabled || countData.smsBtnDisabled,
                  })}
                >
                  {countData.smsBtnTxt}
                </span>
              }
            >
              <SmsCodeInput placeholder="请填写验证码" />
            </Form.Item>
          </>
        )}
      </Form>
    </>
  );
}
