/**
 * @file 云极简授信LP首页
 * <AUTHOR>
 */

import {
  Fragment,
  useCallback,
  useEffect,
  useRef,
  useState,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { definePageConfig, defineServerDataLoader, defineDataLoader, ClientOnly } from 'ice';
import { Button, Toast, Modal } from 'antd-mobile';
import classnames from 'classnames';
import { useDebounceFn } from 'ahooks';
import { log } from '@alife/dtao-iec-spm-log';

import { useVisibilityChangeRefresh } from '@/hooks';
import {
  postCreditApply,
  postCreditApplySupplement,
  queryCreditOrder,
} from '@/store/credit/actions';
import {
  idempotentId,
  _,
  getMsgByErrorCode,
  getErrorCodeFromRejectRes,
  dayjsFormat,
  addMinutes,
  getFailedCodeFromOrder,
} from '@/utils';
import {
  BottomBarWrap,
  IconInstitution,
  RateExplanationPopup,
  FullLoading,
  CommonPopup,
  UnsignedAgreementsAliyun,
  UnsignedAgreementsAliyunRef,
} from '@/components';
import RedPacketOffer from '@/components/Yun/RedPacketOffer';
import PersonalInfoPopup from '@/components/Yun/PersonalInfoPopup';
import { PAGES, CREDIT_APPLY_ERROR_CODE, CREDIT_SUPPLEMENT_ERROR_CODE } from '@/common/constant';
import { APPLICATION_DATA_FIELD_MAP as FIELD_MAP, checkAliyunCreditRouter } from '@/store/credit/model';
import Authentication from '@/components/Authentication';
import BorrowingGuidance from '@/components/BorrowingGuidance';
import LinkUtil, { navigatorOpenURL } from '@/utils/link';
import useCountDown from '@/hooks/useCountDown';
import { useFSPData } from '@/hooks/useFSPData';
import { creditLpSSR, creditLpCSR, CreditLpFSPData } from '@/services/credit-lp';
import useCreditConsult from '@/hooks/useCreditConsult';
import { getInsititutionCodeList } from '@/store/credit/lib';
import { genApplyExposePlatformPromotionOfferStr } from '@/store/lib/format';
import { lpAliyunPageLog } from '@/utils/goc';

import dayjs from 'dayjs';
import Layout from '@/components/Layout/aliyun';

import styles from './index.module.scss';
import { addInitLog } from '@/utils/log';

type PROCESS_ACTION = 'SUBMITING' | 'NULL';

interface CreditLpProps {
  onShowBottomBar: (val: boolean) => void;
  customStyle: any;
}

export const serverDataLoader = defineServerDataLoader(async () => {
  const data = await creditLpSSR();
  return data;
});

export const dataLoader = defineDataLoader(async () => {
  const data = await creditLpCSR('PREFETCH');
  return data;
});

const institutionCreditApplyParams = {
  needInstitutionApplyTime: true,
};

// 缓存用户是否跳转过淘宝认证页面
const XFD_UNIAPP_TO_TB_CERTIFICATION_STORAGE_KEY = 'XFD_UNIAPP_TO_TB_CERTIFICATION_STORAGE_KEY';

function CreditLpComp(props: CreditLpProps, ref) {
  const { onShowBottomBar, customStyle } = props;
  const { fspData } = useFSPData<CreditLpFSPData>({
    init: creditLpCSR,
  });
  const { creditConsultData, queryConsult } = useCreditConsult({
    default: fspData?.data?.creditApplyConsult,
    requestParams: { requestPurpose: 'CREDIT_CONSULT' },
  });

  const [curCreditOrder, setCurCreditOrder] = useState<any>(null);
  const [showSimpleCreditResult, setShowSimpleCreditResult] = useState(false);

  const { data } = fspData || {};
  const rateDeclarePopupRef = useRef<any>(null);
  const [processAction, setProcessAction] = useState<PROCESS_ACTION>();
  const { start, count } = useCountDown({ duration: 1000 });
  const [countStarted, setCountStarted] = useState(false);
  const [loadingVisible, setLoadingVisible] = useState(false);
  const creditAgreementsRef = useRef<UnsignedAgreementsAliyunRef>(null);
  const popupRef = useRef<any>(null);
  const AuthenticationRef = useRef<any>(null);
  const PersonalInfoPopupRef = useRef<any>(null);


  const handleKnowMoreClick = () => {
    log.addClickLog('jieqian-know-more');
    popupRef?.current?.toggleVisible(true);
  };

  const handleInsititutionClick = () => {
    log.addClickLog('institution-click');
    LinkUtil.pushPage(PAGES.InstList);
  };

  const handleButtonClick = async () => {
    log.addClickLog('main-btn-click', {
      isPlatformAgreementSigned: data?.creditApplyConsult?.isPlatformAgreementSigned,
      // subStatus: latestCreditApplyOrder?.subStatus,
    });

    const consultRes = await queryConsult();
    if (!consultRes) {
      Toast.show('申请失败，请稍后再试');
      return;
    }
    const { latestCreditApplyOrder, applyType, creditPlatform, creditType } = consultRes;
    // 如果不准入

    if (latestCreditApplyOrder) {
      if (latestCreditApplyOrder?.status === 'INIT') {
        Toast.show('申请单状态异常，请联系客服');
        return;
      }

      if (latestCreditApplyOrder.subStatus === 'WAIT_TO_AUTH') {
        log.addShowLog('wait-to-auth');
        Toast.show({
          icon: 'loading',
          content: '即将进行人脸认证',
          duration: 1000,
        });
        setTimeout(() => {
          AuthenticationRef.current?.exec(_.get(latestCreditApplyOrder, 'applicationData.authentication.token'));
        }, 1000);
        return;
      }

      if (latestCreditApplyOrder.subStatus === 'WAIT_TO_REAL_NAME_VERIFY') {
        PersonalInfoPopupRef.current.show({
          creditOrderData: latestCreditApplyOrder,
        });
        return;
      }

      if (latestCreditApplyOrder.subStatus === 'REAL_NAME_VERIFIED') {
        LinkUtil.pushPage(PAGES.CreditNewIdentity, {
          creditApplyOrderId: latestCreditApplyOrder.creditApplyOrderId,
        });
        return;
      }

      if (latestCreditApplyOrder?.subStatus === 'WAIT_TO_SELECT_INSTITUTION') {
        creditAgreementsRef.current?.show({
          action: 'compulsory',
        });
        return;
      }

      if (latestCreditApplyOrder?.subStatus === 'INSTITUTION_CREDITING') {
        log.addOtherLog('simple-credit-lp-institution-crediting');
        setCurCreditOrder(latestCreditApplyOrder);
        return;
      }
    }

    setProcessAction('SUBMITING');
    try {
      const postRes = await postCreditApply({
        requestId: idempotentId(),
        applyType,
        creditPlatform,
        applyTime: Date.now(),
        channel: 'APP',
        creditType,
        exposePlatformPromotionOfferList: genApplyExposePlatformPromotionOfferStr(
          consultRes?.platformPromotionOfferList,
        ),
      });
      if (postRes.creditApplyOrderId) {
        log.addSuccessLog('credit-lp-apply-success', { subStatus: postRes.subStatus });
        queryConsult();
        queryCreditOrderFn(postRes.creditApplyOrderId);
        // 重新授信申请成功后 清除跳转淘宝认证页面的缓存
        window.localStorage.removeItem(XFD_UNIAPP_TO_TB_CERTIFICATION_STORAGE_KEY);
      } else {
        throw new Error(getErrorCodeFromRejectRes(postRes));
      }
    } catch (e) {
      log.addErrorLog('credit-lp-apply-error', { code: e.message });
      Modal.alert({
        title: '申请失败',
        content: getMsgByErrorCode(e.message),
      });
    } finally {
      setProcessAction('NULL');
    }
  };

  const { run: handleMainBtnClick } = useDebounceFn(handleButtonClick, {
    wait: 1000,
    leading: true,
    trailing: false,
  });

  const getMainBtnText = () => {
    if (creditConsultData?.isPlatformAgreementSigned) {
      return '申请额度';
    }
    return '同意协议并申请额度';
  };

  const renderRate = () => {
    return (
      <RateExplanationPopup
        interestRate={{
          interestRatePercent:
            data?.creditApplyConsult?.creditDefaultFactor?.minInterestRatePercent,
          dailyInterestRatePercent:
            data?.creditApplyConsult?.creditDefaultFactor?.minDailyInterestRatePercent,
          days: data?.creditApplyConsult?.creditDefaultFactor?.days || 360,
        }}
        ref={rateDeclarePopupRef}
        isLp
      />
    );
  };

  const handleApplyError = (order) => {
    const failCode = getFailedCodeFromOrder(order);
    if (CREDIT_APPLY_ERROR_CODE[failCode]) {
      setShowSimpleCreditResult(true);
    }
    queryConsult();
  };

  const queryCreditOrderFn = async (orderId?, otherParams?) => {
    try {
      const orderRes = await queryCreditOrder({
        creditApplyOrderId: orderId || curCreditOrder?.creditApplyOrderId,
        ...otherParams,
      });
      if (orderRes.status === 'INIT') {
        Toast.show('申请单状态异常，请联系客服');
        return;
      }

      if (orderRes.status === 'SUCCEEDED') {
        LinkUtil.locationReplace(PAGES.Home);
        return;
      }
      if (orderRes.status === 'FAILED') {
        handleApplyError(orderRes);
      }
      if (orderRes.subStatus === 'WAIT_TO_AUTH') {
        log.addShowLog('wait-to-auth');
        Toast.show({
          icon: 'loading',
          content: '即将进行人脸认证',
          duration: 1000,
        });
        setTimeout(() => {
          AuthenticationRef.current?.exec(_.get(orderRes, 'applicationData.authentication.token'));
        }, 1000);
      }

      if (orderRes.subStatus === 'WAIT_TO_REAL_NAME_VERIFY') {
        PersonalInfoPopupRef.current.show({
          creditOrderData: orderRes,
        });
      }

      if (orderRes.subStatus === 'REAL_NAME_VERIFIED') {
        LinkUtil.pushPage(PAGES.CreditNewIdentity, {
          creditApplyOrderId: orderId,
        });
      }

      if (orderRes.subStatus === 'WAIT_TO_SELECT_INSTITUTION') {
        creditAgreementsRef.current?.show({
          action: 'compulsory',
        });
      }

      if (orderRes.subStatus === 'INSTITUTION_CREDITING') {
        log.addShowLog('simple-credit-lp-institution-crediting');

        if (!orderRes?.institutionApplyTime) {
          // 正常不会出现这个情况
          setTimeout(() => {
            queryCreditOrderFn(orderId, institutionCreditApplyParams);
          }, 3000);
          return;
        }

        const afterApplyTimeOneMinute = addMinutes(orderRes?.institutionApplyTime, 1);
        const countMinutes = parseInt(String((+afterApplyTimeOneMinute - +dayjs()) / 1000));

        setTimeout(() => {
          queryCreditOrderFn(orderId, {
            needCreditAuditStatus: countMinutes <= 0,
            ...institutionCreditApplyParams,
          });
        }, 3000);

        if (!countStarted) {
          if (countMinutes && countMinutes > 0) {
            log.addShowLog('institution-crediting-countdown', {
              list: getInsititutionCodeList(orderRes?.institutionDecideResultVOList),
            });
          }
          start(countMinutes);
          setCountStarted(true);
        }
      }
      setCurCreditOrder(orderRes);
    } catch (error) {
      Toast.show('查询申请单失败，请稍后再试');
      log.addErrorLog('query-credit-order-fn', { code: error.message });
    } finally {
      setLoadingVisible(false);
    }
  };

  const handleAuthSuccess = async () => {
    log.addSuccessLog('lp-simple-auth');
    setLoadingVisible(true);

    // 这里处理一下授信单状态未变更，做轮询
    const pollFn = async () => {
      const orderRes = await queryCreditOrder({
        creditApplyOrderId: curCreditOrder?.creditApplyOrderId,
      });

      if (orderRes.subStatus === 'WAIT_TO_SELECT_INSTITUTION') {
        setLoadingVisible(false);
        setCurCreditOrder(orderRes);
        creditAgreementsRef.current?.show({
          action: 'compulsory',
        });
      } else {
        setTimeout(pollFn, 1000);
      }
    };
    pollFn();
  };

  const handleAuthFailed = () => {
    log.addErrorLog('lp-simple-auth-failed');
    Toast.show({
      icon: 'fail',
      content: '核身失败',
    });
  };

  const handleSupplementError = (e) => {
    const code = _.get(e, 'errorList[0].code') || e.message;
    log.addErrorLog('lp-simple-supplement-error', { code });
    const supplementErrorMsg = CREDIT_SUPPLEMENT_ERROR_CODE[code] || CREDIT_SUPPLEMENT_ERROR_CODE.default;

    Modal.alert({
      title: '提交失败',
      content: supplementErrorMsg,
    });
  };

  const handlePersonalInfoSubmit = async (values) => {
    const applicationData = {
      ...values,
    };


    setLoadingVisible(true);


    // 字段处理，身份证转大写
    const licenseNo = String(_.get(applicationData, FIELD_MAP.licenseNo)).trim();
    const licenseName = String(_.get(applicationData, FIELD_MAP.licenseName)).replace(/\s/g, '');

    _.set(applicationData, FIELD_MAP.licenseType, 'IDENTITY_CARD');
    _.set(applicationData, FIELD_MAP.licenseNo, licenseNo.toUpperCase());
    _.set(applicationData, FIELD_MAP.licenseName, licenseName);

    try {
      const supplementRes: any = await postCreditApplySupplement({
        applicationData: JSON.stringify(applicationData),
        currentSubStatus: _.get(curCreditOrder, 'subStatus'),
        creditApplyOrderId: _.get(curCreditOrder, 'creditApplyOrderId'),
        channel: _.get(curCreditOrder, 'channel'),
      });

      if (_.includes(['WAIT_TO_AUTH', 'WAIT_TO_SELECT_INSTITUTION', 'FAILED'], supplementRes.subStatus)) {
        PersonalInfoPopupRef.current.hide();
        await queryCreditOrderFn();
      } else {
        throw new Error(getErrorCodeFromRejectRes(supplementRes));
      }
    } catch (e) {
      handleSupplementError(e);
    } finally {
      setLoadingVisible(false);
    }
  };

  const doInit = () => {
    // CSR逻辑
    if (creditConsultData) {
      addInitLog(fspData);
      // 底部bottomBar展示
      if (creditConsultData?.isPlatformAgreementSigned) {
        onShowBottomBar && onShowBottomBar(true);
      }
      // 异常路由
      const routerPage = checkAliyunCreditRouter(creditConsultData);
      lpAliyunPageLog({
        routerPage,
        creditConsultData,
      });

      if ([PAGES.Home, PAGES.CreditFallback, PAGES.CreditLpSSR].includes(routerPage)) {
        LinkUtil.locationReplace(routerPage);
        return;
      }
      if (routerPage === PAGES.Index) {
        log.addOtherLog('credit-simple-to-index');
        LinkUtil.resetToPage('index');
      }

      if (creditConsultData?.latestCreditApplyOrder) {
        setCurCreditOrder(creditConsultData?.latestCreditApplyOrder);
      }

      if (creditConsultData?.latestCreditApplyOrder?.subStatus === 'INSTITUTION_CREDITING') {
        queryCreditOrderFn(
          creditConsultData?.latestCreditApplyOrder?.creditApplyOrderId,
          institutionCreditApplyParams,
        );
        // countDownRef.current.startTime = Date.now();
      }
    } else {
      lpAliyunPageLog({});
    }
  };

  const handleSecondaryCreditApply = useCallback(async () => {
    log.addClickLog('secondary-credit-apply');
    try {
      const postRes = await postCreditApply({
        requestId: idempotentId(),
        applyType: creditConsultData?.applyType,
        applyTime: Date.now(),
        channel: 'APP',
        creditPlatform: creditConsultData?.creditPlatform,
        creditType: creditConsultData?.creditType,
        exposePlatformPromotionOfferList: genApplyExposePlatformPromotionOfferStr(
          creditConsultData?.platformPromotionOfferList,
        ),
      });
      if (postRes.creditApplyOrderId) {
        queryCreditOrderFn(postRes.creditApplyOrderId);
      } else {
        throw new Error(getErrorCodeFromRejectRes(postRes));
      }
    } catch (e) {
      log.addErrorLog('secondary-credit-apply', {
        code: e.message,
      });
      Modal.alert({
        title: '申请失败',
        content: getMsgByErrorCode(e.message),
      });
    }
  }, [creditConsultData]);

  useVisibilityChangeRefresh(queryConsult);

  const handleAgreementsCompleted = useCallback(
    async ({ institutionList }) => {
      log.addOtherLog('credit-agreements-completed');
      setLoadingVisible(true);

      try {
        const res: any = await postCreditApplySupplement({
          currentSubStatus: curCreditOrder.subStatus,
          creditApplyOrderId: curCreditOrder.creditApplyOrderId,
          selectedInstitutionList: institutionList,
        });
        if (res.subStatus === 'INSTITUTION_CREDITING') {
          queryCreditOrderFn(curCreditOrder.creditApplyOrderId, institutionCreditApplyParams);
        } else {
          throw new Error(getErrorCodeFromRejectRes(res));
        }
      } catch (e) {
        log.addErrorLog('credit-institution-submit');
        Modal.alert({
          title: '提交失败',
          content: getMsgByErrorCode(e.message, CREDIT_SUPPLEMENT_ERROR_CODE),
        });
      } finally {
        setLoadingVisible(false);
      }
    },
    [curCreditOrder],
  );

  const handleUpdateRealNameInfo = useCallback(() => {
    log.addClickLog('update-real-name-info-click');
    // 点击按钮前端缓存
    window.localStorage.setItem(XFD_UNIAPP_TO_TB_CERTIFICATION_STORAGE_KEY, 'true');
    // 跳转淘宝认证信息页
    navigatorOpenURL('https://market.m.taobao.com/app/msd/personal-cert-h5/index.html?bizSource=taobao_site&ticks=scene_certify_initiative_taobao&certWay=PRC_USER_SUBMIT_INFO_AUTH&loadingVisible=false&bxfrom=initiative');
  }, []);

  useImperativeHandle(ref, () => ({
    handleKnowMoreClick,
  }));

  useEffect(() => {
    doInit();
  }, [creditConsultData]);

  const renderSimpleCreditResult = useCallback(() => {
    const { canCreditApply, canCreditApplyTime, applyType, latestCreditApplyOrder } =
      creditConsultData || {};
    const { subStatus, status, creditProcessStatus } =
      curCreditOrder || latestCreditApplyOrder || {};
    const code = getFailedCodeFromOrder(curCreditOrder);

    // 机构授信中状态
    if (_.includes(['INSTITUTION_CREDITING'], subStatus)) {
      log.addShowLog('simple-credit-institution-crediting');
      let tipText = '';
      if (count <= 0 && creditProcessStatus === 'MANUAL_REVIEW') {
        log.addShowLog('institution-crediting-manual-review');
        tipText = '金融机构正加急为您处理中，有可能会向您致电，请您留意接听电话';
      } else {
        tipText = '金融机构已收到您的额度申请，请耐心等待';
      }
      return (
        <div className={styles.simpleCreditResultWrap}>
          <div className={styles.simpleCreditResultTitle}>当前可借 (元)</div>
          <div className={styles.simpleCreditResultQuota}>****</div>
          <div className={styles.simpleCreditResultTip}>{tipText}</div>
          <div className={styles.simpleCreditResultAction}>
            <Button
              block
              className={styles.simpleCreditResultBtn}
              color="primary"
              disabled
              loading={count <= 0}
              loadingText={'额度计算中'}
            >
              额度计算中{` ${count}s`}
            </Button>
          </div>
        </div>
      );
    }

    // 失败状态
    if (status === 'FAILED') {
      // 身份证姓名不匹配
      if (code === 'IDCARDNO_NAME_NOT_MATCH' && canCreditApply) {
        // 判断缓存 展示缓存展示已授权未授信页面
        if (window.localStorage.getItem(XFD_UNIAPP_TO_TB_CERTIFICATION_STORAGE_KEY) === 'true') {
          return null;
        }
        // 否则展示更新实名信息
        return (
          <div className={styles.simpleCreditResultWrap}>
            <img className={styles.simpleCreditResultImg} src={'https://gw.alicdn.com/imgextra/i3/O1CN01b6f5881cs2eZW2Ps2_!!6000000003655-2-tps-560-400.png'} />
            <div className={styles.simpleCreditResultFailedTitle}>额度评估未通过</div>
            <div className={styles.simpleCreditResultDesc}>您在淘宝网留存的实名信息有误，请前往更新，更新完成后可尝试重新申请额度</div>
            <div className={styles.simpleCreditResultAction}>
              <Button onClick={handleUpdateRealNameInfo} className={styles.actionBtn} color="primary">
                更新实名信息
              </Button>
            </div>
          </div>
        );
      }

      if (applyType === 'NEW' && canCreditApply) {
        // 如果前一笔失败，而且过了冷静期的情况，正常展示LP页，这里处理的code码是当前刚发起申请的时候需要展示不准入状态
        if (!CREDIT_APPLY_ERROR_CODE[code] || !showSimpleCreditResult) {
          return;
        }
      }

      let title = '暂无法为您提供服务';
      let desc: any = '';
      let imgSrc =
        'https://gw.alicdn.com/imgextra/i4/O1CN01315vbY1EbVZs2EWB9_!!6000000000370-2-tps-560-400.png';
      let action: any = null;
      let canSecondCreditApply = false;

      // 如果是二次授信且可申请
      if (applyType === 'SECONDARY' && canCreditApply) {
        canSecondCreditApply = true;
        // 二次授信
        title = '额度评估未通过';
        imgSrc =
          'https://gw.alicdn.com/imgextra/i3/O1CN01b6f5881cs2eZW2Ps2_!!6000000003655-2-tps-560-400.png';
        desc = '您还有机会重新评估';
        action = (
          <Button onClick={handleSecondaryCreditApply} className={styles.actionBtn} color="primary">
            重新评估额度
          </Button>
        );
      }

      // 不可申请
      if (!canCreditApply) {
        // 不可申请， 判断是否有冷静期
        const isSameYear =
          canCreditApplyTime && dayjs(canCreditApplyTime).get('year') === dayjs().get('year');
        title = '暂无法为您提供服务';
        desc = canCreditApplyTime ? (
          <span className={styles.applyTimeText}>
            预估
            <span className={styles.creditApplyTimeText}>
              {dayjsFormat(canCreditApplyTime, isSameYear ? 'MM月DD日' : 'YYYY年MM月DD日')}
            </span>
            可再来尝试申请
          </span>
        ) : (
          '抱歉，您当前不满足服务使用条件'
        );
      }

      // 判断是当前发起申请的
      if (CREDIT_APPLY_ERROR_CODE[code]) {
        desc = CREDIT_APPLY_ERROR_CODE[code];
      }
      log.addShowLog('credit-result-failed', {
        code,
        canSecondCreditApply,
        canCreditApplyTime,
      });
      return (
        <div className={styles.simpleCreditResultWrap}>
          <img className={styles.simpleCreditResultImg} src={imgSrc} />
          <div className={styles.simpleCreditResultFailedTitle}>{title}</div>
          <div className={styles.simpleCreditResultDesc}>{desc}</div>
          <div className={styles.simpleCreditResultAction}>{action}</div>
        </div>
      );
    }

    return null;
  }, [creditConsultData, curCreditOrder, count]);

  const renderKnowMorePopup = () => {
    return (
      <CommonPopup ref={popupRef} title="了解借钱">
        <div className={styles.container}>
          <div className={styles.rateDescItem}>
            <div className={styles.left}>
              <img src="https://gw.alicdn.com/imgextra/i2/O1CN016n90vT1MsvZemowJq_!!6000000001491-2-tps-64-64.png" />
            </div>
            <div className={styles.right}>
              <div className={styles.title}>轻松使用</div>
              <div className={styles.desc}>
                无抵押无担保，最高额度
                {data?.creditApplyConsult?.creditDefaultFactor?.maxQuotaInTenThousand}万
              </div>
            </div>
          </div>
          <div className={styles.rateDescItem}>
            <div className={styles.left}>
              <img src="https://gw.alicdn.com/imgextra/i1/O1CN01O0ppUn1MpGPD72kzQ_!!6000000001483-2-tps-64-64.png" />
            </div>
            <div className={styles.right}>
              <div className={styles.title}>费用透明</div>
              <div className={styles.desc}>
                年利率{data?.creditApplyConsult?.creditDefaultFactor?.minInterestRatePercent}%~
                {data?.creditApplyConsult?.creditDefaultFactor?.maxInterestRatePercent}
                %，不借款不收取任何费用
              </div>
            </div>
          </div>
          <div className={styles.rateDescItem}>
            <div className={styles.left}>
              <img src="https://gw.alicdn.com/imgextra/i4/O1CN019aOBZw1ZBV1Ull7XO_!!6000000003156-2-tps-64-64.png" />
            </div>
            <div className={styles.right}>
              <div className={styles.title}>灵活借还</div>
              <div className={styles.desc}>支持提前还款，无手续费</div>
            </div>
          </div>
        </div>
      </CommonPopup>
    );
  };

  if (!data?.creditApplyConsult?.admitted) {
    // LinkUtil.locationReplace(PAGES.CreditFallback);
    return null;
  }

  return (
    <div style={customStyle} className={styles.lpWrap}>
      <FullLoading visible={loadingVisible} />
      {renderSimpleCreditResult() ||
        (data?.creditApplyConsult && (
          <Fragment>
            <div onClick={handleInsititutionClick} className={styles.lpHeadLine}>
              贷款及相关服务由合作机构
              <span>
                {_.map(
                  data?.creditApplyConsult?.creditDefaultFactor?.institutionList?.slice(0, 3),
                  (item) => {
                    return (
                      <span className={styles.institutionLogoWrap} key={`institution-${item}`}>
                        <IconInstitution className={styles.institutionLogo} type={item} />
                      </span>
                    );
                  },
                )}
              </span>
              提供
              <img src="https://gw.alicdn.com/imgextra/i1/O1CN01X2ZZdK1bfqSnqW7tw_!!6000000003493-2-tps-32-32.png" />
            </div>
            <div className={styles.mainCard}>
              <div className={styles.cardTitle}>当前可借 (元)</div>
              <div className={styles.cardQuota}>****</div>
              <div className={styles.cardYearRate}>
                可提现到银行卡，利率低至
                {data?.creditApplyConsult?.creditDefaultFactor?.minInterestRatePercent}%
              </div>
              <RedPacketOffer offerList={creditConsultData?.platformPromotionOfferList} />
              <div
                className={classnames([styles.cardMainBtn, true && styles.hasRedEnvelopeCoupon])}
              >
                <Button
                  onClick={handleMainBtnClick}
                  block
                  color="primary"
                  loading={processAction === 'SUBMITING'}
                  loadingText="确认中"
                >
                  {getMainBtnText()}
                </Button>
              </div>
              {creditConsultData?.isPlatformAgreementSigned === false && (
                <div className={styles.cardAgreements}>
                  <UnsignedAgreementsAliyun
                    prefix="阅读"
                    bizType="PLATFORM"
                    renderTitle={() => {
                      return <span className={styles.agreementNameText}>相关协议</span>;
                    }}
                  />
                  并同意在淘宝网认证的姓名、身份证号及留存的手机号用于本业务
                </div>
              )}
            </div>
          </Fragment>
        ))}
      <BorrowingGuidance isPlatformAgreementSigned={creditConsultData?.isPlatformAgreementSigned === true} />
      {creditConsultData?.isPlatformAgreementSigned === false && (
        <div className="page-bottom-text">
          <KnowMoreBtn onClick={handleKnowMoreClick} />
        </div>
      )}
      <UnsignedAgreementsAliyun
        bizType="CREDIT"
        institutionList={getInsititutionCodeList(
          _.get(curCreditOrder, 'institutionDecideResultVOList'),
        )}
        ref={creditAgreementsRef}
        renderTitle={() => null}
        popupTitle="授信机构相关协议"
        onCompleted={handleAgreementsCompleted}
      />
      <ClientOnly>{renderRate}</ClientOnly>
      <ClientOnly>{renderKnowMorePopup}</ClientOnly>
      {
        // INSTITUTION_CREDITING和WAIT_TO_SELECT_INSTITUTION不渲染核身组件
        !_.includes(['WAIT_TO_SELECT_INSTITUTION', 'INSTITUTION_CREDITING'], _.get(curCreditOrder, 'subStatus')) &&
          <Authentication
            ref={AuthenticationRef}
            onSuccess={handleAuthSuccess}
            onFail={handleAuthFailed}
            directly
          />
      }
      <PersonalInfoPopup ref={PersonalInfoPopupRef} onSubmit={handlePersonalInfoSubmit} />
    </div>
  );
}

const KnowMoreBtn = (props) => {
  return (
    <span className={styles.bottomKnowMore} onClick={props?.onClick}>
      了解借钱
      <img src="https://gw.alicdn.com/imgextra/i3/O1CN01AWDoQL1zxklKndPFm_!!6000000006781-2-tps-48-48.png" />
    </span>
  );
};

export default function CreditLp() {
  return (
    <Layout>
      <BottomBarWrap HomeComp={forwardRef(CreditLpComp)} isLp PageBottomTipRender={KnowMoreBtn} />
    </Layout>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '',
  spm: {
    spmB: PAGES.CreditSimpleLp,
  },
}));
