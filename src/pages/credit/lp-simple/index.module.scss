.lpWrap {
  padding: 24rpx 16rpx!important;
  .lpHeadLine {
    display: flex;
    align-items: center;
    margin-bottom: 40rpx;
    color: #50607a;
    font-size: 24rpx;
    margin-left: 16rpx;

    .institutionLogoWrap {
      width: 30rpx;
      height: 30rpx;
      background-color: #fff;
      border-radius: 50%;
      display: inline-flex;
      align-items: center;
      justify-content: center;

      &:not(:last-child) {
        margin-right: -12rpx;
      }

      .institutionLogo {
        width: 24rpx;
        height: 24rpx;
      }
    }

    img {
      width: 16rpx;
      height: 16rpx;
      margin-left: 8rpx;
    }
  }

  .mainCard {
    background: #fff;
    padding: 120rpx 64rpx;
    text-align: center;
    border-radius: 24rpx;
    .cardTitle {
      color: #111;
      font-size: 32rpx;
      margin-bottom: 24rpx;
    }

    .cardQuota {
      font-family: "ALIBABA NUMBER FONT MD";
      // font-family: "AlibabaSans102Ver2";
      font-size: 144rpx;
      margin-bottom: 16rpx;
      line-height: 100%;
    }

    .cardYearRate {
      margin-bottom: 8rpx;
      font-size: 26rpx;
      color: #50607a;
    }

    .cardDailyInterest {
      margin-bottom: 16rpx;
      font-size: 26rpx;
      color: #50607a;
    }

    .cardPromotion {
      margin: 60rpx 0;
      background-image: url("https://gw.alicdn.com/imgextra/i1/O1CN01IbYdE31LR9taHkcLh_!!6000000001295-2-tps-590-160.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      height: 160rpx;
      display: flex;

      .cardPromotionLeft {
        flex: 0 0 180rpx;
        display: flex;
        align-items: center;
        align-items: flex-end;
        justify-content: center;
        font-weight: 500;
        color: #ff6a5b;
        font-size: 28rpx;
        padding-bottom: 52rpx;

        .promotionValue {
          font-size: 48rpx;
          line-height: 48rpx;
          margin-right: 6rpx;
        }
      }

      .cardPromotionRight {
        display: flex;
        flex-direction: column;
        padding: 44rpx 24rpx;
        font-size: 26rpx;
        text-align: left;

        .promotionName {
          font-weight: 500;
          margin-bottom: 8rpx;
        }

        .promotionDesc {
          color: #7c889c;
        }
      }
    }

    .interestDes {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      color: #999;
      img {
        width: 28rpx;
        height: 28rpx;
        margin-right: 12rpx;
      }
    }

    .cardMainBtn {
      margin-top: 172rpx;
      &.hasRedEnvelopeCoupon {
        margin-top: 100rpx;
      }
    }

    .cardAgreements {
      margin-top: 16rpx;
      font-size: 20rpx;
      color: #7c889c;
      text-align: left;
    }
  }

  // mainCard小屏 padding上下80rpx
  @media only screen
    and (device-height : 1334rpx) {
    .mainCard {
      padding: 80rpx 64rpx;
    }
  }

  .lpPageIntroduceItemTitle {
    font-size: 36rpx;
    font-weight: 500;
    color: #111;
    text-align: center;
    margin-bottom: 40rpx;
    margin-top: 72rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 141rpx;
      height: 18rpx;
      margin: 0 16rpx;
    }
  }

  .productIntro {
    .productIntroItem {
      background: #fff;
      border-radius: 12rpx;
      padding: 24rpx;
      margin-bottom: 16rpx;

      .productIntroItemTitle {
        font-size: 30rpx;
        font-weight: 500;
        margin-bottom: 12rpx;
      }

      .productIntroItemDesc {
        color: #50607a;
        font-size: 26rpx;
      }
    }
  }
}

.simpleCreditResultWrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 24rpx;
  background: #fff;
  padding: 120rpx 64rpx;
  text-align: center;
  height: 780rpx;
  .simpleCreditResultTitle {
    color: #111;
    font-size: 32rpx;
    margin-bottom: 24rpx;
  }

  .simpleCreditResultQuota {
    font-family: "ALIBABA NUMBER FONT MD";
    font-size: 144rpx;
    margin-bottom: 8rpx;
    line-height: 100%;
  }
  .simpleCreditResultBtnWrap {
    margin-top: 130rpx;
    display: flex;
    width: 100%;
    > div {
      width: 100%;
    }
  }
  .simpleCreditResultBtn {
    align-items: center;
    justify-content: center;
    color: #fff;
    padding: 0 32rpx;
    font-size: 30rpx;
    height: 80rpx;
    border-radius: 12rpx;
    font-weight: normal;
  }
  .simpleCreditResultTip {
    font-size: 26rpx;
    color: #000;
    margin-top: 16rpx;
    height: 78rpx;
  }

  .simpleCreditResultImg {
    width: 280rpx;
    height: 200rpx;
    margin-bottom: 24rpx;
  }
  .simpleCreditResultFailedTitle {
    font-size: 36rpx;
    color: #11192d;
    font-weight: 600;
    margin-bottom: 16rpx;
  }
  .simpleCreditResultDesc {
    font-size: 26rpx;
    color: #7c889c;
  }
  .actionBtn {
    width: 100%;
    height: 80rpx;
    padding: 0;
    margin-top: 120rpx;
  }
  .creditApplyTimeText {
    color: var(--color);
  }
}
.bottomKnowMore {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: rgba($color: #000, $alpha: 0.4);
  img {
    width: 24rpx;
    height: 24rpx;
  }
}

.container {
  padding-top: 40rpx;
  letter-spacing: 0;
  .rateDescItem {
    display: flex;
    margin-bottom: 50rpx;
    height: 120rpx;
    .left {
      width: 56rpx;
      height: 56rpx;
      margin-right: 16rpx;
      margin-left: -2rpx;
      margin-top: -8rpx;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      background: rgba(61, 94, 255, 0.08);
      border-radius: 50%;
      img {
        width: 32rpx;
        height: 32rpx;
      }
    }
    .right {
      flex: 1;
      .title {
        font-size: 32rpx;
        color: rgba(#000, 80%);
        font-weight: 500;
        margin-bottom: 12rpx;
      }
      .desc {
        font-size: 24rpx;
        color: rgba(#000, 40%);
      }
    }
  }
}

.agreementNameText {
  color: #5584ff;
  font-size: 20rpx;
  line-height: 20rpx;
  padding-right: 6rpx;
}

