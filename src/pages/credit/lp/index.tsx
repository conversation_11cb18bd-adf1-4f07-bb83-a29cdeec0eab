import { Fragment, useEffect, useState, useRef } from 'react';
import { definePageConfig } from 'ice';
import { Button, Toast, Modal } from 'antd-mobile';
import styles from './index.module.scss';
import classnames from 'classnames';
import { useCreditRouter, useVisibilityChangeRefresh } from '@/hooks';
import { postCreditApply } from '@/store/credit/actions';
import FullLoading from '@/components/FullLoading';
// import { UnsignedAgreements } from '@/components/UnsignedAgreements';
import { UnsignedAgreementsSandbox } from '@/components/UnsignedAgreementsSandbox';
import IconInstitution from '@/components/IconInstitution';
import { decideRouterPage } from '@/hooks/useCreditSubRouter';
import RateExplanationPopup from '@/components/RateExplanationPopup';
import { useDebounceFn } from 'ahooks';
import { idempotentId, _, getMsgByErrorCode, getErrorCodeFromRejectRes } from '@/utils';
import BottomBarWrap from '@/components/BottomBar';
import { PAGES } from '@/common/constant';
// import CommonNavBar from '@/components/CommonNavBar';
import LinkUtil from '@/utils/link';
import { log } from '@alife/dtao-iec-spm-log';

const CreditLp = (props) => {
  const { creditConsultData, routerPage, inited, queryConsult } = useCreditRouter();
  const [loadingVisible, setLoadingVisible] = useState(false);
  const rateDeclarePopupRef = useRef(null);

  const { onShowBottomBar, customStyle: lpStyle } = props;

  useVisibilityChangeRefresh(queryConsult);

  useEffect(() => {
    if (inited) {
      log.addVisitLog(PAGES.CreditLp, _.pick(creditConsultData, ['admitted', 'canCreditApply']));
    }
  }, [inited]);

  useEffect(() => {
    if (creditConsultData?.isPlatformAgreementSigned) {
      onShowBottomBar && onShowBottomBar(true);
    }
  }, [creditConsultData]);

  const handleInsititutionClick = () => {
    log.addClickLog('institution-click');
    LinkUtil.pushPage(PAGES.InstList);
  };

  const { run: handleMainBtnClick } = useDebounceFn(
    async () => {
      log.addClickLog('main-btn-click', { isPlatformAgreementSigned: creditConsultData?.isPlatformAgreementSigned });

      const newConsultData: any = await queryConsult();
      if (!newConsultData) {
        Toast.show('申请失败，请稍后再试');
        return;
      }
      const { latestCreditApplyOrder, applyType, canCreditApply, creditPlatform, creditType } = newConsultData || {};


      // 如果不准入
      if (!latestCreditApplyOrder && !canCreditApply) {
        LinkUtil.pushPage(PAGES.CreditFallback);
        return;
      }

      // 如果没有授信单或者是失败和取消的状态，且可发起授信
      if (
        !latestCreditApplyOrder ||
        (_.includes(['FAILED', 'CANCELLED'], latestCreditApplyOrder?.status) && canCreditApply)
      ) {
        setLoadingVisible(true);
        try {
          const postRes = await postCreditApply({
            requestId: idempotentId(),
            applyType,
            creditPlatform,
            applyTime: Date.now(),
            channel: 'APP',
            creditType,
          });
          if (postRes.creditApplyOrderId) {
            log.addSuccessLog('credit-lp-apply-success');
            const routerRage = decideRouterPage(postRes);
            LinkUtil.pushPage(routerRage, { creditApplyOrderId: postRes.creditApplyOrderId });
          } else {
            throw new Error(getErrorCodeFromRejectRes(postRes));
          }
        } catch (e) {
          log.addErrorLog('credit-lp-apply-error', { code: e.message });
          Modal.alert({
            title: '申请失败',
            content: getMsgByErrorCode(e.message),
          });
        } finally {
          setLoadingVisible(false);
        }
        return;
      }

      // 其他情况，跳转到对应页面
      if (latestCreditApplyOrder.status === 'INIT') {
        Toast.show('申请单状态异常，请联系客服');
      } else {
        const routerRage = decideRouterPage(latestCreditApplyOrder);
        LinkUtil.pushPage(routerRage, {
          creditApplyOrderId: latestCreditApplyOrder.creditApplyOrderId,
        });
      }
    },
    {
      wait: 1000,
      leading: true,
      trailing: false,
    },
  );

  if (creditConsultData && [PAGES.Home, PAGES.CreditFallback].includes(routerPage)) {
    LinkUtil.locationReplace(routerPage);
    return;
  }

  const lpPageIntroduceItemWrapper = (title) => {
    return (
      <div className={styles.lpPageIntroduceItemTitle}>
        <img src="https://gw.alicdn.com/imgextra/i4/O1CN01qjEJXo1snwvUArrUi_!!6000000005812-2-tps-141-18.png" />
        {title}
        <img src="https://gw.alicdn.com/imgextra/i4/O1CN01eKtWO820Ve1U538VF_!!6000000006855-2-tps-141-18.png" />
      </div>
    );
  };

  const handleHelpClick = async () => {
    rateDeclarePopupRef?.current.toggleVisible(true);
  };

  const { creditDefaultFactor = {}, isPlatformAgreementSigned } = creditConsultData || {};

  let mainBtnText = '同意协议并申请额度';
  if (isPlatformAgreementSigned) {
    mainBtnText = '申请额度';
  }

  return (
    <div style={{ ...lpStyle }} className={classnames([styles.lpWrap])}>
      {/* <CommonNavBar /> */}
      <FullLoading visible={!inited || loadingVisible} />
      {creditConsultData && (
        <Fragment>
          <div onClick={handleInsititutionClick} className={styles.lpHeadLine}>
            贷款及相关服务由合作机构
            <span>
              {_.map(creditDefaultFactor?.institutionList?.slice(0, 3), (item) => {
                return (
                  <span className={styles.institutionLogoWrap}>
                    <IconInstitution className={styles.institutionLogo} type={item} />
                  </span>
                );
              })}
            </span>
            提供
            <img src="https://gw.alicdn.com/imgextra/i1/O1CN01X2ZZdK1bfqSnqW7tw_!!6000000003493-2-tps-32-32.png" />
          </div>
          <div className={styles.mainCard}>
            <div className={styles.cardTitle}>您的额度 (元)</div>
            <div className={styles.cardQuota}>*****</div>
            <div className={styles.cardYearRate}>
              年利率(单利) {creditDefaultFactor.minInterestRatePercent}%起，借1千元用一天仅
              {creditDefaultFactor.minDailyInterestPerThousand}元起
            </div>
            <div className={styles.cardDailyInterest} />
            <div className={styles.interestDes}>
              <img
                onClick={handleHelpClick}
                src="https://gw.alicdn.com/imgextra/i4/O1CN01tb0F0f1PGrSDZi6qy_!!6000000001814-2-tps-28-28.png"
              />
              最终以实际授信利率为准
            </div>
            <div className={styles.cardMainBtn}>
              <Button onClick={handleMainBtnClick} block color="primary">
                {mainBtnText}
              </Button>
            </div>
            {isPlatformAgreementSigned === false && (
              <div className={styles.cardAgreements}>
                <UnsignedAgreementsSandbox prefix="查阅" bizType="PLATFORM" />
              </div>
            )}
          </div>
          <div className={styles.productIntro}>
            {lpPageIntroduceItemWrapper('产品介绍')}
            <div className={styles.productIntroItem}>
              <div className={styles.productIntroItemTitle}>轻松使用</div>
              <div className={styles.productIntroItemDesc}>
                无抵押无担保，最高可审批{creditDefaultFactor.maxQuotaInTenThousand}万额度
              </div>
            </div>
            <div className={styles.productIntroItem}>
              <div className={styles.productIntroItemTitle}>费用透明</div>
              <div className={styles.productIntroItemDesc}>
                年利率{creditDefaultFactor.minInterestRatePercent}%~
                {creditDefaultFactor.maxInterestRatePercent}%，不借款不收取任何费用
              </div>
            </div>
            <div className={styles.productIntroItem}>
              <div className={styles.productIntroItemTitle}>灵活借还</div>
              <div className={styles.productIntroItemDesc}>支持提前还款，无手续费</div>
            </div>
          </div>
        </Fragment>
      )}
      <RateExplanationPopup
        interestRate={{
          interestRatePercent: creditDefaultFactor.minInterestRatePercent,
          dailyInterestRatePercent: creditDefaultFactor.minDailyInterestRatePercent,
          days: creditDefaultFactor.days || 360,
        }}
        ref={rateDeclarePopupRef}
        isLp
      />
    </div>
  );
};

export default () => <BottomBarWrap HomeComp={CreditLp} isLp />;

export const pageConfig = definePageConfig(() => ({
  title: '',
  spm: {
    spmB: PAGES.CreditLp,
  },
}));
