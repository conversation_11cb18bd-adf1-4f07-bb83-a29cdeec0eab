.creditInstitution {
  padding-bottom: 230rpx!important;
  .creditInstitutionTitle1 {
    margin-bottom: 12rpx;
  }
  .singleInstitutionWrap {
    padding: 40rpx 0;
    margin-top: 72rpx;
    margin-bottom: 72rpx;
    font-size: 36rpx;
    text-align: center;
    .institutionLogo {
      margin-bottom: 16rpx;
      text-align: center;
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
    }
  }
  .creditInstitutionChoose {
    border-radius: 12rpx;
    background: #fff;
    padding: 16rpx;
    margin-bottom: 24rpx;
    .creditInstitutionChooseItem {
      margin-bottom: 16rpx;
      .listItemTitle {
        margin-bottom: 16rpx;
        font-weight: 500;
        height: 70rpx;
        display: flex;
        align-items: center;
        font-size: 26rpx;
      }
    }
  }
  .creditInstitutionList {
    padding: 0 24rpx;
  }
  .creditInstitutionListItem {
    height: 92rpx;
    display: flex;
    align-items: center;
    position: relative;

    &:not(:last-child) {
      &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        display: block;
        width: 100%;
        height: 2rpx;
        background-color: #e5e8ec;
        transform: scaleY(0.5);
      }
    }
    .creditInstitutionLogo {
      flex: 0 0 60rpx;
      height: 60rpx;
      margin-right: 12rpx;
    }
    .creditInstitutionName {
      flex: 1 1 auto;
      font-size: 26rpx;
      color: #111;
    }
    .creditInstitutionListItemCheckIcon {
      flex: 0 0 36rpx;
      height: 36rpx;
    }
  }
  .creditInstitutionTips {
    font-size: 26rpx;
    color: #7c889c;
    margin-bottom: 72rpx;
  }
  .nessaryInfoForm {
    padding: 16rpx;
    background: #fff;
    border-radius: 12rpx;
  }
  .addressTextArea {
    text-align: right;
    .addressTextAreaCount {
      color: #7c889c;
    }
  }
}

.supplementForm {
  :global(.adm-list-item-content-prefix) {
    --prefix-width: 150rpx;
  }
}
