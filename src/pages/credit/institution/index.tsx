import { useState, Fragment, useEffect, useRef, useMemo } from 'react';
import { definePageConfig } from 'ice';
import { Button, Form, TextArea, Modal } from 'antd-mobile';
import classnames from 'classnames';
import styles from './index.module.scss';
import { useCreditSubRouter } from '@/hooks';
import { queryCreditSupplementInfomation, postCreditApplySupplement } from '@/store/credit/actions';
import { _, getMsgByErrorCode, addAntdFormErrorLog } from '@/utils';
import { PAGES, INSTITUTION_NAME_MAP } from '@/common/constant';
import {
  IconInstitution,
  UnsignedAgreementsSandbox,
  UnsignedAgreementsSandboxRef,
  FullLoading,
  PopupListField,
  AddressSelector,
} from '@/components';
import LinkUtil from '@/utils/link';
import { useDebounceFn } from 'ahooks';
import { log } from '@alife/dtao-iec-spm-log';

export default function CreditInstitution() {
  const { creditApplyOrderData, subRouterPage, inited: subRouterInited } = useCreditSubRouter();
  const [selectedInstitutionObj, setSelectedInstitutionObj] = useState({});
  const [creditSupplement, setCreditSupplement] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [form] = Form.useForm();
  const agreementRef = useRef<UnsignedAgreementsSandboxRef>();

  const queryCreditSupplementInfomationFn = async () => {
    try {
      const res = await queryCreditSupplementInfomation({
        creditApplyOrderId,
        selectedInstitutionList,
      });

      setCreditSupplement(res);
    } catch (e) {
      log.addErrorLog('credit-institution-query-supplement-error');
    } finally {
      setLoading(false);
    }
  };

  const defaultSelectedInstitution: any = useMemo(() => {
    return _.find(
      creditApplyOrderData?.institutionDecideResultVOList,
      (item) => item.defaultSelected,
    );
  }, [creditApplyOrderData]);

  useEffect(() => {
    if (!_.isEmpty(selectedInstitutionObj)) {
      queryCreditSupplementInfomationFn();
    }
  }, [selectedInstitutionObj]);

  useEffect(() => {
    if (creditApplyOrderData) {
      log.addVisitLog(PAGES.CreditInstitution);
      setSelectedInstitutionObj(_.reduce(creditApplyOrderData?.institutionDecideResultVOList, (prev, item) => {
        prev[item?.institution] = item;
        return prev;
      }, {}));
    }
  }, [creditApplyOrderData]);

  useEffect(() => {
    if (!creditSupplement) return;
    const supplementData = _.get(creditApplyOrderData, 'applicationData.supplementData');
    if (!form.getFieldValue('profession')) {
      if (supplementData?.profession) {
        form.setFieldValue('profession', supplementData.profession);
      } else {
        const creditSupplementProfession = _.get(creditSupplement, 'profession') || {};
        if (!creditSupplementProfession.defaultValue) return;
        form.setFieldValue(
          'profession',
          _.find(creditSupplementProfession.options, (item) => {
            return item.value === creditSupplementProfession.defaultValue;
          }),
        );
      }
    }

    if (!form.getFieldValue('region')) {
      if (supplementData?.region) {
        form.setFieldValue('region', {
          ..._.pick(supplementData, ['province', 'city', 'county']),
        });
        form.setFieldValue('residenceAddress', supplementData.residenceAddress);
      }
    }
  }, [creditSupplement]);

  if (subRouterInited && subRouterPage !== PAGES.CreditInstitution) {
    LinkUtil.locationReplace(subRouterPage, {
      creditApplyOrderId: _.get(creditApplyOrderData, 'creditApplyOrderId'),
    });
  }

  const { institutionDecideResultVOList, creditApplyOrderId, subStatus } =
    creditApplyOrderData || {};

  const selectedInstitutionList = useMemo(() => {
    return Object.keys(selectedInstitutionObj);
  }, [selectedInstitutionObj]);

  const { run: handleSubmit } = useDebounceFn(
    async () => {
      log.addClickLog('submit-click');
      try {
        await form.validateFields();
        agreementRef.current?.show({
          action: 'compulsory',
        });
      } catch (error) {
        addAntdFormErrorLog(error.errorFields);
      }
    },
    {
      wait: 500,
      leading: true,
      trailing: false,
    },
  );

  if (!creditApplyOrderData) {
    return <FullLoading visible />;
  }

  if (!defaultSelectedInstitution) {
    Modal.alert({
      title: '服务器开小差',
      content: '请稍后重试',
      onConfirm: LinkUtil.popPage,
    });
    return null;
  }

  // 可选机构
  const selectableInstitutionList: any = _.filter(
    institutionDecideResultVOList,
    (item) => !item.defaultSelected,
  );
  // 是否有可选机构
  const hasSelectableInstitution = !_.isEmpty(selectableInstitutionList);
  const needFurtherInformation = !_.isEmpty(creditSupplement);
  const needProfession = !_.isEmpty(creditSupplement?.profession);
  const needResidenceAddress = !_.isEmpty(creditSupplement?.residenceAddress);

  const handleInstitutionItemClick = (item) => {
    setSelectedInstitutionObj((prev) => {
      if (selectedInstitutionObj[item.institution]) {
        delete prev[item.institution];
        return { ...prev };
      }
      return {
        ...prev,
        [item.institution]: item,
      };
    });
  };

  const doPostData = async () => {
    let values;
    try {
      values = await form.validateFields();
    } catch (error) {
      addAntdFormErrorLog(error.errorFields);
      return;
    }

    setLoading(true);

    try {
      let supplementData;
      if (needFurtherInformation) {
        supplementData = {};
        if (!_.isEmpty(values.region) && needResidenceAddress) {
          _.set(supplementData, 'province', values.region.province);
          _.set(supplementData, 'city', values.region.city);
          _.set(supplementData, 'county', values.region.county);
          _.set(supplementData, 'residenceAddress', values.residenceAddress);
        }
        if (!_.isEmpty(values.profession) && needProfession) {
          _.set(supplementData, 'profession', values.profession);
        }
      }
      const res: any = await postCreditApplySupplement({
        currentSubStatus: subStatus,
        creditApplyOrderId,
        selectedInstitutionList,
        applicationData: _.isEmpty(supplementData) ? undefined : { supplementData },
      });
      if (res.subStatus === 'INSTITUTION_CREDITING') {
        LinkUtil.replacePage(PAGES.CreditResult, {
          creditApplyOrderId,
        });
      } else {
        throw new Error(_.get(res, 'rejectReason.rejectCode'));
      }
    } catch (e) {
      log.addErrorLog('credit-institution-submit');
      Modal.alert({
        title: '提交失败',
        content: getMsgByErrorCode(e.message),
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.creditInstitution}>
      <FullLoading visible={loading} />
      <div className={classnames([styles.creditInstitutionTitle1, 'page-title-1'])}>
        已为您智能选择以下机构
      </div>
      {!hasSelectableInstitution ? (
        <div className={styles.singleInstitutionWrap}>
          <IconInstitution
            className={styles.institutionLogo}
            type={defaultSelectedInstitution.institution}
          />
          <div>{INSTITUTION_NAME_MAP[defaultSelectedInstitution.institution]}</div>
        </div>
      ) : (
        <Fragment>
          <div className={styles.creditInstitutionTips}>
            为了向您提供更优质的信贷服务，将依次推荐您已确认勾选的信贷机构，若有一家审批成功，则将不会继续向其他机构提交申请
          </div>
          <div className="page-title-2">机构选择</div>
          <div className={styles.creditInstitutionChoose}>
            <div className={styles.creditInstitutionChooseItem}>
              <div className={styles.listItemTitle}>已为您推荐</div>
              <div className={styles.creditInstitutionList}>
                <div className={styles.creditInstitutionListItem}>
                  <IconInstitution
                    className={styles.creditInstitutionLogo}
                    type={defaultSelectedInstitution.institution}
                  />
                  <div className={styles.creditInstitutionName}>
                    {INSTITUTION_NAME_MAP[defaultSelectedInstitution.institution]}
                  </div>
                  <img
                    className={classnames([
                      styles.creditInstitutionListItemCheckIcon,
                      styles.transparent,
                    ])}
                    src="https://gw.alicdn.com/imgextra/i4/O1CN01IVqXz426RsTMpmHhl_!!6000000007659-2-tps-72-72.png"
                  />
                </div>
              </div>
            </div>
            <div className={styles.creditInstitutionChooseItem}>
              <div className={styles.listItemTitle}>您还可选择</div>
              <div className={styles.creditInstitutionList}>
                {_.map(selectableInstitutionList, (institutionItem) => {
                  return (
                    <div
                      key={institutionItem.institution}
                      onClick={() => handleInstitutionItemClick(institutionItem)}
                      className={styles.creditInstitutionListItem}
                    >
                      <IconInstitution
                        className={styles.creditInstitutionLogo}
                        type={institutionItem.institution}
                      />
                      <div className={styles.creditInstitutionName}>
                        {INSTITUTION_NAME_MAP[institutionItem.institution]}
                      </div>
                      {selectedInstitutionObj[institutionItem.institution] ? (
                        <img
                          className={classnames([styles.creditInstitutionListItemCheckIcon])}
                          src="https://gw.alicdn.com/imgextra/i2/O1CN01Gp26M61CKtxPaZlqe_!!6000000000063-2-tps-72-72.png"
                        />
                      ) : (
                        <img
                          className={classnames([styles.creditInstitutionListItemCheckIcon])}
                          src="https://gw.alicdn.com/imgextra/i1/O1CN01dKED271Vrr9S8QOuy_!!6000000002707-2-tps-72-72.png"
                        />
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </Fragment>
      )}
      {needFurtherInformation && (
        <Fragment>
          <div className="page-title-2">补充信息</div>
          <div className={styles.nessaryInfoForm}>
            <Form className={styles.supplementForm} form={form} layout="horizontal">
              {needProfession && (
                <Form.Item
                  name="profession"
                  label="职业"
                  rules={[
                    {
                      required: true,
                      message: '请选择你的职业',
                    },
                  ]}
                >
                  <PopupListField
                    className="normal-input-field"
                    popupProps={{
                      title: '请选择你的职业',
                    }}
                    placeholder="请选择你的职业"
                    checkListProps={{
                      options: _.get(creditSupplement, 'profession.options'),
                    }}
                  />
                </Form.Item>
              )}
              {needResidenceAddress && (
                <Fragment>
                  <Form.Item
                    label="所在地区"
                    name="region"
                    rules={[
                      {
                        required: true,
                        message: '请选择你的地区',
                      },
                    ]}
                  >
                    <AddressSelector />
                  </Form.Item>
                  <Form.Item
                    label="详细地址"
                    name="residenceAddress"
                    rules={[
                      {
                        required: true,
                        message: '请输入详细地址',
                      },
                      {
                        pattern: /^.{4,30}$/,
                        message: '请输入4~30个汉字',
                      },
                    ]}
                  >
                    <TextArea
                      rows={2}
                      placeholder="小区、门牌号等"
                      className={styles.addressTextArea}
                      showCount={() => (
                        <span className={styles.addressTextAreaCount}>4～30个汉字</span>
                      )}
                      maxLength={30}
                    />
                  </Form.Item>
                </Fragment>
              )}
            </Form>
          </div>
        </Fragment>
      )}

      <div className="page-bottom-bar">
        <div className="agreement-radio-line">
          {!_.isEmpty(selectedInstitutionList) && (
            <UnsignedAgreementsSandbox
              ref={agreementRef}
              onCompleted={doPostData}
              bizType="CREDIT"
              institutionList={selectedInstitutionList}
              title="授信机构相关协议"
              popupTitle="授信机构相关协议"
              prefix="查阅"
            />
          )}
        </div>
        <Button onClick={handleSubmit} className="main-btn" block color="primary">
          同意协议并申请额度
        </Button>
      </div>
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '额度申请',
  spm: {
    spmB: PAGES.CreditInstitution,
  },
  window: {
    navBarImmersive: false,
    navBarBgColor: '#f3f6f8',
    pageBgColor: '#f3f6f8',
  },
}));
