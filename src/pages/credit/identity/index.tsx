import { useEffect, useRef, useState, useCallback } from 'react';
import { definePageConfig } from 'ice';
import { Button, Form, Toast, Modal } from 'antd-mobile';
import classnames from 'classnames';
import dayjs from 'dayjs';
import CommonPopup from '@/components/CommonPopup';
import { useCreditSubRouter, useSmsCodeCount } from '@/hooks';
import UploadImage from '@/components/UploadImage';
import FullLoading from '@/components/FullLoading';
import {
  dayjsFormat,
  _,
  getMsgByErrorCode,
  fieldDesensitization,
  addAntdFormErrorLog,
  getErrorCodeFromRejectRes,
} from '@/utils';
import { DetailItem } from '@/components/DetailItemList';
import { useDebounceFn } from 'ahooks';
import { APPLICATION_DATA_FIELD_MAP as FIELD_MAP } from '@/store/credit/model';
import { sendSmsCode, checkSmsCode, fileFactoryConfirm, OcrFrontIdCardDTO, OcrBackIdCardDTO } from '@/store/common/actions';
import { postCreditApplySupplement } from '@/store/credit/actions';
import { PAGES, SMSCODE_REG, PHONE_REG, ErrorMessageMap, SMS_BASE_PARAMS } from '@/common/constant';
import PhoneNumberInput from '@/components/PhoneNumberInput';
import SmsCodeInput from '@/components/SmsCodeInput';
import LinkUtil from '@/utils/link';
import styles from './index.module.scss';
import { log } from '@alife/dtao-iec-spm-log';

interface OcrData {
  front: OcrFrontIdCardDTO;
  back: OcrBackIdCardDTO;
}

export default function CreditIdentity() {
  const {
    creditApplyOrderData,
    subRouterPage,
    inited: subRouterInited,
  } = useCreditSubRouter();
  const commonPopupRef = useRef<any>(null);
  const [loadingVisible, setLoadingVisible] = useState(false);
  const [curSmsCodeData, setCurSmsCodeData] = useState<any>(null);
  const [smsCodeDisabled, setSmsCodeDisabled] = useState(true);
  const [formBtnDisabled, setFormBtnDisabled] = useState(true);
  const [idcardErrorStatus, setIdcardErrorStatus] = useState({
    front: '',
    back: '',
  });
  const [ocrData, setOcrData] = useState<OcrData>({
    front: {},
    back: {},
  });
  const [form] = Form.useForm();
  const { startCount, ...countData } = useSmsCodeCount();

  const { run: debounceResetToLp } = useDebounceFn(
    useCallback(() => {
      LinkUtil.resetToPage(PAGES.CreditLp);
    }, []),
    {
      wait: 2000,
      leading: true,
      trailing: false,
    },
  );

  if (subRouterInited && subRouterPage !== PAGES.CreditIdentity) {
    debounceResetToLp();
  }

  const handleShowCommonPopup = () => {
    log.addClickLog('idcard-not-around');
    commonPopupRef.current.toggleVisible(true);
  };

  const { run: handleSubmit } = useDebounceFn(
    async () => {
      log.addClickLog('identity-submit');
      // 先判断ocr是否成功
      if (!_.isEmpty(idcardErrorStatus.front) || !_.isEmpty(idcardErrorStatus.back)) {
        Toast.show(idcardErrorStatus.front) || _.isEmpty(idcardErrorStatus.back);
        return;
      }

      // 表单校验
      const values = await executeFormValidate();
      if (!values) return;

      const applicationData = {
        ...values,
      };
      setLoadingVisible(true);

      try {
        const smsCodeCheckRes: any = await checkSmsCode({
          ...curSmsCodeData,
          code: values.smscode,
          phone: _.get(values, FIELD_MAP.phone),
        });
        if (!smsCodeCheckRes.result) {
          throw new Error('SMSCODE_CHECK_FAILED');
        }
      } catch (error) {
        log.addErrorLog('smscode-check', { error: error.message });
        Toast.show('验证码校验失败');
        setLoadingVisible(false);
        return;
      }

      const licensePictureFront = _.get(values, FIELD_MAP.licensePictureFront);
      const licensePictureBack = _.get(values, FIELD_MAP.licensePictureBack);

      // 照片文件工厂confirm处理
      try {
        await Promise.all([
          fileFactoryConfirm({
            fileFactoryNo: licensePictureFront.fileFactoryNo,
          }),
          fileFactoryConfirm({
            fileFactoryNo: licensePictureBack.fileFactoryNo,
          }),
        ]);
      } catch (e) {
        log.addLog('idcard-image-confirm-error', 'error', { error: e });
        Modal.alert({
          title: '申请失败',
          content: ErrorMessageMap.BUSY_DEFUALT,
        });
        setLoadingVisible(false);
        return;
      }

      // 字段处理
      try {
        _.set(applicationData, FIELD_MAP.licenseAddress, ocrData.front.address);
        _.set(applicationData, FIELD_MAP.licenseName, ocrData.front.name);
        _.set(applicationData, FIELD_MAP.licenseNo, ocrData.front.num);
        _.set(applicationData, FIELD_MAP.licenseBirthday, +dayjs(ocrData.front.birth));
        _.set(
          applicationData,
          FIELD_MAP.licenseGender,
          _.get(
            {
              男: 'MALE',
              女: 'FEMALE',
            },
            ocrData.front.sex as string,
          ),
        );
        _.set(applicationData, FIELD_MAP.licenseNation, ocrData.front.nationality);
        _.set(applicationData, FIELD_MAP.licenseAuthDepartment, ocrData.back.issue);
        _.set(applicationData, FIELD_MAP.licenseType, 'IDENTITY_CARD');
        _.set(applicationData, FIELD_MAP.attachmentVOList, [
          licensePictureFront,
          licensePictureBack,
        ]);

        _.set(applicationData, FIELD_MAP.licenseEffectDate, +dayjs(ocrData.back.startDate));
        if (ocrData.back.endDate === '长期') {
          _.set(applicationData, FIELD_MAP.licenseLongTermEffective, true);
          _.set(applicationData, FIELD_MAP.licenseExpiredDate, null); // 如果长期这个字段传null
        } else {
          _.set(applicationData, FIELD_MAP.licenseLongTermEffective, false);
          _.set(applicationData, FIELD_MAP.licenseExpiredDate, +dayjs(ocrData.back.endDate));
        }

        delete applicationData.license.pictureFront;
        delete applicationData.license.pictureBack;

        const res: any = await postCreditApplySupplement({
          applicationData: JSON.stringify(applicationData),
          currentSubStatus: _.get(creditApplyOrderData, 'subStatus'),
          creditApplyOrderId: _.get(creditApplyOrderData, 'creditApplyOrderId'),
          channel: _.get(creditApplyOrderData, 'channel'),
        });

        if (res.subStatus === 'WAIT_TO_AUTH') {
          log.addSuccessLog('post-credit-supplement');
          LinkUtil.pushPage(PAGES.CreditSign, {
            creditApplyOrderId: _.get(creditApplyOrderData, 'creditApplyOrderId'),
          });
        } else {
          throw new Error(getErrorCodeFromRejectRes(res));
        }
        setLoadingVisible(false);
      } catch (e) {
        log.addLog('post-credit-supplement-error', 'error', { error: e });
        Modal.alert({
          title: '申请失败',
          content: getMsgByErrorCode(e.message),
        });
        setLoadingVisible(false);
      }
    },
    {
      wait: 500,
      leading: true,
      trailing: false,
    },
  );

  const executeFormValidate = async (name?) => {
    let validateResult;
    try {
      validateResult = await form.validateFields(name ? [name] : undefined);
      return validateResult;
    } catch (error) {
      addAntdFormErrorLog(error.errorFields);
    }
  };

  const handleOcrSuccess = (ocrResult, type) => {
    setOcrData((prev) => {
      return {
        ...prev,
        [type]: ocrResult,
      };
    });
    log.addSuccessLog('ocr-success', { type });

    changeIdCardErrorStatus(type, null);
  };

  const handleOcrFailed = async (e, type) => {
    log.addLog('ocr-failed', 'error', { type, e });

    if (e?.code === 'UNDER_18') {
      const msg = '未满18岁，无法申请额度';
      changeIdCardErrorStatus(type, msg);
      Toast.show(msg);
    } else if (e?.code === 'SAME_PERSON') {
      const msg = `您上传的身份证已在${
        e.payload ? `淘宝账号${e.payload}` : '其他淘宝账号'
      }申请额度，不可重复申请`;
      changeIdCardErrorStatus(type, msg);
      Toast.show('您上传的身份证已有申请记录');
    } else {
      const errorMsg = `身份证${type === 'front' ? '人像面' : '国徽面'}识别失败，请重新上传`;
      changeIdCardErrorStatus(type, errorMsg);
    }

    setOcrData((prev) => {
      return {
        ...prev,
        [type]: null,
      };
    });
  };

  const handleSendSmsCodeBtnClick = async () => {
    log.addClickLog('send-sms-code-btn-click');
    if (smsCodeDisabled || countData.smsBtnDisabled) {
      return;
    }

    const validateResult = await executeFormValidate(FIELD_MAP.phone);
    if (!validateResult) {
      Toast.show('请先输入手机号');
      return;
    }
    const phoneValue = _.get(validateResult, FIELD_MAP.phone);

    try {
      const sendRes: any = await sendSmsCode({
        phone: phoneValue,
        ...SMS_BASE_PARAMS,
      });
      if (sendRes.result) {
        setCurSmsCodeData({
          ...SMS_BASE_PARAMS,
          requestId: sendRes.requestId,
        });
        Toast.show('短信发送成功');
        startCount();
      } else {
        throw new Error();
      }
    } catch (error) {
      log.addLog('send-smscode-error', 'error', { error });
      Toast.show('短信发送失败');
    }
  };

  const changeIdCardErrorStatus = (type, errorMsg) => {
    if (errorMsg) {
      Toast.show(errorMsg);
    }
    setIdcardErrorStatus((prev) => {
      return {
        ...prev,
        [type]: errorMsg,
      };
    });
  };

  const idcardUploadItemValitor = useCallback(
    ({ type, value }) => {
      if (_.isEmpty(value)) {
        changeIdCardErrorStatus(type, '请上传身份证');
        return Promise.reject(new Error('请上传身份证'));
      }

      changeIdCardErrorStatus(type, null);

      return Promise.resolve();
    },
    [ocrData],
  );

  const handleValuesChange = (fieldObj, allValues) => {
    const phoneValue = _.get(allValues, FIELD_MAP.phone);
    const smsCodeValue = _.get(allValues, 'smscode');

    if (PHONE_REG.test(phoneValue)) {
      setSmsCodeDisabled(false);
      if (SMSCODE_REG.test(smsCodeValue)) {
        setFormBtnDisabled(false);
      }
    } else {
      setSmsCodeDisabled(true);
    }
  };

  const UploadFormItemComponentRender = useCallback(
    ({ field, type }) => {
      const emptyRender = () => {
        return (
          <div
            className={classnames([
              styles.uploadEmpty,
              type === 'front' ? styles.uploadItemFront : styles.uploadItemBack,
            ])}
          >
            <img src="https://gw.alicdn.com/imgextra/i4/O1CN0170eqkV1VVPkQtTjYM_!!6000000002658-2-tps-144-144.png" />
            {type === 'front' ? '上传人像面' : '上传国徽面'}
          </div>
        );
      };
      const uploadedImgRender = () => {
        return (
          <div
            className={classnames([
              styles.uploadedImg,
              type === 'front' ? styles.uploadedImgFront : styles.uploadedImgBack,
            ])}
          >
            <div className={styles.uploadedImgContent}>
              <img src="https://gw.alicdn.com/imgextra/i4/O1CN01Zmo1Nu1K1gGJn1Kom_!!6000000001104-2-tps-72-72.png" />
              上传成功
            </div>
            <div className={styles.uploadItemCorner}>重新上传</div>
          </div>
        );
      };

      return (
        <Form.Item
          noStyle
          name={field}
          rules={[
            {
              validator: (rules, value) => idcardUploadItemValitor({ type, value }),
            },
          ]}
        >
          <UploadImage
            className={classnames([
              idcardErrorStatus[type] && styles.idcardUploadItemError,
              styles.idcardUploadItem,
            ])}
            ocrConfig={{
              ocrType: type,
              preCheckCifSamePerson: true,
            }}
            emptyRender={emptyRender}
            uploadedImgRender={uploadedImgRender}
            onOcrSuccess={(res) => handleOcrSuccess(res, type)}
            onOcrFailed={(e) => handleOcrFailed(e, type)}
          />
        </Form.Item>
      );
    },
    [ocrData.front, ocrData.back, idcardErrorStatus],
  );

  useEffect(() => {
    const applicationData = _.get(creditApplyOrderData, 'applicationData');
    if (applicationData) {
      const licensePictureFront = _.find(
        _.get(applicationData, 'license.attachmentVOList'),
        (item) => item.type === 'PERSON_ID_CARD_FACE',
      );
      const licensePictureBack = _.find(
        _.get(applicationData, 'license.attachmentVOList'),
        (item) => item.type === 'PERSON_ID_CARD_NATION',
      );

      form.setFieldValue(FIELD_MAP.phone, _.get(applicationData, 'phone.phoneNo'));
      form.setFieldValue(FIELD_MAP.licensePictureFront, licensePictureFront);
      form.setFieldValue(FIELD_MAP.licensePictureBack, licensePictureBack);
    }
  }, [creditApplyOrderData]);

  useEffect(() => {
    if (!_.isEmpty(ocrData.front)) {
      executeFormValidate([FIELD_MAP.licensePictureFront]);
    }
  }, [ocrData.front]);

  useEffect(() => {
    if (!_.isEmpty(ocrData.back)) {
      executeFormValidate([FIELD_MAP.licensePictureBack]);
    }
  }, [ocrData.back]);

  useEffect(() => {
    log.addVisitLog(PAGES.CreditIdentity);
  }, []);

  const selectedPhoto = _.get(ocrData, 'front.selectedPhoto');
  const licenseName = _.get(ocrData, 'front.name');
  const licenseNo = _.get(ocrData, 'front.num');
  const startDate = _.get(ocrData, 'back.startDate');
  const endDate = _.get(ocrData, 'back.endDate');

  return (
    <div className={styles.creditIdentity}>
      <FullLoading visible={loadingVisible} />
      <div className={classnames([styles.creditIdentityTitle1, 'page-title-1'])}>
        请完善个人信息
      </div>
      <div className={styles.creditIdentityTips}>
        <img
          className={styles.creditIdentityTipsIcon}
          src="https://gw.alicdn.com/imgextra/i2/O1CN01C5ECWo1kem43yU528_!!6000000004709-2-tps-28-36.png"
        />
        <div className={styles.creditIdentityTipsText}>
          根据法律法规规定，我们将收集您的实名信息，提供给您后续申请授信/借款的金融机构，用于提供信贷服务
        </div>
      </div>
      <div className={classnames([styles.creditIdentityTitle2, 'page-title-2'])}>
        <span className="with-asterisk">请上传身份证</span>
      </div>
      <div className={styles.creditIdentityIdcardTip}>
        请确保身份证照片<span>边框完整</span>
        <span>字体清晰</span>
        <span>亮度均匀</span>
      </div>
      <Form form={form} onValuesChange={handleValuesChange}>
        <div className={styles.creditIdentityIdcardUpload}>
          <div className={styles.idcardUploadWrap}>
            {UploadFormItemComponentRender({
              field: FIELD_MAP.licensePictureFront,
              type: 'front',
            })}
            {UploadFormItemComponentRender({
              field: FIELD_MAP.licensePictureBack,
              type: 'back',
            })}
          </div>
          <div className={styles.idcardErrorMessage}>
            {_.get(idcardErrorStatus, 'front') || _.get(idcardErrorStatus, 'back')}
          </div>
          <div className={styles.idcardInfo}>
            {licenseName && (
              <DetailItem
                label="姓名"
                value={!selectedPhoto ? fieldDesensitization(licenseName, 0, 1) : licenseName}
              />
            )}
            {licenseNo && (
              <DetailItem
                label="身份证号"
                value={!selectedPhoto ? fieldDesensitization(licenseNo, 1, 2) : licenseNo}
              />
            )}
            {startDate && (
              <DetailItem
                label="有效期"
                value={`${dayjsFormat(startDate)}至${
                  endDate === '长期' ? '长期' : dayjsFormat(endDate)
                }`}
              />
            )}
          </div>
          <div className={styles.idcardBottomTip}>
            <a onClick={handleShowCommonPopup}>身份证不在身边？</a>
          </div>
        </div>
        <div className="page-title-2">
          <span className="with-asterisk">手机号</span>
        </div>
        <Form.Item
          name={FIELD_MAP.phone}
          rules={[
            {
              required: true,
              message: '请输入手机号',
            },
            {
              pattern: PHONE_REG,
              message: '手机号码格式错误',
            },
          ]}
        >
          <PhoneNumberInput placeholder="请填写手机号" />
        </Form.Item>
        <div className="page-title-2">
          <span className="with-asterisk">验证码</span>
        </div>
        <Form.Item
          name={'smscode'}
          rules={[
            {
              required: true,
              message: '请输入验证码',
            },
            {
              pattern: SMSCODE_REG,
              validateTrigger: 'onBlur',
              message: '验证码格式错误',
            },
          ]}
          className={styles.smsCodeFormItem}
          extra={
            <span
              onClick={handleSendSmsCodeBtnClick}
              className={classnames({
                'sms-code-btn': true,
                disabled: smsCodeDisabled || countData.smsBtnDisabled,
              })}
            >
              {countData.smsBtnTxt}
            </span>
          }
        >
          <SmsCodeInput placeholder="请填写验证码" />
          {/* <Input maxLength={6} pattern="[0-9]*" placeholder="请填写验证码" /> */}
        </Form.Item>
      </Form>
      <div className="page-bottom-bar">
        <Button
          disabled={_.isEmpty(ocrData.front) || _.isEmpty(ocrData.front) || formBtnDisabled}
          onClick={handleSubmit}
          block
          color="primary"
        >
          提交
        </Button>
      </div>
      <CommonPopup forceRender ref={commonPopupRef} title="身份证照片下载路径">
        <img src="https://gw.alicdn.com/imgextra/i3/O1CN01d0vfqE1fQy3nF4iOi_!!6000000004002-2-tps-1500-2272.png" />
      </CommonPopup>
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '实名认证',
  spm: {
    spmB: PAGES.CreditIdentity,
  },
  window: {
    navBarImmersive: false,
  },
}));
