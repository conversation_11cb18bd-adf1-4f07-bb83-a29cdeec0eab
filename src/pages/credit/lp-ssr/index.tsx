/**
 * @file 蚂蚁智信未授信首页
 * <AUTHOR>
 */

import { Fragment, useEffect, useRef, useState } from 'react';
import { definePageConfig, defineServerDataLoader, defineDataLoader, ClientOnly } from 'ice';
import { Button, Toast, Modal } from 'antd-mobile';
import { useDebounceFn } from 'ahooks';
import { log } from '@alife/dtao-iec-spm-log';

import { useVisibilityChangeRefresh } from '@/hooks';
import { postCreditApply } from '@/store/credit/actions';
import { decideRouterPage } from '@/hooks/useCreditSubRouter';
import { idempotentId, _, getMsgByErrorCode, getErrorCodeFromRejectRes } from '@/utils';
import { BottomBarWrap, UnsignedAgreementsSandbox, IconInstitution, RateExplanationPopup } from '@/components';
import { PAGES } from '@/common/constant';
import LinkUtil from '@/utils/link';
import { useFSPData } from '@/hooks/useFSPData';
import { checkAliyunCreditRouter } from '@/store/credit/model';
import { creditLpSSR, creditLpCSR, CreditLpFSPData } from '@/services/credit-lp';
import { queryConsult } from '@/store/credit/lib';
import Layout from '@/components/Layout/aliyun';
import { lpAliyunPageLog } from '@/utils/goc';

import styles from './index.module.scss';
import { addInitLog } from '@/utils/log';

type PROCESS_ACTION = 'SUBMITING' | 'NULL';

interface CreditLpProps {
  onShowBottomBar: (val: boolean) => void;
  customStyle: any;
}

export const serverDataLoader = defineServerDataLoader(async () => {
  const data = await creditLpSSR();
  return data;
});

export const dataLoader = defineDataLoader(async () => {
  const data = await creditLpCSR('PREFETCH');
  return data;
});

function CreditLpComp(props: CreditLpProps) {
  const { onShowBottomBar, customStyle } = props;
  const { fspData } = useFSPData<CreditLpFSPData>({
    init: creditLpCSR,
  });
  const { data } = fspData || {};
  const rateDeclarePopupRef = useRef<any>(null);
  const [processAction, setProcessAction] = useState<PROCESS_ACTION>();

  const handleInsititutionClick = () => {
    log.addClickLog('institution-click');
    LinkUtil.pushPage(PAGES.InstList);
  };

  const handleButtonClick = async () => {
    log.addClickLog('main-btn-click', {
      isPlatformAgreementSigned: data?.creditApplyConsult?.isPlatformAgreementSigned,
    });
    const consultRes = await queryConsult();
    if (!consultRes) {
      Toast.show('申请失败，请稍后再试');
      return;
    }
    const {
      latestCreditApplyOrder,
      applyType,
      canCreditApply,
      creditPlatform,
      creditType,
    } = consultRes;
    // 如果不准入
    if (checkAliyunCreditRouter(consultRes) === PAGES.CreditFallback) {
      LinkUtil.pushPage(PAGES.CreditFallback);
      return;
    }
    if (checkAliyunCreditRouter(consultRes) === PAGES.Home) {
      LinkUtil.replacePage(PAGES.Home);
      return;
    }
    // 如果没有授信单或者是失败和取消的状态，且可发起授信
    if (
      !latestCreditApplyOrder ||
      (_.includes(['FAILED', 'CANCELLED'], latestCreditApplyOrder?.status) && canCreditApply)
    ) {
      setProcessAction('SUBMITING');
      try {
        const postRes = await postCreditApply({
          requestId: idempotentId(),
          applyType,
          creditPlatform,
          applyTime: Date.now(),
          channel: 'APP',
          creditType,
        });
        if (postRes.creditApplyOrderId) {
          log.addSuccessLog('credit-lp-apply-success');
          const routerRage = decideRouterPage(postRes);
          LinkUtil.pushPage(routerRage, { creditApplyOrderId: postRes.creditApplyOrderId });
        } else {
          throw new Error(getErrorCodeFromRejectRes(postRes));
        }
      } catch (e) {
        log.addErrorLog('credit-lp-apply-error', { code: e.message });
        Modal.alert({
          title: '申请失败',
          content: getMsgByErrorCode(e.message),
        });
      } finally {
        setProcessAction('NULL');
      }
      return;
    }
    // 其他情况，跳转到对应页面
    if (latestCreditApplyOrder.status === 'INIT') {
      Toast.show('申请单状态异常，请联系客服');
    } else {
      const routerRage = decideRouterPage(latestCreditApplyOrder);
      LinkUtil.pushPage(routerRage, {
        creditApplyOrderId: latestCreditApplyOrder.creditApplyOrderId,
      });
    }
  };

  const { run: handleMainBtnClick } = useDebounceFn(handleButtonClick, {
    wait: 1000,
    leading: true,
    trailing: false,
  });

  const lpPageIntroduceItemWrapper = (title) => {
    return (
      <div className={styles.lpPageIntroduceItemTitle}>
        <img src="https://gw.alicdn.com/imgextra/i4/O1CN01qjEJXo1snwvUArrUi_!!6000000005812-2-tps-141-18.png" />
        {title}
        <img src="https://gw.alicdn.com/imgextra/i4/O1CN01eKtWO820Ve1U538VF_!!6000000006855-2-tps-141-18.png" />
      </div>
    );
  };

  const handleHelpClick = async () => {
    rateDeclarePopupRef?.current.toggleVisible(true);
  };

  const getMainBtnText = () => {
    if (fspData?.data?.creditApplyConsult?.isPlatformAgreementSigned) {
      return '申请额度';
    }
    return '同意协议并申请额度';
  };

  const renderRate = () => {
    return (
      <RateExplanationPopup
        interestRate={{
          interestRatePercent: data?.creditApplyConsult?.creditDefaultFactor?.minInterestRatePercent,
          dailyInterestRatePercent: data?.creditApplyConsult?.creditDefaultFactor?.minDailyInterestRatePercent,
          days: data?.creditApplyConsult?.creditDefaultFactor?.days || 360,
        }}
        ref={rateDeclarePopupRef}
        isLp
      />
    );
  };

  const doInit = () => {
    // CSR逻辑
    if (data?.creditApplyConsult) {
      addInitLog(fspData);
      const { creditApplyConsult } = data;
      // 底部bottomBar展示
      if (creditApplyConsult?.isPlatformAgreementSigned) {
        onShowBottomBar && onShowBottomBar(true);
      }
      // 异常路由
      const routerPage = checkAliyunCreditRouter(creditApplyConsult);
      // 曝光埋点
      lpAliyunPageLog({
        routerPage,
        creditConsultData: creditApplyConsult,
      });
      if ([PAGES.Home, PAGES.CreditFallback].includes(routerPage)) {
        LinkUtil.locationReplace(routerPage);
      }
    } else {
      lpAliyunPageLog({});
    }
  };

  useVisibilityChangeRefresh(queryConsult);

  useEffect(() => {
    doInit();
  }, [data?.creditApplyConsult]);

  if (!data?.creditApplyConsult?.admitted) {
    return null;
  }

  return (
    <div style={customStyle} className={styles.lpWrap}>
      {data?.creditApplyConsult && (
        <Fragment>
          <div onClick={handleInsititutionClick} className={styles.lpHeadLine}>
            贷款及相关服务由合作机构
            <span>
              {_.map(data?.creditApplyConsult?.creditDefaultFactor?.institutionList?.slice(0, 3), (item) => {
                return (
                  <span className={styles.institutionLogoWrap} key={`institution-${item}`}>
                    <IconInstitution className={styles.institutionLogo} type={item} />
                  </span>
                );
              })}
            </span>
            提供
            <img src="https://gw.alicdn.com/imgextra/i1/O1CN01X2ZZdK1bfqSnqW7tw_!!6000000003493-2-tps-32-32.png" />
          </div>
          <div className={styles.mainCard}>
            <div className={styles.cardTitle}>您的额度 (元)</div>
            <div className={styles.cardQuota}>*****</div>
            <div className={styles.cardYearRate}>
              年利率(单利) {data?.creditApplyConsult?.creditDefaultFactor?.minInterestRatePercent}%起，借1千元用一天仅
              {data?.creditApplyConsult?.creditDefaultFactor?.minDailyInterestPerThousand}元起
            </div>
            <div className={styles.cardDailyInterest} />
            <div className={styles.interestDes}>
              <img
                onClick={handleHelpClick}
                src="https://gw.alicdn.com/imgextra/i4/O1CN01tb0F0f1PGrSDZi6qy_!!6000000001814-2-tps-28-28.png"
              />
              最终以实际授信利率为准
            </div>
            <div className={styles.cardMainBtn}>
              <Button
                onClick={handleMainBtnClick}
                block
                color="primary"
                loading={processAction === 'SUBMITING'}
                loadingText="确认中"
              >
                {getMainBtnText()}
              </Button>
            </div>
            {data?.creditApplyConsult?.isPlatformAgreementSigned === false && (
              <div className={styles.cardAgreements}>
                <UnsignedAgreementsSandbox prefix="查阅" bizType="PLATFORM" />
              </div>
            )}
          </div>
          <div className={styles.productIntro}>
            {lpPageIntroduceItemWrapper('产品介绍')}
            <div className={styles.productIntroItem}>
              <div className={styles.productIntroItemTitle}>轻松使用</div>
              <div className={styles.productIntroItemDesc}>
                无抵押无担保，最高可审批{data?.creditApplyConsult?.creditDefaultFactor?.maxQuotaInTenThousand}万额度
              </div>
            </div>
            <div className={styles.productIntroItem}>
              <div className={styles.productIntroItemTitle}>费用透明</div>
              <div className={styles.productIntroItemDesc}>
                年利率{data?.creditApplyConsult?.creditDefaultFactor?.minInterestRatePercent}%~
                {data?.creditApplyConsult?.creditDefaultFactor?.maxInterestRatePercent}%，不借款不收取任何费用
              </div>
            </div>
            <div className={styles.productIntroItem}>
              <div className={styles.productIntroItemTitle}>灵活借还</div>
              <div className={styles.productIntroItemDesc}>支持提前还款，无手续费</div>
            </div>
          </div>
        </Fragment>
      )}
      <ClientOnly>{renderRate}</ClientOnly>
    </div>
  );
}

export default function CreditLp() {
  return (
    <Layout>
      <BottomBarWrap HomeComp={CreditLpComp} isLp />
    </Layout>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '',
  spm: {
    spmB: PAGES.CreditLp,
  },
}));
