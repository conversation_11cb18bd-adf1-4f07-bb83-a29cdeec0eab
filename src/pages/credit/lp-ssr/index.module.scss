.lpWrap {
  .lpHeadLine {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
    color: #50607a;
    font-size: 24rpx;

    .institutionLogoWrap {
      width: 30rpx;
      height: 30rpx;
      background-color: #fff;
      border-radius: 50%;
      display: inline-flex;
      align-items: center;
      justify-content: center;

      &:not(:last-child) {
        margin-right: -12rpx;
      }

      .institutionLogo {
        width: 24rpx;
        height: 24rpx;
      }
    }

    img {
      width: 16rpx;
      height: 16rpx;
      margin-left: 8rpx;
    }
  }

  .mainCard {
    background: #fff;
    padding: 60rpx 48rpx;
    text-align: center;
    border-radius: 24rpx;

    .cardTitle {
      color: #111;
      font-size: 26rpx;
      margin-bottom: 24rpx;
    }

    .cardQuota {
      font-family: "ALIBABA NUMBER FONT MD";
      font-size: 72rpx;
      margin-bottom: 16rpx;
      line-height: 100%;
    }

    .cardYearRate {
      margin-bottom: 8rpx;
      font-size: 26rpx;
      color: #50607a;
    }

    .cardDailyInterest {
      margin-bottom: 16rpx;
      font-size: 26rpx;
      color: #50607a;
    }

    .cardPromotion {
      margin: 60rpx 0;
      background-image: url("https://gw.alicdn.com/imgextra/i1/O1CN01IbYdE31LR9taHkcLh_!!6000000001295-2-tps-590-160.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      height: 160rpx;
      display: flex;

      .cardPromotionLeft {
        flex: 0 0 180rpx;
        display: flex;
        align-items: center;
        align-items: flex-end;
        justify-content: center;
        font-weight: 500;
        color: #ff6a5b;
        font-size: 28rpx;
        padding-bottom: 52rpx;

        .promotionValue {
          font-size: 48rpx;
          line-height: 48rpx;
          margin-right: 6rpx;
        }
      }

      .cardPromotionRight {
        display: flex;
        flex-direction: column;
        padding: 44rpx 24rpx;
        font-size: 26rpx;
        text-align: left;

        .promotionName {
          font-weight: 500;
          margin-bottom: 8rpx;
        }

        .promotionDesc {
          color: #7c889c;
        }
      }
    }

    .interestDes {
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 28rpx;
        height: 28rpx;
        margin-right: 12rpx;
      }

      font-size: 24rpx;
      color: #7c889c;
    }

    .cardMainBtn {
      margin-top: 60rpx;
    }

    .cardAgreements {
      margin-top: 16rpx;
      font-size: 20rpx;
      text-align: left;
      color: #7c889c;
    }
  }

  .lpPageIntroduceItemTitle {
    font-size: 36rpx;
    font-weight: 500;
    color: #111;
    text-align: center;
    margin-bottom: 40rpx;
    margin-top: 72rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 141rpx;
      height: 18rpx;
      margin: 0 16rpx;
    }
  }

  .productIntro {
    .productIntroItem {
      background: #fff;
      border-radius: 12rpx;
      padding: 24rpx;
      margin-bottom: 16rpx;

      .productIntroItemTitle {
        font-size: 30rpx;
        font-weight: 500;
        margin-bottom: 12rpx;
      }

      .productIntroItemDesc {
        color: #50607a;
        font-size: 26rpx;
      }
    }
  }
}