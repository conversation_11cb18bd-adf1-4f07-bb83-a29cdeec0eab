import { useEffect } from 'react';
import { definePageConfig } from 'ice';
import CommonResult from '@/components/CommonResult';
import styles from './index.module.scss';
import classnames from 'classnames';
import { PAGES } from '@/common/constant';
import { log } from '@alife/dtao-iec-spm-log';

export default function CreditFallback() {
  useEffect(() => {
    log.addVisitLog(PAGES.CreditFallback);
  }, []);

  return (
    <div className={classnames([styles.creditFallbackWrap])}>
      <CommonResult
        customImg={
          <img
            className={styles.creditFallbackImg}
            src="https://gw.alicdn.com/imgextra/i1/O1CN01HRl1SY1hnzCHMWwF6_!!6000000004323-2-tps-560-400.png"
          />
        }
        title="服务逐步开放中，敬请期待"
      />
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '',
  spm: {
    spmB: PAGES.CreditFallback,
  },
}));
