import { useState, useEffect, useRef } from 'react';
import { definePageConfig } from 'ice';
import CommonResult from '@/components/CommonResult';
import { Button, Modal } from 'antd-mobile';
import styles from './index.module.scss';
import { postCreditApply, queryCreditOrder, queryCreditApplyConsult } from '@/store/credit/actions';
import { CREDIT_APPLY_ORDER_DTO, CREDIT_APPLY_CONSULT_DTO } from '@/store/types';
import {
  getQueryParams,
  _,
  idempotentId,
  dayjsFormat,
  getMsgByErrorCode,
  getErrorCodeFromRejectRes,
} from '@/utils';
import { decideRouterPage } from '@/hooks/useCreditSubRouter';
import { PAGES } from '@/common/constant';
import LinkUtil from '@/utils/link';
import dayjs from 'dayjs';
import { log } from '@alife/dtao-iec-spm-log';

const StatusImgSrcMap = {
  SUCCEEDED:
    'https://gw.alicdn.com/imgextra/i3/O1CN01CZZC7Q1U2jLc8wZff_!!6000000002460-2-tps-560-400.png',
  FAILED:
    'https://gw.alicdn.com/imgextra/i2/O1CN010wKY4c1Nu8CBZpfPK_!!6000000001629-2-tps-560-400.png',
  PROCESSING:
    'https://gw.alicdn.com/imgextra/i4/O1CN01q37eHu1fXq1Q17NTr_!!6000000004017-2-tps-560-400.png',
};

let pageVisitTime = 0;

export default function CreditResult() {
  const [creditOrderData, setCreditOrderData] = useState<CREDIT_APPLY_ORDER_DTO | null>(null);
  const [consultData, setConsultData] = useState<CREDIT_APPLY_CONSULT_DTO | null>(null);
  const _timer = useRef<any>();

  const queryFn = async (creditApplyOrderId) => {
    const res = await queryCreditOrder({
      creditApplyOrderId,
      needCreditAuditStatus: true,
    });
    if (res.status === 'PROCESSING') {
      log.addVisitLog('final-credit-result', {
        status: 'PROCESSING',
      });
      _timer.current = setTimeout(() => {
        queryFn(creditApplyOrderId);
      }, 3000);
    }

    if (res.status === 'FAILED') {
      log.addOtherLog('final-credit-result', {
        status: 'FAILED',
        code: getErrorCodeFromRejectRes(res),
        waitingTime: Date.now() - pageVisitTime,
      });
      const consultRes = await queryCreditApplyConsult();
      setConsultData(consultRes);
    }

    if (res.status === 'SUCCEEDED') {
      log.addOtherLog('final-credit-result', {
        status: 'SUCCEEDED',
        waitingTime: Date.now() - pageVisitTime,
      });
      LinkUtil.locationReplace(PAGES.Home);
      return;
    }

    setCreditOrderData(res);
  };

  const handleSecondaryCreditApply = async () => {
    log.addClickLog('secondary-credit-apply');
    try {
      const postRes = await postCreditApply({
        requestId: idempotentId(),
        applyType: consultData?.applyType,
        applyTime: Date.now(),
        channel: 'APP',
        creditPlatform: consultData?.creditPlatform,
        creditType: consultData?.creditType,
      });
      if (postRes.creditApplyOrderId) {
        const routerRage = decideRouterPage(postRes);
        LinkUtil.replacePage(routerRage, { creditApplyOrderId: postRes.creditApplyOrderId });
      } else {
        throw new Error(getErrorCodeFromRejectRes(postRes));
      }
    } catch (e) {
      log.addErrorLog('secondary-credit-apply', {
        code: e.message,
      });
      Modal.alert({
        title: '申请失败',
        content: getMsgByErrorCode(e.message),
      });
    }
  };

  useEffect(() => {
    log.addVisitLog(PAGES.CreditResult);
    pageVisitTime = Date.now();
    const urlCreditApplyOrderId = _.get(getQueryParams(), 'creditApplyOrderId');

    if (urlCreditApplyOrderId) {
      queryFn(urlCreditApplyOrderId);
    } else {
      Modal.alert({
        title: '查询失败',
        content: '授信单号为空',
        onConfirm: LinkUtil.popPage,
      });
    }
  }, []);

  if (!creditOrderData?.status) return null;

  if (creditOrderData.status === 'FAILED') {
    const { canCreditApply, applyType, canCreditApplyTime } = consultData || {};
    let title;
    let action;
    let desc;
    if (applyType === 'SECONDARY' && canCreditApply) {
      log.addShowLog('secondary-credit-apply');
      // 二次授信
      title = '额度申请未通过，可重新申请';
      desc = '您可选择其他机构再次申请额度';
      action = (
        <Button onClick={handleSecondaryCreditApply} className={styles.actionBtn} color="primary">
          重新申请
        </Button>
      );
    }
    if (!canCreditApply) {
      // 如果不可二次授信了， 展示下次可授信的时间
      title = '暂无合适的机构可为您提供服务';
      const isSameYear = canCreditApplyTime && dayjs(canCreditApplyTime).get('year') === dayjs().get('year');
      action = canCreditApplyTime ? (
        <span className={styles.applyTimeText}>预估{dayjsFormat(canCreditApplyTime, isSameYear ? 'MM月DD日' : 'YYYY年MM月DD日')}可尝试再次申请</span>
      ) : null;
    }

    return (
      <div className={styles.creditResult}>
        <CommonResult
          customImg={
            <img className={styles.statusImg} src={StatusImgSrcMap[creditOrderData.status]} />
          }
          title={title}
          description={desc}
        />
        <div className="flex-center">{action}</div>
      </div>
    );
  }

  const description = creditOrderData?.creditProcessStatus === 'MANUAL_REVIEW' ? '金融机构正加急为您处理中，有可能会向您致电，请您留意接听电话' : '额度评估结果将在30分钟内以短信的形式通知您，请耐心等待';

  return (
    <div className={styles.creditResult}>
      <CommonResult
        customImg={<img className={styles.statusImg} src={StatusImgSrcMap.PROCESSING} />}
        title="额度评估中"
        description={description}
      />
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '申请结果',
  spm: {
    spmB: PAGES.CreditResult,
  },
}));
