import {
  getBioInfo,
  getEnvData,
  startVerifyIdentity,
} from '@/components/SSD/VerifyIdentity/libs/bridge';
import { query, verify } from '@/store/authentication/actions';
import { Button, Modal, Toast } from 'antd-mobile';
import { Clipboard } from '@ali/uni-api';
import { useCallback, useRef, useState } from 'react';
import { PRODUCT, TENANT } from '@/common/constant';
import { useVerifyIdentity } from '@/components';
import { pay } from '@/utils/pay';
import { bind } from '@/utils/bind';
import { navigatorOpenURL } from '@/utils/link';
import { getSecureToken } from '@/utils/umid-token';
import { getAlipayApdidToken, getAlipayApdidTokenV2 } from '@/utils/bridges';
import { first, get } from 'lodash-es';
import { isMtopCodeTimeout } from '@/utils';

const Test = () => {
  const [currentVerifyId, setCurrentVerifyId] = useState(
    'componentVerify_23256e75332a764b1d031342ec00385908RZ00B_sideloan_lend',
  );
  const retryTimes = useRef(0);

  const handleStartVerify = useCallback(() => {
    currentVerifyId &&
      startVerifyIdentity(currentVerifyId)
        .then((res) => {
          Clipboard.set({
            value: JSON.stringify(res),
          });
          Modal.alert({
            content: JSON.stringify(res, null, 2),
            confirmText: '我知道了',
          });
        })
        .catch((e) => {
          Clipboard.set({
            value: JSON.stringify(e),
          });
          Modal.alert({
            content: JSON.stringify(e, null, 2),
            confirmText: '我知道了',
          });
        });
  }, [currentVerifyId]);

  const [bioInfo, setBioInfo] = useState<any>(null);
  const [envData, setEnvData] = useState<any>(null);

  const handleBioInfo = () => {
    getBioInfo().then((res) => {
      setBioInfo(JSON.stringify(res, null, 2));
    });
  };

  const handleEnvData = () => {
    getEnvData().then(async (res) => {
      setEnvData(JSON.stringify(res));
      await Clipboard.set({
        value: JSON.stringify(res),
      });
      Toast.show({
        content: '复制成功',
      });
    });
  };

  const handleBind = async () => {
    try {
      const res = await bind({
        signStr:
          '_input_charset="utf-8"&app_name="tb"&appenv="appid=taobao^system=bindcardapp^version=undefined"&biz_type="bindcardapp"&source="suishendai"&backUrl=&unSuccessUrl=',
      });
      Toast.show({
        icon: 'success',
        content: JSON.stringify(res),
      });
    } catch (e) {
      Toast.show({
        icon: 'fail',
        content: JSON.stringify(e),
      });
    }
  };

  const handlePay = async () => {
    try {
      const res = await pay({
        signStr:
          'app_id=2021001156656281&app_name=tb&appenv=system=taobao^version=10.41.1&biz_content={}&biz_type=fas&charset=utf-8&dispatch_target=fascore&extern_token=********************************&method=alipay.pcredit.loan.sideloanrepay.repay.apply&monitorType=TAOBAO_TEST&partnerUserId=2088721242535391&sign_type=RSA2&timestamp=2024-11-05 21:45:20&tradeNoType=outOrderNo&trade_no=2024110510199318530105010000140000222206&version=1.0&sign=VLrJiIXhik5c2Os1wAy8u8g0K%2BvWatrjmPi5uAREGkonZNPtRb7M0HRJK%2FbLvXBEBo6g5EDhTJm6h1XUwHcwTqt22jGLmZlIGT7asaJQnd%2FRI2HJeK4YASOFax2RyW%2B10MUo6Vm%2FhyCXjfIG1iQb%2B24%2BtqgJYpSXdfVe7wOC9Hq2wgJNL%2FEhyiBYH20pxv%2FI9HUlmu6bK8b0ymU9ceuXUZaIm7V3eyQuoDiBje1vxfxMI3C%2FilCOdbFt9HEIDIzLnHriNOxYvhTEnkfKT2DBMxPD0q%2FexAKQs8m7rs7z6MUPRcP9hhSyS8v5hMQs9ibhfWpl%2Fti1RFCy1PVokShlDg%3D%3D',
      });
      Toast.show({
        icon: 'success',
        content: JSON.stringify(res),
      });
    } catch (e) {
      console.log(e);
      // Toast.show({
      //   icon: 'fail',
      //   content: JSON.stringify(e),
      // });
    }
  };

  const clipSet = async () => {
    await Clipboard.get({
      onSuccess: (event: any) => {
        Toast.show({
          content: JSON.stringify(event),
        });
      },
    });
  };

  const [queryRes, setQueryRes] = useState<any>(null);

  const handleQueryVerifyTask = () => {
    query({
      authenticationToken: 'AT990100001043524110500004766025',
      product: PRODUCT,
      tenant: TENANT,
    })
      .then((res) => {
        setQueryRes(JSON.stringify(res, null, 2));
      })
      .catch((err) => {
        console.log(err);
        setQueryRes(JSON.stringify(err, null, 2));
      });
  };

  const handleClick = () => {
    navigatorOpenURL(
      'https://30-8-112-157-3000.reviewcook.taobao.com/test?guan_socket=S7uDJAT3AS3MS_3oEQe7&guan_debugtool=eruda&forceThemis=true',
    );
  };

  const { startVerify } = useVerifyIdentity();

  const handleHookTest = async () => {
    try {
      const res = await startVerify({
        authenticationToken: 'AT990100001098424110500004766052',
      });
      Toast.show({
        icon: 'success',
        content: JSON.stringify(res),
      });
    } catch (e) {
      Toast.show({
        icon: 'fail',
        content: JSON.stringify(e),
      });
    }
  };

  const alipayApdidToken = async () => {
    try {
      const res = await getAlipayApdidToken();
      Toast.show({
        content: JSON.stringify(res),
        duration: 0,
      });
    } catch (e) {
      Toast.show({
        content: JSON.stringify(e),
        duration: 0,
      });
    }
  };

  const alipayApdidToken2 = async () => {
    try {
      const res = await getAlipayApdidTokenV2();
      Toast.show({
        content: JSON.stringify(res),
        duration: 0,
      });
    } catch (e) {
      Toast.show({
        content: JSON.stringify(e),
        duration: 0,
      });
    }
  };

  const getSecToken = async () => {
    const res = await getSecureToken();
    await Clipboard.set({
      value: JSON.stringify(res),
    });
    Toast.show({
      content: JSON.stringify(res),
      duration: 0,
    });
  };

  const [authToken, setAuthToken] = useState<string>('AT990100001031224111300004774051');

  const handleTestVerify = useCallback(() => {
    authToken &&
      startVerify({
        authenticationToken: authToken,
      }).then((res) => {
        // Toast.show({
        //   content: JSON.stringify(res),
        //   duration: 0,
        // });
        console.log(res);
      });
  }, [authToken, startVerify]);

  const handleGetSecureToken = useCallback(async () => {
    const res = await getSecureToken();
    Toast.show({
      content: JSON.stringify(res),
      duration: 0,
    });
  }, []);

  const delay = async (ms) => {
    return new Promise((resolve) => setTimeout(resolve, ms));
  };

  const verifyRetry = async (params: any) => {
    try {
      // 服务端验证
      console.log('参数', params);
      const verifyRes = await verify(params);
      retryTimes.current = 0;
      return verifyRes;
    } catch (e) {
      console.log(get(e, 'errorList[0].code'));
      if (isMtopCodeTimeout(get(e, 'errorList[0].code')) && retryTimes.current < 3) {
        retryTimes.current++;
        await delay(500);
        await verifyRetry(params);
      } else {
        retryTimes.current = 0;
        console.log('重试失败');
        throw new Error('VERIFY_FAIL');
      }
    }
  };

  const handleVerify = async () => {
    try {
      await verifyRetry({
        product: PRODUCT,
        tenant: TENANT,
        stepToken: '121',
        authenticationMethod: 'aa',
      });
      console.log('123');
    } catch (e) {
      if (first(e?.ret) === 'TIMEOUT::接口超时') {
        console.log('接口超时！！！');
      }
    }
  };

  return (
    <div>
      <div>核身测试</div>
      <Button onClick={handleBioInfo}>getBioInfo</Button>
      <div>{bioInfo}</div>
      <Button onClick={handleEnvData}>getEnvData</Button>
      <div>{envData}</div>
      <Button>verify</Button>
      <Button onClick={handleQueryVerifyTask}>queryVerifyTask</Button>
      <div>{queryRes}</div>
      <Button onClick={handlePay}>handlePay</Button>
      <Button onClick={handleBind}>handleBind</Button>
      <Button onClick={handleClick}>跳转</Button>
      <Button onClick={clipSet}>SET</Button>
      <Button onClick={alipayApdidToken}>alipayApdidToken</Button>
      <Button onClick={alipayApdidToken2}>alipayApdidToken2</Button>
      <Button onClick={handleGetSecureToken}>handleGetSecureToken</Button>
      <Button onClick={getSecToken}>getSecToken</Button>
      <Button onClick={handleVerify}>超时重试</Button>
      <div>
        端能力测试
        <input
          placeholder="在这里输入 verifyId"
          value={currentVerifyId}
          onChange={(e) => setCurrentVerifyId(e.target.value)}
        />
        <Button onClick={handleStartVerify}>开始验证</Button>
      </div>

      <Button onClick={handleHookTest}>hook 测试</Button>

      <div>
        核身测试
        <input
          placeholder="在这里输入 authToken"
          value={authToken}
          onChange={(e) => setAuthToken(e.target.value)}
        />
        <Button onClick={handleTestVerify}>开始核身</Button>
      </div>
      <input type="number" pattern="[0-9]*" />
    </div>
  );
};

export default Test;
