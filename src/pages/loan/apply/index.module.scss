@import '../../../assets/styles/reset.css';
@import '../../../assets/styles/font.css';

.loanPage {
  padding: 16rpx 32rpx;
  padding-bottom: 88rpx;
  .loanForm {
    :global(.adm-form-item.adm-form-item-horizontal .adm-form-item-label) {
      line-height: unset;
    }
    :global(.adm-form-item.adm-form-item-horizontal .adm-list-item-content) {
      --align-items: center;
    }
    :global(.adm-form-item-feedback-error) {
      text-align: right;
    }
  }
  .wrap {
  }
  .panel {
    border-radius: 18rpx;
    padding: 24rpx;
    background-color: #fff;
    margin-bottom: 24rpx;
    &:last-child {
      margin-bottom: 0;
    }
    :global(.adm-form-item) {
      padding-bottom: 12rpx;
      &:last-child {
        padding-bottom: 0;
      }
    }
  }
  .displayNone {
    display: none;
  }
  .submit {
    --fix-bottom-bg: #fff;
  }
}

.text {
  font-size: 26rpx;
  line-height: 39rpx;
  text-align: right;
  color: #7c889c;
  font-family: 'ALIBABA NUMBER FONT MD';
}

.agreementRelatedInfoFormItemWrap {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  font-weight: 500;
}
.agreementAndRelatedInfoPanelMarginBottom {
  margin-bottom: 24rpx;
}

.promotion {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 32rpx;
  .promotionName {
    color: #11192d;
    font-weight: 500;
  }
}

.hide {
  opacity: 0;
  width: 0;
  height: 0;
  padding: 0 !important;
}


.hover {
  position: fixed;
  width: 100%;
  height: 100%;
  // height: 100vh;
  top: 0;
  left: 0;
  z-index: 999;
}
