/**
 * @file 支用相关的检验条件
 */

import { loanApplyAmountValidator } from '@/store/loan/validator';
import { LoanModeValue } from '@/store/types';
import { log } from '@alife/dtao-iec-spm-log';
import { isEmpty } from 'lodash-es';

interface loanApplyAmountRuleOption {
  max?: string;
  min?: string;
  step?: string;
}

export const loanApplyAmount = {
  rules: (option: loanApplyAmountRuleOption) => [{
    validator: (_: any, value: string) => {
      return loanApplyAmountValidator(value, option);
    },
  }],
};

export const loanPurpose = {
  rules: [{
    required: true, message: '请选择借款用途',
  }],
};

export const supplementData = {
  rules: (validateFields: any) => [{
    validator: async () => {
      const res = await validateFields();
      if (!res?.errorFields?.length) {
        return Promise.resolve();
      }
      return Promise.reject(new Error('您未完成个人信息填写，请补充后再借款'));
    },
  }],
};

export const license = {
  rules: (validateFields: any) => [{
    validator: async (_: any, value: any) => {
      if (isEmpty(value) || isEmpty(value?.attachmentVOList)) {
        return Promise.reject(new Error('请上传身份证'));
      }
      const res = await validateFields();
      if (!res?.errorFields?.length) {
        return Promise.resolve();
      }
      return Promise.reject(new Error());
    },
  }],
};


export const loanModeValue = {
  rules: () => [{
    validator: async (_: any, value: LoanModeValue) => {
      if (isEmpty(value)) {
        log.addErrorLog('loanModeValue-validate');
        return Promise.reject(new Error('请选择借款期限、还款方式'));
      }
      if (!value?.loanTermValue || !value?.repaymentMethodValue) {
        log.addErrorLog('loanModeValue-validate');
        return Promise.reject(new Error('请选择借款期限、还款方式'));
      }
      log.addSuccessLog('loanModeValue-validate');
      return Promise.resolve();
    },
  }],
};

export const loanTerm = {
  rules: [{ required: true, message: '请选择借款期限' }],
};

export const repaymentMethod = {
  rules: [{ required: true, message: '请选择还款方式' }],
};

export const receiveBankCard = {
  rules: [{ required: true, message: '请添加收款账户' }],
};

export const institution = {
  rules: [{ required: true, message: '请确认放款机构' }],
};
