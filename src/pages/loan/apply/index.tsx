/**
 * @file 支用页面
 * <AUTHOR>
 */

import { useCallback, useEffect, useRef, useState } from 'react';
import { definePageConfig } from 'ice';
import { Button, Form, Toast, Modal } from 'antd-mobile';
import { first, keys, isEmpty, set } from 'lodash-es';
import { useDebounceFn } from 'ahooks';
import classNames from 'classnames';
import { log } from '@alife/dtao-iec-spm-log';
import { number } from '@ali/iec-dtao-utils';
import { loanAliyunPageLog } from '@/utils/goc';

import { toLoanSign, reload } from '@/utils/link';
import {
  Account, Agreement, AgreementRef, Institution,
  LoanPurposeProps, SupplementData, SupplementDataInfo,
  InteresetRate, SupplementRef, Reject, Coupon, CertUpdatePanel,
  CertUpdateRef, RedPacketPopover, RepaymentMethodNew,
} from './components';

import {
  loanPurpose, supplementData, loanApplyAmount,
  receiveBankCard, institution, license, loanModeValue,
} from './fields';
import { AmountInput, FixedBottom, ArrowIcon, checkHmRejectAlert } from '@/components';
import { AmountInputRef } from '@/components/AmountInput';
import { LoanApplySkeleton } from '@/components/Skeleton';
import { getSecureToken } from '@/utils/umid-token';
import {
  models, ApplyFormData, FieldObj, FIELD_NAME,
  formatAgreementPreviewParams, LoanApplyRejectReason,
} from '@/store/loan';
import { PAGES, ErrorMessageMap, HM_REJECT_UPLOAD_TB_VERSION } from '@/common/constant';
import { getRejectReason, addRejectLog } from '@/utils/reject';
import { useVisibilityChangeRefresh } from '@/hooks';
import { getAppVersion, isVersionGreater } from '@/utils';

import styles from './index.module.scss';

export const pageConfig = definePageConfig(() => ({
  title: '借钱',
  spm: {
    spmB: PAGES.LoanApply,
  },
  window: {
    navBarImmersive: false,
  },
}));

export default function LoanPage() {
  const {
    schemeStore, trialStore, processAction, loanBankCardStore, loanTermOptions,
    repaymentMethodOptions, doInit, doApply, doApplyAdmit, doUpdate, doTrial,
    checkSubmitDisabled, doUpdateLoanBankCard, changeProcessAction, doLoanModeChange,
    loanAgreements,
  } = models.useLoan();
  const [form] = Form.useForm<ApplyFormData>();
  const [showAgreementAndRelatedInfoPanel, setShowAgreementAndRelatedInfoPanel] = useState(false);
  const loanAgreementRef = useRef<AgreementRef>();
  const supplementDataValue = Form.useWatch('supplementData', form);
  const supplementRef = useRef<SupplementRef>();
  const certUpdateRef = useRef<CertUpdateRef>();
  const signedRef = useRef(false);
  const accountAddRef = useRef(false);
  const amountInputRef = useRef<AmountInputRef>();

  const loanAmountTips = () => {
    if (schemeStore?.loanSchemaQuota?.minStartLoanAmount) {
      return `${schemeStore.loanSchemaQuota.minStartLoanAmount}元起借，按天算息`;
    }
    return '按天算息';
  };

  const getAgreementPreviewParams = useCallback(() => {
    return formatAgreementPreviewParams(form.getFieldsValue(), trialStore);
  }, [form, trialStore]);

  const handleTrialError = useCallback((e?: any) => {
    addRejectLog(e);
    Modal.show({
      content: getRejectReason(e),
      actions: [{
        key: 'retry',
        text: '点击重试',
        primary: true,
        onClick: reload,
      }],
    });
  }, []);

  const clearBankError = () => {
    form.setFields([{
      name: 'receiveBankCard',
      errors: [],
    }]);
  };

  const handleInit = useCallback(async () => {
    const res: any = await doInit();
    form.setFieldsValue(res);
    // if (res?.license) {
    //   setTimeout(() => {
    //     form.validateFields();
    //   }, 5000);
    // }
    if (isEmpty(res)) {
      loanAliyunPageLog({ message: 'init-consult' });
      return;
    }
    try {
      await doTrial(res);
      loanAliyunPageLog({ scheme: res });
    } catch (e: any) {
      loanAliyunPageLog({ message: 'init-trial' });
      handleTrialError(e);
    }
  }, [form, doInit, doTrial, handleTrialError]);

  const handleReInit = async () => {
    if (!accountAddRef?.current) {
      return;
    }
    accountAddRef.current = false;
    log.addShowLog(`${PAGES.LoanApply}-re-init`);
    const loanAccountValue = await doUpdateLoanBankCard();
    if (!isEmpty(loanAccountValue)) {
      log.addSuccessLog(`${PAGES.LoanApply}-bind-card-success`);
      clearBankError();
      form.setFieldValue('receiveBankCard', loanAccountValue);
    }
  };

  const resetAgreementCheck = useCallback(() => {
    form.setFieldValue('agreementCheck', false);
    log.addOtherLog('rest-agreement-check');
  }, [form]);

  const handleUpdateField = useCallback((name: FIELD_NAME, value: any) => {
    form.setFieldValue(name, value);
  }, [form]);

  const handleFinishFailed = useCallback((errorInfo: any) => {
    log.addErrorLog('loan-apply-invalid', errorInfo);
  }, []);

  const handleApplyFailed = useCallback((rejectReason?: LoanApplyRejectReason) => {
    const applyRejectCode = rejectReason?.rejectCode || '';
    switch (applyRejectCode) {
      case 'CONTRACT_PERSON_CAN_NOT_BE_SELF': {
        // TODO: 最好改一下
        Toast.show({
          content: ErrorMessageMap.CONTRACT_PERSON_CAN_NOT_BE_SELF,
        });
        form.setFields([{
          name: 'supplementData',
          errors: [ErrorMessageMap.CONTRACT_PERSON_CAN_NOT_BE_SELF],
        }]);
        break;
      }
      default:
        Modal.show({
          content: ErrorMessageMap[applyRejectCode] || ErrorMessageMap.BUSY_DEFUALT,
          closeOnAction: true,
          actions: [{
            key: 'retry',
            text: '我知道了',
            primary: true,
          }],
        });
        break;
    }
  }, [form]);

  const handleAccountAddClick = () => {
    accountAddRef.current = true;
  };

  const handleReload = () => {
    handleReInit();
    if (signedRef.current) {
      log.addOtherLog('loan-signed-reload-new');
      reload();
    }
  };

  const handleSubmit = useCallback(async (data: ApplyFormData) => {
    log.addClickLog('loan-apply');
    const applyAdmitRes = await doApplyAdmit(data);
    if (!applyAdmitRes?.admitted) {
      log.addErrorLog('loan-admit-reject', applyAdmitRes);
      handleApplyFailed(applyAdmitRes?.rejectReason);
      return;
    }
    if (!data?.agreementCheck) {
      log.addShowLog('loan-apply-force');
      loanAgreementRef.current?.show({
        action: 'compulsory',
        params: getAgreementPreviewParams(),
      });
      return;
    }
    log.addSuccessLog('loan-apply-force');
    try {
      const secureTokenRes = await getSecureToken();
      set(data, 'umidToken', secureTokenRes?.umidToken);

      const res = await doApply(data);
      resetAgreementCheck();
      if (!res?.admitted) {
        log.addErrorLog('loan-apply-reject', res);
        handleApplyFailed(res?.rejectReason);
        return;
      }
      if (res?.loanOrderId && res?.status === 'AUTHENTICATING') {
        log.addSuccessLog('loan-apply-fetch-new');
        signedRef.current = true;
        toLoanSign(res?.loanOrderId);
      } else {
        log.addErrorLog('loan-apply-fetch', res);
        throw new Error('LOAN_APPLY_FAILED');
      }
    } catch (e) {
      resetAgreementCheck();
      Toast.show({
        // TODO: 和产品确认一下这个异常的兜底文案
        content: '申请失败，请稍后重试',
      });
      log.addErrorLog('loan-apply-error', e);
    }
  }, [doApply, getAgreementPreviewParams, resetAgreementCheck, handleApplyFailed, doApplyAdmit]);

  const handleAgreementCompleted = useCallback(() => {
    form.submit();
  }, [form]);

  const handleValuesChangeFn = useCallback(async (fieldObj: FieldObj, scheme: ApplyFormData) => {
    try {
      const changeNames = keys(fieldObj);
      if (changeNames?.length === 1) {
        const changeName = first(changeNames) as FIELD_NAME;
        log.addChangeLog(`loan-${changeName}`);
        await doUpdate({
          name: changeName,
          data: scheme,
          updateField: handleUpdateField,
        });
      } else {
        log.addErrorLog('update-twice');
      }
    } catch (e) {
      log.addErrorLog('update-trial');
      handleTrialError(e);
    }
  }, [doUpdate, handleUpdateField, handleTrialError]);

  const { run: handleValuesChange } = useDebounceFn(handleValuesChangeFn, {
    wait: 500,
    leading: true,
    trailing: true,
  });

  const checkLoading = () => {
    return processAction === 'AUTENTICATING' || processAction === 'SUBMITTING';
  };

  const renderFooter = () => {
    const isLoading = checkLoading();
    return (
      <FixedBottom className={styles.submit}>
        <RedPacketPopover offerList={schemeStore?.platformPromotionOfferList}>
          <Button
            loading={isLoading}
            loadingText="确认中"
            disabled={checkSubmitDisabled()}
            block
            color="primary"
            type="submit"
          >
            继续借钱
          </Button>
        </RedPacketPopover>
      </FixedBottom>
    );
  };

  const renderSupplementData = () => {
    if (schemeStore?.supplementData && !isEmpty(schemeStore?.supplementData)) {
      return (
        <>
          <Form.Item
            required={false}
            rules={supplementData.rules(supplementRef?.current?.validateFields)}
            label="个人信息"
            name="supplementData"
          >
            <SupplementData
              ref={supplementRef}
              collections={schemeStore?.supplementData}
            />
          </Form.Item>
          <SupplementDataInfo
            data={supplementDataValue}
          />
        </>
      );
    }
    return null;
  };

  const doReject = async () => {
    // 需要展示身份证上传
    if (schemeStore?.license && !isEmpty(schemeStore?.license)) {
      const appVersion = await getAppVersion(3);
      // 判断是否大于等于版本号 否则拦截
      const checkVersionFlag = isVersionGreater(appVersion, HM_REJECT_UPLOAD_TB_VERSION) || appVersion === HM_REJECT_UPLOAD_TB_VERSION;
      if (checkVersionFlag) {
        return;
      }

      if (checkHmRejectAlert(PAGES.LoanApply)) {
        amountInputRef.current?.blur?.();
      }
    }
  };

  useEffect(() => {
    // 判断是否拦截
    doReject();
  }, [schemeStore?.license]);

  const renderCertUpdate = () => {
    if (schemeStore?.license && !isEmpty(schemeStore?.license)) {
      log.addOtherLog('cert-update-process');
      return (
        <Form.Item
          label="身份证"
          required={false}
          name="license"
          rules={license.rules(certUpdateRef?.current?.validateFields)}
        >
          <CertUpdatePanel ref={certUpdateRef} />
        </Form.Item>
      );
    }
    return null;
  };

  const renderPromoition = () => {
    if (trialStore?.promotionAmount) {
      const promotionAmountValue = number.getNumber(trialStore?.promotionAmount);
      if (promotionAmountValue !== '--' && promotionAmountValue > 0) {
        return (
          <div className={classNames(styles.panel, styles.promotion)}>
            <p className={styles.promotionName}>优惠</p>
            <Coupon
              processAction={processAction}
              trialStore={trialStore}
            />
          </div>
        );
      }
    }
    return null;
  };

  const checkPanelNone = () => {
    if (!loanBankCardStore?.defaultValue) {
      return true;
    }
    return false;
  };

  const checkHide = () => {
    if (isEmpty(trialStore) || !trialStore) {
      return true;
    }
    return false;
  };

  const checkAppendHide = () => {
    if (isEmpty(schemeStore?.license) && isEmpty(schemeStore?.supplementData)) {
      return true;
    }
    return false;
  };

  const handleAgreementEC = () => {
    changeProcessAction('NULL');
  };

  const renderSupplementCert = () => {
    return (
      <div className={classNames(styles.panel, checkAppendHide() && styles.hide)}>
        {renderCertUpdate()}
        {renderSupplementData()}
      </div>
    );
  };

  const renderNormal = () => {
    if (schemeStore?.loanSchemaQuota) {
      const { minStartLoanAmount, maxLoanAmount, step } = schemeStore?.loanSchemaQuota;
      const loanApplyAmountRule = loanApplyAmount.rules({
        min: minStartLoanAmount,
        max: maxLoanAmount,
        step,
      });
      return (
        <Form
          form={form}
          className={styles.loanForm}
          footer={renderFooter()}
          onFinish={handleSubmit}
          onFinishFailed={handleFinishFailed}
          onValuesChange={handleValuesChange}
          layout="horizontal"
          hasFeedback
        >
          <Form.Item rules={loanApplyAmountRule} name="loanApplyAmount">
            <AmountInput
              ref={amountInputRef}
              label={loanAmountTips()}
              recommends={schemeStore?.loanSchemaQuota?.recommendAmountList}
              max={maxLoanAmount}
              inputMode="numeric"
              autoFocus
            />
          </Form.Item>
          <InteresetRate trialStore={trialStore} />
          <div className={classNames(styles.wrap, checkHide() && styles.hide)}>
            {renderPromoition()}
            <div className={styles.panel}>
              <Form.Item
                required={false}
                // @ts-ignore
                rules={loanModeValue.rules()}
                label="怎么还"
                name="loanMode"
              >
                <RepaymentMethodNew
                  repaymentMethodOptions={repaymentMethodOptions}
                  loanTermOptions={loanTermOptions}
                  trialStore={trialStore}
                  processAction={processAction}
                  doLoanModeChange={doLoanModeChange}
                />
              </Form.Item>
              <Form.Item required={false} rules={receiveBankCard.rules} label="收款账户" name="receiveBankCard">
                <Account
                  options={loanBankCardStore?.options}
                  onAddClick={handleAccountAddClick}
                />
              </Form.Item>
            </div>
            {renderSupplementCert()}
            <div className={classNames(styles.panel, checkPanelNone() && styles.displayNone)}>
              <div onClick={() => setShowAgreementAndRelatedInfoPanel(!showAgreementAndRelatedInfoPanel)} className={classNames(styles.agreementRelatedInfoFormItemWrap, showAgreementAndRelatedInfoPanel && styles.agreementAndRelatedInfoPanelMarginBottom)}>
                <div className={styles.promotionName}>协议及其相关信息</div>
                <div><ArrowIcon className={styles.icon} /></div>
              </div>
              <div className={classNames(!showAgreementAndRelatedInfoPanel && styles.displayNone)}>
                <Form.Item label="借款合同" name="agreementCheck">
                  <Agreement
                    ref={loanAgreementRef}
                    institution={schemeStore?.institution}
                    onCompleted={handleAgreementCompleted}
                    getParams={getAgreementPreviewParams}
                    processAction={processAction}
                    onError={handleAgreementEC}
                    onClose={handleAgreementEC}
                    customListResult={loanAgreements}
                  />
                </Form.Item>
                <Form.Item required={false} rules={institution.rules} label="放款机构" name="institution">
                  <Institution capitalInstitution={trialStore?.capitalInstitution} />
                </Form.Item>
                <Form.Item required={false} rules={loanPurpose.rules} label="借款用途" name="loanPurpose">
                  <LoanPurposeProps options={schemeStore?.loanPurpose?.options} />
                </Form.Item>
              </div>
            </div>
          </div>
        </Form>
      );
    }
    return null;
  };

  const renderDefault = () => {
    return (
      <>
        <AmountInput disabled label={loanAmountTips()} />
        <LoanApplySkeleton />
      </>
    );
  };

  const renderMain = () => {
    if (schemeStore?.admitted === true) {
      return renderNormal();
    } else if (schemeStore?.admitted === false) {
      return (
        <Reject schemeStore={schemeStore} />
      );
    }
    return renderDefault();
  };

  // 支用申请期间禁用页面操作
  const renderHover = () => {
    if (
      processAction === 'AUTENTICATING' ||
      processAction === 'SUBMITTING'
    ) {
      return (<div className={styles.hover} />);
    }
    return null;
  };

  useEffect(() => {
    handleInit();
  }, []);

  useVisibilityChangeRefresh(handleReload);

  return (
    <div className={styles.loanPage}>
      {renderHover()}
      {renderMain()}
    </div>
  );
}
