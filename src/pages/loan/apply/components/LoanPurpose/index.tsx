/**
 * @file 借款用途
 * <AUTHOR>
 */

import { useCallback } from 'react';

import { PopupListField } from '@/components';

import styles from './index.module.scss';

interface LoanPurposeProps {
  options?: any[];
}

export default function LoanPurpose(props: LoanPurposeProps) {
  const { options, ...other } = props;

  const renderPanel = useCallback((value?: Option) => {
    return <p className={styles.text}>{value?.label}</p>;
  }, []);

  return (
    <PopupListField
      className={styles.loanPurpose}
      placeholder="请选择借款用途"
      renderPanel={renderPanel}
      popupProps={{
        title: '借款用途',
      }}
      checkListProps={{
        options,
      }}
      logKey="loan-purpose"
      {...other}
    />
  );
}
