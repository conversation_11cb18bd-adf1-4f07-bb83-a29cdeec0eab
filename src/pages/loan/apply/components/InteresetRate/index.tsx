/**
 * @file 利率
 */

import { TrialResponse } from '@/store/loan';
import { isEmpty } from 'lodash-es';

import styles from './index.module.scss';
import { PROCESS_ACTION } from '@/store/loan/types';
import { number } from '@ali/iec-dtao-utils';

interface InterestRateProps {
  trialStore?: TrialResponse;
  processAction?: PROCESS_ACTION;
}

export default function InterestRate(props: InterestRateProps) {
  const { trialStore, processAction } = props;

  if (isEmpty(trialStore) || !trialStore) {
    return (
      <div className={styles.interestRate}>
        <span className={styles.text}>
          &nbsp;
        </span>
      </div>
    );
  }

  if (processAction === 'TRIALING') {
    return (
      <div className={styles.interestRate}>
        <span className={styles.text}>
          计算中...
        </span>
      </div>
    );
  }

  const checkPromotion = () => {
    if (trialStore) {
      const { baseInterestRatePercent, interestRatePercent } = trialStore;
      if (
        baseInterestRatePercent &&
        interestRatePercent &&
        baseInterestRatePercent !== interestRatePercent
      ) {
        return true;
      }
    }
    return false;
  };

  const renderPromotion = () => {
    const { amountFormat } = number;
    return (
      <>
        <span className={styles.text}>
          年利率（单利）
        </span>
        <span className={styles.text}>
          {amountFormat(trialStore.interestRatePercent)}%
        </span>
        <span className={styles.textLine}>
          {amountFormat(trialStore.baseInterestRatePercent)}%
        </span>
      </>
    );
  };

  const renderBase = () => {
    const { amountFormat } = number;
    return (
      <>
        <span className={styles.text}>
          年利率（单利）
        </span>
        <span className={styles.text}>
          {amountFormat(trialStore.interestRatePercent)}%
        </span>
      </>
    );
  };

  return (
    <div className={styles.interestRate}>
      {checkPromotion() ? renderPromotion() : renderBase()}
    </div>
  );
}
