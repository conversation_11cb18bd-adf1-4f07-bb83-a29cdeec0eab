.text {
  text-align: right;
  color: #7c889c;
}

.list {
  padding: 18rpx;
  background-color: #f3f6f8;
  border-radius: 18rpx;
  .item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 12rpx;
    &:last-child {
      padding-bottom: 0;
    }
    .label, .value {
      font-size: 26rpx;
      line-height: 39rpx;
      color: #7c889c;
    }
    .label {
      min-width: 110rpx;
    }
    .value {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      margin-left: 18rpx;
    }
  }
}

.supplementDataPopup {
  height: 1000rpx;
}

.container {
  position: relative;
}
