/**
 * @file 首支补充个人信息
 */

import { useCallback, useRef, forwardRef, useImperativeHandle, useState, useEffect } from 'react';
import { keys, join, isArray } from 'lodash-es';
import { log } from '@alife/dtao-iec-spm-log';

import { SupplementData as Supplement, SupplementDataRef, PopupPanelField } from '@/components';
import type { SupplementDataCollection, SupplementDataFormData } from '@/store/types';
import { mapContent } from '@/components/SupplementData/options';
import { checkSupplementDataEmpty } from '@/store/loan';

import styles from './index.module.scss';

interface SupplementDataProps {
  value?: SupplementDataFormData;
  onChange?: (data: unknown) => void;
  collections?: SupplementDataCollection;
}

export interface SupplementRef {
  validateFields: any;
}

export const SupplementData = forwardRef((props: SupplementDataProps, ref) => {
  const { onChange, value, collections } = props;
  const [message, setMessage] = useState<string | null>(null);
  const formRef = useRef<SupplementDataRef>();

  const onFinish = useCallback((close: any) => (data: unknown) => {
    onChange && onChange(data);
    log.addSuccessLog('supplement-data-submit');
    close?.close && close.close();
  }, [onChange]);

  const handleSupplementChange = useCallback((data: unknown) => {
    onChange && onChange(data);
    log.addSuccessLog('supplement-data-submit');
  }, [onChange]);

  // const renderPanel = useCallback(() => {
  //   if (checkSupplementDataEmpty(value)) {
  //     return <p className={styles.text}>请添加个人信息</p>;
  //   }
  //   return <p className={styles.text}>修改</p>;
  // }, [value]);

  const renderPanel = () => {
    return <p className={styles.text}>{message}</p>;
  };

  const validateFields = useCallback(async () => {
    try {
      const res = await formRef.current?.form?.validateFields();
      return res;
    } catch (e) {
      log.addErrorLog('supplement-data-invalid', e?.errorFields || {});
      return e;
    }
  }, []);

  const validateChangeMessage = useCallback(async (infoValue) => {
    if (checkSupplementDataEmpty(infoValue)) {
      setMessage('请添加个人信息');
    } else {
      try {
        const formValidateFields = await validateFields();
        if (formValidateFields?.errorFields?.length > 0) {
          setMessage('有信息尚未完善');
        } else {
          setMessage('修改');
        }
      } catch (errorInfo) {
        setMessage('有信息尚未完善');
      }
    }
  }, [validateFields]);

  useEffect(() => {
    validateChangeMessage(value);
  }, [value, validateChangeMessage]);

  useImperativeHandle(ref, () => ({
    validateFields,
  }));

  const renderForm = useCallback((close: any) => {
    return (
      <Supplement
        ref={formRef}
        initData={value}
        collections={collections}
        onFinish={onFinish(close)}
        onChange={handleSupplementChange}
      />
    );
  }, [onFinish, handleSupplementChange, collections, value]);

  return (
    <PopupPanelField
      contentClassName={styles.supplementData}
      renderFold={renderForm}
      renderPanel={renderPanel}
      containerClassName={styles.container}
      popupProps={{
        title: '个人信息',
        forceRender: true,
        bodyClassName: styles.supplementDataPopup,
      }}
      onChange={onChange}
      value={value}
    />
  );
});

interface SupplementDataInfoProps {
  data?: SupplementDataFormData;
}

export function SupplementDataInfo(props: SupplementDataInfoProps) {
  const { data = {} } = props;

  const getRegion = (region?: any) => {
    try {
      const nameList = keys(region);
      const valueList = nameList.map((name) => region[name]?.label || '');
      const name = join(valueList, '');
      if (name || data?.residenceAddress) {
        return `${name}${data?.residenceAddress}`;
      }
      return '';
    } catch (e) {
      return '';
    }
  };

  const getContacts = (contacts?: any) => {
    try {
      const valueList = contacts.map((contact) => {
        const { name, phone, relationship } = contact;
        if (name || phone || relationship) {
          return `${name || '未填写'},${phone || '未填写'},${relationship?.label || '未选择'}`;
        }
        return '';
      });
      if (join(valueList, '')) {
        return valueList;
      }
      return [];
    } catch (e) {
      return [];
    }
  };

  const renderLabel = (name: string) => {
    switch (name) {
      case 'profession':
      case 'education':
      case 'monthlyIncome':
      case 'maritalStatus':
        return data[name]?.label;
      case 'companyName':
      case 'addressDetail':
        return data[name];
      case 'region':
        return getRegion(data[name]);
      case 'emergencyContact':
        return getContacts(data[name]);
      default: return null;
    }
  };

  const renderList = () => {
    const nameList = keys(data);
    return nameList.map((name, index) => {
      const content = mapContent[name];
      if (data[name]) {
        const label = renderLabel(name);
        if (label && name !== 'emergencyContact') {
          return (
            <div className={styles.item} key={`${name}-${index}`}>
              <p className={styles.label}>
                {content?.label}
              </p>
              <p className={styles.value}>{label}</p>
            </div>
          );
        }
        if (isArray(label) && name === 'emergencyContact') {
          if (label?.length) {
            return label.map((labelItem, labelIndex) => {
              return (
                <div className={styles.item} key={`${name}-${index}-${labelIndex}`}>
                  <p className={styles.label}>
                    {content?.label} {label.length > 1 ? labelIndex + 1 : ''}
                  </p>
                  <p className={styles.value}>{labelItem}</p>
                </div>
              );
            });
          }
        }
        return null;
      } else {
        return null;
      }
    });
  };

  if (checkSupplementDataEmpty(data)) {
    return null;
  }

  return (
    <div className={styles.list}>
      {renderList()}
    </div>
  );
}
