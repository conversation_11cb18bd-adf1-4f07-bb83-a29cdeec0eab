/**
 * @file 信息补充模块
 * <AUTHOR>
 */

import { useState, useRef, forwardRef, useImperativeHandle, useCallback, useEffect } from 'react';
import { Button, Form, Toast } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';
import { cloneDeep, isEmpty, set } from 'lodash-es';
import dayjs from 'dayjs';

import { PopupPanelField, CommonPopup, LicenseUpload } from '@/components';
import UploadImage from '@/components/UploadImage';
import { PROFILE_DATA_FIELD_MAP as FIELD_MAP } from '@/store/credit/model';
import { OcrFrontIdCardDTO, OcrBackIdCardDTO, processLicensePictureData } from '@/store/common/actions';
import { DetailItem } from '@/components/DetailItemList';
import { _, dayjsFormat } from '@/utils';
import { CertLicense } from '@/store/types';

import styles from './index.module.scss';

interface CertUpdatePanelProps {
  value?: CertLicense;
  onChange?: (val: any) => void;
}

interface OcrData {
  front: OcrFrontIdCardDTO;
  back: OcrBackIdCardDTO;
}

export interface CertUpdateRef {
  validateFields?: any;
}

const XFD_UNIAPP_LOAN_PAGE_CERT_INFO_STORAGE_KEY = 'XFD_UNIAPP_LOAN_PAGE_CERT_INFO_STORAGE_KEY';

export const CertUpdatePanel = forwardRef((props: CertUpdatePanelProps, ref) => {
  const { value, onChange } = props;
  const [idcardErrorStatus, setIdcardErrorStatus] = useState({
    front: '',
    back: '',
  });
  const [form] = Form.useForm();
  const [ocrData, setOcrData] = useState<OcrData>({
    front: {},
    back: {},
  });
  const commonPopupRef = useRef<any>(null);
  const startDate = _.get(ocrData, 'back.startDate');
  const endDate = _.get(ocrData, 'back.endDate');
  const emptyRef = useRef<any>([]);

  const handleShowCommonPopup = () => {
    log.addClickLog('idcard-not-around');
    commonPopupRef.current.toggleVisible(true);
  };

  const renderPanel = () => {
    if (!isEmpty(value?.attachmentVOList)) {
      return <p className={styles.text}>已上传</p>;
    }
    return <p className={styles.text}>请上传身份证</p>;
  };

  const changeIdCardErrorStatus = (type, errorMsg) => {
    setIdcardErrorStatus((prev) => {
      return {
        ...prev,
        [type]: errorMsg,
      };
    });
  };

  const handleOcrFailed = async (e, type) => {
    log.addErrorLog('ocr-failed', { type, e });
    if (e?.code === 'UNDER_18') {
      const msg = '未满18岁，无法申请额度';
      changeIdCardErrorStatus(type, msg);
      Toast.show(msg);
    } else if (e?.code === 'SAME_PERSON') {
      const msg = `您上传的身份证已在${
        e.payload ? `淘宝账号${e.payload}` : '其他淘宝账号'
      }申请额度，不可重复申请`;
      changeIdCardErrorStatus(type, msg);
      Toast.show('您上传的身份证已有申请记录');
    } else if (e?.code === 'LICENSE_CHECK_INVALID') {
      Toast.show(e?.msg);
      changeIdCardErrorStatus(type, e?.msg);
    } else if (e?.code === 'EXPIRE') {
      Toast.show(e?.msg);
      changeIdCardErrorStatus(type, e?.msg);
    } else {
      const errorMsg = `身份证${type === 'front' ? '人像面' : '国徽面'}识别失败，请重新上传`;
      changeIdCardErrorStatus(type, errorMsg);
    }
    setOcrData((prev) => {
      return {
        ...prev,
        [type]: null,
      };
    });
  };

  const handleOcrSuccess = (ocrResult, type) => {
    setOcrData((prev) => {
      return {
        ...prev,
        [type]: ocrResult,
      };
    });
    changeIdCardErrorStatus(type, null);
  };

  const handleSaveData = (data) => {
    // 提交完成本地存储数据 设置过期时间48h
    try {
      log.addSuccessLog('cert-update-panel-storage-success');
      window.localStorage.setItem(XFD_UNIAPP_LOAN_PAGE_CERT_INFO_STORAGE_KEY, JSON.stringify({
        data,
        expiredTime: dayjs().add(48, 'hour'),
      }));
    } catch (error) {
      log.addErrorLog('cert-update-panel-storage-error', { code: error });
    }
  };

  const handleValuesChange = (changeValue: any, values: any) => {
    if (!values[FIELD_MAP.licensePictureFront]?.fileFactoryNo || !values[FIELD_MAP.licensePictureBack]?.fileFactoryNo) {
      return;
    }

    log.addOtherLog('form-values-change-do-submit');
    handleSubmit()(values);
  };

  const handleSubmit = useCallback((close?: any) => async (values: any) => {
    log.addSubmitLog('cert-update-panel-submit');
    try {
      const licensePictureFront = _.get(values, FIELD_MAP.licensePictureFront);
      const licensePictureBack = _.get(values, FIELD_MAP.licensePictureBack);
      const ocrFront = cloneDeep(ocrData?.front);
      // 使用原始的名字和身份证号，其实校验过两者是一致的
      set(ocrFront, 'name', value?.name);
      set(ocrFront, 'num', value?.licenseNo);
      const applicationData = await processLicensePictureData({
        licensePictureFront,
        licensePictureBack,
        ocrBack: ocrData?.back,
        ocrFront: ocrData?.front,
      });
      log.addSuccessLog('cert-update-panel-submit');
      close?.close && close.close();
      onChange && onChange(applicationData);
    } catch (e) {
      Toast.show({
        content: '系统繁忙，请稍后重试',
      });
    }
  }, [ocrData]);

  useEffect(() => {
    try {
      const storageData = window.localStorage.getItem(XFD_UNIAPP_LOAN_PAGE_CERT_INFO_STORAGE_KEY);
      const { data, expiredTime } = JSON.parse(storageData || '{}');
      // 在48h内 回填数据
      if (data && expiredTime && dayjs().isBefore(expiredTime)) {
        const { licensePictureFront, licensePictureBack, ocrFront, ocrBack } = data || {};
        form.setFieldsValue({
          [FIELD_MAP.licensePictureFront]: licensePictureFront,
          [FIELD_MAP.licensePictureBack]: licensePictureBack,
        });
        setOcrData({
          front: ocrFront,
          back: ocrBack,
        });
        log.addSuccessLog('cert-update-panel-backfill-success');
      }
    } catch (error) {
      log.addErrorLog('cert-update-panel-backfill-error', { code: error });
    }
  }, []);

  const idcardUploadItemValitor = ({ type, val }) => {
    if (_.isEmpty(val) && emptyRef.current.length < 1) {
      const emptyContext = '请上传身份证';
      emptyRef.current.push(emptyContext);
      changeIdCardErrorStatus(type, emptyContext);
      return Promise.reject(new Error(emptyContext));
    }
    if (type === 'front' && idcardErrorStatus?.front) {
      return Promise.reject(new Error(idcardErrorStatus?.front));
    }
    if (type === 'back' && idcardErrorStatus?.back) {
      return Promise.reject(new Error(idcardErrorStatus?.back));
    }
    emptyRef.current = [];
    changeIdCardErrorStatus(type, null);
    return Promise.resolve();
  };

  const getDateValue = () => {
    if (startDate && endDate) {
      return `${dayjsFormat(startDate)}至${endDate === '长期' ? '长期' : dayjsFormat(endDate)}`;
    }
    return '上传身份证照片后查看';
  };

  const UploadFormItemComponentRender = ({ field, type }) => {
    return (
      <Form.Item
        noStyle
        name={field}
        rules={[
          {
            validator: (rules, val) => idcardUploadItemValitor({ type, val }),
          },
        ]}
      >
        <UploadImage
          needHarmonyOSCompatible
          className={styles.idcardUploadItem}
          ocrConfig={{
            ocrType: type,
            preLicenseCheck: true,
          }}
          emptyRender={LicenseUpload.emptyRender}
          uploadedImgRender={LicenseUpload.uploadedImgRender}
          onOcrSuccess={(res) => handleOcrSuccess(res, type)}
          onOcrFailed={(e) => handleOcrFailed(e, type)}
        />
      </Form.Item>
    );
  };

  const renderSubmit = () => {
    return (
      <Button type="submit" block color="primary" className={styles.submit}>完成</Button>
    );
  };

  const renderForm = (close: any) => {
    return (
      <div className={styles.certUpdateContainer}>
        <h5 className={styles.title}>
          请上传身份证<b className={styles.point}>*</b>
        </h5>
        <div className={styles.desc}>
          <p className={styles.name}>请确保身份证照片</p>
          <b className={styles.bold}>清晰完整</b>
          <p className={styles.name}>，属于</p>
          <b className={styles.bold}>{value?.name}</b>
          <p className={styles.name}>本人</p>
        </div>
        <Form
          className={styles.certUpdateForm}
          form={form}
          footer={renderSubmit()}
          onFinish={handleSubmit(close)}
          onValuesChange={handleValuesChange}
        >
          <div className={styles.creditIdentityIdcardUpload}>
            <div className={styles.idcardUploadWrap}>
              {UploadFormItemComponentRender({
                field: FIELD_MAP.licensePictureFront,
                type: 'front',
              })}
              {UploadFormItemComponentRender({
                field: FIELD_MAP.licensePictureBack,
                type: 'back',
              })}
            </div>
            <div className={styles.idcardErrorMessage}>
              {_.get(idcardErrorStatus, 'front') || _.get(idcardErrorStatus, 'back')}
            </div>
            <div className={styles.idcardInfo}>
              <DetailItem label="姓名" value={value?.name} />
              <DetailItem label="身份证号" value={value?.licenseNo} />
              <DetailItem
                label="有效期"
                value={getDateValue()}
              />
            </div>
            <div className={styles.idcardBottomTip}>
              <a onClick={handleShowCommonPopup}>身份证不在身边？</a>
            </div>
          </div>
        </Form>
      </div>
    );
  };

  const handleBeforeClose = async () => {
    log.addOtherLog('cert-update-panel-do-before-close');
    const values = form.getFieldsValue();
    const licensePictureFront = _.get(values, FIELD_MAP.licensePictureFront);
    const licensePictureBack = _.get(values, FIELD_MAP.licensePictureBack);
    handleSaveData({
      licensePictureFront,
      licensePictureBack,
      ocrBack: ocrData?.back,
      ocrFront: ocrData?.front,
    });
  };

  const renderCertUpdate = () => {
    return (
      <PopupPanelField
        contentClassName={styles.cretUpdate}
        renderFold={renderForm}
        renderPanel={renderPanel}
        containerClassName={styles.container}
        popupProps={{
          title: '上传身份证',
          forceRender: true,
          bodyClassName: styles.supplementDataPopup,
        }}
        onChange={onChange}
        value={value}
        logKey="cert-update-panel"
        handleBeforeClose={handleBeforeClose}
      />
    );
  };

  const validateFields = async () => {
    try {
      const res = await form?.validateFields();
      return res;
    } catch (e) {
      log.addErrorLog('cert-update-panel-invalid', e?.errorFields || {});
      return e;
    }
  };

  useEffect(() => {
    const attachmentVOList = _.get(value, 'attachmentVOList');
    if (attachmentVOList) {
      const licensePictureFront = _.find(
        _.get(value, 'attachmentVOList'),
        (item) => item.type === 'PERSON_ID_CARD_FACE',
      );
      const licensePictureBack = _.find(
        _.get(value, 'attachmentVOList'),
        (item) => item.type === 'PERSON_ID_CARD_NATION',
      );
      form.setFieldValue(FIELD_MAP.licensePictureFront, licensePictureFront);
      form.setFieldValue(FIELD_MAP.licensePictureBack, licensePictureBack);
    }
  }, [value, form]);

  useImperativeHandle(ref, () => ({
    validateFields,
  }));

  return (
    <div className={styles.creditUpdatePanel}>
      {renderCertUpdate()}
      <CommonPopup
        forceRender
        ref={commonPopupRef}
        title="身份证照片下载路径"
        style={{ zIndex: 1200 }}
      >
        <img src="https://gw.alicdn.com/imgextra/i3/O1CN01d0vfqE1fQy3nF4iOi_!!6000000004002-2-tps-1500-2272.png" />
      </CommonPopup>
    </div>
  );
});
