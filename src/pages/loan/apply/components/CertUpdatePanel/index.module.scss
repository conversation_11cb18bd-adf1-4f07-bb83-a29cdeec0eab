.submit {
  width: 100%;
}

.certUpdateForm {
  :global(.adm-form-footer) {
    position: absolute;
    width: 100%;
    bottom: 0;
    background-color: #fff;
    padding: 0;
  }
}

.creditIdentityTitle1 {
  margin-bottom: 12rpx;
}
.creditIdentityTips {
  background: rgba(49, 204, 129, 0.08);
  padding: 6rpx 16rpx;
  display: flex;
  border-radius: 12rpx;
  margin-bottom: 72rpx;
  .creditIdentityTipsIcon {
    width: 28rpx;
    height: 36rpx;
    margin-right: 12rpx;
  }
  .creditIdentityTipsText {
    font-size: 24rpx;
    color: #7c889c;
    line-height: 36rpx;
  }
}
.creditIdentityIdcardTip {
  color: #7c889c;
  span {
    margin-left: 8rpx;
    color: #50607a;
    font-weight: 500;
  }
}
.creditIdentityIdcardUpload {
  background: #f3f6f8;
  border-radius: 18rpx;
  padding: 16rpx;
  margin: 24rpx 0;
  .idcardUploadWrap {
    padding: 0 18rpx;
    display: flex;
    justify-content: space-between;
  }
  .idcardUploadItem {
    flex: 0 0 297rpx;
    height: 182rpx;
    text-align: center;
    display: flex;
    justify-content: center;
    position: relative;
  }
  .idcardInfo {
    margin-top: 24rpx;
  }
  .idcardBottomTip {
    text-align: right;
    margin-top: 24rpx;
    font-size: 24rpx;
  }
}
.idcardErrorMessage {
  margin-top: 24rpx;
  font-size: 26rpx;
  color: #f00;
}

.uploadedImg {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  flex-direction: column;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  height: 100%;
  font-size: 24rpx;
  .uploadedImgContent {
    background: #fff;
    border-radius: 18rpx;
    color: #111;
    font-size: 24rpx;
    height: 36rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 12rpx;
    img {
      width: 24rpx;
      height: 24rpx;
      margin-right: 8rpx;
    }
  }
  .uploadItemCorner {
    position: absolute;
    top: 0;
    right: 0;
    border-radius: 0 12rpx;
    background: #111;
    color: #fff;
    line-height: 36rpx;
    height: 36rpx;
    padding: 0 8rpx;
  }
}

.uploadedImgFront {
  background-image: url('https://gw.alicdn.com/imgextra/i4/O1CN01BRWmZO1wJ2f5vNGQZ_!!6000000006286-2-tps-298-183.png');
}
.uploadedImgBack {
  background-image: url('https://gw.alicdn.com/imgextra/i4/O1CN016Llh1q1LmgVoszLxt_!!6000000001342-2-tps-299-184.png');
}

.uploadEmpty {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  flex-direction: column;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  height: 100%;
  font-size: 24rpx;
  &:not(:last-child) {
    margin-bottom: 24rpx;
  }
  img {
    width: 72rpx;
    height: 72rpx;
    margin-bottom: 8rpx;
  }
}
.uploadItemFront {
  background-image: url('https://gw.alicdn.com/imgextra/i3/O1CN01fqE6sf1DbDfaoRfPu_!!6000000000234-2-tps-301-186.png');
}
.uploadItemBack {
  background-image: url('https://gw.alicdn.com/imgextra/i3/O1CN017zM0fd1GYsUYE47xu_!!6000000000635-2-tps-301-186.png');
}

.text {
  text-align: right;
  color: #7c889c;
}

.title {
  font-size: 30rpx;
  line-height: 45rpx;
  color: #11192d;
  padding-bottom: 8rpx;
  .point {
    font-weight: 400;
    font-size: 26rpx;
    line-height: 39rpx;
    color: #4066ff;
  }
}

.desc {
  display: flex;
  align-items: center;
  color: #7c889c;
  font-size: 24rpx;
  padding-bottom: 12rpx;
  .bold {
    font-size: 24rpx;
    color: #50607a;
    font-weight: 500;
  }
}

.supplementDataPopup {
}

.container {
  position: relative;
  height: 70vh;
}
