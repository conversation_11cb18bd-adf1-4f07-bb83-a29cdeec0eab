/**
 * @file 收款账户组件
 * <AUTHOR>
 */

import { log } from '@alife/dtao-iec-spm-log';
import { useCallback } from 'react';

import type { ReceiveBankCard } from '@/store/loan';
import { PopupPanelField, BankInfoTip, BankCardList } from '@/components';
import { toLoanBindCard } from '@/utils/link';
import { BankCardDTO } from '@/store/types';

import styles from './index.module.scss';
import { isEmpty } from 'lodash-es';

interface LoanAccountProps {
  value?: ReceiveBankCard;
  options?: BankCardDTO[];
  onChange?: any;
  onAddClick: () => void;
}

export default function Account(props: LoanAccountProps) {
  const { value, options, onChange, onAddClick } = props;

  const handleOnChange = (bankCardValue: BankCardDTO) => {
    if (bankCardValue) {
      onChange({
        bindCardNo: bankCardValue?.value,
        bankCardNo: bankCardValue?.bankCardNo,
        bankCode: bankCardValue?.bankCode,
        bankName: bankCardValue?.bankName,
      });
    }
    return bankCardValue;
  };

  const handleClick = useCallback(() => {
    onAddClick && onAddClick();
    log.addClickLog('bind-card-link');
    toLoanBindCard();
  }, []);

  const renderPanel = useCallback(() => {
    return (
      <BankInfoTip
        className={styles.account}
        code={value?.bankCode}
        name={value?.bankName}
        cardNo={value?.bankCardNo}
      />
    );
  }, [value]);

  const renderFold = (foldProps: any) => {
    return (
      <BankCardList
        value={value}
        options={options}
        desc="立即到账，转账提现全免费"
        onChange={handleOnChange}
        onAddClick={onAddClick}
        afterOnChange={foldProps?.close}
      />
    );
  };

  if (!value && isEmpty(options)) {
    return (
      <div className={styles.add}>
        <div className={styles.addBtn} onClick={handleClick}>
          <i className={styles.addBlue} />
          <span className={styles.text}>添加</span>
        </div>
      </div>
    );
  }

  return (
    <PopupPanelField
      renderPanel={renderPanel}
      renderFold={renderFold}
      popupProps={{
        title: '收款账户',
      }}
      logKey="loan-purpose"
    />
  );
}
