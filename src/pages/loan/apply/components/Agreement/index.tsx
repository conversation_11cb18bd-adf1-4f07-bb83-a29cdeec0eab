/**
 * @file 支用协议组件
 * <AUTHOR>
 */

import { useCallback, useRef, forwardRef, useImperativeHandle } from 'react';
import { log } from '@alife/dtao-iec-spm-log';
import classNames from 'classnames';

import { UnsignedAgreementsAliyun, UnsignedAgreementsAliyunRef, UnsignedAgreementsSandbox } from '@/components';

import styles from './index.module.scss';
import { PROCESS_ACTION } from '@/store/loan';

interface AgreementProps {
  institution?: string;
  onCompleted: any;
  getParams: any;
  processAction?: PROCESS_ACTION;
  onError: any;
  onClose: any;
  customListResult?: any;
}

export type AgreementRef = UnsignedAgreementsAliyunRef;

export const Agreement = forwardRef((props: AgreementProps, ref) => {
  const { institution, onCompleted, getParams, processAction, customListResult, ...other } = props;
  const agreementPreviewRef = useRef<AgreementRef>();

  const renderTitle = useCallback(() => {
    return null;
  }, []);

  const handleClick = useCallback(() => {
    if (processAction === 'TRIALING') {
      return;
    }
    agreementPreviewRef.current?.show({
      action: 'preview',
      params: getParams(),
    });
    log.addClickLog('loan-agreement-preview');
  }, [getParams, processAction]);

  const handleCompleted = useCallback(() => {
    onCompleted();
    log.addSuccessLog('loan-agreement-force-complete');
  }, [onCompleted]);

  useImperativeHandle(ref, () => ({
    show: agreementPreviewRef.current?.show,
  }));

  return (
    <div className={styles.loanAgreement}>
      <span
        className={classNames(styles.text, processAction === 'TRIALING' && styles.disabled)}
        onClick={handleClick}
      >
        查看
      </span>
      {institution === 'LX' ? (
        <UnsignedAgreementsAliyun
          ref={agreementPreviewRef}
          bizType="LOAN"
          institutionList={institution ? [institution] : []}
          renderTitle={renderTitle}
          onCompleted={handleCompleted}
          customListResult={customListResult}
          unInit
          {...other}
        />
      ) : (
        <UnsignedAgreementsSandbox
          ref={agreementPreviewRef}
          bizType="LOAN"
          institutionList={institution ? [institution] : []}
          renderTitle={renderTitle}
          onCompleted={handleCompleted}
          {...other}
        />
      )}
    </div>
  );
});
