/**
 * @file 支用不准入的各种状况
 * <AUTHOR>
 */

import { Button, Modal } from 'antd-mobile';

import { toHome, reload, toIndex, popPage, replacePage } from '@/utils/link';
import { CommonResult, NewAmountInput } from '@/components';
import type { LoanStore } from '@/store/loan/types';
import { PAGES, ErrorMessageMap } from '@/common/constant';

import styles from './index.module.scss';

interface RejectProps {
  schemeStore: LoanStore;
}

export default function Reject(props: RejectProps) {
  const { rejectReason } = props.schemeStore;
  const REJECT_CONFIG = {
    REFRESH: {
      onClick: reload,
      title: ErrorMessageMap.BUSY_DEFUALT,
      btnText: '重试',
    },
    IDENTITY_CARD_EXPIRED: {
      customImg: <img className={styles.image} src="https://gw.alicdn.com/imgextra/i1/O1CN014Q9hkI1lIA8dwRNxJ_!!6000000004795-2-tps-560-400.png" />,
      title: '身份证已过期',
      description: '您上传的身份证已过期，暂无法借款',
      btnText: '更新身份证',
      onClick: () => replacePage(PAGES.CenterCertManage),
    },
    LOAN_DISABLED: {
      customImg: <img className={styles.image} src="https://gw.alicdn.com/imgextra/i2/O1CN01cBULzT27vTga6tKrh_!!6000000007859-2-tps-560-400.png" />,
      title: '服务升级中',
      description: '暂无法提供服务，请耐心等待',
    },
    INSTITUTION_SHUT_DOWN: {
      customImg: <img className={styles.image} src="https://gw.alicdn.com/imgextra/i2/O1CN01cBULzT27vTga6tKrh_!!6000000007859-2-tps-560-400.png" />,
      title: '服务升级中',
      description: '暂无法提供服务，请耐心等待',
    },
    NO_HAS_CREDIT_CONTRACT: {
      title: '请先申请额度再查看',
      onClick: toIndex,
    },
    NO_AVAILABLE_QUOTA: {
      title: '额度不足，暂无法借款',
      onClick: popPage,
    },
    ON_GOING_CONTRACT_MORE_THAN_UPPER_LIMIT: {
      title: '借款数量超限，暂无法再借一笔',
      onClick: popPage,
    },
    QUOTA_STATUS_FROZEN: {
      title: '您的额度已冻结，暂无法借款',
      onClick: popPage,
    },
    ON_GOING_LOAN_ORDER_EXIST: {
      title: '您当前有一笔借款进行中',
      onClick: popPage,
    },
  };

  const renderDefault = () => {
    const option = REJECT_CONFIG[rejectReason?.rejectCode || ''];
    return (
      <div className={styles.reject}>
        <CommonResult
          status="PROCESSING"
          customImg={option?.customImg}
          title={option?.title || rejectReason?.rejectDesc}
          description={option?.description || ''}
        />
        <Button
          className={styles.button}
          color="primary"
          onClick={option?.onClick || toHome}
        >
          {option?.btnText || '跳转首页'}
        </Button>
      </div>
    );
  };

  const renderMain = () => {
    if (rejectReason?.rejectCode === 'HAS_OVERDUE') {
      Modal.show({
        title: '暂无法借款',
        content: '您当前存在已逾期的借款，请还清后再借',
        actions: [{
          key: 'retry',
          text: '我知道了',
          primary: true,
          onClick: toHome,
        }],
      });
      return (
        <NewAmountInput disabled label="" />
      );
    } else {
      return renderDefault();
    }
  };

  return renderMain();
}
