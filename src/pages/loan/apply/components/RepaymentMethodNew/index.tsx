/**
 * @file 还款方式组件（新）
 * <AUTHOR>
 */

import { number } from '@ali/iec-dtao-utils';
import { isEmpty } from 'lodash-es';
import classNames from 'classnames';

import type { RepaymentMethodDTO, LoanTermDTO, LoanModeValue } from '@/store/types';
import type { PROCESS_ACTION, TrialResponse } from '@/store/loan/types';
import { formatRepaymentMethod, formatLoanTerm } from '@/store/lib/format';
import { _, getMD } from '@/utils';
import { PopupPanelField, Selector, RepaymentMethodDescNew } from '@/components';

import styles from './index.module.scss';
import { Button } from 'antd-mobile';
import { useEffect, useRef } from 'react';
import { log } from '@alife/dtao-iec-spm-log';

interface RepaymentMethodNewProps {
  loanTermOptions?: LoanTermDTO[];
  repaymentMethodOptions?: RepaymentMethodDTO[];
  value?: LoanModeValue;
  trialStore?: TrialResponse;
  processAction?: PROCESS_ACTION;
  onChange?: any;
  doLoanModeChange: any;
}

export default function RepaymentMethodNew(props: RepaymentMethodNewProps) {
  const {
    repaymentMethodOptions,
    loanTermOptions,
    trialStore,
    processAction,
    value: modeValue,
    onChange,
    doLoanModeChange,
  } = props;
  const popupRef = useRef<any>();

  const formatOnChange = (value: LoanTermDTO) => {
    return value;
  };

  const handleClose = () => {
    popupRef?.current?.close();
  };

  const handleLoanTermChange = (value?: any) => {
    const loanTermChangeValue = doLoanModeChange({
      changeName: 'loanTerm',
      loanTermValue: value,
      repaymentMethodValue: modeValue?.repaymentMethodValue,
    });
    onChange && onChange(loanTermChangeValue);
  };

  const handleRepaymentMethodChange = (value?: any) => {
    const repaymentMethodChangeValue = doLoanModeChange({
      changeName: 'repaymentMethod',
      loanTermValue: modeValue?.loanTermValue,
      repaymentMethodValue: value,
    });
    onChange && onChange(repaymentMethodChangeValue);
  };

  const renderFold = () => {
    const loanTermLen = loanTermOptions?.length || 0;
    const repaymentLen = repaymentMethodOptions?.length || 0;
    return (
      <div className={styles.fold}>
        <div className={classNames(styles.foldItem, loanTermLen === 1 && styles.foldItemLine)}>
          <p className={styles.foldItemName}>借款期限</p>
          {loanTermLen > 1 ? (<Selector
            size="large"
            options={loanTermOptions as any}
            color="primary"
            columns={loanTermOptions?.length}
            logKey="loan-term"
            formatOnChange={formatOnChange}
            onChange={handleLoanTermChange}
            // @ts-ignore
            value={modeValue?.loanTermValue?.value}
          />) : <p className={styles.foldItemValue}>{formatLoanTerm(modeValue?.loanTermValue)}</p>}
        </div>
        <div className={classNames(styles.foldItem, repaymentLen === 1 && styles.foldItemLine)}>
          <p className={styles.foldItemName}>还款方式</p>
          {repaymentLen > 1 ? (<Selector
            size="large"
            options={repaymentMethodOptions as any}
            color="primary"
            columns={repaymentMethodOptions?.length}
            logKey="loan-term"
            onChange={handleRepaymentMethodChange}
            value={modeValue?.repaymentMethodValue}
          />) : <p className={styles.foldItemValue}>{formatRepaymentMethod({ value: modeValue?.repaymentMethodValue })}</p>}
        </div>
        <div className={styles.foldFooter}>
          <RepaymentMethodDescNew
            options={repaymentMethodOptions}
            trialStore={trialStore}
            processAction={processAction}
            value={modeValue}
          />
        </div>
        <div className={styles.bottom}>
          <Button className={styles.confirmBtn} onClick={handleClose} color="primary">
            确定
          </Button>
        </div>
      </div>
    );
  };

  const getDesc = () => {
    const { amountFormat } = number;
    if (processAction === 'TRIALING') {
      return '计算中，请稍等...';
    }
    if (trialStore?.installmentPlanList) {
      const firstInstanllmentPlan = _.first(trialStore.installmentPlanList);
      if (firstInstanllmentPlan?.endDate) {
        return `首期${getMD(firstInstanllmentPlan.endDate)}，应还${amountFormat(firstInstanllmentPlan?.totalAmount)}元`;
      }
    }
    return '';
  };

  const renderPanel = () => {
    return (
      <div className={styles.panel}>
        <p className={styles.title}>
          {formatLoanTerm(modeValue?.loanTermValue)}
          <b className={styles.line}>｜</b>
          {formatRepaymentMethod({ value: modeValue?.repaymentMethodValue })}
        </p>
        <p className={styles.desc}>
          {getDesc()}
        </p>
      </div>
    );
  };

  const checkDisabled = () => {
    if (processAction === 'TRIALING' || isEmpty(trialStore)) {
      return true;
    }
    return false;
  };

  useEffect(() => {
    log.addShowLog('aliyun-repayment-method-new');
  }, []);

  return (
    <PopupPanelField<string>
      renderPanel={renderPanel}
      renderFold={renderFold}
      disabled={checkDisabled()}
      popupProps={{
        title: '怎么还',
      }}
      ref={popupRef}
      contentClassName={styles.customContent}
    />
  );
}
