.repaymentMethodPopup {
  padding: 0 24rpx;
}

.title {
  font-size: 26rpx;
  font-weight: 500;
  line-height: 39rpx;
  color: #111;
  text-align: right;
  font-family: 'ALIBABA NUMBER FONT MD';
}

.desc {
  font-size: 24rpx;
  line-height: 36rpx;
  text-align: right;
  color: #7c889c;
  font-family: 'ALIBABA NUMBER FONT MD';
}

.fold {
  position: relative;
  height: 70vh;
  margin-top: 32rpx;
  .bottom {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    background-color: #fff;
    padding: 32rpx 16rpx;
  }
  .confirmBtn {
    width: 100%;
    height: 80rpx;
    padding: 10px;
  }
}

.loading {
  display: flex;
  text-align: center;
  justify-content: center;
  align-items: center;
  padding-top: 318rpx;
  .text {
    font-size: 32rpx;
    color: #7c889c;
  }
}

.line {
  font-family: PingFang SC;
  font-size: 26rpx;
  font-weight: 500;
  line-height: 39rpx;
  color: #11192d;
}

.foldItem {
  background-color: #fff;
  padding: 0 32rpx;
  padding-bottom: 36rpx;
  .foldItemName {
    font-size: 26rpx;
    font-weight: 500;
    line-height: 40rpx;
    color: #111;
    padding-bottom: 16rpx;
  }
}

.customContent {
  background-color: #f3f6f8 !important;
  padding: 0 !important;
}


.foldItemLine {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.foldItemValue {
  font-size: 26rpx;
  line-height: 39rpx;
  text-align: right;
  color: #7c889c;
}
