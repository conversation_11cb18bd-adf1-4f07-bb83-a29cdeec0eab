/**
 * @file 授信核身页面
 */

import { useCallback, useEffect, useState } from 'react';
import { definePageConfig } from 'ice';
import { Modal } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';

import { Authentication, FullLoading } from '@/components';
import { useQueryPoll, actions, QueryResponse } from '@/store/loan';
import { getLoanOrderId } from '@/utils/params';
import { PAGES, ErrorMessageMap } from '@/common/constant';
import { onScroll } from '@/utils/nav';
import { popPage, toLoanResult } from '@/utils/link';

import styles from './index.module.scss';
import { includes } from 'lodash-es';
import { LOAN_STATUS } from '@/store/types';
import { loanSignPagePageLog } from '@/utils/goc';

export default function LoanSignPage() {
  const [result, setResult] = useState<QueryResponse>();
  const { payload, doPoll } = useQueryPoll();
  const [visible, setVisible] = useState(false);
  const END_STATUS: any = ['AUTHENTICATED', 'LOANING', 'FAILED', 'SUCCEEDED', 'CANCELLED'];

  const handleSuccess = useCallback(async () => {
    setVisible(true);
    const loanOrderId = getLoanOrderId();
    if (loanOrderId) {
      await doPoll({
        end: END_STATUS,
        request: {
          loanOrderId,
        },
      });
    }
  }, []);

  const handleResult = useCallback((status?: LOAN_STATUS) => {
    if (includes(END_STATUS, status)) {
      log.addSuccessLog('loan-signed-success');
      toLoanResult(getLoanOrderId());
    }
  }, []);

  const doInit = useCallback(async () => {
    try {
      const loanOrderId = getLoanOrderId();
      if (loanOrderId) {
        setVisible(true);
        const res = await actions.query({
          loanOrderId,
        });
        loanSignPagePageLog({
          success: true,
        });
        setVisible(false);
        // 异常情况
        if (!res?.status || res?.status === 'INIT') {
          throw new Error();
        }
        if (res?.status === 'AUTHENTICATING' || res?.status === 'AUTHENTICATED') {
          log.addSuccessLog('loan-order-fetch');
          setResult(res);
        } else {
          handleResult(res?.status);
        }
      } else {
        throw new Error();
      }
    } catch (e) {
      setVisible(false);
      loanSignPagePageLog({
        success: false,
        errorMsg: e?.message,
      });
      Modal.show({
        content: ErrorMessageMap.BUSY_DEFUALT,
        actions: [{
          key: 'retry',
          primary: true,
          text: '我知道了',
          onClick: popPage,
        }],
      });
    }
  }, []);

  useEffect(() => {
    onScroll();
    doInit();
  }, []);

  useEffect(() => {
    handleResult(payload?.status);
  }, [payload?.status]);

  return (
    <div className={styles.loanSignPage}>
      <FullLoading visible={visible} />
      <Authentication
        authenticationToken={result?.authenticationToken}
        onSuccess={handleSuccess}
        onFail={() => {}}
      />
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '借钱',
  spm: {
    spmB: PAGES.LoanSign,
  },
  window: {
    navBarImmersive: false,
  },
}));
