/**
 * @file 支用结果页面
 * <AUTHOR>
 */

import { definePageConfig } from 'ice';
import { useCallback, useEffect, useMemo, useState } from 'react';
import classnames from 'classnames';
import { number } from '@ali/iec-dtao-utils';
import { Button, Modal } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';

import { LOAN_END_STATUS, useQueryPoll } from '@/store/loan';
import { CommonResult, BankInfoTip, FullLoading, TipsBlock } from '@/components';
import { getLoanOrderId } from '@/utils/params';
import { reload, replacePage, toHome, toLoanSign, navigatorOpenURL } from '@/utils/link';
import { getFailedCodeFromOrder, _ } from '@/utils';

import styles from './index.module.scss';
import { PAGES, ErrorMessageMap, yunCustomerServiceLink } from '@/common/constant';
import { queryCustomerProfile } from '@/store/center/actions';
import { getPromotionOfferData } from '@/store/lib/format';

export const pageConfig = definePageConfig(() => ({
  title: '借钱',
  spm: {
    spmB: PAGES.LoanResult,
  },
}));

export default function LoanResultPage() {
  const [profile, setProfile] = useState<any>();
  const { payload, doPoll } = useQueryPoll();
  const MSG_MAP = {
    BANK_CARD_ABNORMAL: '您的银行卡状态异常，请使用正常服务中的银行卡收款',
    ID_CARD_INVALID: '您的身份证已过期，请联系客服处理',
    LOAN_APPLICATION_INFO_EXPIRED: '当前借款被拒绝，请重试',
    FACE_FILE_EMPTY: '当前借款被拒绝，请重试',
    FACE_PHOTO_CHECK_ERROR: '当前借款被拒绝，请重试',
    RETRY_MAX_LIMITS: '当前借款被拒绝，请重试',
    FACE_PHOTO_LACK: '当前借款被拒绝，请重试',
    FACE_PHOTO_VAGUE: '当前借款被拒绝，请重试',
    ID_CARD_PHOTO_LACK: '请先上传身份证后再借款',
    ID_CARD_PHOTO_VAGUE: '您的身份证照片模糊，请更新清晰照片后再借款',
    ILLEGAL_LOAN_APPLY_NO: '当前借款被拒绝，请重试',
    RISK_REFUSE: '当前借款被拒绝，请重试',
    OTHER: '当前借款被拒绝，请重试',
    OCCUPY_QUOTA_FAILED: '占用额度失败，请重试',
  };

  const checkIdentityCardExpire = () => {
    if (profile) {
      const content: any = _.get(_.first(profile), 'content');
      if (content?.status === 'APPROACHING_EXPIRATION') {
        return true;
      }
    }
    return false;
  };

  const getDesc = () => {
    return `处理完成将放款到${payload?.receiveBankCard?.bankName}账户`;
  };

  const getTips = () => {
    if (payload?.loanProcessStatus === 'MANUAL_REVIEW') {
      return '放款机构有可能会向您致电，请留意接听电话';
    }
    return '';
  };

  const handleBack = useCallback(() => {
    log.addClickLog('back-home');
    toHome();
  }, []);

  const renderBankIcon = useMemo(() => {
    if (payload?.receiveBankCard?.bankCode) {
      const { bankCode, bankCardNo, bankName } = payload.receiveBankCard;
      return (
        <div className={styles.bank}>
          <BankInfoTip code={bankCode} name={bankName} cardNo={bankCardNo} />
        </div>
      );
    }
    return null;
  }, [payload]);

  const renderRedPacket = () => {
    const packetImgMap = {
      SEND_SUCCEED:
        'https://gw.alicdn.com/imgextra/i3/O1CN01DAY8Ad1sweV8ekZzR_!!*************-2-tps-108-135.png',
      SENDING:
        'https://gw.alicdn.com/imgextra/i3/O1CN01DAY8Ad1sweV8ekZzR_!!*************-2-tps-108-135.png',
      SEND_FAILED:
        'https://gw.alicdn.com/imgextra/i2/O1CN01klWSkA1e2rdd2QFKD_!!*************-2-tps-108-135.png',
    };
    const promotionOfferData = getPromotionOfferData(payload?.platformPromotionOfferList, 'LOAN_RESULT_PAGE');
    if (!promotionOfferData || !promotionOfferData.offerSendStatus) return null;
    log.addShowLog('red-packet-show');
    // 跳转到客服
    const handleGoToCS = () => {
      log.addClickLog('red-packet-contactcs');
      navigatorOpenURL(yunCustomerServiceLink);
    };
    // 取给的url， 一般是手淘卡券页
    const handleGoToTaobaoKaquanPage = () => {
      if (!promotionOfferData.url) return;
      log.addClickLog('red-packet-offerurl', { url: promotionOfferData.url });
      navigatorOpenURL(promotionOfferData.url);
    };
    const { offerSendStatus } = promotionOfferData;
    let title;
    let tip;
    if (!promotionOfferData.offerSendStatus) return null;
    if (offerSendStatus === 'SEND_SUCCEED') {
      title = promotionOfferData.promotionOfferDescription;
      tip = '可在「我的淘宝-红包」查看';
    }
    if (offerSendStatus === 'SEND_FAILED') {
      title = promotionOfferData.promotionOfferDescription;
      tip = '请联系客服';
    }
    if (offerSendStatus === 'SENDING') {
      title = promotionOfferData.promotionOfferDescription;
      tip = '稍后可在「我的淘宝-红包」查看';
    }

    return (
      <div className={styles.redPacketWrap}>
        <div className={styles.leftArea}>
          <img className={styles.redPacketImg} src={packetImgMap[offerSendStatus]} />
        </div>
        <div className={styles.midArea}>
          <div className={styles.line1}>{title}</div>
          <div className={styles.line2}>{tip}</div>
        </div>
        {offerSendStatus === 'SEND_FAILED' && (
          <div
            onClick={handleGoToCS}
            className={classnames([styles.rightButton, styles.rightButtonToContact])}
          >
            去联系
          </div>
        )}
        {offerSendStatus === 'SEND_SUCCEED' && (
          <div
            onClick={handleGoToTaobaoKaquanPage}
            className={classnames([styles.rightButton, styles.rightButtonToUse])}
          >
            去查看
          </div>
        )}
      </div>
    );
  };

  const renderIdCardTip = () => {
    if (checkIdentityCardExpire()) {
      return (
        <TipsBlock className={styles.card}>
          <div className={styles.idCardTip}>
            <div className={styles.left}>
              <p className={styles.main}>身份证即将过期</p>
              <p className={styles.sub}>请尽快更新身份证信息，以免影响后续借款</p>
            </div>
            <div className={styles.right}>
              <Button
                className={styles.updateButton}
                color="primary"
                onClick={() => replacePage(PAGES.CenterCertManage)}
              >
                去更新
              </Button>
            </div>
          </div>
        </TipsBlock>
      );
    }
    return null;
  };

  const renderProcess = () => {
    return (
      <>
        <CommonResult
          title="您的借款正在处理中，请稍候"
          status="PROCESSING"
          tips={getTips()}
          description={getDesc()}
        />
        {renderBankIcon}
      </>
    );
  };

  const renderSuccess = () => {
    return (
      <>
        <CommonResult
          title={`成功借款${number.amountFormat(payload?.loanedAmount)}元`}
          status="SUCCESS"
          description="已放款到收款账户"
        />
        {renderBankIcon}
        {renderRedPacket()}
        {renderIdCardTip()}
        <Button className={styles.button} color="primary" onClick={handleBack}>
          返回首页
        </Button>
      </>
    );
  };

  const renderFail = useCallback(() => {
    const desc = MSG_MAP[getFailedCodeFromOrder(payload)] || '系统异常，请重试';
    return (
      <>
        <CommonResult title="借款失败" status="FAILED" description={desc} />
        <Button className={styles.button} color="primary" onClick={handleBack}>
          返回首页
        </Button>
      </>
    );
  }, [payload]);

  const doInit = useCallback(async () => {
    try {
      const loanOrderId = getLoanOrderId();
      const profileRes = await queryCustomerProfile({
        typeList: ['IDENTITY_CARD'],
      });
      setProfile(profileRes?.result);
      if (loanOrderId) {
        await doPoll({
          end: LOAN_END_STATUS,
          request: {
            loanOrderId,
            needLoanAuditStatus: true,
          },
        });
      } else {
        throw new Error();
      }
    } catch (e) {
      log.addErrorLog('result-poll');
      Modal.show({
        content: ErrorMessageMap.BUSY_DEFUALT,
        actions: [
          {
            primary: true,
            key: 'retry',
            onClick: reload,
            text: '我知道了',
          },
        ],
      });
    }
  }, []);

  useEffect(() => {
    doInit();
    log.addVisitLog(PAGES.LoanResult);
  }, []);

  const renderResult = () => {
    switch (payload?.status) {
      case 'AUTHENTICATING':
        toLoanSign(getLoanOrderId());
        return null;
      case 'AUTHENTICATED':
      case 'LOANING':
        log.addOtherLog('result-loading');
        return renderProcess();
      case 'CANCELLED':
      case 'FAILED':
        log.addOtherLog('result-failed');
        return renderFail();
      case 'SUCCEEDED':
        log.addOtherLog('result-succeeded');
        return renderSuccess();
      case 'INIT':
        log.addOtherLog('result-error');
        toHome();
        return null;
      default:
        return null;
    }
  };

  return (
    <div className={styles.resultPage}>
      <FullLoading visible={!payload} />
      {renderResult()}
    </div>
  );
}
