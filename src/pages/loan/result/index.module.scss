.resultPage {
  padding: 32rpx;
  text-align: center;
  .bank {
    margin: auto;
    border-radius: 8rpx;
    padding: 12rpx 18rpx;
    background-color: #f3f6f8;
    width: fit-content;
    margin-bottom: 80rpx;
  }
  .right {
    .updateButton {
      height: 56rpx;
      color: #fff;
      font-size: 26rpx;
      font-weight: 600;
      width: 126rpx;
      padding: 0;
      background: #4066ff;
      margin-top: 16rpx;
    }
  }
  .idCardTip {
    display: flex;
    flex-direction: row;
    text-align: left;
    align-items: center;
    justify-content: space-between;
    .main {
      font-size: 26rpx;
      font-weight: 500;
      line-height: 39rpx;
      color: #111;
      padding-top: 24rpx;
    }
    .sub {
      font-size: 24rpx;
      line-height: 36rpx;
      padding-top: 12rpx;
      color: #7c889c;
    }
  }
  .card {
    margin-top: 24rpx;
  }

  .button {
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
    bottom: 188rpx;
    width: 240rpx;
    height: 80rpx;
    padding: 0;
    border: 1rpx solid #4066ff;
    background-color: transparent;
    color: #4066ff;
  }
}
.commonResultWrap {
  margin-bottom: 80rpx;
}

.redPacketWrap {
  padding: 24rpx;
  border-radius: 12rpx;
  background: #fff;
  display: flex;
  .redColor {
    color: #f33;
  }
  .leftArea {
    background: #f3f6f8;
    border-radius: 12rpx;
    width: 104rpx;
    height: 104rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12rpx;
    .redPacketImg {
      width: 54rpx;
      height: 68rpx;
    }
  }
  .midArea {
    flex: 1 1 auto;
    text-align: left;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .line1 {
      color: #11192d;
      line-height: 30rpx;
      margin-bottom: 16rpx;
      font-weight: 600;
    }
    .line2 {
      color: #7c889c;
    }
  }
  .rightButton {
    flex: 0 0 126rpx;
    border-radius: 12rpx;
    color: #fff;
    height: 56rpx;
    background: #f33;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-top: 24rpx;
    font-weight: 600;
  }
  .rightButtonToUse {
    background: #f33;
    color: #fff;
  }
  .rightButtonToContact {
    background: #f3f6f8;
    color: #11192d;
  }
}

