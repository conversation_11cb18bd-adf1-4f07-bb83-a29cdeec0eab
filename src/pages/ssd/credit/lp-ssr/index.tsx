/**
 * @file 蚂蚁智信授信SSR
 * <AUTHOR>
 */


import { useRef, useState, useEffect, useCallback, Fragment } from 'react';
import { definePageConfig, defineServerDataLoader, defineDataLoader, ClientOnly } from 'ice';
import { log } from '@alife/dtao-iec-spm-log';
import dayjs from 'dayjs';
import { Button, Toast, Modal } from 'antd-mobile';
import { useDebounceFn } from 'ahooks';

import CommonPopup from '@/components/CommonPopup';
import classnames from 'classnames';
import { useVisibilityChangeRefresh } from '@/hooks';
import { getEnvData } from '@/components/SSD/VerifyIdentity/libs/bridge';
import { SsdHomeSkeleton } from '@/components/SSD/Skeleton';
import {
  idempotentId,
  _,
  getMsgByErrorCode,
  getErrorCodeFromRejectRes,
  dayjsFormat,
  getFailedCodeFromOrder,
  getOrigin,
} from '@/utils';
import BottomTextBar from '@/components/SSD/BottomTextBar';
import { UnsignedAgreementsSSD, UnsignedAgreementGroup } from '@/components/SSD/UnsignedAgreements';
import { useVerifyIdentity } from '@/components/index';
import {
  postCreditApply,
  queryCreditOrder,
  postCreditApplySupplement,
  queryInstitutionCreditOrder,
} from '@/store/credit/actions';
import LinkUtil from '@/utils/link';
import { PAGES, SSDcreditLpWaitingSeconds } from '@/common/constant';
import useCountDown from '@/hooks/useCountDown';
import { VerifyStatus } from '@/components/SSD/VerifyIdentity/libs/types';
import { getSecureToken } from '@/utils/umid-token';
import { ssdCreditLpSSR, ssdCreditLpCSR, SsdCreditLpFSPData } from '@/services/ssd-credit-lp';
import { useFSPData } from '@/hooks/useFSPData';
import { checkUnsignAgreementEmpty, getCreditType, checkInstitutionCreditingFn } from '@/store/credit/lib';
import { checkMayizhixinCreditRouter } from '@/store/credit/model';
import Layout from '@/components/Layout/ssd';
import { addInitLog } from '@/utils/log';
import useCreditConsult from '@/hooks/useCreditConsult';

import styles from './index.module.scss';

export const serverDataLoader = defineServerDataLoader(async () => {
  const data = await ssdCreditLpSSR();
  return data;
});

export const dataLoader = defineDataLoader(async () => {
  const data = await ssdCreditLpCSR('PREFETCH');
  return data;
});

function getValueFromExtension(extension, key) {
  try {
    return _.get(extension, key);
  } catch (error) {
    return null;
  }
}

function addMinutes(t, num) {
  return dayjs(t).add(num, 'minutes');
}

export default function SsdCreditLp() {
  const { fspData } = useFSPData<SsdCreditLpFSPData>({
    init: ssdCreditLpCSR,
  });
  const { creditConsultData, queryConsult } = useCreditConsult({
    default: fspData?.data?.creditApplyConsult,
  });
  const [curCreditOrder, setCurCreditOrder] = useState<any>(null);
  const [loadingVisible, setLoadingVisible] = useState(false);
  const [showMinuteLevelText, setShowMinuteLevelText] = useState(false);
  const [isInstitutionCrediting, setIsInstitutionCrediting] = useState(false);
  const [creditFailPopupData, setCreditFailPopupData] = useState<any>({});
  const [showApplyRejectResult, setShowApplyRejectResult] = useState(false);
  const commonPopupRef = useRef<any>(null);
  const platformAgreementsRef = useRef<any>(null);
  const creditAgreementsRef = useRef<any>(null);
  const [institutionCreditOrder, setInstitutionCreditOrder] = useState<any>(null);
  const { start, count } = useCountDown({ duration: 1000 });
  const { startVerify } = useVerifyIdentity();

  useVisibilityChangeRefresh(queryConsult);

  const queryInstitutionCreditTime = async (orderId) => {
    try {
      const res = await queryInstitutionCreditOrder({
        creditApplyOrderId: orderId,
      });
      setInstitutionCreditOrder(res);
    } catch (error) {
      log.addErrorLog('query-institution-credit-order-error', { code: error.message });
    }
  };

  const queryCreditOrderFn = async (orderId, verifyStarted = false) => {
    try {
      const orderRes = await queryCreditOrder({
        creditApplyOrderId: orderId,
      });
      if (orderRes.status === 'INIT') {
        Toast.show('申请单状态异常，请联系客服');
      }
      if (orderRes.status === 'SUCCEEDED') {
        log.addSuccessLog('ssd-credit-lp-credit-success');
        LinkUtil.toSsdHomePlus();
      }
      if (orderRes.status === 'FAILED') {
        handleApplyError(orderRes);
      }
      if (orderRes.subStatus === 'WAIT_TO_SIGN_AGREEMENT') {
        // 弹出协议弹窗
        await creditAgreementsRef.current.doInit({
          fundSupplierCode: getValueFromExtension(orderRes.extension, 'fundSupplierCode'),
          extension: orderRes.extension,
          creditType: getCreditType(fspData?.data?.creditApplyConsult),
        });
        await creditAgreementsRef.current.show({
          action: 'compulsory',
        });
        log.addShowLog('ssd-credit-lp-credit-agreement-show');
      }

      if (orderRes.subStatus === 'WAIT_TO_AUTH') {
        if (verifyStarted) {
          setTimeout(() => {
            queryCreditOrderFn(orderId, true);
          }, 1000);
          return;
        }
        setLoadingVisible(true);

        log.addShowLog('ssd-credit-lp-verify-start');
        const verifyRes = await startVerify({
          authenticationToken: _.get(orderRes, 'applicationData.authentication.token'),
        });

        if (verifyRes.status === VerifyStatus.PASS) {
          log.addSuccessLog('ssd-credit-lp-verify-pass');
          queryCreditOrderFn(orderId, true);
          setIsInstitutionCrediting(true);
          start(SSDcreditLpWaitingSeconds); // 核身成功之后开始读秒
        } else if (verifyRes.status === VerifyStatus.CANCEL) {
          // 取消 do nothing
        } else {
          // Toast.show('扫脸认证失败');
        }
        log.addOtherLog('ssd-credit-lp-verify-result', { status: verifyRes.status });
        setLoadingVisible(false);
      }
      if (checkInstitutionCreditingFn(orderRes.subStatus)) {
        log.addShowLog('ssd-credit-lp-institution-crediting');
        setIsInstitutionCrediting(true);
        setTimeout(() => {
          queryCreditOrderFn(orderId);
        }, 1500);
      }
      setCurCreditOrder(orderRes);
    } catch (error) {
      log.addErrorLog('query-credit-order-fn');
    } finally {
      setLoadingVisible(false);
    }
  };

  // 初始化只使用fspData的数据
  const doInit = async () => {
    if (!fspData?.data?.creditApplyConsult) {
      return;
    }
    const { creditApplyConsult } = fspData.data;
    addInitLog(fspData);

    const routerPage = checkMayizhixinCreditRouter(fspData?.data?.creditApplyConsult);
    // 已开通
    if (routerPage === PAGES.SsdHome) {
      LinkUtil.locationReplace(PAGES.SsdHome);
      return;
    }
    if (routerPage === PAGES.SsdHomePlus) {
      LinkUtil.locationReplace(PAGES.SsdHomePlus);
      return;
    }
    // 极简开通
    if (routerPage === PAGES.SsdCreditLpSimple) {
      LinkUtil.locationReplace(PAGES.SsdCreditLpSimple);
      return;
    }
    // 非蚂蚁智信
    if (creditApplyConsult?.creditPlatform !== 'MAYI_ZHIXIN') {
      LinkUtil.locationReplace(PAGES.CreditLp);
      return;
    }
    log.addVisitLog(PAGES.SsdCreditLp, _.pick(creditApplyConsult, ['admitted', 'canCreditApply']));
    const creditApplyOrder = creditApplyConsult?.latestCreditApplyOrder;
    if (creditApplyOrder) {
      setCurCreditOrder(creditApplyOrder);
      if (checkInstitutionCreditingFn(creditApplyOrder.subStatus)) {
        await queryCreditOrderFn(creditApplyOrder.creditApplyOrderId);
      }
    }
  };

  const handleCreditAgreementCompleted = useCallback(async () => {
    log.addOtherLog('ssd-credit-lp-credit-agreement-completed');
    const { creditApplyOrderId, subStatus } = curCreditOrder;
    setLoadingVisible(true);
    try {
      const supplementRes: any = await postCreditApplySupplement({
        currentSubStatus: subStatus,
        creditApplyOrderId,
        extension: creditAgreementsRef.current?.getExtension(),
      });

      if (supplementRes.subStatus === 'WAIT_TO_AUTH') {
        queryCreditOrderFn(creditApplyOrderId);
      }
    } catch (e) {
      log.addErrorLog('credit-agreement-completed');
      Modal.alert({
        title: '申请失败',
        content: getMsgByErrorCode(e.message),
      });
      setLoadingVisible(false);
    }
  }, [curCreditOrder]);

  const handleAgreementClick = useCallback((title: string, activeKey?: string) => {
    platformAgreementsRef.current?.show({
      action: 'preview',
      activeKey,
      popupProps: {
        title,
      },
    });
    log.addShowLog('ssd-credit-lp-platform-agreement-show');
  }, []);

  const handleApplyError = (order) => {
    const code = getFailedCodeFromOrder(order);
    if (
      [
        'S_UNBOUND_MOBILE_NO',
        'C_PERMIT_ALIPAY_ACCOUNT',
        'S_SAME_PERSON_DIFFERENT_ALIPAY_ACCOUNT',
      ].includes(code)
    ) {
      if (code === 'S_UNBOUND_MOBILE_NO') {
        setCreditFailPopupData({
          code,
          iconUrl:
            'https://gw.alicdn.com/imgextra/i3/O1CN01s4i72Q28gC5EPLgQg_!!*************-2-tps-192-192.png',
          title: (
            <div>
              <div>
                你绑定的支付宝账号
                <span className={styles.accountName}>
                  {getValueFromExtension(order.extension, 'alipayDesensitizeLoginId')}
                </span>
              </div>

              <div>需补充手机号，更新后可继续查看额度</div>
            </div>
          ),
          desc: '建议前往支付宝进行更新',
          btnText: '前往支付宝',
          link: getValueFromExtension(order.extension, 'unLimitUrl'),
        });
      }
      if (code === 'C_PERMIT_ALIPAY_ACCOUNT') {
        setCreditFailPopupData({
          code,
          iconUrl:
            'https://gw.alicdn.com/imgextra/i2/O1CN01zBssXf1y95IKJCdzu_!!*************-2-tps-192-192.png',
          title: '认证身份可继续查看额度',
          desc: '完善个人信息',
          btnText: '去认证',
          link: getValueFromExtension(order.extension, 'unLimitUrl'),
        });
      }
      if (code === 'S_SAME_PERSON_DIFFERENT_ALIPAY_ACCOUNT') {
        setCreditFailPopupData({
          code,
          iconUrl:
            'https://gw.alicdn.com/imgextra/i4/O1CN01cKITBm1z4KnlI6ynV_!!*************-2-tps-192-192.png',
          title: (
            <div>
              <div>
                你在账号
                <span className={styles.accountName}>
                  {getValueFromExtension(order.extension, 'otherAlipayBoundTaobaoNick')}
                </span>
              </div>
              <div>已有随身贷额度</div>
            </div>
          ),
          desc: '请前往「手机淘宝-我的淘宝-设置」切换账号',
          // btnText: '切换账号',
        });
      }
      log.addShowLog('ssd-credit-lp-credit-fail-popup-show', { code });
      commonPopupRef.current.toggleVisible(true);
    } else {
      setShowApplyRejectResult(true);
      queryConsult();
    }
  };

  const { run: handleMainBtnClick } = useDebounceFn(
    async (noUnSignAgreementVal) => {
      log.addClickLog('main-btn-click', { noUnSignAgreementVal });
      const newConsultData: any = await queryConsult();
      if (!newConsultData) {
        Toast.show('申请失败，请稍后再试');
        return;
      }

      const { applyType, canCreditApply, creditPlatform } = newConsultData || {};

      // 如果不准入
      if (!canCreditApply) {
        Toast.show('暂无法申请');
        return;
      }

      setLoadingVisible(true);

      try {
        const applyExtension = {
          ...platformAgreementsRef.current?.getExtension(),
          envData: await getEnvData(),
        };
        const secureTokenRes = await getSecureToken();
        if (secureTokenRes?.umidToken) {
          _.set(applyExtension, 'umidToken', secureTokenRes?.umidToken);
        }
        if (secureTokenRes?.apdidToken) {
          _.set(applyExtension, 'apdidToken', secureTokenRes?.apdidToken);
        }
        _.set(applyExtension, 'origin', getOrigin());
        const postRes = await postCreditApply({
          requestId: idempotentId(),
          applyType,
          creditPlatform,
          applyTime: Date.now(),
          channel: 'APP',
          extension: applyExtension,
          creditType: getCreditType(fspData?.data?.creditApplyConsult),
        });
        if (postRes.creditApplyOrderId) {
          log.addSuccessLog('ssd-credit-lp-apply-success');
          queryCreditOrderFn(postRes.creditApplyOrderId);
        } else {
          throw new Error(getErrorCodeFromRejectRes(postRes));
        }
      } catch (e) {
        log.addErrorLog('ssd-credit-lp-apply-error', { code: e.message });
        setLoadingVisible(false);
        Modal.alert({
          title: '申请失败',
          content: getMsgByErrorCode(e.message),
        });
      }
    },
    {
      wait: 1000,
      leading: true,
      trailing: false,
    },
  );

  const checkCreditFallBack = () => {
    if (!creditConsultData) {
      return true;
    }
    // 申请后不准入
    if (showApplyRejectResult === true) {
      return true;
    }
    const { latestCreditApplyOrder, canCreditApply } = creditConsultData;
    // 申请失败、不准入
    if (latestCreditApplyOrder?.status === 'FAILED' && !canCreditApply) {
      return true;
    }
    // 不准入
    if (!canCreditApply && !latestCreditApplyOrder) {
      return true;
    }
    return false;
  };

  const getExtensionParams = useCallback(
    (unsignedAgreementGroup: UnsignedAgreementGroup) => {
      try {
        const curCreditOrderExtension = curCreditOrder?.extension;
        const unsignedAgreementExtension: any = unsignedAgreementGroup?.extension;
        const fundSupplierCode = getValueFromExtension(curCreditOrderExtension, 'fundSupplierCode');
        return {
          fundSupplierCode,
          extension: JSON.stringify({
            passthroughInfo: {
              ...unsignedAgreementExtension?.passthroughInfo,
              ...curCreditOrderExtension?.passthroughInfo,
            },
          }),
        };
      } catch (error) {
        return {};
      }
    },
    [curCreditOrder],
  );

  const renderAgreementsName = (res) => {
    if (res?.unSignedAgreementGroupList) {
      const items = _.filter(res.unSignedAgreementGroupList, (item) => {
        if (_.isEmpty(item.unSignedAgreementList)) {
          return false;
        }
        return ['AGREEMENT_CENTER', 'MAYI_ZHIXIN'].includes(item.source);
      });
      return !_.isEmpty(items) ? (
        <span className={styles.agreementNames}>
          <span className={styles.commonText}>查阅</span>
          {items.map((item, index) => {
            const title = item.source === 'MAYI_ZHIXIN' ? '随身贷相关协议' : '借钱服务相关协议';
            const agreementCode = _.get(_.first(item?.unSignedAgreementList), 'code');
            return (
              <span key={`platform-unsigned-${index}`}>
                {index > 0 ? <span className={styles.commonText}>及</span> : ''}
                <span
                  onClick={() => handleAgreementClick('服务协议', agreementCode)}
                  className={styles.nameItem}
                >
                  {title}
                </span>
              </span>
            );
          })}
        </span>
      ) : null;
    }
    return null;
  };

  const renderFailedStatusMainCard = () => {
    if (!creditConsultData) {
      return null;
    }
    try {
      const isFAILED = _.get(creditConsultData, 'latestCreditApplyOrder.status') === 'FAILED';
      const failedCode = _.get(creditConsultData, 'latestCreditApplyOrder.failedReason.failedCode') || '';
      const isMayiConsultFailed = !failedCode.startsWith('APPLY_FAILED_');
      const isMayiApplyFailed = failedCode.startsWith('APPLY_FAILED_');

      log.addShowLog('ssd-credit-lp-failed-status-card', {
        failedCode,
        isFAILED,
        isMayiConsultFailed,
        isMayiApplyFailed,
        isNotAoGe: !_.get(creditConsultData, 'latestCreditApplyOrder'),
      });
      if (isMayiConsultFailed) {
        log.addShowLog('credit-failed-isMayiConsultFailed');
      }
      if (isMayiApplyFailed) {
        log.addShowLog('credit-failed-isMayiApplyFailed');
      }
    } catch (error) {}
    const { canCreditApplyTime } = creditConsultData;
    if (canCreditApplyTime) {
      log.addShowLog('credit-failed-hasCanCreditApplyTime');
    } else {
      log.addShowLog('credit-failed-noCanCreditApplyTime');
    }
    return (
      <div className={styles.mainCard}>
        <div className={styles.failedIcon}>
          <img src="https://gw.alicdn.com/imgextra/i3/O1CN01aaQw1u1GouNhG8gR1_!!6000000000670-2-tps-192-192.png" />
        </div>
        <div className={styles.failedTitle}>
          {canCreditApplyTime ? '额度评估未通过' : '随身贷逐步开放中'}
        </div>
        {canCreditApplyTime ? (
          <div className={styles.failedDesc}>
            预估{dayjsFormat(canCreditApplyTime, 'MM月DD日')}可再来尝试申请
          </div>
        ) : (
          <div className={styles.failedDesc}>服务暂未开放到你，额度开放时将及时通知你</div>
        )}
      </div>
    );
  };

  const renderNormalStatusMainCard = () => {
    return (
      <div className={styles.mainCard}>
        <div className={styles.cardTitle}>你可以借</div>
        <div className={styles.quotaStar}>*****</div>
        {renderCardDesc()}
        {renderMainBtn()}
        <div className={classnames([checkUnsignAgreementEmpty(fspData?.data?.unsignedAgreementQuery, isInstitutionCrediting) && styles.hidePlatformAgreements])}>
          <UnsignedAgreementsSSD
            renderName={renderAgreementsName}
            bizType="PLATFORM"
            creditType={getCreditType(fspData?.data?.creditApplyConsult)}
            creditPlatform="MAYI_ZHIXIN"
            institutionList={['MAYI_ZHIXIN']}
            ref={platformAgreementsRef}
            containerClassName={styles.agreementContainer}
            customPayload={fspData?.data?.unsignedAgreementQuery}
            useInit={false}
          />
        </div>
      </div>
    );
  };

  const handlecreditFailPopupBtnClick = useCallback((link, code) => () => {
    log.addClickLog('credit-fail-popup-btn', { code });
    LinkUtil.navigatorToAlipay(link);
  }, []);

  // const handleGotoModifyBindAlipay = () => {
  //   log.addClickLog('goto-modify-bind-alipay');
  //   LinkUtil.navigatorToOutside(
  //     'https://passport.taobao.com/ac/h5/alipay_management.html?fromSite=0#/',
  //   );
  // };

  const renderCardDesc = () => {
    if (!creditConsultData) {
      return null;
    }
    const { latestCreditApplyOrder } = creditConsultData;
    if (isInstitutionCrediting || checkInstitutionCreditingFn(latestCreditApplyOrder?.subStatus)) {
      if (!showMinuteLevelText) {
        return <div className={styles.cardDesc} />;
      }
      // 如果超过1分钟， 就用加速审批中兜底
      const afterApplyTimeOneMinute = addMinutes(institutionCreditOrder?.institutionApplyTime, 1);
      const text =
        institutionCreditOrder?.institutionApplyTime &&
        dayjs().isBefore(dayjs(afterApplyTimeOneMinute))
          ? '预计1分钟内完成'
          : '加速审批中';
      return (
        <div className={styles.cardDesc}>
          {/* <img src="https://gw.alicdn.com/imgextra/i1/O1CN01c1ugpV1HVxcFrZiL6_!!6000000000764-2-tps-56-56.png" /> */}
          <span className={styles.cardDescText}>{text}，完成后短信通知你</span>
        </div>
      );
    }
    return (
      <div className={styles.cardDesc}>
        <span className={styles.cardDescText}>利率低至3.6%</span>
        <span className="common-divide-line-vertical"> </span>
        <span className={styles.cardDescText}>借1千元1天仅0.1元</span>
      </div>
    );
  };

  const renderMainBtn = () => {
    if (isInstitutionCrediting) {
      return (
        <div className={styles.mainBtnWrap}>
          {/* {institutionCreditOrder?.institutionApplyTime && (
            <div className={styles.mainBtnPopover}>
              加速审批中，预计
              {dayjsFormat(addMinutes(institutionCreditOrder.institutionApplyTime, 5), 'HH:mm')}
              前审批完成
            </div>
          )} */}
          <Button
            className={styles.mainBtn}
            disabled
            size="large"
            block
            shape="rounded"
            color="primary"
          >
            额度计算中 {count > 0 ? `${count}s` : ''}
          </Button>
        </div>
      );
    }
    const noUnSignAgreement = checkUnsignAgreementEmpty(fspData?.data?.unsignedAgreementQuery);
    const mainBtnText = noUnSignAgreement ? '查看额度' : '同意协议并查看额度';
    return (
      <Button
        loading={loadingVisible}
        loadingText={mainBtnText}
        className={styles.mainBtn}
        onClick={() => handleMainBtnClick(noUnSignAgreement)}
        disabled={noUnSignAgreement === null}
        size="large"
        block
        shape="rounded"
        color="primary"
      >
        {mainBtnText}
      </Button>
    );
  };

  const pageHeadRender = () => {
    return (
      <Fragment>
        <div className={styles.title}>
          <i className={styles.logo} />
          随身贷
        </div>
        <div className={styles.description}>蚂蚁智信及其合作伙伴为你提供服务</div>
      </Fragment>
    );
  };

  const renderCompulsory = () => {
    const orderExtension = _.get(curCreditOrder, 'extension');
    const alipayDenseyLoginId = getValueFromExtension(
      orderExtension,
      'alipayDesensitizeLoginId',
    );
    const userDenseName = getValueFromExtension(orderExtension, 'userDesensitizeName');
    if (!alipayDenseyLoginId) return null;
    return (
      <div className={styles.alipayAccountArea}>
        <div className={styles.areaLine1}>
          你的支付宝账号
          <div className={styles.userAlipayAccount}>
            {alipayDenseyLoginId}（{userDenseName}）
          </div>
        </div>
        {/* <span onClick={handleGotoModifyBindAlipay} className={styles.areaModifyText}>
          修改 */}
        {/* <img src="https://gw.alicdn.com/imgextra/i2/O1CN011FMLaF1hWa9QC8S0y_!!*************-2-tps-48-48.png" /> */}
        {/* </span> */}
      </div>
    );
  };

  const renderCreditSignAgreementName = () => {
    return null;
  };

  const renderCreditUnsigned = () => {
    return (
      <UnsignedAgreementsSSD
        renderName={renderCreditSignAgreementName}
        renderCompulsory={renderCompulsory}
        useInit={false}
        onCompleted={handleCreditAgreementCompleted}
        bizType="CREDIT"
        creditType={getCreditType(fspData?.data?.creditApplyConsult)}
        creditPlatform="MAYI_ZHIXIN"
        institutionList={['MAYI_ZHIXIN']}
        popupTitle="查看额度"
        ref={creditAgreementsRef}
        getExtensionParams={getExtensionParams}
        containerClassName={styles.agreementContainer}
      />
    );
  };

  const renderFailPopup = () => {
    return (
      <CommonPopup ref={commonPopupRef}>
        <div className={styles.creditFailPopupContainer}>
          <div className={styles.creditFailIconWrap}>
            <img src={creditFailPopupData.iconUrl} />
          </div>
          <div className={styles.creditFailTitle}>{creditFailPopupData.title}</div>
          <div className={styles.creditFailDesc}>{creditFailPopupData.desc}</div>
          <div>
            {creditFailPopupData.btnText && (
              <Button
                onClick={handlecreditFailPopupBtnClick(creditFailPopupData.link, creditFailPopupData.code)}
                shape="rounded"
                color="primary"
                block
                size="large"
              >
                {creditFailPopupData.btnText}
              </Button>
            )}
          </div>
        </div>
      </CommonPopup>
    );
  };

  const checkIsOpen = () => {
    try {
      if (fspData?.data?.creditApplyConsult) {
        const openRoute = checkMayizhixinCreditRouter(fspData?.data?.creditApplyConsult);
        const creditPlatform = fspData?.data?.creditApplyConsult?.creditPlatform;
        if (openRoute === PAGES.SsdHome || openRoute === PAGES.SsdHomePlus || creditPlatform !== 'MAYI_ZHIXIN') {
          return true;
        }
      }
      return false;
    } catch (e) {
      return false;
    }
  };

  // 初始化
  useEffect(() => {
    doInit();
  }, [fspData?.data?.creditApplyConsult]);

  // 轮询授信单过程中当前授信单状态变化
  useEffect(() => {
    if (curCreditOrder) {
      if (checkInstitutionCreditingFn(curCreditOrder.subStatus) && count === 0) {
        log.addShowLog('institution-crediting-minute-level');
        setShowMinuteLevelText(true);
        !institutionCreditOrder && queryInstitutionCreditTime(curCreditOrder.creditApplyOrderId);
      }
    }
  }, [count, curCreditOrder]);

  // 埋点专用
  useEffect(() => {
    if (!checkCreditFallBack() && !showApplyRejectResult) {
      log.addShowLog('credit-apply-normal');
    }
  }, [creditConsultData, showApplyRejectResult]);

  // 降级后优先展示骨架屏
  if (!fspData) {
    return (
      <Layout>
        <div className={styles.ssdCreditLp}>
          {pageHeadRender()}
          <SsdHomeSkeleton />
        </div>
      </Layout>
    );
  }

  // 已开通或阿里云链路不进行渲染
  if (checkIsOpen()) {
    return (
      <Layout>
        <div className={styles.ssdCreditLp} />
      </Layout>
    );
  }

  // SSR
  return (
    <Layout>
      <div className={styles.ssdCreditLp}>
        {pageHeadRender()}
        {checkCreditFallBack() ? renderFailedStatusMainCard() : renderNormalStatusMainCard()}
        <BottomTextBar
          hideCenter={!fspData?.data?.creditApplyConsult?.isPlatformAgreementSigned}
        />
        <ClientOnly>
          {renderFailPopup}
        </ClientOnly>
        <ClientOnly>
          {renderCreditUnsigned}
        </ClientOnly>
      </div>
    </Layout>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '借钱',
  spm: {
    spmB: PAGES.SsdCreditLp,
  },
}));
