.ssdCreditLp {
  padding: 24rpx 16rpx;
  text-align: center;
  .logo {
    display: inline-block;
    background-image: url("https://gw.alicdn.com/imgextra/i3/O1CN01wlOyxv1SEygc0C2cH_!!6000000002216-2-tps-144-144.png");
    width: 44rpx;
    height: 44rpx;
    background-repeat: no-repeat;
    background-size: contain;
    margin-right: 8rpx;
  }
  .title {
    font-weight: 500;
    font-size: 36rpx;
    color: #000;
    margin-bottom: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .description {
    font-weight: 400;
    font-size: 24rpx;
    padding-top: 4rpx;
    margin-bottom: 40rpx;
    color: rgba(#000, 40%);
    letter-spacing: 0;
  }
  .mainCard {
    padding: 157rpx 32rpx;
    padding-top: 162rpx;
    background-color: #fff;
    border-radius: 24rpx;
    height: 841rpx;
    .cardTitle {
      font-size: 32rpx;
      color: #000;
      margin-bottom: 6rpx;
    }
    .quotaStar {
      font-size: 132rpx;
      font-family: "AlibabaSans102Ver2";
      height: 166rpx;
      letter-spacing: 7rpx;
    }
    .mainBtn {
      width: 526rpx;
      text-align: center;
      margin: auto;
      margin-top: 121rpx;
      margin-bottom: 24rpx;
      font-weight: 600;
    }
    .mainBtnBottomText {
      color: rgba(#000, 40%);
    }
    .cardDesc {
      min-height: 33rpx;
      font-size: 24rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      :global(.common-divide-line-vertical) {
        height: 20rpx;
        margin: 0 16rpx;
      }
      .cardDescText {
        color: rgba(#000, 80%);
      }
      img {
        width: 28rpx;
        height: 28rpx;
        margin-right: 12rpx;
      }
    }

    .failedIcon {
      margin-top: 107rpx;
      margin-bottom: 48rpx;
      text-align: center;
      img {
        width: 96rpx;
        height: 96rpx;
      }
    }
    .failedTitle {
      font-size: 36rpx;
      color: rgba(#000, 80%);
      margin-bottom: 16rpx;
    }
    .failedDesc {
      font-size: 28rpx;
      color: rgba(#000, 40%);
    }
  }
}

.agreementNames {
  position: relative;
  letter-spacing: 0;
  .nameItem {
    opacity: 1;
    color: var(--color);
  }
  .commonText {
    color: rgba(#000, 40%);
  }
}

.alipayAccountArea {
  background-color: #f6f8fa;
  border-radius: 16rpx;
  padding: 32rpx;
  position: relative;
  .areaLine1 {
    color: rgba(#000, 40%);
    font-size: 28rpx;
    line-height: 40rpx;
  }
  .userAlipayAccount {
    display: inline-block;
    font-size: 28rpx;
    line-height: 40rpx;
    color: rgba(#000, 80%);
    margin-left: 8rpx;
  }
  .areaModifyText {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 24rpx;
    color: #4b6b99;
    display: inline-flex;
    align-items: center;
    img {
      width: 24rpx;
      height: 24rpx;
      margin-left: 4rpx;
    }
  }
}

.creditFailPopupContainer {
  text-align: center;
  padding-top: 80rpx;
  .creditFailIconWrap {
    img {
      width: 96rpx;
      height: 96rpx;
    }
  }
  .creditFailTitle {
    font-weight: 500;
    font-size: 36rpx;
    color: rgba(#000, 80%);
    margin-top: 48rpx;
  }
  .creditFailDesc {
    display: inline-block;
    width: 580rpx;
    font-size: 28rpx;
    color: rgba(#000, 40%);
    margin-top: 16rpx;
    margin-bottom: 200rpx;
  }
}

.mainBtnPopover {
  position: absolute;
  top: -55rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgb(231, 241, 255);
  padding: 12rpx 15rpx;
  color: #1677ff;
  font-size: 24rpx;
  display: flex;
  border-radius: 8rpx;
  white-space: nowrap;
  z-index: 10;
  &::after {
    content: "";
    display: block;
    position: absolute;
    bottom: -5rpx;
    left: 50%;
    transform: translateX(-50%) rotate(-45deg);
    background-color: rgb(231, 241, 255);
    width: 12rpx;
    height: 12rpx;
    border-radius: 3rpx;
    box-sizing: content-box !important;
  }
}
.mainBtnWrap {
  position: relative;
}

.accountName {
  color: var(--primary);
}

.agreementContainer {
  height: 1080rpx;
}


.hidePlatformAgreements {
  opacity: 0;
  height: 0;
}