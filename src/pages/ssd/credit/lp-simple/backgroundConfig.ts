// 背景图配置
export const BACKGROUND_CONFIG = {
  // 默认背景图（无红包状态）
  DEFAULT: {
    SIMPLE_CREDIT_V1: 'https://gw.alicdn.com/imgextra/i2/O1CN018JMO5J1gI62bWozk7_!!6000000004118-0-tps-1500-3248.jpg',
    SIMPLE_CREDIT_V4: 'https://gw.alicdn.com/imgextra/i1/O1CN01PtkS8K1uFicfKola0_!!6000000006008-0-tps-1500-3248.jpg',
    SIMPLE_CREDIT_V5: 'https://gw.alicdn.com/imgextra/i2/O1CN01BOsEmq22OtU4KsAoB_!!6000000007111-0-tps-1500-3248.jpg',
    SIMPLE_CREDIT_V6: 'https://gw.alicdn.com/imgextra/i2/O1CN018JMO5J1gI62bWozk7_!!6000000004118-0-tps-1500-3248.jpg',
  },

  // 普通红包背景图（按版本）
  RED_PACKET_VERSIONS: {
    SIMPLE_CREDIT_V1: 'https://gw.alicdn.com/imgextra/i2/O1CN016vm0vn1R5zMAzvCye_!!6000000002061-0-tps-1500-3248.jpg',
    SIMPLE_CREDIT_V3: 'https://gw.alicdn.com/imgextra/i4/O1CN01KzrH5L1S0mT8t0pg7_!!6000000002185-0-tps-1500-3248.jpg',
    SIMPLE_CREDIT_V4: 'https://gw.alicdn.com/imgextra/i2/O1CN01hJzLkO1Z1sjPoG3zP_!!6000000003135-0-tps-1500-3248.jpg',
    // NOTE: v5 效果不佳下线。无红包效果
    SIMPLE_CREDIT_V5: 'https://gw.alicdn.com/imgextra/i2/O1CN016vm0vn1R5zMAzvCye_!!6000000002061-0-tps-1500-3248.jpg',
    // NOTE: v6 有红包展示气泡，不展示红包背景
    SIMPLE_CREDIT_V6: 'https://gw.alicdn.com/imgextra/i2/O1CN018JMO5J1gI62bWozk7_!!6000000004118-0-tps-1500-3248.jpg',
  },

  // 芭芭农场专属背景图（按版本）
  BABANONGCHANG_VERSIONS: {
    SIMPLE_CREDIT_V1: 'https://gw.alicdn.com/imgextra/i1/O1CN01KI7OU21fJBO4S6E2C_!!6000000003985-0-tps-1500-3248.jpg',
    SIMPLE_CREDIT_V3: 'https://gw.alicdn.com/imgextra/i3/O1CN01yWQYEq27ieejhGTJK_!!6000000007831-0-tps-1500-3248.jpg',
    SIMPLE_CREDIT_V4: 'https://gw.alicdn.com/imgextra/i1/O1CN012Man6g20KeafMfBNM_!!6000000006831-0-tps-1500-3248.jpg',
    // NOTE: v5 效果不佳下线。无红包效果
    SIMPLE_CREDIT_V5: 'https://gw.alicdn.com/imgextra/i1/O1CN01KI7OU21fJBO4S6E2C_!!6000000003985-0-tps-1500-3248.jpg',
    // NOTE: v6 有红包展示气泡，不展示红包背景
    SIMPLE_CREDIT_V6: 'https://gw.alicdn.com/imgextra/i2/O1CN018JMO5J1gI62bWozk7_!!6000000004118-0-tps-1500-3248.jpg',
  },
};

/**
 * 获取背景图URL
 * @param creditTypeVersion 页面版本 (v1|v3|v4|v5)
 * @param hasPageRedPacket 是否有红包
 * @param origin 来源标识
 * @returns 背景图URL
 */
export const getBackgroundImage = (
  creditTypeVersion: string,
  hasPageRedPacket: boolean,
  origin?: string,
) => {
  // 红包版本
  if (hasPageRedPacket) {
    // 芭芭农场特供
    if (origin === 'babanongchang') {
      return BACKGROUND_CONFIG.BABANONGCHANG_VERSIONS[creditTypeVersion];
    }
    return BACKGROUND_CONFIG.RED_PACKET_VERSIONS[creditTypeVersion];
  }

  // 无红包：展示默认背景图
  return BACKGROUND_CONFIG.DEFAULT[creditTypeVersion];
};
