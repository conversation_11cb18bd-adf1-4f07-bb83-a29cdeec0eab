import { useMemo } from 'react';
import { UnsignedAgreementList } from '@/store/agreement/actions';
import { log } from '@alife/dtao-iec-spm-log';

function arrayToTextWithLimit(arr: string[], limit: number = arr.length): string {
  if (!Array.isArray(arr) || arr.length === 0 || limit <= 0) {
    return '';
  }

  // 限制展示的个数
  const displayedList = arr.slice(0, limit);
  // const remainingCount = arr.length - limit;

  if (displayedList.length === 1) {
    return displayedList[0];
  }

  const lastInstitution = displayedList.pop() as string;
  const baseText = `${displayedList.join('、') }及${ lastInstitution}`;

  return baseText;
}

function getFundSupplierNameList(unsignedAgreementList: UnsignedAgreementList[]) {
  try {
    return Array.from(new Set(unsignedAgreementList?.reduce((prev, next) => {
      return [
        ...prev,
        ...(next.unSignedAgreementList || [])?.map((it) => it.fundSupplierName!),
      ];
    }, [] as string[]).filter(((_) => !!_))));
  } catch {
    log.addErrorLog('calculate-fund-supplier-name-list', {
      unsignedAgreement: JSON.stringify(unsignedAgreementList),
    });
    return [];
  }
}

function getAgreementNameList(unsignedAgreementList: UnsignedAgreementList[]) {
  try {
    return Array.from(new Set(unsignedAgreementList?.reduce((prev, next) => {
      return [...prev, ...(next.unSignedAgreementList || [])?.map((it) => it.name!)];
    }, [] as string[]).filter(((_) => !!_))));
  } catch {
    log.addErrorLog('calculate-agreement-name-list', {
      unsignedAgreement: JSON.stringify(unsignedAgreementList),
    });
    return [];
  }
}

interface TitleProps {
  className?: string;
  contentClassName?: string;
  unsignedAgreementList?: UnsignedAgreementList[];
}

const Title = (props: TitleProps) => {
  const { className, contentClassName, unsignedAgreementList = [] } = props;

  const fundSupplierNameList = useMemo(() => {
    return getFundSupplierNameList(unsignedAgreementList);
  }, [unsignedAgreementList]);

  const agreementNameList = useMemo(() => {
    return getAgreementNameList(unsignedAgreementList);
  }, [unsignedAgreementList]);

  if (!fundSupplierNameList?.length || !agreementNameList?.length) {
    return null;
  }

  return (
    <div className={className}>
      <div className={contentClassName}>
        为帮助你获取额度，推荐随身贷合作机构{arrayToTextWithLimit(fundSupplierNameList)}{fundSupplierNameList.length > 1 && '等依次'}评估。
      </div>
      <div className={contentClassName}>
        查阅并同意<strong>{arrayToTextWithLimit(agreementNameList, 4)}</strong>等{agreementNameList.length}份协议。
      </div>
    </div>
  );
};

export default Title;
