.popup {
  height: 938rpx;
}

.popupContainer {
  font-family: <PERSON>Fang SC;

  .icon {
    margin: 14rpx auto 0;
    width: 84rpx;
    height: 84rpx;
    background-image: url("https://gw.alicdn.com/imgextra/i1/O1CN01JurUS525iXGbhJu8V_!!6000000007560-2-tps-192-192.png");
    background-repeat: no-repeat;
    background-size: contain;
  }

  .title {
    margin-top: 34rpx;
    font-weight: 500;
    font-size: 36rpx;
    color: #1677ff;
    text-align: center;
  }

  .desc {
    margin-top: 39rpx;
    font-weight: 500;
    font-size: 36rpx;
    color: rgba(#000, 0.8);
    text-align: center;
  }

  .block {
    margin: 96rpx 8rpx 0;
    padding: 0 24rpx 24rpx;
    text-align: justify;
    background-color: #f8f8f8;
    border-radius: 16rpx;

    .limit {
      padding-top: 24rpx;
      max-height: 168rpx;
      overflow: auto;
      scrollbar-width: none;
      -ms-overflow-style: none;

      &::-webkit-scrollbar {
        display: none;
      }

      .note {
        font-size: 24rpx;
        line-height: 36rpx;
        color: rgba(#000, 0.4);
      }
    }

    .btn {
      margin-top: 12rpx;
      font-size: 24rpx;
      color: rgba(#000, 0.6);
    }
  }

  .buttonWrap {
    margin: 72rpx 8rpx 0;
  }
}
