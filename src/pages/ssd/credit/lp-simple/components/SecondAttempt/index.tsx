/**
 * @file 二次推荐机构
 * <AUTHOR>
 */

import { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { Button } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';

import { CommonPopup } from '@/components';
import { ICommonPopupRef } from '@/components/CommonPopup';
import { getMinReadTime } from '@/components/SSD/UnsignedAgreements/format';
import type { UnsignedAgreementList } from '@/store/agreement/actions';
import useCountDown from '@/hooks/useCountDown';
import RedPacketPopover from '@/components/SSD/RedPacketPopover';
import type { PromotionOffer } from '@/store/types';

import PreviewPrefix from '../Title';
import styles from './index.module.scss';

interface SecondAttemptProps {
  disabled?: boolean;
  loading?: boolean;
  platformPromotionOfferList?: PromotionOffer[];
  onClose?: () => void;
  onAgreementClick: () => void;
  onConfirm?: () => void;
}

export interface SecondAttemptRef {
  show: (unSignedAgreementGroupList?: UnsignedAgreementList[]) => void;
  close: () => void;
}

const SecondAttempt = forwardRef((props: SecondAttemptProps, ref: React.Ref<SecondAttemptRef>) => {
  const {
    disabled,
    loading,
    platformPromotionOfferList,
    onClose,
    onAgreementClick,
    onConfirm,
  } = props;
  const [unsignedAgreementList, setUnsignedAgreementList] = useState<UnsignedAgreementList[]>([]);
  // 用于预览的协议
  const commonPopupRef = useRef<ICommonPopupRef>(null);
  const countDownEndRef = useRef(false);

  const { count, start } = useCountDown({
    duration: 1000,
    onEnd: () => {
      countDownEndRef.current = true;
    },
  });

  const show = (unSignedAgreementGroupList: UnsignedAgreementList[]) => {
    log.addClickLog('second-attempt-show');
    setUnsignedAgreementList(unSignedAgreementGroupList);
    commonPopupRef.current?.toggleVisible(true);
    const minReadTime = getMinReadTime({
      unSignedAgreementGroupList,
    });
    if (minReadTime && !countDownEndRef.current) {
      start(minReadTime);
    }
  };

  const close = () => {
    log.addClickLog('second-attempt-close');
    commonPopupRef.current?.toggleVisible(false);
    onClose && onClose();
  };

  useImperativeHandle(ref, () => ({
    show,
    close,
  }));

  const handleClick = () => {
    log.addClickLog('second-attempt-submit');
    close();
    onConfirm?.();
  };

  return (
    <CommonPopup ref={commonPopupRef} bodyClassName={styles.popup}>
      <div className={styles.popupContainer}>
        <div className={styles.icon} />
        <div className={styles.title}>仅差一步</div>
        <div className={styles.desc}>追加机构继续获取额度</div>
        <div className={styles.block}>
          <div className={styles.limit}>
            <PreviewPrefix
              className={styles.note}
              unsignedAgreementList={unsignedAgreementList}
              seriousMode
            />
          </div>
          <div className={styles.btn} onClick={onAgreementClick}>查看全部协议</div>
        </div>
        <div className={styles.buttonWrap}>
          <RedPacketPopover offerList={platformPromotionOfferList} displayPosition="LP_PAGE_OPENING_BUTTON_BUBBLE">
            <Button
              onClick={handleClick}
              shape="rounded"
              color="primary"
              block
              size="large"
              disabled={count > 0 || disabled}
              loading={loading}
            >
              同意并继续
              {count > 0 && <span>&nbsp;{count}s</span>}
            </Button>
          </RedPacketPopover>
        </div>
      </div>
    </CommonPopup>
  );
});

export default SecondAttempt;
