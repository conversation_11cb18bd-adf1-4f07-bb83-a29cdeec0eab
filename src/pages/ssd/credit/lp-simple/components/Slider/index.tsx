/**
 * @file 轮播图
 * <AUTHOR>
 */

import { Swiper } from 'antd-mobile';

import styles from './index.module.scss';
import classNames from 'classnames';

export default function Slider() {
  const bgList = [{
    bg: 'https://gw.alicdn.com/imgextra/i4/O1CN011UjOdm1zqQQxGRfXx_!!6000000006765-0-tps-1500-3248.jpg',
  }, {
    bg: 'https://gw.alicdn.com/imgextra/i2/O1CN018kSiqm1oWlf5BI9uX_!!6000000005233-0-tps-1500-3248.jpg',
  }, {
    bg: 'https://gw.alicdn.com/imgextra/i1/O1CN01TdPDCM1rkuk5bXuLU_!!6000000005670-0-tps-1500-3248.jpg',
  }];

  const items = bgList.map((item, index) => (
    <Swiper.Item
      key={`swiper-${index}`}
      className={classNames(index > 0 && styles.swiperItem)}
    >
      <img
        className={styles.content}
        src={item?.bg}
      />
    </Swiper.Item>
  ));

  return (
    <>
      <Swiper autoplay loop className={styles.swiper}>
        {items}
      </Swiper>
      <p className={styles.aiStyle}>图片由Ai生成</p>
    </>
  );
}
