.agreements {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  padding-top: 32rpx;
  justify-content: center;
  .check {
    position: relative;
    --icon-size: 32rpx;
    top: -2rpx;
    :global(.adm-checkbox-icon) {
      position: relative;
      top: 2rpx;
      --adm-color-light: #ccc !important;
    }
  }
  .desc {
    font-size: 24rpx;
    color: #ccc;
    text-align: left;
  }
  .agreeText {
    padding-left: 8rpx;
    font-size: 22rpx;
    color: rgba(#000, 40%);
    margin-left: 7rpx;
  }
  .agreementName1 {
    font-size: 22rpx;
    color: #4b6b99;
  }
  .agreementName2 {
    font-size: 22rpx;
    color: #4b6b99;
  }
}

.shake {
  animation: shake 800ms ease-in-out;
}
/* 水平抖动，核心代码 */
@keyframes shake {
  10%, 90% { transform: translate3d(-1px, 0, 0); }
  20%, 80% { transform: translate3d(+2px, 0, 0); }
  30%, 70% { transform: translate3d(-4px, 0, 0); }
  40%, 60% { transform: translate3d(+4px, 0, 0); }
  50% { transform: translate3d(-4px, 0, 0); }
}

.redPacketType {
  :global(.adm-checkbox-checked .adm-checkbox-icon) {
    color: #ff0020;
    background-color: #fff;
    border-color: #fff;
  }

  .agreeText {
    color: rgba(#fff, 80%);
  }

  .agreementName1, .agreementName2 {
    color: #fff;
  }
}

.SIMPLE_CREDIT_V2 {
  .agreeText {
    color: rgba(#fff, 40%);
  }
  .agreementName1, .agreementName2 {
    color: rgba(#fff, 60%);
  }
  .check {
    :global(.adm-checkbox-icon) {
      color: #1677ff !important;
    }
  }
  :global(.adm-checkbox .adm-checkbox-icon) {
    border: 2rpx solid rgba(#fff, 60%) !important;
  }
  :global(.adm-checkbox.adm-checkbox-checked .adm-checkbox-icon) {
    background-color: #fff !important;
  }
}
