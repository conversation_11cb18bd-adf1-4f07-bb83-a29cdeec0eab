/**
 * @file 协议
 * <AUTHOR>
 */

import { Checkbox } from 'antd-mobile';
import { useEffect, useRef } from 'react';
import { filter, first, isEmpty, set } from 'lodash-es';
import classNames from 'classnames';

import { UnsignedAgreementsSSD, UnsignedAgreementsSSDRef } from '@/components';
import { getCreditType } from '@/store/credit/lib';

import styles from './index.module.scss';
import { SsdCreditLpSimpleFSPData } from '@/services/ssd-credit-lp-simple';
import { QueryUnSignAgreementListResponse } from '@/store/agreement/actions';
import { log } from '@alife/dtao-iec-spm-log';
import { CREDIT_TYPE_VERSION } from '@/store/types';

interface AgreementsProps {
  onChange: (checkValue: boolean) => void;
  value: boolean;
  payload?: SsdCreditLpSimpleFSPData;
  disabled?: boolean;
  baseLog?: any;
  doProcessChange: any;
  shake?: boolean;
  useCheck?: boolean;
  agreementText?: string;
  creditTypeVersion?: CREDIT_TYPE_VERSION;
  styleType?: 'redPacketType' | '';
}

function getAgreements(payload?: SsdCreditLpSimpleFSPData, bizType?: string, source?: string): any {
  const unSignedAgreementGroupList = payload?.unsignedAgreementQuery?.unSignedAgreementGroupList;
  if (!isEmpty(unSignedAgreementGroupList)) {
    const base = {
      bizType,
    };
    if (source) {
      set(base, 'source', source);
    }
    return {
      unSignedAgreementGroupList: filter(unSignedAgreementGroupList, base),
    };
  }
  return [];
}

function checkUnSignedListNoEmpty(payload?: QueryUnSignAgreementListResponse) {
  if (payload?.unSignedAgreementGroupList) {
    const listGroup = first(payload?.unSignedAgreementGroupList);
    return !isEmpty(listGroup?.unSignedAgreementList);
  }
  return false;
}

export default function Agreements(props: AgreementsProps) {
  const {
    payload, onChange, value, disabled = false, baseLog = {},
    doProcessChange, shake = false, useCheck, agreementText = '我已阅读并同意',
    creditTypeVersion, styleType,
  } = props;
  const platformAgreementsRef = useRef<UnsignedAgreementsSSDRef>();
  const creditAgreementsRef = useRef<UnsignedAgreementsSSDRef>();
  const platformUnSignedAgreementGroupList = getAgreements(payload, 'PLATFORM');
  const creditUnSignedAgreementGroupList = getAgreements(payload, 'CREDIT', 'MAYI_ZHIXIN');

  const handlePlatformClick = () => {
    log.addShowLog('ssd-credit-lp-platform-agreement-show', baseLog);
    platformAgreementsRef?.current?.show({
      action: 'preview',
    });
  };

  const handleCreditClick = () => {
    log.addShowLog('ssd-credit-lp-platform-agreement-show', baseLog);
    creditAgreementsRef?.current?.show({
      action: 'preview',
    });
  };

  const renderName = () => {
    return null;
  };

  const handleChange = (agreementValue?: any) => {
    onChange(agreementValue);
  };

  const handleAgreementClick = () => {
    if (value) {
      handleChange(false);
    } else {
      handleChange(true);
    }
  };

  const checkDisabled = () => {
    if (disabled) {
      return disabled;
    }
    if (
      !checkUnSignedListNoEmpty(platformUnSignedAgreementGroupList) &&
      !checkUnSignedListNoEmpty(creditUnSignedAgreementGroupList)
    ) {
      log.addErrorLog('credit-simple-agreement-empty');
      doProcessChange('ERROR');
      return true;
    }
    return false;
  };

  useEffect(() => {
    log.addShowLog('credit-simple-agreement-show');
  }, []);

  return (
    <div
      className={
      classNames(
        styles.agreements,
        shake && styles.shake,
        creditTypeVersion && styles[creditTypeVersion],
        styleType && styles[styleType],
      )}
    >
      {useCheck ? (
        <Checkbox
          disabled={checkDisabled()}
          className={styles.check}
          checked={value}
          onChange={handleChange}
        />
      ) : null}
      <p className={styles.desc}>
        <span onClick={handleAgreementClick} className={styles.agreeText}>{agreementText}</span>
        {checkUnSignedListNoEmpty(platformUnSignedAgreementGroupList) ? (
          <span onClick={handlePlatformClick} className={styles.agreementName1}>
            《借钱相关服务协议》
          </span>
        ) : null}
        {checkUnSignedListNoEmpty(creditUnSignedAgreementGroupList) ? (
          <span onClick={handleCreditClick} className={styles.agreementName2}>
            《授信及个人征信查报授权》
          </span>
        ) : null}
      </p>
      <UnsignedAgreementsSSD
        renderName={renderName}
        bizType="PLATFORM"
        creditPlatform="MAYI_ZHIXIN"
        institutionList={['MAYI_ZHIXIN']}
        ref={platformAgreementsRef}
        containerClassName={styles.agreementContainer}
        customPayload={platformUnSignedAgreementGroupList}
        useInit={false}
        creditType={getCreditType(payload?.creditApplyConsult)}
        popupTitle="借钱相关服务协议"
      />
      <UnsignedAgreementsSSD
        renderName={renderName}
        bizType="CREDIT"
        creditPlatform="MAYI_ZHIXIN"
        institutionList={['MAYI_ZHIXIN']}
        ref={creditAgreementsRef}
        containerClassName={styles.agreementContainer}
        customPayload={creditUnSignedAgreementGroupList}
        creditType={getCreditType(payload?.creditApplyConsult)}
        useInit={false}
        popupTitle="授信及个人征信查报授权"
      />
    </div>
  );
}
