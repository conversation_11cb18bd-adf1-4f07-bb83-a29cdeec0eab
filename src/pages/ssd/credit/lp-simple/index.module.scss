.lpSimple {
  overflow: hidden;
  padding: 24rpx 16rpx;
  text-align: center;

  .mainCard {
    padding: 147rpx 32rpx;
    padding-top: 162rpx;
    margin-top: 32rpx;
    background-color: #fff;
    border-radius: 24rpx;
    height: 822rpx;

    .cardTitle {
      font-size: 36rpx;
      margin-bottom: 6rpx;
      color: rgba(#000, 80%);
    }

    .quotaStar {
      font-size: 130rpx;
      font-family: "AlibabaSans102Ver2";
      padding-top: 33rpx;
      padding-left: 8rpx;
      letter-spacing: 7rpx;
    }

    .mainBtn {
      width: 526rpx;
      height: 98rpx;
      text-align: center;
      margin: auto;
      margin-top: 103rpx;
      margin-bottom: 24rpx;
      font-size: 36rpx;
      font-weight: 600;

      span {
        position: relative;
        font-size: 36rpx;
        font-weight: 600;
        top: -3rpx;
      }

      :global(.adm-button-loading-wrapper) {
        font-size: 36rpx;
        font-weight: 600;
      }
    }

    .mainBtn1 {
      width: 526rpx;
      height: 98rpx;
      text-align: center;
      margin: auto;
      margin-top: 132rpx;
      margin-bottom: 24rpx;
      font-size: 36rpx;
      font-weight: 600;

      span {
        position: relative;
        font-size: 36rpx;
        font-weight: 600;
        top: -2rpx;
      }

      .text {
        position: static;
        top: unset;

        b {
          font-weight: 550;
          font-family: "AlibabaSans102Ver2";
        }
      }

      :global(.adm-button-loading-wrapper) {
        font-size: 36rpx;
        font-weight: 600;
      }
    }

    .mainBtnBottomText {
      color: rgba(#000, 40%);
    }

    .cardDesc {
      min-height: 33rpx;
      font-size: 28rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      color: rgba(#000, 80%);

      :global(.common-divide-line-vertical) {
        height: 20rpx;
        margin: 0 16rpx;
      }

      img {
        width: 28rpx;
        height: 28rpx;
        margin-right: 12rpx;
      }
    }

    .cardDescContainer {
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: 16rpx;

      .alipayIcon {
        width: 42rpx;
        height: 38rpx;
        background-image: url('https://gw.alicdn.com/imgextra/i4/O1CN01aFGYL92940iJl6MpI_!!6000000008013-2-tps-80-80.png');
        background-repeat: no-repeat;
        background-size: contain;
      }

      .cardDesc1 {
        font-size: 28rpx;
        color: rgba(#000, 80%);
        width: 100%;
        height: 32rpx;
      }

      .cardDesc {
        font-size: 28rpx;
        color: #1677ff;
        margin-left: 13rpx;
      }
    }

    .cardDescOld {
      min-height: 33rpx;
      font-size: 24rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      :global(.common-divide-line-vertical) {
        height: 20rpx;
        margin: 0 16rpx;
      }

      .cardDescTextOld {
        color: rgba(#000, 80%);
        white-space: nowrap;
      }
    }

    .failedIcon {
      margin-top: 107rpx;
      margin-bottom: 48rpx;
      text-align: center;

      img {
        width: 96rpx;
        height: 96rpx;
      }
    }

    .failedTitle {
      font-size: 36rpx;
      color: rgba(#000, 80%);
      margin-bottom: 16rpx;
    }

    .failedDesc {
      font-size: 28rpx;
      color: rgba(#000, 40%);
    }

    .failedDescMulti {
      line-height: 45rpx;
    }
  }

  .mainCardOld {
    margin-top: 32rpx;
  }

  .bottom {
    position: absolute;
    // NOTE: 兼容红包宽屏模式
    // bottom: 315rpx;
    bottom: 18.8%;
    z-index: 0;
    width: 100%;
  }

  .bottom2 {
    bottom: 121rpx;
  }

  .redPacketPageV3 {
    position: relative;
    margin: 0;
    padding: 0;
    width: 100vw;
    height: 100vh;
    border-radius: unset;
    background-size: 100% 100%;
    background-repeat: no-repeat;

    .mainContent {
      position: absolute;
      top: 28.44%;
      left: 50%;
      transform: translateX(-50%);

      .quotaStar {
        padding-top: 5rpx;
      }

      .cardDescTextOld {
        margin-top: -24rpx;
      }
    }
  }
}

.submit {
  height: 98rpx;
  width: 606rpx;
  background-color: #1677ff;
  border-radius: 49rpx;
  margin: auto;
  --adm-font-size-9: 36rpx;
  font-weight: 600;

  span {
    position: relative;
    font-size: 36rpx;
    font-weight: 600;
    top: -2rpx;
  }

  :global(.adm-button-loading-wrapper) {
    position: relative;
    font-size: 36rpx;
    font-weight: 600;
    margin-left: 90rpx;
    top: -2rpx;
  }
}

.redPacketSubmit {
  width: 446rpx;
  font-weight: 500;
  font-size: 36rpx;
  color: #ff0020;
  background-color: #fff;
  border-color: #fff;
}

.submit1 {
  width: 526rpx;
  height: 98rpx;
  background-color: #1677ff;
  border-radius: 49rpx;
  margin: auto;
  --adm-font-size-9: 36rpx;
  margin-left: -8rpx;

  span {
    position: relative;
    font-size: 36rpx;
    top: -2rpx;
  }
}

.submitOld {
  margin-top: 121rpx;
}

.lpOpen {
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.banBottom {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 32rpx 48rpx;
  padding-bottom: 42rpx;

  .gap {
    width: 100%;
    height: 10rpx;
  }

  .banTitle {
    font-size: 28rpx;
    color: rgba(#000, 80%);
    text-align: center;
    padding-top: 10rpx;
    padding-bottom: 32rpx;
    letter-spacing: 0;
  }
}

.banOpenMask {
  background: transparent !important;
}

.banOpenBody {
  border-radius: 24rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 6rpx 8rpx 0 rgba(0, 0, 0, 0.05);
}

.banOpen {
  --max-width: 610rpx !important;

  :global(.adm-center-popup-wrap) {
    top: 69.3% !important;
    width: 610rpx;
  }
}

.sNumber {
  margin-top: 42rpx;
}

.toastContent {
  font-size: 28rpx;
  color: #fff;
}

.agreement {
  bottom: 0;
  top: unset;
  height: 836rpx;
  width: 100%;
}

.agreementSerial {
  bottom: 0;
  top: unset;
  height: 940rpx;
  width: 100%;
}

.previewTitle {
  padding: 0 40rpx 24rpx;
  font-size: 24rpx;
  color: rgba(#000, 0.6);
}

.compulsoryTitle {
  font-size: 24rpx;
  color: rgba(#000, 0.6);
}

.redPacketContent {
  position: absolute;
  // NOTE: 兼容红包宽屏模式
  // bottom: 601rpx;
  bottom: 37%;
  left: 50%;
  transform: translateX(-50%);

  .redPacketAmountSymbol {
    display: inline-block;
    width: 42rpx;
    font-family: AlibabaSans102Ver2;
    color: #ff0020;
    font-size: 72rpx;
    font-weight: 700;
    transform: scaleX(0.9) translateX(-19rpx);
  }

  .redPacketAmount {
    font-family: AlibabaSans102Ver2;
    font-size: 108rpx;
    color: #ff0020;
  }

  .redPacketContentLine {
    margin-top: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .redPacketDescription {
    opacity: 0.8;
    font-family: PingFangSC;
    font-weight: 500;
    font-size: 36rpx;
    color: #000;
    white-space: nowrap;
  }

  .redPacketTip {
    display: block;
    margin-left: 8rpx;
    font-size: 22rpx;
    color: #888;
    width: 24rpx;
    height: 24rpx;
    background-image: url('https://gw.alicdn.com/imgextra/i2/O1CN016HnmaT1IOv93xBOJw_!!6000000000884-2-tps-200-200.png');
    background-size: cover;
    background-position: 50% 50%;
    background-repeat: no-repeat;
  }
}

@media only screen and (device-width : 414px) and (device-height : 736px) and (-webkit-device-pixel-ratio : 3) {
  .lpSimple {
    .bottom {
      position: absolute;
      bottom: 217rpx;
      z-index: 0;
      width: 100%;
    }

    .bottom2 {
      bottom: 151rpx;
    }
  }
}

.SIMPLE_CREDIT_V2 {
  .submit {
    background-color: #fff;
    --border-color: transparent;
    --border-width: 0;
    color: #061329;
    height: 98rpx;
    width: 558rpx;
    border-radius: 49rpx;
  }

  .bottom2 {
    bottom: 208rpx;
  }
}
