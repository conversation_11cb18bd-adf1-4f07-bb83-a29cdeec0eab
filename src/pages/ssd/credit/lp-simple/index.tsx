/**
 * @file 极简开通
 * <AUTHOR>
 */

import { definePageConfig, defineServerDataLoader, defineDataLoader, ClientOnly } from 'ice';
import { Button, Toast, Modal, Dialog } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';
import { useEffect, useRef, useState } from 'react';
import { filter, first, get, includes, isEmpty, isFunction, set } from 'lodash-es';
import classNames from 'classnames';
import { useDebounceFn } from 'ahooks';
import { NavBar } from '@ali/uni-api';

import TaoRedPacketRule from '@/components/TaoRedPacketRule';
import { useVerifyIdentity, CommonPopup, UnsignedAgreementsSSD, UnsignedAgreementsSSDRef, checkHmRejectAlert } from '@/components';
import BottomTextBar from '@/components/SSD/BottomTextBar';
import { VerifyStatus } from '@/components/SSD/VerifyIdentity/libs/types';
import ScrollNumbers from '@/components/ScrollNumbers';
import Layout from '@/components/Layout/ssd';
import useCreditConsult from '@/hooks/useCreditConsult';
import RedPacketPopover from '@/components/SSD/RedPacketPopover';
import { useFSPData } from '@/hooks/useFSPData';
import { PAGES, SSDSimpleCreditLpWaitingSeconds } from '@/common/constant';
import { getEnvData } from '@/components/SSD/VerifyIdentity/libs/bridge';
import { checkMayizhixinCreditRouter } from '@/store/credit/model';
import { genApplyExposePlatformPromotionOfferStr, getPromotionOfferData } from '@/store/lib/format';
import { postCreditApply, queryInstitutionCreditOrder, queryCreditOrder } from '@/store/credit/actions';
import LinkUtil from '@/utils/link';
import { getSecureToken } from '@/utils/umid-token';
import { checkInstitutionCreditingFn, getCreditType } from '@/store/credit/lib';
import {
  idempotentId,
  getMsgByErrorCode,
  getErrorCodeFromRejectRes,
  getApplyTime,
  getFailedCodeFromOrder,
  beforeMinutes,
  getOrigin,
  getMD,
} from '@/utils';
import {
  ssdCreditLpSimpleCSR,
  ssdCreditLpSimpleSSR,
  SsdCreditLpSimpleFSPData,
} from '@/services/ssd-credit-lp-simple';
import { CREDIT_APPLY_CONSULT_DTO, CREDIT_APPLY_ORDER_DTO } from '@/store/types';
import type { UnsignedAgreementList } from '@/store/agreement/actions';
import { useUnsignedAgreement, useCountDown } from '@/hooks';
import { addInitLog } from '@/utils/log';
import { lpMayizhixinPageLog } from '@/utils/goc';

import { Slogan, Slider, Agreements, Limit, SecondAttempt, Title, type SecondAttemptRef } from './components';
import { getBackgroundImage } from './backgroundConfig';
import styles from './index.module.scss';
import './index.scss';

type PROCESS_ACTION = 'SUBMITING' | 'VERIFYING' | 'PROCESSING' | 'NULL' | 'ERROR';

function getAgreements(
  unSignedAgreementGroupList?: UnsignedAgreementList[],
  bizType?: string,
  source?: string,
): any {
  if (!isEmpty(unSignedAgreementGroupList)) {
    const base = {
      bizType,
    };
    if (source) {
      set(base, 'source', source);
    }
    return {
      unSignedAgreementGroupList: filter(unSignedAgreementGroupList, base),
    };
  }
  return [];
}

const isApplySecondary = (creditConsult?: CREDIT_APPLY_CONSULT_DTO | null) => creditConsult?.applyType === 'SECONDARY';

const needFocusRead = (list?: UnsignedAgreementList[]) => {
  try {
    return list?.some((item) => {
      return item.unSignedAgreementList?.some((it) => it.forceRead && it.minReadTime);
    });
  } catch {
    return false;
  }
};

export const serverDataLoader = defineServerDataLoader(async () => {
  const data = await ssdCreditLpSimpleSSR();
  return data;
});

export const dataLoader = defineDataLoader(async () => {
  const data = await ssdCreditLpSimpleCSR('PREFETCH');
  return data;
});

export default function LpSimple() {
  const { fspData } = useFSPData<SsdCreditLpSimpleFSPData>({
    init: ssdCreditLpSimpleCSR,
  });
  const { data } = fspData || {};
  const { creditConsultData, queryConsult } = useCreditConsult({
    default: fspData?.data?.creditApplyConsult,
    requestParams: { requestPurpose: 'CREDIT_CONSULT' },
  });
  const { unsignedAgreementData, queryUnsignedAgreement } = useUnsignedAgreement({
    default: fspData?.data?.unsignedAgreementQuery,
    requestParams: {
      bizType: 'PLATFORM_AND_CREDIT',
      creditPlatform: 'MAYI_ZHIXIN',
      institutionList: ['MAYI_ZHIXIN'],
      creditType: creditConsultData?.creditType,
    },
  });
  const [creditOrderRes, setCreditOrderRes] = useState<CREDIT_APPLY_ORDER_DTO | null>(
    // @ts-ignore
    creditConsultData?.latestCreditApplyOrder,
  );
  const { startVerify } = useVerifyIdentity();
  const { start, count } = useCountDown({ duration: 1000 });
  const [processAction, setProcessAction] = useState<PROCESS_ACTION>('NULL');
  const [institutionCreditOrder, setInstitutionCreditOrder] = useState<any>(null);
  const [agreementCheck, setAgreementCheck] = useState(false);
  const [showApplyRejectResult, setShowApplyRejectResult] = useState(false);
  const [hasApplyFailed, setHasApplyFailed] = useState(false);
  const [banCheck, setBanCheck] = useState(false);
  const commonPopupRef = useRef<any>(null);
  const verifyedRef = useRef(false);
  const rerfTryRef = useRef(5);
  const secondAttemptRef = useRef<SecondAttemptRef>(null);
  const creditAgreementRef = useRef<UnsignedAgreementsSSDRef>();
  /** 一次授信、二次授信协议强读共用标记 */
  const agreementCompleted = useRef(false);

  const creditUnSignedAgreementGroupList = getAgreements(unsignedAgreementData?.unSignedAgreementGroupList, 'CREDIT', 'MAYI_ZHIXIN');
  const serialApply = hasApplyFailed &&
    isApplySecondary(creditConsultData) &&
    creditConsultData?.canCreditApply;
  const hasPageRedPacket = !!getPromotionOfferData(
    creditConsultData?.platformPromotionOfferList || [],
    'LP_PAGE',
  )?.type;

  const onChangeProcessAction = (action: PROCESS_ACTION) => {
    setProcessAction(action);
  };

  const getCreditTypeVersion = () => {
    try {
      // @ts-ignore
      return JSON.parse(creditConsultData?.extension)?.creditTypeVersion;
    } catch (e) {
      return '';
    }
  };
  const baseLog = {
    creditTypeVersion: getCreditTypeVersion(),
  };

  const setThemeDark = async () => {
    try {
      if (checkVersion(2)) {
        await NavBar.setTheme({
          // @ts-ignore
          theme: 'DARK',
        });
      }
    } catch (e) {}
  };

  const setThemeLight = async (force?: boolean) => {
    try {
      if (checkVersion(2) || force) {
        await NavBar.setTheme({
          // @ts-ignore
          theme: 'LIGHT',
        });
      }
    } catch (e) {}
  };

  const getAgreementExtension = (agreements: any, bizType: string) => {
    try {
      if (agreements?.unSignedAgreementGroupList) {
        const targetList = filter(agreements.unSignedAgreementGroupList, {
          source: 'MAYI_ZHIXIN',
          bizType,
        });
        if (targetList?.length === 1) {
          const target = first(targetList);
          if (target?.extension) {
            return target?.extension;
          }
        }
      }
      throw new Error();
    } catch (e) {
      return null;
    }
  };

  const queryInstitutionCreditTime = async () => {
    if (
      !creditOrderRes?.creditApplyOrderId ||
      !checkInstitutionCreditingFn(creditOrderRes?.subStatus)
    ) {
      return;
    }
    try {
      // 每一个授信申请单ID仅可查询一次
      if (isEmpty(institutionCreditOrder)) {
        const res = await queryInstitutionCreditOrder({
          creditApplyOrderId: creditOrderRes?.creditApplyOrderId,
        });
        setInstitutionCreditOrder(res);
      }
    } catch (error) {
      log.addErrorLog('credit-simple-institution-credit-order', { code: error.message });
    }
  };

  const handleStartVerify = async (payload: CREDIT_APPLY_ORDER_DTO | null) => {
    try {
      // 已核身直接跳过，继续轮询
      if (verifyedRef?.current) {
        log.addOtherLog('credit-simple-verified', baseLog);
        return {
          success: true,
        };
      }
      log.addShowLog('credit-simple-verify-start', baseLog);
      setProcessAction('VERIFYING');
      const toastFn = Toast.show({
        content: <span className={styles.toastContent}>即将人脸验证...</span>,
        duration: 3000,
      });
      const verifyRes = await startVerify({
        authenticationToken: get(payload, 'applicationData.authentication.token'),
      });
      if (isFunction(toastFn?.close)) {
        toastFn.close();
      }
      if (verifyRes?.status === VerifyStatus.PASS) {
        log.addSuccessLog('ssd-credit-lp-verify-pass', baseLog);
        setThemeLight();
        start(SSDSimpleCreditLpWaitingSeconds);
        setProcessAction('PROCESSING');
        verifyedRef.current = true;
        return {
          success: true,
        };
      } else if (verifyRes?.status === VerifyStatus.CANCEL) {
        log.addClickLog('ssd-credit-lp-verify-cancel', baseLog);
        setProcessAction('NULL');
        return {
          success: false,
        };
      }
      log.addOtherLog('ssd-credit-lp-verify-result', {
        ...baseLog,
        status: verifyRes.status,
      });
      throw new Error();
    } catch (e) {
      log.addErrorLog('credit-simple-verify-error');
      setProcessAction('NULL');
      Toast.show({
        content: '核身失败，请稍后重试',
      });
      return {
        success: false,
      };
    }
  };

  const handleInitFailed = () => {
    Dialog.show({
      content: '系统开小差，请稍后重试',
      closeOnAction: true,
      actions: [[{
        text: '请稍后重试',
        onClick: () => {
          LinkUtil.resetToPage('index');
        },
        key: 'confirm',
        bold: true,
      }]],
    });
  };

  // 注意，这里是一个递归闭包，不要再这里使用state值进行判断，是需要使用请用ref
  const doOrderQuery = async (creditApplyOrderId?: string) => {
    if (!creditApplyOrderId) {
      // 异常提示
      return;
    }
    try {
      const payload = await queryCreditOrder({
        creditApplyOrderId,
      });
      if (payload) {
        const { status, subStatus } = payload;
        // 避免更新太频繁，只有新的授信单据结果和旧的完全不同才更新
        if (JSON.stringify(payload) !== JSON.stringify(creditOrderRes)) {
          setCreditOrderRes(payload);
        }
        // 主状态：状态异常
        if (status === 'INIT') {
          log.addErrorLog('credit-simple-init');
          Toast.show('申请单状态异常，请联系客服');
          return;
        }
        // 主状态：授信成功
        if (status === 'SUCCEEDED') {
          log.addSuccessLog('ssd-credit-lp-credit-success', baseLog);
          LinkUtil.toSsdHomePlus();
          return;
        }
        // 主状态：授信失败
        if (status === 'FAILED' || status === 'CANCELLED') {
          log.addOtherLog('credit-simple-credit-failed', baseLog);
          // 先保障完成申请失败处理（重点保障授信咨询重新咨询完成），然后取消重置 processAction
          // 否则会因为页面中存在多份授信申请状态（授信单 and 授信咨询状态不一致），导致页面出现闪跳情况
          await handleApplyFailed(payload);
          setProcessAction('NULL');
          return;
        }
        // 主状态：授信中
        if (status === 'PROCESSING') {
          // 子状态：待核身
          if (subStatus === 'WAIT_TO_AUTH') {
            log.addShowLog('ssd-credit-lp-verify-start', {
              ...baseLog,
              ifForceRead: needFocusRead(unsignedAgreementData?.unSignedAgreementGroupList),
            });
            const verifyRes = await handleStartVerify(payload);
            if (verifyRes?.success) {
              setTimeout(() => {
                doOrderQuery(payload?.creditApplyOrderId);
              }, 900);
            }
            return;
          }
          // 子状态：机构授信中
          if (checkInstitutionCreditingFn(subStatus) || subStatus === 'WAIT_TO_SIGN_AGREEMENT') {
            log.addShowLog('ssd-credit-lp-institution-crediting', baseLog);
            setTimeout(() => {
              doOrderQuery(payload?.creditApplyOrderId);
            }, 900);
          }
        }
      }
    } catch (e) {
      if (rerfTryRef?.current) {
        setTimeout(() => {
          doOrderQuery(creditApplyOrderId);
        }, 900);
        rerfTryRef.current--;
      } else {
        log.addErrorLog('simple-do-query-poll');
        handleInitFailed();
      }
    }
  };

  const handleAgreementChange = (checkValue: boolean) => {
    if (checkValue) {
      log.addClickLog('credit-simple-agreement-checked', {
        ...baseLog,
        checked: checkValue,
      });
    } else {
      log.addClickLog('credit-simple-agreement-cancel', {
        ...baseLog,
        checked: checkValue,
      });
    }
    setAgreementCheck(checkValue);
  };

  const getApplyParams = async (consultRes: CREDIT_APPLY_CONSULT_DTO | null) => {
    const { applyType, creditPlatform, creditType, platformPromotionOfferList } = consultRes || {};
    const origin = getOrigin();
    const platformExtension = getAgreementExtension(unsignedAgreementData, 'PLATFORM');
    const creditExtension = getAgreementExtension(unsignedAgreementData, 'CREDIT');
    if (isEmpty(platformExtension) || isEmpty(creditExtension)) {
      throw new Error();
    }
    const { passthroughInfo: platformPassthroughInfo, ...platformOther } = platformExtension;
    const { passthroughInfo: creditPassthroughInfo, ...creditOther } = creditExtension;
    // TODO: 联调需要关注这里的字段
    const applyExtension = {
      passthroughInfo: {
        ...platformPassthroughInfo,
        ...creditPassthroughInfo,
      },
      ...platformOther,
      ...creditOther,
      creditTypeVersion: getCreditTypeVersion(),
      envData: await getEnvData(),
    };
    const secureTokenRes = await getSecureToken();
    if (secureTokenRes?.umidToken) {
      set(applyExtension, 'umidToken', secureTokenRes?.umidToken);
    }
    if (secureTokenRes?.apdidToken) {
      set(applyExtension, 'apdidToken', secureTokenRes?.apdidToken);
    }
    set(applyExtension, 'origin', origin);
    const baseParams = {
      requestId: idempotentId(),
      applyType,
      creditType,
      creditPlatform,
      applyTime: getApplyTime(),
      channel: 'APP',
      extension: applyExtension,
      exposePlatformPromotionOfferList:
        genApplyExposePlatformPromotionOfferStr(platformPromotionOfferList),
    };
    return baseParams;
  };

  const doFocusRead = () => {
    if (serialApply) {
      // 当前页面申请失败后继续串行送审
      secondAttemptRef.current?.show(unsignedAgreementData?.unSignedAgreementGroupList);
    } else {
      // 首次申请 / 申请失败后页面复访
      creditAgreementRef.current?.show({
        action: 'compulsory',
      });
    }
    log.addShowLog('ssd-credit-lp-credit-agreement-compulsory', {
      applyType: creditConsultData?.applyType,
      serialApply,
    });
  };

  const handleSubmit = async () => {
    // 鸿蒙拦截
    if (checkHmRejectAlert(PAGES.SsdCreditLpSimple)) {
      return;
    }
    if (!agreementCheck) {
      log.addClickLog('credit-simple-btn-without-agreement', baseLog);
      setBanCheck(true);
      setTimeout(() => {
        setBanCheck(false);
      }, 800);
      return;
    }
    // 发起授信
    try {
      log.addClickLog('main-btn-click', {
        ...baseLog,
        noUnSignAgreementVal: data?.creditApplyConsult?.isPlatformAgreementSigned,
      });
      setProcessAction('SUBMITING');
      const consultRes = await queryConsult();
      // 如果没有授信单或者是失败和取消的状态，且可发起授信
      if (consultRes?.canCreditApply) {
        if (
          needFocusRead(unsignedAgreementData?.unSignedAgreementGroupList) &&
          !agreementCompleted.current
        ) {
          doFocusRead();
          setProcessAction('NULL');
          return;
        }
        const applyParams = await getApplyParams(consultRes);
        const postRes = await postCreditApply(applyParams);
        if (postRes?.creditApplyOrderId) {
          log.addSuccessLog('ssd-credit-lp-apply-success', baseLog);
          doOrderQuery(postRes?.creditApplyOrderId);
          return;
        } else {
          throw new Error(getErrorCodeFromRejectRes(postRes));
        }
      } else if (consultRes?.canCreditApply === false) {
        // 不准入
        setProcessAction('NULL');
        log.addErrorLog('ssd-credit-lp-apply-reject');
        return;
      }
      throw new Error();
    } catch (e) {
      log.addErrorLog('ssd-credit-lp-apply-error', { code: e.message });
      Modal.alert({
        title: '申请失败',
        content: getMsgByErrorCode(e.message),
      });
      setProcessAction('NULL');
    }
  };

  const { run: handleSubmitFn } = useDebounceFn(handleSubmit, {
    wait: 1000,
    leading: true,
    trailing: false,
  });

  // 检查不准入code
  const checkFailCode = (order?: CREDIT_APPLY_ORDER_DTO) => {
    const code = getFailedCodeFromOrder(order);
    const LIMIT_CODE = [
      'S_UNBOUND_MOBILE_NO',
      'C_PERMIT_ALIPAY_ACCOUNT',
      'S_SAME_PERSON_DIFFERENT_ALIPAY_ACCOUNT',
    ];
    if (!includes(LIMIT_CODE, code)) {
      return true;
    }
    return false;
  };

  const handleApplyFailed = async (order?: CREDIT_APPLY_ORDER_DTO) => {
    // 特殊拒绝码处理
    if (!checkFailCode(order)) {
      const code = getFailedCodeFromOrder(order);
      log.addShowLog('ssd-credit-lp-credit-fail-popup-show', {
        ...baseLog,
        code,
      });
      commonPopupRef?.current?.toggleVisible(true);
      return;
    }

    // 降级直接展示不准入状态
    const S_DEGRADE = 'S_DEGRADE';
    if (getFailedCodeFromOrder(order) === S_DEGRADE) {
      setShowApplyRejectResult(true);
      log.addShowLog('ssd-credit-lp-credit-reject-show', {
        ...baseLog,
        code: S_DEGRADE,
      });
      return;
    }

    // 设置申请失败标记
    setHasApplyFailed(true);
    const consultRes = await queryConsult();
    // 串行授信
    if (isApplySecondary(consultRes) && consultRes?.canCreditApply) {
      log.addShowLog('ssd-credit-lp-serial-apply');
      // 请求新的协议列表
      const unsignedAgreement = await queryUnsignedAgreement();
      // 重置页面主题
      setThemeLight(true);
      // 核身标记重置，二次送审允许二次核身
      verifyedRef.current = false;
      // 协议列表强读才能弹出半屏
      if (needFocusRead(unsignedAgreement?.unSignedAgreementGroupList)) {
        // 重置协议强读确认标记
        agreementCompleted.current = false;
        // 弹出半屏
        secondAttemptRef.current?.show(unsignedAgreement?.unSignedAgreementGroupList);
        // log
        log.addShowLog('ssd-credit-lp-credit-agreement-compulsory', {
          applyType: consultRes?.applyType,
          serialApply,
        });
      }
      return;
    }

    setShowApplyRejectResult(true);
    log.addShowLog('ssd-credit-lp-credit-reject-show', baseLog);
  };

  const checkRedirectRouter = () => {
    if (!fspData?.data?.creditApplyConsult) {
      return;
    }

    const routerPage = checkMayizhixinCreditRouter(fspData?.data?.creditApplyConsult);
    // 曝光日志
    lpMayizhixinPageLog({
      routerPage,
      creditConsultData: fspData?.data?.creditApplyConsult,
    });
    if (routerPage === PAGES.Index) {
      log.addOtherLog('ssd-credit-simple-to-index');
      LinkUtil.resetToPage('index');
      return;
    }
    // 已开通
    // @ts-ignore
    if (routerPage === PAGES.SsdHome) {
      // log.addOtherLog('credit-simple-to-home', baseLog);
      LinkUtil.locationReplace(PAGES.SsdHome);
      return;
    }
    if (routerPage === PAGES.SsdHomePlus) {
      // log.addOtherLog('credit-simple-to-home', baseLog);
      LinkUtil.locationReplace(PAGES.SsdHomePlus);
      return;
    }
    // 标准开通
    if (routerPage === PAGES.SsdCreditLpSSR) {
      // log.addOtherLog('credit-simple-to-home', baseLog);
      LinkUtil.locationReplace(PAGES.SsdCreditLpSSR);
    }
  };

  // 检查是否机构授信中
  const checkCreditInstituting = (order?: CREDIT_APPLY_ORDER_DTO | null) => {
    if (checkInstitutionCreditingFn(order?.subStatus) || order?.subStatus === 'WAIT_TO_SIGN_AGREEMENT') {
      return true;
    }
    if (processAction === 'PROCESSING') {
      return true;
    }
    return false;
  };

  // 检查是否不准入
  const checkCreditFallBack = () => {
    if (!creditConsultData) {
      return true;
    }
    const { latestCreditApplyOrder, canCreditApply } = creditConsultData;
    // 申请后不准入
    if (showApplyRejectResult === true) {
      return true;
    }
    // 申请失败、不准入
    if (
      latestCreditApplyOrder?.status === 'FAILED' &&
      !canCreditApply
    ) {
      return true;
    }
    // 不准入
    if (!canCreditApply && !latestCreditApplyOrder) {
      return true;
    }
    return false;
  };

  // const cancelBanCheck = useCallback(() => {
  //   setBanCheck(false);
  // }, []);


  const checkVersion = (version) => {
    try {
      // @ts-ignore
      const creditTypeVersion = JSON.parse(creditConsultData?.extension)?.creditTypeVersion;
      if (creditTypeVersion === `SIMPLE_CREDIT_V${version}`) {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  };

  const doInit = () => {
    // 异常
    if (fspData?.success === false) {
      lpMayizhixinPageLog({});
      handleInitFailed();
      return;
    }
    // 未开始
    if (!fspData?.data?.creditApplyConsult) {
      return;
    }
    log.setGlobalExtra({
      creditType: getCreditType(creditConsultData),
      applyType: creditConsultData?.applyType,
      hasPageRedPacket,
      origin: getOrigin(),
    });
    const { creditApplyConsult } = fspData.data;
    // 第三版不需要校验协议是否勾选
    if (checkVersion(3)) {
      setAgreementCheck(true);
    }
    addInitLog(fspData);
    checkRedirectRouter();
    const creditApplyOrder = creditApplyConsult?.latestCreditApplyOrder;
    if (checkCreditInstituting(creditApplyOrder)) {
      doOrderQuery(creditApplyOrder?.creditApplyOrderId);
    }
  };

  const handleAgreementShow = () => {
    creditAgreementRef.current?.show({
      action: 'preview',
      popupProps: {
        transparentMask: true,
        position: 'right',
        bodyClassName: serialApply ? styles.agreementSerial : styles.agreement,
      },
    });
  };

  const handleAgreementCompleted = () => {
    log.addOtherLog('ssd-credit-lp-credit-agreement-completed');
    agreementCompleted.current = true;
    if (!agreementCheck) {
      // 帮用户自动勾选
      setAgreementCheck(true);
    }
    handleSubmitFn();
  };

  const handleAgreementEC = () => {
    setProcessAction('NULL');
  };

  const checkCanOpen = () => {
    if (checkCreditInstituting(creditOrderRes)) {
      return false;
    }
    if (checkCreditFallBack()) {
      return false;
    }
    if (serialApply) {
      return false;
    }
    if (creditConsultData?.canCreditApply) {
      return true;
    }
    return false;
  };

  const getSloganDesc = () => {
    if (checkCanOpen() || checkVersion(3)) {
      return '蚂蚁智信及其合作伙伴为你提供官方服务';
    }
    return '';
  };

  // const renderBanOpen = () => {
  //   return (
  //     <CenterPopup
  //       visible={banCheck}
  //       className={styles.banOpen}
  //       maskClassName={styles.banOpenMask}
  //       bodyClassName={styles.banOpenBody}
  //       onMaskClick={cancelBanCheck}
  //     >
  //       <div className={styles.banBottom}>
  //         <p className={styles.banTitle}>我已阅读并同意以下协议</p>
  //         <div className={styles.gap} />
  //         <Button
  //           className={styles.submit1}
  //           block
  //           color="primary"
  //           onClick={handleSubmit}
  //         >
  //           继续开通
  //         </Button>
  //       </div>
  //     </CenterPopup>
  //   );
  // };

  const getCustomClassName = () => {
    // 第三版和旧版一样，非开通阶段样式和旧版一样
    if (checkVersion(3) || !checkCanOpen()) {
      return 'common-page-ssd';
    }
    return '';
  };

  const checkWithSafe = () => {
    if (checkVersion(3) && !hasPageRedPacket) {
      return false;
    }
    return checkCanOpen();
  };

  const renderSlider = () => {
    // 获取来源渠道
    const origin = getOrigin();
    // 获取当前版本
    const creditTypeVersion = getCreditTypeVersion();

    if (hasPageRedPacket) {
      log.addShowLog('lp-ssd-simple-credit-red-packet', {
        creditTypeVersion,
        origin,
      });
    }

    // 根据版本设置不同的类名，同时设置背景图
    if (checkVersion(2)) {
      return <Slider />;
    }

    // 获取背景图URL
    const backgroundImage = getBackgroundImage(
      creditTypeVersion,
      hasPageRedPacket,
      origin,
    );

    // NOTE: 包含 v1、v4、v5 三个版本
    return <div className="lp-simple-bg" style={{ backgroundImage: `url(${backgroundImage})` }} />;
  };

  const renderSerial = () => {
    return (
      <div className={styles.mainCard}>
        <p className={styles.cardTitle}>可借</p>
        <p className={styles.quotaStar}>*****</p>
        <div className={styles.cardDescContainer}>
          <p className={styles.cardDesc1}>支付宝用户可享，可提现到银行卡</p>
        </div>
        <RedPacketPopover offerList={creditConsultData?.platformPromotionOfferList} displayPosition="LP_PAGE_OPENING_BUTTON_BUBBLE">
          <Button
            className={styles.mainBtn}
            color="primary"
            onClick={handleSubmitFn}
            block
            disabled={processAction !== 'NULL'}
            loading={processAction !== 'NULL' && processAction !== 'ERROR'}
            loadingText="继续查看额度"
          >
            继续查看额度
          </Button>
        </RedPacketPopover>
      </div>
    );
  };

  const renderOpenV3 = () => {
    // 获取来源渠道
    const origin = getOrigin();
    // 获取背景图URL
    const backgroundImage = getBackgroundImage(
      'SIMPLE_CREDIT_V3',
      hasPageRedPacket,
      origin,
    );
    let btnText = '';
    if (isApplySecondary(creditConsultData)) {
      btnText = '继续查看额度';
    } else if (hasPageRedPacket) {
      btnText = '立即开通';
    } else {
      btnText = '同意协议并查看额度';
    }
    if (hasPageRedPacket) {
      log.addShowLog('lp-ssd-simple-credit-red-packet', {
        creditTypeVersion: 'SIMPLE_CREDIT_V3',
        origin,
      });
    }
    return (
      <div
        className={classNames(styles.mainCard, styles.mainCardOld, {
          [styles.redPacketPageV3]: hasPageRedPacket,
        })}
        style={{ backgroundImage: `url(${backgroundImage})` }}
      >
        <div className={styles.mainContent}>
          <p className={styles.cardTitle}>你可以借</p>
          <p className={styles.quotaStar}>*****</p>
          <div className={styles.cardDescOld}>
            <span className={styles.cardDescTextOld}>利率低至3.6%</span>
            <span className="common-divide-line-vertical"> </span>
            <span className={styles.cardDescTextOld}>借1千元1天仅0.1元</span>
          </div>
        </div>
        {hasPageRedPacket && renderRedPacketContent()}
        <div className={classNames({
          [styles.bottom]: hasPageRedPacket,
        })}
        >
          <Button
            className={classNames(styles.submit, styles.submitOld, {
              [styles.redPacketSubmit]: hasPageRedPacket,
            })}
            color="primary"
            onClick={handleSubmitFn}
            block
            disabled={processAction !== 'NULL'}
            loading={processAction !== 'NULL' && processAction !== 'ERROR'}
            loadingText={btnText}
          >
            {btnText}
          </Button>
          <Agreements
            agreementText="查看"
            payload={{
              creditApplyConsult: creditConsultData,
              unsignedAgreementQuery: unsignedAgreementData,
            }}
            value={agreementCheck}
            onChange={handleAgreementChange}
            disabled={processAction !== 'NULL'}
            baseLog={baseLog}
            doProcessChange={onChangeProcessAction}
            styleType={hasPageRedPacket ? 'redPacketType' : ''}
          />
        </div>
      </div>
    );
  };

  const handleShowRule = (offerSendRule?: string) => {
    if (!offerSendRule) {
      return;
    }
    Modal.alert({
      title: '活动规则',
      content: <TaoRedPacketRule ruleStr={offerSendRule} />,
    });
  };

  const renderRedPacketContent = () => {
    // NOTE: 品牌版暂无红包背景设计，因此排除 V2
    if (checkVersion(2)) {
      return null;
    }

    const {
      promotionOfferDescription, amount, offerSendScene, offerSendRule,
    } = getPromotionOfferData(
      creditConsultData?.platformPromotionOfferList || [],
      'LP_PAGE',
    ) || {};

    return (
      <div className={styles.redPacketContent}>
        <div>
          <span className={styles.redPacketAmountSymbol}>￥</span>
          <span className={styles.redPacketAmount}>{amount}</span>
        </div>
        <div className={styles.redPacketContentLine}>
          <span className={styles.redPacketDescription}>{promotionOfferDescription}</span>
          {
            offerSendScene === 'XFD_CREDIT_SUCCESS_TAO_COUPON'
              ? <i className={styles.redPacketTip} onClick={() => handleShowRule(offerSendRule)} />
              : null
          }
        </div>
      </div>
    );
  };

  const renderOpenButton = () => {
    let btnText = '';
    if (isApplySecondary(creditConsultData)) {
      btnText = '继续查看额度';
    } else if (hasPageRedPacket) {
      btnText = '立即开通';
    } else {
      btnText = '查看我的额度';
    }
    return (
      <Button
        className={classNames(styles.submit, {
          [styles.redPacketSubmit]: hasPageRedPacket &&
            (checkVersion(1) || checkVersion(4) || checkVersion(5)),
        })}
        color="primary"
        onClick={handleSubmitFn}
        block
        disabled={processAction !== 'NULL'}
        loading={processAction !== 'NULL' && processAction !== 'ERROR'}
        loadingText={btnText}
      >
        {btnText}
      </Button>
    );
  };

  const renderOpen = () => {
    const button = renderOpenButton();
    return (
      <div className={styles.open}>
        {renderSlider()}
        {/* 标准版红包文案 */}
        {hasPageRedPacket && renderRedPacketContent()}
        <div className={classNames(styles.bottom, checkVersion(2) && styles.bottom2)}>
          {
            checkVersion(2)
              ? (
                <RedPacketPopover
                  offerList={creditConsultData?.platformPromotionOfferList}
                  styleType={checkVersion(2) ? 'orangeType' : 'normal'}
                  displayPosition="LP_PAGE"
                >
                  {button}
                </RedPacketPopover>
              )
              : button
          }

          <Agreements
            payload={{
              creditApplyConsult: creditConsultData,
              unsignedAgreementQuery: unsignedAgreementData,
            }}
            agreementText={hasPageRedPacket ? '同意' : '我已阅读并同意'}
            value={agreementCheck}
            onChange={handleAgreementChange}
            disabled={processAction !== 'NULL'}
            baseLog={baseLog}
            doProcessChange={onChangeProcessAction}
            useCheck
            shake={banCheck}
            creditTypeVersion={getCreditTypeVersion()}
            styleType={hasPageRedPacket ? 'redPacketType' : ''}
          />
        </div>
      </div>
    );
  };

  const renderProcessCount = () => {
    return (
      <div className={styles.mainCard}>
        <p className={styles.cardTitle}>可借</p>
        <ScrollNumbers className={styles.sNumber} count={5} />
        {/* <div className={styles.cardDescContainer}>
          <i className={styles.alipayIcon} />
          <p className={styles.cardDesc}>
            支付宝保障账户安全，可提现到卡
          </p>
        </div> */}
        <RedPacketPopover
          offerList={
            creditOrderRes?.exposePlatformPromotionOfferList ||
            creditConsultData?.platformPromotionOfferList
          }
          displayPosition="LP_PAGE_OPENING_BUTTON_BUBBLE"
        >
          <Button
            className={styles.mainBtn1}
            color="primary"
            block
            disabled
          >
            {count ? <span className={styles.text}>额度计算中 <b>{count}s</b></span> : '额度计算中'}
          </Button>
        </RedPacketPopover>
      </div>
    );
  };

  const renderProcessEnd = () => {
    const nowDiffMinutes = beforeMinutes(institutionCreditOrder?.institutionApplyTime, 1);
    let text = '';
    if (institutionCreditOrder?.institutionApplyTime) {
      if (nowDiffMinutes) {
        text = '预计1分钟内完成，完成后短信通知你';
      } else {
        text = '加速审批中，完成后短信通知你';
      }
    }
    return (
      <div className={styles.mainCard}>
        <p className={styles.cardTitle}>可借</p>
        <p className={styles.quotaStar}>*****</p>
        <div className={styles.cardDescContainer}>
          <p className={styles.cardDesc1}>{text}</p>
        </div>
        <RedPacketPopover
          offerList={
            creditOrderRes?.exposePlatformPromotionOfferList ||
            creditConsultData?.platformPromotionOfferList
          }
          displayPosition="LP_PAGE_OPENING_BUTTON_BUBBLE"
        >
          <Button
            className={styles.mainBtn}
            color="primary"
            block
            disabled
          >
            额度计算中
          </Button>
        </RedPacketPopover>
      </div>
    );
  };

  const renderProcess = () => {
    if (count) {
      return renderProcessCount();
    }
    return renderProcessEnd();
  };

  const renderRejectDesc = () => {
    if (!creditConsultData) {
      return null;
    }

    const { canCreditApplyTime, latestCreditApplyOrder } = creditConsultData;
    if (!canCreditApplyTime) {
      return (
        <div className={styles.failedDesc}>服务暂未开放到你，额度开放时将及时通知你</div>
      );
    }

    if (latestCreditApplyOrder?.extension?.coolOffType === 'LONG') {
      return (
        <>
          <div className={classNames(styles.failedDesc, styles.failedDescMulti)}>
            请保持良好的信用习惯
          </div>
          <div className={classNames(styles.failedDesc, styles.failedDescMulti)}>
            可再次申请时将及时通知你
          </div>
        </>
      );
    }

    return (
      <div className={styles.failedDesc}>
        预估{getMD(canCreditApplyTime)}可再来尝试申请
      </div>
    );
  };

  // 开通失败&冷静期
  const renderReject = () => {
    if (!creditConsultData) {
      return null;
    }
    try {
      const isFAILED = get(creditConsultData, 'latestCreditApplyOrder.status') === 'FAILED';
      const failedCode = get(creditConsultData, 'latestCreditApplyOrder.failedReason.failedCode') || '';
      const isMayiConsultFailed = !failedCode.startsWith('APPLY_FAILED_');
      const isMayiApplyFailed = failedCode.startsWith('APPLY_FAILED_');
      log.addShowLog('ssd-credit-lp-failed-status-card', {
        ...baseLog,
        failedCode,
        isFAILED,
        isMayiConsultFailed,
        isMayiApplyFailed,
        isNotAoGe: !get(creditConsultData, 'latestCreditApplyOrder'),
      });
      if (isMayiConsultFailed) {
        log.addShowLog('credit-failed-isMayiConsultFailed', baseLog);
      }
      if (isMayiApplyFailed) {
        log.addShowLog('credit-failed-isMayiApplyFailed', baseLog);
      }
    } catch (error) {}
    const { canCreditApplyTime } = creditConsultData;
    if (canCreditApplyTime) {
      log.addShowLog('credit-failed-hasCanCreditApplyTime', baseLog);
    } else {
      log.addShowLog('credit-failed-noCanCreditApplyTime', baseLog);
    }

    return (
      <div className={styles.mainCard}>
        <div className={styles.failedIcon}>
          <img src="https://gw.alicdn.com/imgextra/i3/O1CN01aaQw1u1GouNhG8gR1_!!6000000000670-2-tps-192-192.png" />
        </div>
        <div className={styles.failedTitle}>
          {canCreditApplyTime ? '额度评估未通过' : '随身贷逐步开放中'}
        </div>
        {renderRejectDesc()}
      </div>
    );
  };

  const renderFailPopup = () => {
    return (
      <CommonPopup ref={commonPopupRef}>
        <Limit orderRes={creditOrderRes} />
      </CommonPopup>
    );
  };

  const renderMain = () => {
    if (creditConsultData?.creditContractStatus === 'QUALIFIED') {
      return null;
    }
    // 机构授信中
    if (checkCreditInstituting(creditOrderRes)) {
      return renderProcess();
    }
    // 不准入
    if (checkCreditFallBack()) {
      return renderReject();
    }
    // 可开通
    if (creditConsultData?.canCreditApply) {
      // 串行授信
      if (serialApply) {
        return renderSerial();
      }
      if (checkVersion(3)) {
        return renderOpenV3();
      }
      return renderOpen();
    }
    return null;
  };

  const renderPreviewTitle = () => {
    return (
      <Title
        className={styles.previewTitle}
        unsignedAgreementList={unsignedAgreementData?.unSignedAgreementGroupList}
        seriousMode={isApplySecondary(creditConsultData)}
      />
    );
  };

  const renderCompulsoryTitle = () => {
    return (
      <Title
        className={styles.compulsoryTitle}
        unsignedAgreementList={unsignedAgreementData?.unSignedAgreementGroupList}
        seriousMode={isApplySecondary(creditConsultData)}
      />
    );
  };

  const renderSecond = () => {
    return (
      <SecondAttempt
        ref={secondAttemptRef}
        disabled={processAction !== 'NULL'}
        loading={processAction !== 'NULL' && processAction !== 'ERROR'}
        platformPromotionOfferList={creditConsultData?.platformPromotionOfferList}
        onClose={handleAgreementEC}
        onAgreementClick={handleAgreementShow}
        onConfirm={handleAgreementCompleted}
      />
    );
  };

  // 初始化
  useEffect(() => {
    doInit();
  }, [fspData]);

  useEffect(() => {
    setTimeout(() => {
      log.addShowLog('institution-crediting-minute-level', baseLog);
      queryInstitutionCreditTime();
    }, 1000);
  }, [creditOrderRes, institutionCreditOrder]);

  // 埋点专用
  useEffect(() => {
    if (!checkCreditFallBack() && !showApplyRejectResult) {
      log.addShowLog('credit-apply-normal', baseLog);
    }
  }, [creditConsultData, showApplyRejectResult]);

  useEffect(() => {
    setThemeDark();
  }, [fspData?.data?.creditApplyConsult?.extension]);

  // 降级后优先展示骨架屏
  if (!fspData) {
    return (
      <Layout>
        <div className={styles.lpSimple} />
      </Layout>
    );
  }

  return (
    <Layout
      hiddenSafe={checkWithSafe()}
      customClassName={getCustomClassName()}
    >
      <div className={classNames(
        styles.lpSimple,
        checkWithSafe() && styles.lpOpen,
        styles[getCreditTypeVersion()],
      )}
      >
        <Slogan
          withSafe={checkWithSafe()}
          desc={getSloganDesc()}
          creditTypeVersion={getCreditTypeVersion()}
          isProcessing={checkCreditInstituting(creditOrderRes) || serialApply}
        />
        {renderMain()}
      </div>
      <ClientOnly>
        {renderFailPopup}
      </ClientOnly>
      <BottomTextBar
        customClassName={styles.bottomBar}
        hideCenter={!creditConsultData?.isPlatformAgreementSigned}
        lightColor={checkVersion(2) && !serialApply}
      />
      <ClientOnly>
        {renderSecond}
      </ClientOnly>
      {/* 用途：首访强读、复访强读、串行二次授信预览 */}
      <UnsignedAgreementsSSD
        ref={creditAgreementRef}
        bizType="CREDIT"
        creditType={getCreditType(creditConsultData)}
        popupTitle="协议详情"
        creditPlatform="MAYI_ZHIXIN"
        institutionList={['MAYI_ZHIXIN']}
        customPayload={creditUnSignedAgreementGroupList}
        onCompleted={handleAgreementCompleted}
        useInit={false}
        renderName={() => null}
        onError={handleAgreementEC}
        onClose={handleAgreementEC}
        renderPreviewTitle={renderPreviewTitle}
        renderCompulsoryTitle={renderCompulsoryTitle}
        buttonText="同意协议并继续"
        force={false}
        useContentsNew
      />
      {/* {<ClientOnly>{renderBanOpen}</ClientOnly>} */}
    </Layout>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '借钱',
  spm: {
    spmB: PAGES.SsdCreditLpSimple,
  },
}));
