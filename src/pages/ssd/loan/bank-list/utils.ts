import { ajax } from '@ali/iec-dtao-utils';

enum EnvEnum {
  dev = 'dev',
  test = 'test',
  pre = 'pre',
  prod = 'prod',
  local = 'local',
  stable = 'stable',
}

function getNewDataUrl(url?: string, diyEnv?: any) {
  const fendieEnv = diyEnv || getFenddieEnv();
  const env = fendieEnv === 'prod' ? '' : fendieEnv;
  return `https://render${env}.alipay.com/p/yuyan/${url}/zh_CN.json`;
}

export const newGetH5Data = async (url?: string, diyEnv?: any) => {
  try {
    const rst = await ajax._axios(getNewDataUrl(url, diyEnv));
    const data = rst?.data;
    if (data) {
      return data;
    }
    return {};
  } catch (e) {
    const err = e || {};
    console.error('h5data error', err);
    return {};
  }
};

export function getFenddieEnv(url?: string): EnvEnum {
  let env = EnvEnum.prod;
  const _url = url || window.location.href;
  if (_url.indexOf('local.') > 0 || _url.indexOf('localhost') > 0) {
    env = EnvEnum.dev;
  } else if (_url.indexOf('h5_dev') > 0 || _url.indexOf('renderdev') > 0) {
    env = EnvEnum.dev;
  } else if (_url.indexOf('h5_test') > 0) {
    env = EnvEnum.test;
  } else if (_url.indexOf('render-pre') > 0 || _url.indexOf('renderpre') > 0) {
    env = EnvEnum.pre;
  } else if (_url.indexOf('alipay.net') > 0) {
    env = EnvEnum.dev;
  }
  return env;
}
