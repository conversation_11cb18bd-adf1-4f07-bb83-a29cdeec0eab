import { useState, useEffect, useCallback } from 'react';
import { definePageConfig } from 'ice';

import CommonFixedNavBar from '@/components/CommonFixedNavBar';
import { newGetH5Data } from './utils';
import { PAGES } from '@/common/constant';
import { log } from '@alife/dtao-iec-spm-log';

import styles from './index.module.scss';

export const pageConfig = definePageConfig(() => ({
  title: '详情',
  spm: {
    spmB: PAGES.SsdBankList,
  },
}));

// 支持银行里列表
export default function SupportBankList() {
  const [bankList, setBankList] = useState<any>([]);

  const doInit = async () => {
    try {
      const res = await newGetH5Data('fengdie-lendweb_support-bank-config-h5data');
      setBankList(res?.bankList);
    } catch (e) {}
  };

  const handleImgError = useCallback((e: any) => {
    if (e?.target) {
      e.target.src = 'https://mdn.alipayobjects.com/fin_instasset/uri/img/as/instasset/DEFAULT_BANK/BANK_LOGO/PURE?zoom=88w_88h.png';
    }
  }, []);

  useEffect(() => {
    log.addVisitLog(PAGES.SsdBankList);
    doInit();
  }, []);

  return (
    <>
      <CommonFixedNavBar bgColor="#fff" />
      <div className={styles['mayijie-sbl-container']}>
        <ul>
          <li>
            <span className={styles.title1}>支持以下银行</span>
            <span className={styles.title2}>（共{bankList.length}家）</span>
          </li>
          {bankList.map((item, index) => {
            return (
              <li key={index}>
                <img
                  className={styles.logo}
                  src={`https://mdn.alipayobjects.com/fin_instasset/uri/img/as/instasset/${item.instId}/BANK_LOGO/PURE?zoom=88w_88h.png`}
                  onError={handleImgError}
                  alt=""
                />
                <span className={styles['bank-name']}>
                  {item?.bankName}
                </span>
              </li>
            );
          })}
        </ul>
      </div>
    </>
  );
}
