.resultPage {
  padding: 32rpx;
  text-align: center;
  .bank {
    margin: auto;
    border-radius: 8rpx;
    padding: 12rpx 18rpx;
    margin-top: 32rpx;
    background-color: #f3f6f8;
    width: fit-content;
  }
  .button {
    width: 368rpx;
    margin-top: 80rpx;
  }
  .idCardTip {
    display: flex;
    flex-direction: row;
    text-align: left;
    align-items: flex-end;
    justify-content: space-between;
    .main {
      font-size: 26rpx;
      font-weight: 500;
      line-height: 39rpx;
      color: #111;
      padding-top: 24rpx;
    }
    .sub {
      font-size: 24rpx;
      line-height: 36rpx;
      padding-top: 12rpx;
      color: #7c889c;
    }
  }
  .result {
    padding-top: 363rpx;
  }
  .processing {
    background-image: url('https://gw.alicdn.com/imgextra/i2/O1CN01309efA1IZudLXKlDO_!!*************-2-tps-192-192.png');
    background-repeat: no-repeat;
    background-size: contain;
    width: 96rpx;
    height: 96rpx;
    margin: auto;
  }
  .success {
    background-image: url('https://gw.alicdn.com/imgextra/i3/O1CN01DXYN0V28PhpFbPBLR_!!6000000007925-2-tps-192-192.png');
    background-repeat: no-repeat;
    background-size: contain;
    width: 96rpx;
    height: 96rpx;
    margin: auto;
  }
  .fail {
    background-image: url('https://gw.alicdn.com/imgextra/i2/O1CN01hov7Mh22uUkxNIs0d_!!6000000007180-2-tps-192-192.png');
    background-repeat: no-repeat;
    background-size: contain;
    width: 96rpx;
    height: 96rpx;
    margin: auto;
  }
}
