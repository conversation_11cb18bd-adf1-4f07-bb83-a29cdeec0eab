/**
 * @file 支用结果页面
 * <AUTHOR>
 */

import { definePageConfig } from 'ice';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { number } from '@ali/iec-dtao-utils';
import { Button, Dialog } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';

import { LOAN_END_STATUS, useQueryPoll } from '@/store/loan';
import { SSDCommonResult, BankInfoTip, FullLoading, CountDown, CountDownRef } from '@/components';
import { getLoanOrderId } from '@/utils/params';
import { popPage, reload, resetToPage, toLoanSign } from '@/utils/link';

import styles from './index.module.scss';
import { PAGES, ErrorMessageMap } from '@/common/constant';
import { getFailedCodeFromOrder } from '@/utils';

export const pageConfig = definePageConfig(() => ({
  title: '借钱',
  spm: {
    spmB: PAGES.SsdLoanResult,
  },
}));

export default function LoanResultPage() {
  const { payload, doPoll } = useQueryPoll();
  const [countEnd, setCountEnd] = useState(false);
  const countDownRef = useRef<CountDownRef>();
  const FAIL_CODE_MAP_DESC = {
    L_RISK_REJECT: '请保持良好信用，过段时间再试',
    L_LOAN_COUNT_EXCEED: '借款笔数已达上限，可还几笔后再试',
    L_LOAN_FAIL: '可尝试更换收款账户后重新借一笔',
  };

  const handleBack = useCallback(() => {
    log.addClickLog('back-home');
    resetToPage(PAGES.SsdHome);
  }, []);

  const handleCountEnd = useCallback(() => {
    setTimeout(() => {
      setCountEnd(true);
    }, 500);
  }, []);

  const renderBankIcon = useMemo(() => {
    if (payload?.receiveBankCard?.bankCode) {
      const { bankCode, bankCardNo, bankName } = payload.receiveBankCard;
      return (
        <div className={styles.bank}>
          <BankInfoTip
            code={bankCode}
            name={bankName}
            cardNo={bankCardNo}
            source="mycdn"
          />
        </div>
      );
    }
    return null;
  }, [payload]);

  const renderProcessImg = useCallback(() => {
    if (countEnd) {
      log.addOtherLog('ssd-loan-result-count-end');
      return (
        <div className={styles.processing} />
      );
    }
    return (
      <CountDown ref={countDownRef} onEnd={handleCountEnd} />
    );
  }, [countEnd, handleCountEnd]);

  const renderProcess = () => {
    return (
      <>
        <SSDCommonResult
          customImg={renderProcessImg()}
          title="你的借款正在处理中，请稍候"
          status="PROCESSING"
          description={`处理完成将放款到${payload?.receiveBankCard?.bankName}账户`}
        />
        {renderBankIcon}
      </>
    );
  };

  const renderSuccess = () => {
    return (
      <>
        <SSDCommonResult
          title={`成功借款${number.amountFormat(payload?.loanedAmount)}元`}
          status="SUCCESS"
          description="已放款到收款账户"
        />
        {renderBankIcon}
        <Button className={styles.button} color="primary" onClick={handleBack}>
          返回首页
        </Button>
      </>
    );
  };

  const renderFail = () => {
    const desc = FAIL_CODE_MAP_DESC[getFailedCodeFromOrder(payload)] || '抱歉，暂时无法提供借款服务';
    return (
      <>
        <SSDCommonResult
          title="借款失败"
          status="FAILED"
          description={desc}
        />
        <Button className={styles.button} color="primary" onClick={handleBack}>
          返回首页
        </Button>
      </>
    );
  };

  const doInit = useCallback(async () => {
    try {
      const loanOrderId = getLoanOrderId();
      if (loanOrderId) {
        await doPoll({
          end: LOAN_END_STATUS,
          request: {
            loanOrderId,
          },
        });
      } else {
        throw new Error();
      }
    } catch (e) {
      log.addErrorLog('result-poll');
      Dialog.show({
        content: ErrorMessageMap.BUSY_DEFUALT,
        actions: [[{
          key: 'cancel',
          onClick: popPage,
          text: '返回',
        }, {
          key: 'retry',
          onClick: reload,
          bold: true,
          text: '点击重试',
        }]],
      });
    }
  }, []);

  useEffect(() => {
    doInit();
    log.addVisitLog(PAGES.LoanResult);
  }, []);

  useEffect(() => {
    if (payload?.status === 'LOANING' || payload?.status === 'AUTHENTICATED') {
      countDownRef.current?.start(3);
    }
  }, [payload?.status]);

  const renderResult = () => {
    switch (payload?.status) {
      case 'AUTHENTICATED':
      case 'LOANING':
        log.addOtherLog('result-loading');
        return renderProcess();
      case 'CANCELLED':
      case 'FAILED':
        log.addOtherLog('result-failed');
        return renderFail();
      case 'SUCCEEDED':
        log.addOtherLog('result-succeeded');
        return renderSuccess();
      case 'INIT':
        log.addOtherLog('result-error');
        toLoanSign(getLoanOrderId());
        return null;
      default:
        return null;
    }
  };

  return (
    <div className={styles.resultPage}>
      <FullLoading visible={!payload} />
      <div className={styles.result}>
        {renderResult()}
      </div>
    </div>
  );
}
