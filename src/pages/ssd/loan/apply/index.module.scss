@import '../../../../assets/styles/reset.css';
@import '../../../../assets/styles/font.css';

.loanPage {
  padding: 16rpx 32rpx;
  width: 100%;
  .loanForm {
    :global(.adm-form-item.adm-form-item-horizontal .adm-form-item-label) {
      line-height: unset;
    }
    :global(.adm-form-item.adm-form-item-horizontal .adm-list-item-content) {
      --align-items: center;
    }
    :global(.adm-form-item-feedback-error) {
      text-align: right;
    }
  }
  .wrap {
    display: flex;
    flex-direction: column;
  }
  .panel {
    display: flex;
    flex-direction: column;
    border-radius: 18rpx;
    padding: 24rpx;
    background-color: #fff;
    margin-bottom: 24rpx;
    &:last-child {
      margin-bottom: 0;
    }
    :global(.adm-form-item) {
      padding-bottom: 24rpx;
      &:last-child {
        padding-bottom: 0;
      }
    }
  }
  .displayNone {
    display: none;
  }
  .submit {}
  .reject {
    text-align: center;
    .button {
      width: 240rpx;
    }
  }
}

.text {
  font-size: 26rpx;
  line-height: 39rpx;
  text-align: right;
  color: #7c889c;
  font-family: 'ALIBABA NUMBER FONT MD';
}

.reject {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  height: 40%;
}


.right {
  bottom: 0;
  top: unset;
  height: calc(67vh - 8rpx);
  width: 100%;
}


.promotion {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 52rpx;
  .name {
    color: var(--adm-color-text-secondary);
  }
}

.hide {
  width: 0;
  height: 0;
  opacity: 0;
  margin: 0 !important;
  padding: 0 !important;
}

.hover {
  position: fixed;
  width: 100%;
  height: 100%;
  // height: 100vh;
  top: 0;
  left: 0;
  z-index: 999;
}

.applyBtn {
  :global(.adm-button-loading-wrapper) {
    margin-left: 68rpx;
  }
}
