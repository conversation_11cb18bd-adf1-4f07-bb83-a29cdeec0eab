/**
 * @file 支用相关的检验条件
 */

import { loanApplyAmountValidator } from '@/store/loan/validator';

interface loanApplyAmountRuleOption {
  max?: string;
  min?: string;
  step?: string;
}

export const loanApplyAmount = {
  rules: (option: loanApplyAmountRuleOption) => [{
    validator: (_: any, value: string) => {
      return loanApplyAmountValidator(value, option);
    },
  }],
};

export const loanPurpose = {
  rules: [{
    required: true, message: '请选择借款用途',
  }],
};

export const loanTerm = {
  rules: [{ required: true, message: '请选择借款期限' }],
};

export const repaymentMethod = {
  rules: [{ required: true, message: '请选择还款方式' }],
};

export const receiveBankCard = {
  rules: [{ required: true, message: '请添加收款账户' }],
};

export const institution = {
  rules: [{ required: true, message: '请确认放款机构' }],
};
