/**
 * @file 随身贷支用页面
 * <AUTHOR>
 */

import { useCallback, useEffect, useRef } from 'react';
import { definePageConfig } from 'ice';
import { Button, Dialog, Form, Toast, Modal } from 'antd-mobile';
import { first, keys, isEmpty } from 'lodash-es';
import { useDebounceFn } from 'ahooks';
import { log } from '@alife/dtao-iec-spm-log';
import classNames from 'classnames';
import { number } from '@ali/iec-dtao-utils';

import { reload, toSsdLoanResult, popPage } from '@/utils/link';
import {
  Account, Institution, DetailAndAgreements, LoanTerm, LoanPurpose,
  LoanPurposeRef, RepaymentMethod, Reject, Coupon,
} from './components';
import {
  loanPurpose, loanApplyAmount, loanTerm,
  repaymentMethod, receiveBankCard, institution,
} from './fields';
import { SsdLoanApplySkeleton } from '@/components/SSD/Skeleton';
import {
  NewAmountInput, FixedBottom, UnsignedAgreementsSSD, UnsignedAgreementsSSDRef,
  useVerifyIdentity, UnsignedAgreementGroup,
} from '@/components';
import {
  ssdModels, ApplyFormData, FieldObj, FIELD_NAME, APPLY_END_STATUS, useQueryPoll,
  ssdFormat,
  ApplyResponse,
  LoanApplyRejectReason,
} from '@/store/loan';
import { ErrorMessageMap, PAGES } from '@/common/constant';
import { getSecureToken } from '@/utils/umid-token';
import { VerifyErrorCode, VerifyStatus } from '@/components/SSD/VerifyIdentity/libs/types';
import { getRejectReason, addRejectLog } from '@/utils/reject';

import styles from './index.module.scss';

export const pageConfig = definePageConfig(() => ({
  title: '借钱',
  spm: {
    spmB: PAGES.SsdLoanApply,
  },
  window: {
    navBarImmersive: false,
  },
}));

export default function SsdLoanApply() {
  const {
    schemeStore, trialStore, processAction, loanAgreements,
    doInit, doApplyAdmit, doApply, doUpdate, checkSubmitDisabled, changeProcessAction,
  } = ssdModels.useSsdLoan();
  const { getEnvData, startVerify } = useVerifyIdentity();
  const { doPoll } = useQueryPoll({
    pure: true,
    time: 1000,
  });
  const [form] = Form.useForm<ApplyFormData>();
  const loanAgreementRef = useRef<UnsignedAgreementsSSDRef>();
  const loanPurposeRef = useRef<LoanPurposeRef>();
  const loanApplyAmountValue = Form.useWatch('loanApplyAmount', form);

  const loanAmountTips = () => {
    if (schemeStore?.loanSchemaQuota?.minStartLoanAmount) {
      return `${schemeStore.loanSchemaQuota.minStartLoanAmount}元起借，按天算息，随借随还`;
    }
    return '1元起借，按天算息，随借随还';
  };

  const initErrorDialog = useCallback((e?: any) => {
    addRejectLog(e);
    Dialog.show({
      content: getRejectReason(e),
      closeOnAction: true,
      actions: [[{
        key: 'cancel',
        text: '以后再说',
        onClick: popPage,
      }, {
        text: '重新计算',
        onClick: reload,
        key: 'confirm',
        bold: true,
      }]],
    });
  }, []);

  const handleInit = useCallback(async () => {
    const secureTokenRes = await getSecureToken();
    const res: any = await doInit(secureTokenRes);
    if (isEmpty(res?.initLoanScheme)) {
      log.addErrorLog('init-consult-error');
      return;
    }
    form.setFieldsValue(res.initLoanScheme);
    log.addSuccessLog('init-consult');
    // try {
    //   // 按最大金额发起一次试算
    //   await doTrial({
    //     ...res?.initLoanScheme,
    //     loanApplyAmount: res?.maxLoanAmount,
    //   });
    //   // 试算结束后查询协议
    //   log.addSuccessLog('init-trial');
    // } catch (e: any) {
    //   log.addErrorLog('init-trial');
    //   initErrorDialog();
    // }
  }, [form, doInit]);

  const handleLoanAgreementShow = useCallback(() => {
    log.addClickLog('loan-agreement-show');
    loanAgreementRef.current?.show({
      action: 'preview',
      popupProps: {
        transparentMask: true,
        position: 'right',
        bodyClassName: styles.right,
      },
    });
  }, []);

  const handleLoanPurposeShow = useCallback(() => {
    log.addClickLog('loan-purpose-show');
    loanPurposeRef.current?.show();
  }, []);

  const resetAgreementCheck = useCallback(() => {
    form.setFieldValue('agreementCheck', false);
    log.addOtherLog('rest-agreement-check');
  }, [form]);

  const handleUpdateField = useCallback((name: FIELD_NAME, value: any) => {
    form.setFieldValue(name, value);
  }, [form]);

  const handleFinishFailed = useCallback((errorInfo: any) => {
    log.addErrorLog('loan-apply-invalid', errorInfo);
  }, []);


  const handleApplyFailed = (rejectReason?: LoanApplyRejectReason) => {
    const applyRejectCode = rejectReason?.rejectCode || '';
    switch (applyRejectCode) {
      default:
        Modal.show({
          content: ErrorMessageMap[applyRejectCode] || ErrorMessageMap.BUSY_DEFUALT,
          closeOnAction: true,
          actions: [{
            key: 'retry',
            text: '我知道了',
            primary: true,
          }],
        });
        break;
    }
  };

  const handleApplyVerifySuccess = useCallback((loanOrderId: string) => () => {
    changeProcessAction('NULL');
    log.addSuccessLog('loan-apply-verify-success');
    toSsdLoanResult(loanOrderId);
  }, []);

  // 支用完核身
  const handleVerifyIdentity = async (applyPayLoad: ApplyResponse) => {
    const { loanOrderId, authenticationToken } = applyPayLoad;
    log.addSuccessLog('loan-apply-verify-identity');
    changeProcessAction('AUTENTICATING');
    const verifyRes = await startVerify({
      authenticationToken,
    });
    if (verifyRes?.status === VerifyStatus.CANCEL) {
      log.addOtherLog('loan-apply-verify-identity-cancel');
      changeProcessAction('NULL');
      return;
    }
    if (verifyRes?.errorCode === VerifyErrorCode.AUTH_HIGH_RISK) {
      log.addOtherLog('loan-apply-verify-identity-high-risk');
      changeProcessAction('NULL');
      return;
    }
    if (verifyRes?.status !== VerifyStatus.PASS) {
      log.addErrorLog('loan-apply-verify-identity-error');
      // Toast.show({
      //   content: '核身验证失败，请重试',
      // });
      changeProcessAction('NULL');
      return;
    }
    if (verifyRes?.status === VerifyStatus.PASS) {
      log.addSuccessLog('loan-apply-verify-identity-pass');
      await doPoll({
        end: APPLY_END_STATUS,
        request: {
          loanOrderId,
        },
        onEnd: handleApplyVerifySuccess(loanOrderId),
      });
    }
  };

  // 发起支用
  const handleSubmit = async (data: ApplyFormData) => {
    log.addClickLog('loan-apply');
    const envData = await getEnvData();
    const admitRes = await doApplyAdmit({
      applyFormData: data,
      envData,
    });
    if (!admitRes?.admitted) {
      log.addErrorLog('loan-admit-reject', admitRes);
      handleApplyFailed(admitRes?.rejectReason);
      return;
    }
    // 不在组件内判断强制阅读了，比较麻烦
    if (!data?.agreementCheck && loanAgreements?.forceRead) {
      log.addShowLog('loan-apply-force');
      loanAgreementRef.current?.show({
        action: 'compulsory',
      });
      return;
    }
    if (data?.agreementCheck) {
      log.addSuccessLog('loan-apply-force');
    }
    try {
      const secureTokenRes = await getSecureToken();
      const res = await doApply({
        applyFormData: data,
        envData,
        umidToken: secureTokenRes?.umidToken,
        apdidToken: secureTokenRes?.apdidToken,
      });
      resetAgreementCheck();
      if (!res?.admitted) {
        log.addErrorLog('loan-apply-reject', res);
        handleApplyFailed(res?.rejectReason);
        changeProcessAction('NULL');
        return;
      }
      if (res?.loanOrderId && res?.authenticationToken && res?.status === 'AUTHENTICATING') {
        // 支用成功开始核身
        await handleVerifyIdentity(res);
      } else {
        log.addErrorLog('loan-apply-fetch', res);
        throw new Error('LOAN_APPLY_FAILED');
      }
    } catch (e) {
      resetAgreementCheck();
      changeProcessAction('NULL');
      Toast.show({
        content: ErrorMessageMap.BUSY_DEFUALT,
      });
      log.addErrorLog('loan-apply-error', e);
    }
  };

  const handleAgreementEC = () => {
    changeProcessAction('NULL');
  };

  const handleAgreementCompleted = useCallback(() => {
    form.submit();
  }, [form]);

  const handleValuesChangeFn = useCallback(async (fieldObj: FieldObj, scheme: ApplyFormData) => {
    try {
      const changeNames = keys(fieldObj);
      if (changeNames?.length === 1) {
        const changeName = first(changeNames) as FIELD_NAME;
        log.addChangeLog(`loan-${changeName}`);
        await doUpdate({
          name: changeName,
          data: scheme,
          updateField: handleUpdateField,
        });
      } else {
        log.addErrorLog('update-twice');
      }
    } catch (e) {
      log.addErrorLog('update-trial');
      initErrorDialog(e);
    }
  }, [doUpdate, handleUpdateField, initErrorDialog]);

  const getLoanAgreementExtensionParams = useCallback((currrentGroup: UnsignedAgreementGroup) => {
    return ssdFormat.getAgreementPreviewExtension(currrentGroup, {
      trialStore,
      formData: form.getFieldsValue(),
    });
  }, [trialStore, form]);

  const checkLoading = () => {
    return processAction === 'AUTENTICATING' || processAction === 'SUBMITTING';
  };

  const { run: handleValuesChange } = useDebounceFn(handleValuesChangeFn, {
    wait: 500,
    leading: true,
    trailing: true,
  });

  const renderPromoition = () => {
    if (trialStore?.promotionAmount) {
      const promotionAmountValue = number.getNumber(trialStore?.promotionAmount);
      if (promotionAmountValue !== '--' && promotionAmountValue > 0) {
        return (
          <div className={styles.promotion}>
            <p className={styles.name}>利息优惠</p>
            <Coupon processAction={processAction} trialStore={trialStore} />
          </div>
        );
      }
    }
    return null;
  };

  const renderFooter = () => {
    const isLoading = checkLoading();
    return (
      <FixedBottom className={classNames(styles.submit, !loanApplyAmountValue && styles.hide)}>
        <Button
          loading={isLoading}
          loadingText="确认中"
          className={styles.applyBtn}
          disabled={checkSubmitDisabled()}
          block
          color="primary"
          type="submit"
        >
          同意协议并确认
        </Button>
      </FixedBottom>
    );
  };

  const renderNormal = () => {
    if (schemeStore?.loanSchemaQuota) {
      const { minStartLoanAmount, maxLoanAmount, step } = schemeStore?.loanSchemaQuota;
      const loanApplyAmountRule = loanApplyAmount.rules({
        min: minStartLoanAmount,
        max: maxLoanAmount,
        step,
      });
      return (
        <Form
          form={form}
          className={styles.loanForm}
          footer={renderFooter()}
          onFinish={handleSubmit}
          onFinishFailed={handleFinishFailed}
          onValuesChange={handleValuesChange}
          layout="horizontal"
          hasFeedback
        >
          <Form.Item rules={loanApplyAmountRule} name="loanApplyAmount">
            <NewAmountInput
              label={loanAmountTips()}
              recommends={schemeStore?.loanSchemaQuota?.recommendAmountList}
              max={maxLoanAmount}
              placeholder={`最多可借${schemeStore?.loanSchemaQuota?.maxLoanAmount}`}
              inputMode="numeric"
              autoFocus
            />
          </Form.Item>
          <div className={classNames(styles.wrap, !loanApplyAmountValue && styles.hide)}>
            <div className={styles.panel}>
              <Form.Item required={false} rules={loanTerm.rules} name="loanTerm">
                <LoanTerm options={schemeStore?.loanTerm?.options} />
              </Form.Item>
              <Form.Item required={false} rules={repaymentMethod.rules} label="怎么还" name="repaymentMethod">
                <RepaymentMethod
                  options={schemeStore?.repaymentMethod?.options}
                  trialStore={trialStore}
                  processAction={processAction}
                />
              </Form.Item>
              {renderPromoition()}
              <Form.Item required={false} rules={receiveBankCard.rules} label="收款账户" name="receiveBankCard">
                <Account />
              </Form.Item>
              <Form.Item
                className={styles.hide}
                required={false}
                rules={institution.rules}
                label="放款机构"
                name="institution"
              >
                <Institution capitalInstitution={trialStore?.capitalInstitution} />
              </Form.Item>
              <Form.Item
                className={styles.hide}
                required={false}
                rules={loanPurpose.rules}
                label="借款用途"
                name="loanPurpose"
              >
                <LoanPurpose
                  ref={loanPurposeRef}
                  options={schemeStore?.loanPurpose?.options}
                />
              </Form.Item>
              <Form.Item
                className={styles.hide}
                label="借款合同"
                name="agreementCheck"
              >
                <UnsignedAgreementsSSD
                  ref={loanAgreementRef}
                  popupTitle="借款相关协议"
                  bizType="LOAN"
                  creditPlatform="MAYI_ZHIXIN"
                  customPayload={loanAgreements?.payLoad}
                  onCompleted={handleAgreementCompleted}
                  getExtensionParams={getLoanAgreementExtensionParams}
                  useInit={false}
                  onError={handleAgreementEC}
                  onClose={handleAgreementEC}
                  force={false}
                  buttonText="同意协议并借钱"
                />
              </Form.Item>
              <DetailAndAgreements
                form={form}
                trialStore={trialStore}
                loanAgreements={loanAgreements?.payLoad}
                onAgreementClick={handleLoanAgreementShow}
                onPurposeClick={handleLoanPurposeShow}
              />
            </div>
          </div>
        </Form>
      );
    }
    return null;
  };

  const renderDefault = () => {
    return (
      <>
        <NewAmountInput disabled label={loanAmountTips()} />
        <SsdLoanApplySkeleton />
      </>
    );
  };

  const renderMain = () => {
    if (schemeStore?.admitted === true) {
      return renderNormal();
    } else if (schemeStore?.admitted === false) {
      return <Reject schemeStore={schemeStore} />;
    }
    return renderDefault();
  };

  // 支用申请期间禁用页面操作
  const renderHover = () => {
    if (
      processAction === 'AUTENTICATING' ||
      processAction === 'SUBMITTING'
    ) {
      return (<div className={styles.hover} />);
    }
    return null;
  };

  useEffect(() => {
    handleInit();
  }, []);

  return (
    <div className={styles.loanPage}>
      {renderHover()}
      {renderMain()}
    </div>
  );
}
