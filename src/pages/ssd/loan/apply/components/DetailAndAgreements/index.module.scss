
.detailAndAgreements {
  padding: 0 !important;

  text-align: right;
  .text {
    font-size: 26rpx;
    line-height: 39rpx;
    color: #7c889c;
    display: inline-block;
  }
  .formItem {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    .formItemLabel {
      font-size: 26rpx;
      color: #111;
      line-height: 39rpx;
    }
    .formItemValue {
      display: flex;
      flex-direction: row;
      align-items: center;
      flex: 1 1;
    }
    .valueText {
      font-weight: 500;
      font-size: 26rpx;
      color: #111;
      text-align: right;
      line-height: 39rpx;
      flex: 1 1;
    }
    .icon {
      margin-left: 8rpx;
    }
  }
}

.popupContent {
  height: calc(67vh - 8rpx);
  .contentBox {
    padding: 0 8rpx;
    padding-top: 16rpx;
    .item {
      display: flex;
      justify-content: space-between;
      padding-bottom: 25rpx;
      .label {
        font-weight: 400;
        font-size: 28rpx;
        color: rgba(#000, 40%);
      }
      .value {
        font-size: 28rpx;
        color: rgba(#000, 80%);
        text-align: right;
        span {
          font-size: 28rpx;
          color: rgba(#000, 80%);
        }
      }
      .link {
        font-size: 28rpx;
        color: #1677ff;
        text-align: right;
      }
    }
  }
}
