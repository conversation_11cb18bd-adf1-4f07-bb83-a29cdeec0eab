/**
 * @file sdd详情及协议专用组件
 */

import { useCallback, useRef } from 'react';
import { Form } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';
import { number } from '@ali/iec-dtao-utils';

import { CommonPopup, ArrowIcon, BankInfoTip } from '@/components';
import Institution from '../Institution';
import { _, getMD } from '@/utils';
import { formatLoanTerm, formatRepaymentMethod } from '@/store/lib/format';
import { TrialResponse } from '@/store/loan';
import { getLoanAgreementsExtensions } from '@/store/loan/ssd/format';
import { QueryUnSignAgreementListResponse } from '@/store/agreement/actions';

import styles from './index.module.scss';

interface DetailAndAgreementsProps {
  form: any;
  trialStore?: TrialResponse;
  loanAgreements?: QueryUnSignAgreementListResponse;
  onPurposeClick: () => void;
  onAgreementClick: () => void;
}

export default function DetailAndAgreements(props: DetailAndAgreementsProps) {
  const popupRef = useRef<any>();
  const { trialStore, form, loanAgreements, onPurposeClick, onAgreementClick } = props;
  const loanApplyAmount = Form.useWatch('loanApplyAmount', form);
  const loanTerm = Form.useWatch('loanTerm', form);
  const receiveBankCard = Form.useWatch('receiveBankCard', form);
  const repaymentMethod = Form.useWatch('repaymentMethod', form);
  const loanPurpose = Form.useWatch('loanPurpose', form);

  const isDeductAlipayAgreementExist = useCallback(() => {
    return getLoanAgreementsExtensions(loanAgreements)?.isDeductAlipayAgreementExist || false;
  }, [loanAgreements]);

  // 展示
  const handleClickShow = useCallback(() => {
    log.addClickLog('ssd-loan-apply-detail-show');
    popupRef.current.toggleVisible(true);
  }, []);

  const getRepaymentDayDesc = (repaymentDay) => {
    if (trialStore?.installmentPlanList) {
      const firstInstanllmentPlan = _.first(trialStore.installmentPlanList);
      if (firstInstanllmentPlan?.endDate) {
        return `每月${repaymentDay}日 首次还款${getMD(firstInstanllmentPlan.endDate)}`;
      }
    }
    return '--';
  };

  const getInterestDesc = (interest?: string) => {
    if (trialStore?.installmentPlanList) {
      const planNum = trialStore.installmentPlanList.length;
      return `借满${planNum}个月，共¥${number.amountFormat(interest)}`;
    }
    return '--';
  };

  const getRateDesc = (rate) => {
    return rate ? `${rate}%` : '--';
  };

  const renderContent = () => {
    const interestRatePercent = getRateDesc(trialStore?.interestRatePercent);
    const dailyInterestRatePercent = getRateDesc(trialStore?.dailyInterestRatePercent);
    const capitalInstitution = trialStore?.capitalInstitution;
    const interest = getInterestDesc(trialStore?.interest);
    const repaymentDay = getRepaymentDayDesc(trialStore?.repaymentDay);

    return (
      <div className={styles.contentBox}>
        <div className={styles.item}>
          <span className={styles.label}>借款金额</span>
          <span className={styles.value}>
            <span className={styles.yuan}>¥</span>{loanApplyAmount}
          </span>
        </div>
        {receiveBankCard?.bankCardNo ? (
          <div className={styles.item}>
            <span className={styles.label}>收款账户</span>
            <BankInfoTip
              className={styles.value}
              name={receiveBankCard?.bankName}
              cardNo={receiveBankCard?.bankCardNo}
            />
          </div>
        ) : null}
        <div className={styles.item}>
          <span className={styles.label}>初始年利率(单利)</span>
          <span className={styles.value}>{interestRatePercent}</span>
        </div>
        <div className={styles.item}>
          <span className={styles.label}>日利率</span>
          <span className={styles.value}>{dailyInterestRatePercent}（年利率/360）</span>
        </div>
        <div className={styles.item}>
          <span className={styles.label}>借款期限</span>
          <span className={styles.value}>{formatLoanTerm(loanTerm)}</span>
        </div>
        <div className={styles.item}>
          <span className={styles.label}>还款方式</span>
          <span className={styles.value}>{formatRepaymentMethod({ value: repaymentMethod })}</span>
        </div>
        <div className={styles.item}>
          <span className={styles.label}>还款日</span>
          <span className={styles.value}>{repaymentDay}</span>
        </div>
        <div className={styles.item}>
          <span className={styles.label}>总利息</span>
          <span className={styles.value}>{interest}</span>
        </div>
        <div className={styles.item}>
          <span className={styles.label}>服务机构</span>
          <Institution
            prefix="随身贷"
            customClassName={styles.value}
            capitalInstitution={capitalInstitution}
          />
        </div>
        <div className={styles.item} onClick={onPurposeClick}>
          <span className={styles.label}>借款用途</span>
          <span className={styles.link}>
            {loanPurpose?.label}
          </span>
        </div>
        <div className={styles.item} onClick={onAgreementClick}>
          <span className={styles.label}>借款相关协议</span>
          <span className={styles.link}>
            查看
          </span>
        </div>
      </div>
    );
  };

  return (
    <div className={styles.detailAndAgreements}>
      <div className={styles.formItem}>
        <p className={styles.formItemLabel}>详情及协议</p>
        <div className={styles.formItemValue} onClick={handleClickShow}>
          <p className={styles.valueText}>
            {isDeductAlipayAgreementExist() ? '到期支付宝自动扣款' : ''}
          </p>
          <ArrowIcon className={styles.icon} />
        </div>
      </div>
      <CommonPopup
        ref={popupRef}
        title={'详情及协议'}
        bodyClassName={styles.popupContent}
      >
        {renderContent()}
      </CommonPopup>
    </div>
  );
}
