/**
 * @file 收款账户组件
 * <AUTHOR>
 */
import type { ReceiveBankCard } from '@/store/loan';
import { ArrowIcon } from '@/components';
import { CardList } from '@alipay/mayijie';
import { getAppInfo, refreshAlipayCookie } from '@/utils/bridges';
import { navigatorOpenURL } from '@/utils/link';
import { isEmpty } from 'lodash-es';
import { log } from '@alife/dtao-iec-spm-log';

import styles from './index.module.scss';

interface LoanAccountProps {
  value?: ReceiveBankCard;
  onChange?: any;
}

export default function Account(props: LoanAccountProps) {
  const { value, onChange } = props;

  const formatValue = () => {
    if (!isEmpty(value)) {
      const { bindCardNo, bankCardNo, bankCode, bankName } = value;
      return {
        bankCode,
        bankContractId: bindCardNo,
        instName: bankName,
        showCardNo: bankCardNo,
      };
    }
    return null;
  };

  const onSelect = (data: any) => {
    onChange && onChange({
      bindCardNo: data?.bankContractId,
      bankCardNo: data?.showCardNo,
      bankCode: data?.instId,
      bankName: data?.instName,
    });
  };

  return (
    <div className={styles.cardListBox}>
      <CardList
        // @ts-ignore
        value={formatValue()}
        onSelect={onSelect}
        url="https://pcloanpromobff.alipay.com/h5/api/suishenLoan/acc/queryAccList.json"
        env="m"
        refreshAlipayCookie={refreshAlipayCookie}
        getAppInfo={getAppInfo}
        openURL={navigatorOpenURL}
        platform="TAOTIAN"
        log={log.addClickLog}
      />
      {!isEmpty(value) ? <ArrowIcon className={styles.icon} /> : null}
    </div>
  );
}
