/**
 * @file 优惠券
 * <AUTHOR>
 */

import { number } from '@ali/iec-dtao-utils';

import { TrialResponse, PROCESS_ACTION } from '@/store/loan/types';

import styles from './index.module.scss';

interface CouponProps {
  trialStore?: TrialResponse;
  processAction?: PROCESS_ACTION;
}

export default function Coupon(props: CouponProps) {
  const { trialStore, processAction } = props;

  const renderPanel = () => {
    if (processAction === 'TRIALING') {
      return (
        <div className={styles.couponInfo}>
          <span className={styles.cal}>
            计算中...
          </span>
        </div>
      );
    }
    if (trialStore?.promotionAmount) {
      const promotionAmount = number.getNumber(trialStore?.promotionAmount);
      if (promotionAmount !== '--' && promotionAmount > 0) {
        return (
          <div className={styles.couponInfo}>
            <i className={styles.icon}>限时降价</i>
            <span className={styles.text}>共优惠</span>
            <span className={styles.promotion}>
              {number.amountFormat(trialStore?.promotionAmount)}元
            </span>
          </div>
        );
      }
    }
    return null;
  };

  return renderPanel();
}
