.loanPurpose {
  --field-panel-text-align: right;
  --field-panel-placeholder-color: #7c889c;
  --field-panel-text-color: #7c889c;
  --panel-justify-content: flex-end;
}

.text {
  text-align: right;
  color: #7c889c;
}

.right {
  bottom: 0;
  top: unset;
  height: calc(67vh - 8rpx);
  width: 100%;
}

.container {
  padding: 0 8rpx;
}

.tips {
  position: relative;
  top: -6rpx;
  font-size: 24rpx;
  color: rgba(#000, 40%);
  text-align: center;
  padding-bottom: 48rpx;
}

.listItem {
  margin-top: 31rpx;
  &:first-child {
    margin-top: 0;
  }
}
