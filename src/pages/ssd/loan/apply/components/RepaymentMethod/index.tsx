/**
 * @file 还款方式组件
 * <AUTHOR>
 */

import { useCallback } from 'react';
import { number } from '@ali/iec-dtao-utils';

import type { RepaymentMethodDTO } from '@/store/types';
import type { PROCESS_ACTION, TrialResponse } from '@/store/loan/types';
import { formatRepaymentMethod } from '@/store/lib/format';
import { _, getMD } from '@/utils';
import { PopupPanelField, Selector, RepaymentMethodDesc } from '@/components';

import styles from './index.module.scss';
import { get } from 'lodash-es';

interface RepaymentMethodProps {
  options?: RepaymentMethodDTO[];
  trialStore?: TrialResponse;
  processAction?: PROCESS_ACTION;
}

export default function RepaymentMethod(props: RepaymentMethodProps) {
  const { options, trialStore, processAction, ...other } = props;

  const renderFold = useCallback((foldProps: any) => {
    return (
      <div className={styles.fold}>
        <Selector
          size="large"
          options={options}
          color="primary"
          columns={options?.length}
          logKey="repayment-method"
          {...foldProps}
        />
        <RepaymentMethodDesc
          value={get(other, 'value')}
          options={options}
          trialStore={trialStore}
          processAction={processAction}
        />
      </div>
    );
  }, [options, trialStore, processAction, other]);

  const getDesc = useCallback(() => {
    const { amountFormat } = number;
    // TODO，计算中不能点击
    if (processAction === 'TRIALING') {
      return '计算中，请稍等...';
    }
    if (trialStore?.installmentPlanList) {
      const firstInstanllmentPlan = _.first(trialStore.installmentPlanList);
      if (firstInstanllmentPlan?.endDate) {
        return `首期${getMD(firstInstanllmentPlan.endDate)}，应还${amountFormat(firstInstanllmentPlan?.totalAmount)}元`;
      }
    }
    return '';
  }, [trialStore, processAction]);

  const renderPanel = useCallback((value: string) => {
    return (
      <div className={styles.panel}>
        <p className={styles.title}>
          {formatRepaymentMethod({ value })}
        </p>
        <p className={styles.desc}>
          {getDesc()}
        </p>
      </div>
    );
  }, [getDesc]);

  return (
    <PopupPanelField<string>
      renderPanel={renderPanel}
      renderFold={renderFold}
      popupProps={{
        title: '怎么还',
      }}
      {...other}
    />
  );
}
