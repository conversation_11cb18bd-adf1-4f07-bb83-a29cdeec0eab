/**
 * @file 利率
 */

import { TrialResponse } from '@/store/loan';
import { isEmpty } from 'lodash-es';

import styles from './index.module.scss';

interface InterestRateProps {
  trialStore?: TrialResponse;
}

export default function InterestRate(props: InterestRateProps) {
  const { trialStore } = props;

  const getRateDesc = (rate?: string) => {
    return rate ? `${rate}%` : '--';
  };

  if (isEmpty(trialStore) || !trialStore) {
    return <div className={styles.interestEmpty} />;
  }

  const checkPromotion = () => {
    if (trialStore) {
      const { baseInterestRatePercent, interestRatePercent } = trialStore;
      if (
        baseInterestRatePercent && interestRatePercent &&
        baseInterestRatePercent !== interestRatePercent
      ) {
        return true;
      }
    }
    return false;
  };

  const renderPromotion = () => {
    return (
      <>
        <span className={styles.text}>
          年利率（单利）
        </span>
        <span className={styles.text}>
          {getRateDesc(trialStore.interestRatePercent)}
        </span>
        <span className={styles.textLine}>
          {getRateDesc(trialStore.baseInterestRatePercent)}
        </span>
      </>
    );
  };

  const renderBase = () => {
    return (
      <>
        <span className={styles.text}>
          年利率（单利）
        </span>
        <span className={styles.text}>
          {getRateDesc(trialStore.interestRatePercent)}
        </span>
      </>
    );
  };

  return (
    <div className={styles.interestRate}>
      {checkPromotion() ? renderPromotion() : renderBase()}
    </div>
  );
}
