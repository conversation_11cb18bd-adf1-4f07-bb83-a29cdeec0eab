/**
 * @file 借款期限组件
 * <AUTHOR>
 */

import type { LoanTermDTO } from '@/store/types';
import { formatLoanTerm } from '@/store/lib/format';
import { FoldPanelField, Selector } from '@/components';

import styles from './index.module.scss';
import { useCallback } from 'react';

interface LoanTermProps {
  options?: LoanTermDTO[];
}

export default function LoanTerm(props: LoanTermProps) {
  const { options, ...other } = props;

  const checkActive = useCallback((option: LoanTermDTO, value: LoanTermDTO) => {
    if (value?.loanTermUnit && value?.value) {
      if (value?.loanTermUnit === option?.loanTermUnit && value?.value === option?.value) {
        return true;
      }
    }
    return false;
  }, []);

  const formatOnChange = useCallback((value: LoanTermDTO) => {
    return value;
  }, []);

  const renderFold = useCallback((foldProps: any) => {
    const optionLen = options?.length || 0;
    return (
      <Selector
        size="large"
        options={options}
        color="primary"
        checkActive={checkActive}
        formatOnChange={formatOnChange}
        columns={optionLen >= 3 ? 3 : optionLen}
        logKey="loan-term"
        {...foldProps}
      />
    );
  }, [options, formatOnChange, checkActive]);

  const renderPanel = useCallback((value: LoanTermDTO) => {
    return (
      <p className={styles.text}>
        {formatLoanTerm(value)}
      </p>
    );
  }, []);

  return (
    <FoldPanelField<LoanTermDTO>
      label="借多久"
      renderFold={renderFold}
      renderPanel={renderPanel}
      logKey="loan-term"
      {...other}
    />
  );
}
