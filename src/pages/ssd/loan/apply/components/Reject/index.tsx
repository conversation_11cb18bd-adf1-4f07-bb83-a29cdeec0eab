/**
 * @file 支用不准入的各种状况
 * <AUTHOR>
 */

import { Button, Dialog } from 'antd-mobile';
import { useEffect, useRef } from 'react';

import { resetToSsdHomePlus, reload, toIndex, popPage, navigatorOpenURL } from '@/utils/link';
import { SSDCommonResult, NewAmountInput, RejectReason, RejectReasonRef } from '@/components';
import { ErrorMessageMap } from '@/common/constant';
import type { LoanStore } from '@/store/loan/types';
import { log } from '@alife/dtao-iec-spm-log';

import styles from './index.module.scss';

interface RejectProps {
  schemeStore: LoanStore;
}

export default function Reject(props: RejectProps) {
  const { rejectReason, extension } = props.schemeStore;
  const popupRef = useRef<RejectReasonRef>();

  const REJECT_CONFIG = {
    NO_HAS_CREDIT_CONTRACT: {
      title: '没有授信合约',
      onClick: toIndex,
    },
    C_PERMIT_ALIPAY_ACCOUNT: {
      customImg: <img className={styles.idCardIcon} src="https://gw.alicdn.com/imgextra/i1/O1CN01FXRUBW21SGjUkPphu_!!*************-2-tps-192-192.png" />,
      title: '请更新身份证',
      description: '根据国家相关法律法规要求，为了持续向你提供借款服务，请上传最新证件照片，更新你预留的身份信息',
      desc: '证件更新后，可继续使用服务',
      btnText: '去更新',
      onClick: () => {
        log.addClickLog('idcard-reject-update-click');
        navigatorOpenURL(extension?.unLimitUrl);
      },
    },
    ON_GOING_CONTRACT_MORE_THAN_UPPER_LIMIT: {
      title: `借款上限${extension?.maxLoanContractCount || '--'}笔，暂无法借钱`,
      onClick: popPage,
      btnText: '返回',
    },
    HAS_OVERDUE: {
      customImg: <img className={styles.idCardIcon} src="https://gw.alicdn.com/imgextra/i2/O1CN01hov7Mh22uUkxNIs0d_!!*************-2-tps-192-192.png" />,
      title: '暂无法借款',
      description: '您当前存在已逾期的借款，请还清后再借',
      onClick: popPage,
      btnText: '返回',
    },
    REFRESH: {
      title: ErrorMessageMap.BUSY_DEFUALT,
      onClick: popPage,
      btnText: '返回',
    },
    LOAN_DISABLED: {
      customImg: <img className={styles.image} src="https://gw.alicdn.com/imgextra/i2/O1CN01hov7Mh22uUkxNIs0d_!!*************-2-tps-192-192.png" />,
      title: '服务升级中',
      description: '暂无法提供服务，请耐心等待',
    },
  };

  const renderDefault = () => {
    const option = REJECT_CONFIG[rejectReason?.rejectCode || ''];
    if (option) {
      return (
        <div className={styles.reject}>
          <SSDCommonResult
            status="FAILED"
            customImg={option?.customImg}
            title={option?.title || rejectReason?.rejectDesc}
          />
          <p className={styles.description}>{option?.description || ''}</p>
          <p className={styles.desc}>{option?.desc || ''}</p>
          <Button
            className={styles.button}
            color="primary"
            size="large"
            onClick={option?.onClick || resetToSsdHomePlus}
          >
            {option?.btnText || '返回'}
          </Button>
        </div>
      );
    }
    return (
      <div className={styles.reject}>
        <SSDCommonResult
          status="PROCESSING"
          title="暂无法为你提供服务"
        />
        <Button
          className={styles.button}
          color="primary"
          onClick={knowReason}
        >
          了解原因
        </Button>
      </div>
    );
  };

  const knowReason = () => {
    popupRef.current?.show();
    log.addClickLog('loan-reject-reason-click');
    log.addShowLog('loan-reject-reason-show');
  };

  const showModal = () => {
    switch (rejectReason?.rejectCode) {
      case 'L_USER_OVD':
        Dialog.show({
          title: '暂无法借款',
          content: '你当前存在已逾期的借款，请还清后再借',
          actions: [{
            key: 'retry',
            text: '我知道了',
            onClick: popPage,
          }],
        });
        break;
      case 'REFRESH':
        Dialog.show({
          content: '啊呀开了个小差，请重试',
          closeOnAction: true,
          actions: [[{
            key: 'cancel',
            text: '以后再说',
            onClick: popPage,
          }, {
            text: '刷新页面',
            onClick: reload,
            key: 'confirm',
            bold: true,
          }]],
        });
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    showModal();
    log.addShowLog('loan-reject-show', { rejectCode: rejectReason?.rejectCode });
  }, []);

  const renderMain = () => {
    if (rejectReason?.rejectCode === 'L_USER_OVD' || rejectReason?.rejectCode === 'REFRESH') {
      return (
        <NewAmountInput disabled label="" />
      );
    } else {
      return renderDefault();
    }
  };

  return (
    <>
      {renderMain()}
      <RejectReason ref={popupRef} />
    </>
  );
}
