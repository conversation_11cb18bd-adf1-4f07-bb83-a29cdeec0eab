/**
 * @file 放款机构组件
 * <AUTHOR>
 */

import { useCallback } from 'react';
import { map } from 'lodash-es';
import classNames from 'classnames';

import { _ } from '@/utils';
import { CapitalInstitutionDTO } from '@/store/types';

import styles from '../../index.module.scss';

interface InstitutionProps {
  capitalInstitution?: CapitalInstitutionDTO[];
  customClassName?: string;
  prefix?: string;
}

export default function Institution(props: InstitutionProps) {
  const { capitalInstitution = [], customClassName, prefix = '' } = props;

  const getCapitalInstitutionName = useCallback(() => {
    if (!capitalInstitution.length) {
      return '';
    }
    const names = map(capitalInstitution, (item) => {
      return item?.institutionName;
    });
    return _.join(names, ',');
  }, [capitalInstitution]);

  return (
    <p className={classNames(styles.text, customClassName && customClassName)}>
      {prefix ? `${prefix}·` : ''}{getCapitalInstitutionName()}
    </p>
  );
}
