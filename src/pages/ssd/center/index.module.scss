.myCenterWrap {
  background-size: cover;
  background-repeat: no-repeat;
  padding: 32rpx 32rpx 210rpx;
  .personName {
    font-size: 36rpx;
    font-weight: 500;
    margin-bottom: 20rpx;
    height: 50rpx;
  }
  .recordWrap {
    display: flex;
    padding: 24rpx 0;
    background: #fff;
    border-radius: 12rpx;
    margin-bottom: 24rpx;
  }
  .recordItem {
    flex: 1 1 50%;
    padding: 8rpx;
    font-size: 30rpx;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    position: relative;
    &:first-child {
      &::after {
        content: "";
        display: block;
        position: absolute;
        top: 16rpx;
        right: 0rpx;
        width: 1rpx;
        height: 40rpx;
        background: #e5e8ec;
      }
    }
    img {
      width: 56rpx;
      height: 56rpx;
      margin-right: 12rpx;
    }
  }
  .serviceWrap {
    padding: 0 24rpx;
    background-color: #fff;
    border-radius: 12rpx;
    .serviceItem {
      font-size: 26rpx;
      height: 108rpx;
      display: flex;
      align-items: center;
      .itemName {
        flex: 1 1 auto;
      }
      .leftIcon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 24rpx;
      }
      .right {
        font-family: "ALIBABA NUMBER FONT RG";
        color: #7c889c;
      }
    }
  }
  .myCenterTip {
    text-align: center;
    color: #7c889c;
  }
}

.blockItem {
  height: 114rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
}
.middleEmpty {
  height: 16rpx;
  background-color: #f5f5f5;
}

.closePopup {
  :global(.adm-popup-body) {
    border-radius: 12rpx 12rpx 0 0;
    min-height: 96rpx;
  }
}
