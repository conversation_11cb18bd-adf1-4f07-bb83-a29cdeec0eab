/**
 * @file 已签署的协议
 */

import { useState, useCallback, useEffect } from 'react';
import { definePageConfig } from 'ice';
import { Modal, Toast } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';
import { groupBy } from 'lodash-es';
import dayjs from 'dayjs';

// import { ArrowIcon } from '@/components';
import { querySsdSignedAgreementPage, SsdSignedAgreementDTO } from '@/store/agreement/actions';
import LinkUtil, { popPage } from '@/utils/link';
import { ErrorMessageMap, PAGES } from '@/common/constant';
import classnames from 'classnames';

import styles from './index.module.scss';

const defaultBizType = 'PLATFORM';

export default function SignedAgreements() {
  const [list, setList] = useState<Record<string, SsdSignedAgreementDTO[]>>({});
  const [isReady, setIsReady] = useState(false);
  const [fetchError, setFetchError] = useState(false);
  const curBizType = defaultBizType;

  const renderItem = useCallback((item: SsdSignedAgreementDTO) => {
    return (
      <div
        onClick={() => handleAgreementClick(item)}
        key={item.code}
        className={styles.agreementItem}
      >
        <div>
          <p className={styles.name}>{item.name}</p>
          <div className={styles.time}>
            生效日期 {item?.signTime ? dayjs(item?.signTime).format('YYYY年MM月DD日') : '-'}
          </div>
        </div>
        <div className={styles.arrowRight} />
      </div>
    );
  }, []);

  const renderEmpty = useCallback(() => {
    return (
      <div className={styles.empty}>
        <img
          className={styles.emptyImg}
          src="https://gw.alicdn.com/imgextra/i1/O1CN01k0MIIk1Uum53BWQrs_!!6000000002578-2-tps-560-400.png"
        />
        <p className={styles.emptyText}>{fetchError ? '网络异常' : '暂无协议'}</p>
      </div>
    );
  }, []);

  const handleAgreementClick = (item: any) => {
    log.addClickLog('signed-agreement-click');
    LinkUtil.pushPage(PAGES.SsdSignedAgreementsPreview, {
      code: item?.code,
      institution: item?.institution,
      agreementNo: item?.agreementNo,
      contractNo: item?.contractNo,
      bizType: item?.type,
    });
  };

  const init = async () => {
    try {
      Toast.show({
        icon: 'loading',
        content: '加载中...',
        duration: 0,
      });
      const [platformRes, creditRes] = await Promise.all([
        querySsdSignedAgreementPage({
          bizType: 'PLATFORM', // 先查一次平台
          sortType: 'SIGN_TIME_DESC',
          creditPlatform: 'MAYI_ZHIXIN',
        }),
        querySsdSignedAgreementPage({
          bizType: 'CREDIT', // 再查一次授信
          sortType: 'SIGN_TIME_DESC',
          creditPlatform: 'MAYI_ZHIXIN',
        }),
      ]);

      // 合并dataList数组
      const combinedDataList = [
        ...(platformRes?.signedAgreementList || []),
        ...(creditRes?.signedAgreementList || []),
      ];

      const groupedData = groupBy(combinedDataList, (item) => {
        const institution = item?.institution || '';
        const type = item?.type || '未知类型';
        return item?.institution ? `${institution}_${type}` : `TB_PLATFORM_${type}`;
      });

      setList(groupedData);
    } catch (e) {
      Modal.show({
        content: ErrorMessageMap.BUSY_DEFUALT,
        actions: [
          {
            primary: true,
            key: 'retry',
            onClick: popPage,
            text: '我知道了',
          },
        ],
      });
      setFetchError(true);
      log.addErrorLog('signed-agreements-fetch');
    } finally {
      setIsReady(true);
      Toast.clear();
    }
  };

  const renderAgreementContainer = (key: any) => {
    return (
      <div key={key} className={styles.groupContainer}>
        {list[key]?.length > 0 && <>{list[key]?.map(renderItem)}</>}
      </div>
    );
  };

  useEffect(() => {
    init();
  }, [curBizType]);

  useEffect(() => {
    log.addVisitLog(PAGES.SsdSignedAgreements);
  }, []);

  if (!isReady) {
    return null;
  }

  return (
    <div className={classnames([styles.signedAgreements])}>
      <div className={styles.agreementItems}>
        {Object.keys(list).length > 0 ? Object.keys(list)?.map(renderAgreementContainer) : renderEmpty()}
      </div>
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '相关协议',
  spm: {
    spmB: PAGES.SsdSignedAgreements,
  },
}));
