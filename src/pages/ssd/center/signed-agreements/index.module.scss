.signedAgreements {
  --primary: #1677ff;
  background: #f3f6f8 !important;
  padding: 0 !important;
  margin: 16rpx;
}

.agreementItems {
  // margin-top: 24rpx;
}
.groupContainer {
  margin-bottom: 16rpx;
  .agreementItem {
    height: auto;
    box-sizing: initial;
    display: flex;
    flex-direction: row;
    align-items: center;
    background: #fff;
    padding: 32rpx 24rpx;
    font-size: 26rpx;
    justify-content: space-between;
    color: #111;
    position: relative;
    &:only-child {
      border-radius: 24rpx !important; // 覆盖原有首尾样式
    }
    &:first-child {
      border-radius: 24rpx 24rpx 0 0;
    }
    &:last-child {
      border-radius: 0 0 24rpx 24rpx;
    }
    &:not(:last-child)::after {
      content: "";
      position: absolute;
      width: 670rpx;
      bottom: 0;
      height: 1px;
      background: #eee;
      transform: scaleY(0.5);
    }
    .name {
      color: #333;
      font-size: 32rpx;
      line-height: 45rpx;
      font-family:  PingFangSC;
    }
    .time {
      margin-top: 12rpx;
      font-weight: 400;
      font-size: 24rpx;
      line-height: 33rpx;
      color: #999;
      font-family:  PingFangSC;
    }
    .arrowRight{
      background-image: url('https://gw.alicdn.com/imgextra/i2/O1CN017iP2OQ1PROdIdv4Wt_!!6000000001837-2-tps-56-56.png');
      background-size: 100%;
      width: 28rpx;
      height: 28rpx;
    }
  }
}

.empty {
  padding-top: 316rpx;
  .emptyImg {
    width: 280rpx;
    margin: auto;
    display: block;
  }
  .emptyText {
    font-size: 36rpx;
    font-weight: 600;
    line-height: 36rpx;
    text-align: center;
    letter-spacing: 0;
    color: #7c889c;
    padding-top: 32rpx;
  }
}
