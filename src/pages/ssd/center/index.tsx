import { useState, useEffect, Fragment, useCallback } from 'react';
import { definePageConfig } from 'ice';
import styles from './index.module.scss';
import { _, getQueryParams, getErrorCodeFromRejectRes, getMsgByErrorCode } from '@/utils';
import { Safe<PERSON><PERSON>, Popup, Dialog } from 'antd-mobile';
import ArrowIcon from '@/components/ArrowIcon';
import FullLoading from '@/components/FullLoading';
import { checkHmRejectAlert } from '@/components/HmReject';
import classnames from 'classnames';
import { useDebounceFn } from 'ahooks';
import { consultReason, queryCreditContract } from '@/store/center/actions';
import { PAGES } from '@/common/constant';
import LinkUtil from '@/utils/link';
import { log } from '@alife/dtao-iec-spm-log';

const CenterSignedAgreementsItem = {
  key: 'SignedAgreements',
  title: '相关协议',
  url: PAGES.SsdSignedAgreements,
  icon: 'https://gw.alicdn.com/imgextra/i1/O1CN01zuOAZl1hCt6v0sFFg_!!*************-2-tps-64-64.png',
};

const HomeMyCenterServiceConfig = [
  {
    key: 'AlipayAccount',
    title: '支付宝账号',
    icon: 'https://gw.alicdn.com/imgextra/i3/O1CN01pHFFKW1tD8j5ZnOJD_!!*************-2-tps-64-64.png',
  },
  {
    key: 'QuotaRate',
    title: '我的额度及更多',
    url: PAGES.SsdMyQuota,
    needQUALIFIED: true,
    icon: 'https://gw.alicdn.com/imgextra/i2/O1CN01xjBF4Z1SRnlrraHN8_!!*************-2-tps-64-64.png',
  },
  {
    key: 'Coupon',
    title: '优惠券',
    url: PAGES.SsdCoupon,
    needQUALIFIED: true,
    icon: 'https://gw.alicdn.com/imgextra/i2/O1CN01Sl58bw1ESo4GLLhUX_!!*************-2-tps-66-66.png',
  },
  CenterSignedAgreementsItem,
  // {
  //   key: 'ServiceManage',
  //   title: '服务管理',
  //   needService: true,
  //   icon: 'https://gw.alicdn.com/imgextra/i1/O1CN019uAYuo26jjvL1CClC_!!*************-2-tps-64-64.png',
  // },
];

export default function MyCenter() {
  const [creditConsultData, setCreditConsultData] = useState<any>(null);
  const [closePopupVisible, setClosePopupVisible] = useState(false);
  const [inited, setInited] = useState(false);
  const closeApplyType = _.get(getQueryParams(), 'quitType') || 'MANUAL';

  const handleServiceManageClick = () => {
    setClosePopupVisible(true);
  };

  const handleClose = () => {
    setClosePopupVisible(false);
  };

  const { run: handleCloseServiceBtnClick } = useDebounceFn(async () => {
    log.addClickLog('service-item-close-btn');
    if (checkHmRejectAlert(PAGES.SsdCenter)) {
      return;
    }
    try {
      const consultRes = await consultReason({
        applyType: closeApplyType,
      });
      if (!consultRes.admitted) {
        throw new Error(getErrorCodeFromRejectRes(consultRes));
      } else {
        LinkUtil.pushPage(PAGES.SsdClose);
      }
    } catch (error) {
      log.addErrorLog('ssd-close-home-admit');
      setClosePopupVisible(false);
      Dialog.alert({
        content: getMsgByErrorCode(error.message),
      });
    }
  }, {
    wait: 500,
    leading: true,
    trailing: false,
  });

  const recordTabRender = useCallback(() => {
    if (creditConsultData?.status !== 'QUALIFIED') return null;
    return (
      <div className={classnames([styles.recordWrap])}>
        <div
          onClick={() => {
            LinkUtil.pushPage(PAGES.RecordList, { tabKey: 'LOAN', isSSD: '1' });
          }}
          className={styles.recordItem}
        >
          <img src="https://gw.alicdn.com/imgextra/i3/O1CN0149pidm1UCLeIQoq9Y_!!6000000002481-2-tps-112-112.png" />
          借款记录
        </div>
        <div
          onClick={() => {
            LinkUtil.pushPage(PAGES.RecordList, { tabKey: 'REPAY', isSSD: '1' });
          }}
          className={styles.recordItem}
        >
          <img src="https://gw.alicdn.com/imgextra/i2/O1CN0114nPis1eN120YhnYI_!!6000*********-2-tps-112-112.png" />
          还款记录
        </div>
      </div>
    );
  }, [creditConsultData]);

  const serviceItemRender = useCallback(
    (item: any) => {
      if (!item) return;

      const { status } = creditConsultData || {};

      if (item.needQUALIFIED && status !== 'QUALIFIED') {
        return;
      }

      if (item.needService && !_.includes(['QUALIFIED', 'CLEARED'], status)) {
        return null;
      }

      let right = <ArrowIcon />;
      if (item.key === 'AlipayAccount') {
        const alipayDesensitizeLoginId = _.get(
          creditConsultData,
          'extension.alipayDesensitizeLoginId',
        );
        if (!alipayDesensitizeLoginId) return null;
        right = _.get(creditConsultData, 'extension.alipayDesensitizeLoginId');
      }

      return (
        <div
          key={item.key}
          onClick={() => {
            log.addClickLog('my-center-service-item', { key: item.key });
            if (item.key === 'ServiceManage') {
              handleServiceManageClick();
              return;
            }
            if (item.key === 'SignedAgreements') {
              LinkUtil.pushPage(item.url);
              return;
            }
            if (item.onClick) {
              item.onClick();
              return;
            }
            if (item.url) {
              LinkUtil.pushPage(item.url);
            }
          }}
          className={styles.serviceItem}
        >
          <img className={styles.leftIcon} src={item.icon} />
          <span className={styles.itemName}>{item.title}</span>
          <span className={styles.right}>{right}</span>
        </div>
      );
    },
    [creditConsultData],
  );

  const homeMyCenterRender = useCallback(() => {
    const name = _.get(creditConsultData, 'extension.userDesensitizeName');
    return (
      <Fragment>
        <div className={styles.personName}>{name ? `${name}，你好` : ' 你当前未完成额度申请'}</div>
        {recordTabRender()}
        <div className={classnames([styles.serviceWrap])}>
          {HomeMyCenterServiceConfig.map(serviceItemRender)}
        </div>
      </Fragment>
    );
  }, [creditConsultData]);

  const queryConsult = async () => {
    try {
      const res = await queryCreditContract();
      setCreditConsultData(res);
    } catch (error) {
      return null;
    } finally {
      setInited(true);
    }
  };

  useEffect(() => {
    log.addVisitLog(PAGES.SsdCenter);
    queryConsult();
  }, []);

  if (!inited) {
    return <FullLoading visible />;
  }

  return (
    <div className={classnames([styles.myCenterWrap])}>
      {homeMyCenterRender()}
      <Popup
        className={styles.closePopup}
        onClose={handleClose}
        onMaskClick={handleClose}
        visible={closePopupVisible}
      >
        <div onClick={handleCloseServiceBtnClick} className={styles.blockItem}>
          关闭服务
        </div>
        <div className={styles.middleEmpty} />
        <div onClick={handleClose} className={styles.blockItem}>
          取消
        </div>
        <SafeArea position="bottom" />
      </Popup>
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '个人中心',
  spm: {
    spmB: PAGES.SsdCenter,
  },
}));
