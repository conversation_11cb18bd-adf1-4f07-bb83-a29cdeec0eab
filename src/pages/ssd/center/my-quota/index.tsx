import { definePageConfig } from 'ice';
import { useState, useEffect } from 'react';
import styles from './index.module.scss';
import FullLoading from '@/components/FullLoading';
import { numberFormat, _, isGreaterThan0, dayjsFormat, getQueryParams, getErrorCodeFromRejectRes, getMsgByErrorCode } from '@/utils';
import { consultReason, queryCreditContract } from '@/store/center/actions';
import { Popup, SafeArea, Dialog } from 'antd-mobile';
import LinkUtil from '@/utils/link';
import { useDebounceFn } from 'ahooks';
import { checkHmRejectAlert } from '@/components/HmReject';

import { log } from '@alife/dtao-iec-spm-log';
import { PAGES } from '@/common/constant';

export default function MyQuota() {
  const [creditContract, setCreditContract] = useState<any>(null);
  const [closePopupVisible, setClosePopupVisible] = useState(false);
  const { creditQuotaInfo, tempQuotaInfo } = creditContract || {};
  const hasTempQuota = isGreaterThan0(_.get(tempQuotaInfo, 'totalQuota'));
  const closeApplyType = _.get(getQueryParams(), 'quitType') || 'MANUAL';

  const handleServiceManageClick = () => {
    log.addClickLog('service-manage-btn');
    setClosePopupVisible(true);
  };

  const handleClose = () => {
    setClosePopupVisible(false);
  };

  const { run: handleCloseServiceBtnClick } = useDebounceFn(async () => {
    log.addClickLog('service-item-close-btn');
    if (checkHmRejectAlert(PAGES.SsdMyQuota)) {
      return;
    }
    try {
      const consultRes = await consultReason({
        applyType: closeApplyType,
      });
      if (!consultRes.admitted) {
        throw new Error(getErrorCodeFromRejectRes(consultRes));
      } else {
        LinkUtil.pushPage(PAGES.SsdClose);
      }
    } catch (error) {
      log.addErrorLog('ssd-close-home-admit');
      setClosePopupVisible(false);
      Dialog.alert({
        content: getMsgByErrorCode(error.message),
      });
    }
  }, {
    wait: 500,
    leading: true,
    trailing: false,
  });

  useEffect(() => {
    log.addVisitLog(PAGES.CenterQuotaRate);
    queryCreditContract({
      needQuerySurplusQuota: true, // 传true才能拿到可用额度
    }).then((res) => {
      setCreditContract(res);
    });
  }, []);

  if (!creditContract) {
    return <FullLoading visible />;
  }

  return (
    <>
      <div className={styles.ssdMyQuota}>
        <div className={styles.quota}>
          <div className={styles.quotaTitle}>总额度 (元)</div>
          <div className={styles.quotaAmount}>
            {numberFormat(_.get(creditQuotaInfo, 'totalQuota'))}
          </div>
          {hasTempQuota && (
            <div className={styles.tempQuotaWrap}>
              <div className={styles.tempQuotaTitle}>含临时额度 (元)</div>
              <div className="flex-align-center">
                <span className={styles.tempQuotaAmount}>
                  {numberFormat(_.get(tempQuotaInfo, 'totalQuota'))}
                </span>
                <span className={styles.tempQuotaDate}>
                  有效期至{dayjsFormat(_.get(tempQuotaInfo, 'quotaEndTime'))}
                </span>
              </div>
            </div>
          )}
          <div className={styles.quotaTypes}>
            <div className={styles.quotaTypeItem}>
              可用额度
              {creditContract.status === 'FROZEN' ? (
                <span className={styles.frozenText}>额度冻结</span>
              ) : (
                <span className={styles.quotaValue}>
                  {numberFormat(_.get(creditQuotaInfo, 'surplusQuota'))}
                </span>
              )}
            </div>
            <div className={styles.quotaTypeItem}>
              已用额度
              <span className={styles.quotaValue}>
                {numberFormat(_.get(creditQuotaInfo, 'occupiedQuota'))}
              </span>
            </div>
          </div>
        </div>
      </div>
      <div onClick={handleServiceManageClick} className={styles.serviceManageBtn}>
        服务管理
      </div>
      <Popup
        className={styles.closePopup}
        onClose={handleClose}
        onMaskClick={handleClose}
        visible={closePopupVisible}
      >
        <div onClick={handleCloseServiceBtnClick} className={styles.blockItem}>
          关闭服务
        </div>
        <div className={styles.middleEmpty} />
        <div onClick={handleClose} className={styles.blockItem}>
          取消
        </div>
        <SafeArea position="bottom" />
      </Popup>
    </>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '我的额度及更多',
}));
