.ssdMyQuota {
  padding: 32rpx 32rpx 210rpx;
}
.quota {
  border-radius: 12rpx;
  padding: 24rpx;
  box-sizing: border-box;
  background-color: #fff;

  .quotaTitle {
    margin-bottom: 12rpx;
    height: 36rpx;
    line-height: 36rpx;
  }
  .quotaAmount {
    font-size: 60rpx;
    font-weight: 500;
    color: #1677ff;
    font-family: "ALIBABA NUMBER FONT MD";
    margin-bottom: 12rpx;
  }
  .tempQuotaWrap {
    font-size: 26rpx;
    background: rgba(243, 246, 248, 0.7);
    border-radius: 12rpx;
    padding: 15rpx 18rpx;
    position: relative;
    &::before {
      content: "";
      display: block;
      position: absolute;
      left: 24rpx;
      background: rgba(243, 246, 248, 0.7);
      border-width: 1rpx 0 0 1rpx;
      transform: rotate(45deg);
      width: 16rpx;
      height: 16rpx;
      top: -8rpx;
    }
    .tempQuotaTitle {
      color: #111;
      margin-bottom: 8rpx;
    }
    .tempQuotaAmount {
      font-size: 28rpx;
      font-weight: 500;
      font-family: "ALIBABA NUMBER FONT MD";
      margin-right: 8rpx;
    }
    .tempQuotaDate {
      color: #7c889c;
      font-family: "ALIBABA NUMBER FONT RG";
    }
  }
  .frozenText {
    color: #f00;
    font-size: 30rpx;
    font-weight: 500;
    margin-left: 12rpx;
  }
  .quotaTypes {
    margin-top: 40rpx;
    display: flex;
    .quotaTypeItem {
      flex: 0 0 50%;
      color: #7c889c;
      font-size: 26rpx;
      display: inline-flex;
      align-items: center;
      .quotaValue {
        font-size: 32rpx;
        font-weight: 500;
        margin-left: 12rpx;
        color: #111;
        font-family: "ALIBABA NUMBER FONT MD";
      }
    }
  }
}

.serviceManageBtn {
  position: fixed;
  bottom: 92rpx;
  left: 50%;
  transform: translateX(-50%);
  border: 2rpx solid #e5e8ec;
  border-radius: 48rpx;
  height: 56rpx;
  font-size: 28rpx;
  z-index: 999;
  font-weight: 600;

  border-radius: 12rpx;
  box-sizing: border-box;
  /* Fill 填充/风灰蓝 #CACFD7 */
  /* 样式描述：弱提示图形模块填充 **Fill normal2** */
  border: 2rpx solid #cacfd7;
  min-width: 80rpx;
  padding: 15rpx 24rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #50607a;
  cursor: pointer;
}

.closePopup {
  :global(.adm-popup-body) {
    border-radius: 12rpx 12rpx 0 0;
    min-height: 96rpx;
  }
  .closePopupBody {
    padding: 32rpx 32rpx 68rpx;
  }
  .blockItem {
    color: #111;
    font-size: 30rpx;
    margin-bottom: 56rpx;
    text-align: center;
  }
  .middleEmpty {
    height: 16rpx;
    background-color: #f5f5f5;
  }
}
