/**
 * @file 蚂蚁 协议预览
 */

import { useCallback, useEffect, useRef, useState } from 'react';
import { definePageConfig } from 'ice';
import { log } from '@alife/dtao-iec-spm-log';
import { get } from 'lodash-es';
import { Button, DotLoading, Toast } from 'antd-mobile';
import { NavBar } from '@ali/uni-api';
import { useDebounceFn } from 'ahooks';

import { PAGES, ErrorMessageMap } from '@/common/constant';
import { popPage } from '@/utils/link';
import { getQueryParams, requestId } from '@/utils';
import { handleATarget } from '@/utils/hack';
import { VerifyService } from '@/components/SSD/VerifyIdentity/libs/service';
import { VerifyStatus } from '@/components/SSD/VerifyIdentity/libs/types';
import { authenticationCreate, agreementDownload, agreementPreview } from './service';
import { ProtocolDownloadPopup, ProtocolDownloadResultPopup } from '@/components';
import { initAes } from '@/utils/aes';

import styles from './index.module.scss';

export const pageConfig = definePageConfig(() => ({
  spm: {
    spmB: PAGES.SignedAgreementPreview,
  },
  title: '协议内容',
  window: {
    navBarImmersive: false,
  },
}));

export default function SignedAgreementPreview() {
  const [empty, setEmpty] = useState<boolean>();
  const [content, setContent] = useState<any>();
  const [previewRes, setPreviewRes] = useState<any>();
  const [downloadIng, setDownloadIng] = useState<boolean>(false);
  const [isReady, setIsReady] = useState(false);
  const downloadPopupRef = useRef<any>();
  const downloadPopupResultRef = useRef<any>();

  const urlParams = getQueryParams();
  const code = get(urlParams, 'code');
  const institution = get(urlParams, 'institution');
  const bizType = get(urlParams, 'bizType');
  const agreementNo = get(urlParams, 'agreementNo');
  const contractNo = get(urlParams, 'contractNo');
  const bizId = get(urlParams, 'bizId') || undefined;
  const fipBizOrderId = get(urlParams, 'fipBizOrderId') || undefined;

  const getPreview = useCallback(async () => {
    try {
      // 替换成agreementPreview
      // 如果是授权/授信协议 必传
      const params = {
        product: 'XFD',
        creditPlatform: 'MAYI_ZHIXIN', // 必传
        institution, // 有就传
        bizType, // 平台-PLATFORM，授信-CREDIT，支用-LOAN
        code, // 协议code，授权/授信协议必传
        agreementNo, // 协议编号，授权/授信协议必传
        contractNo, // 合同编号，授权/授信协议必传
        bizId, // 业务单号，支用协议必传，传loanOrderId
        fipBizOrderId, // fip单号，支用协议必传，传fipLoanOrderId
      };
      const res = await agreementPreview(params);
      setPreviewRes(res);
      const previewContent = get(res, 'content');
      if (previewContent) {
        log.addSuccessLog('ssd-platform-signed-agreements-pdf-success');
        setContent(previewContent);
      } else {
        throw new Error();
      }
    } catch (e) {
      log.addErrorLog('ssd-platform-signed-agreements-pdf-error');
      setEmpty(true);
    } finally {
      setIsReady(true);
    }
  }, []);

  const verify = async (authenticationToken: any): Promise<boolean> => {
    const verifyService = new VerifyService({
      authenticationToken,
    });
    const rst = await verifyService.verify();
    // 取消核身不需要提示异常
    if (rst?.status === VerifyStatus.CANCEL) {
      return false;
    }
    if (rst?.status !== VerifyStatus.PASS) {
      Toast.show('身份验证失败，请重试');
      return false;
    }
    return true;
  };

  const { run: handleDownload } = useDebounceFn(async (email: string) => {
    log.addClickLog('ssd-agreement-download-email-submit');
    // 1. 创建核身任务 验密
    setDownloadIng(true);
    try {
      const res: any = await authenticationCreate({
        product: 'XFD',
        bizId: requestId(), // 保证唯一
        requestId: requestId(), // 幂等号
        point: 'DOWNLOAD_AGREEMENT_JIEBEI',
        origin: 'APP',
        authType: 'PASSWORD',
        authScene: 'DOWNLOAD_AGREEMENT',
      });
      const authenticationToken = res?.taskToken;

      // 2.0 验证核身
      const verifyRst = await verify(authenticationToken);

      if (!verifyRst) {
        return;
      }
      log.addSuccessLog('ssd-agreement-download-auth');

      // 3.0 发起协议下载
      await agreementDownload({
        product: 'XFD',
        creditPlatform: 'MAYI_ZHIXIN', // 授信平台，必传
        code: previewRes?.code, // 协议code，必传
        agreementNo: previewRes?.agreementNo, // 协议编号，必传
        contractNo: previewRes?.contractNo, // 合同编号，必传
        emailAddress: email, // 必传
        authToken: authenticationToken, // 核身token，必传
        institution: previewRes?.institution,
      });
      log.addSuccessLog('ssd-agreement-download');
      downloadPopupRef?.current?.toggle(false);
      downloadPopupResultRef?.current?.toggle();
    } catch (err) {
      log.addErrorLog('ssd-agreement-download-error');
      // hideLoading();
      Toast.show({
        content: ErrorMessageMap.BUSY_DEFUALT,
      });
    } finally {
      setDownloadIng(false);
    }
  });

  const handleSetPageTitleImg = async () => {
    try {
      await NavBar.setRightItem({
        lightImageUrl:
          'https://gw.alicdn.com/imgextra/i2/O1CN01yALjel24BjKT1O8wX_!!6000000007353-2-tps-112-112.png',
        darkImageUrl:
          'https://gw.alicdn.com/imgextra/i3/O1CN01NdF47u1x7sgrSo5B3_!!6000000006397-2-tps-112-112.png',
        onClick: () => {
          downloadPopupRef?.current?.toggle(true);
        },
      });
      // alert('设置成功')
    } catch (error) {}
  };

  useEffect(() => {
    initAes();
    log.addVisitLog('ssd-signed-agreement-detail');
    getPreview();
    handleATarget('signed-agreement-preview');
  }, []);

  useEffect(() => {
    // 协议存在内容再出下载按钮
    if (content) {
      //  蚂蚁协议提供下载渠道
      institution === 'MAYI_ZHIXIN' && handleSetPageTitleImg();
    }
  }, [content]);

  if (empty && isReady) {
    return (
      <div className={styles.empty}>
        <img src="https://gw.alicdn.com/imgextra/i1/O1CN01EBNrhL20DmaYk0Sxe_!!6000000006816-2-tps-560-400.png" />
        <p className={styles.title}>抱歉，协议暂无法预览，请稍后再试</p>
        <Button onClick={popPage} className={styles.back} color="primary">
          我知道了
        </Button>
      </div>
    );
  }

  if (!content) {
    return (
      <div className={styles.loading}>
        <DotLoading />
      </div>
    );
  }

  return (
    <div className={styles.agreement}>
      <div
        className={styles.content}
        dangerouslySetInnerHTML={{ __html: content }}
        id="signed-agreement-preview"
      />
      <ProtocolDownloadPopup
        ref={downloadPopupRef}
        handleDownload={handleDownload}
        downloadIng={downloadIng}
      />
      <ProtocolDownloadResultPopup ref={downloadPopupResultRef} />
    </div>
  );
}
