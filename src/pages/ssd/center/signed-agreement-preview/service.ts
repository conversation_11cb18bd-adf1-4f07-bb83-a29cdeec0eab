import { MtopGet } from '@/utils/mtop';

export function authenticationCreate(data: any) {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.authentication.create',
    data,
  });
}
export function agreementDownload(data: any) {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.signed.agreement.download',
    data,
  });
}

export function agreementPreview(request: any) {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.signed.agreement.view',
    data: request,
  });
}