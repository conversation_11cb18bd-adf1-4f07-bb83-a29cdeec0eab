.ssdHome {
  // min-height: 100vh;
  min-height: calc(100vh - var(--navbar-height) - var(--safe-area-inset-top) - 4rpx);
  padding: 24rpx 16rpx;
  display: flex;
  flex-direction: column;
  text-align: center;

  .main {
    flex: 1;
  }

  .slogan {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    .logo {
      display: block;
      background-image: url('https://gw.alicdn.com/imgextra/i3/O1CN01wlOyxv1SEygc0C2cH_!!6000000002216-2-tps-144-144.png');
      width: 44rpx;
      height: 44rpx;
      background-repeat: no-repeat;
      background-size: contain;
    }
    .title {
      font-weight: 500;
      font-size: 36rpx;
      color: #000;
      text-align: center;
      padding-left: 10rpx;
      line-height: 1;
    }
  }
  .description {
    font-weight: 400;
    font-size: 24rpx;
    color: rgba(#000, 40%);
    padding-top: 20rpx;
    padding-bottom: 40rpx;
    letter-spacing: 0;
  }
}

.skeleton {
  padding-bottom: 276rpx;
}


.bottom {
  position: static;
  height: 130rpx;
}

.hide {
  opacity: 0;
  width: 0;
  height: 0;
}
