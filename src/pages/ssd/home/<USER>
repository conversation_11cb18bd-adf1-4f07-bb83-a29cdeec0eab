/**
 * @file home页面
 */

import { useEffect, useState } from 'react';
import { Dialog } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';
import { definePageConfig } from 'ice';

import Qualified from './Qualified';
import Closed from './Closed';
import { useCreditRouter } from '@/hooks';
import LinkUtil, { replacePage } from '@/utils/link';
import { PAGES } from '@/common/constant';
import BottomTextBar from '@/components/SSD/BottomTextBar';
import Notice from '@/components/SSD/Notice';
import { SsdHomeSkeleton } from '@/components/SSD/Skeleton';
// import Notice from './components/Notice';
import { queryLoanSchemaInfo, LoanSchemaInfoRes } from '@/store/center/actions';
import { visibilityChange } from '@/utils/life-cycle';
import { contractAsync } from '@/store/loan/actions';

import styles from './index.module.scss';

export default function SsdHome() {
  const { creditConsultData, routerPage, queryConsult } = useCreditRouter();
  const [payload, setPayload] = useState<LoanSchemaInfoRes>();

  const doContractAsync = async () => {
    await contractAsync();
  };

  const doInit = async () => {
    try {
      const res = await queryLoanSchemaInfo();
      if (res) {
        setPayload(res);
      } else {
        throw new Error();
      }
    } catch (e) {
      log.addErrorLog('ssd-home-loan-schema-info');
      Dialog.show({
        content: '啊呀开了个小差，请重试',
        closeOnAction: true,
        actions: [[{
          key: 'close',
          text: '我知道了',
        }]],
      });
    }
  };

  const doReInit = async () => {
    await queryConsult();
    await doInit();
  };

  const renderHome = () => {
    switch (payload?.creditContractStatus) {
      case 'QUALIFIED':
      case 'FROZEN':
        return <Qualified payload={payload} />;
      case 'CLOSED':
      case 'CLEARED':
        return <Closed payload={payload} />;
      default:
        return <SsdHomeSkeleton customClassName={styles.skeleton} />;
    }
  };

  // 初始化
  useEffect(() => {
    log.addVisitLog(PAGES.SsdHome);
    doContractAsync();
    doInit();
  }, []);

  // 监听重新渲染
  useEffect(() => {
    return visibilityChange(doReInit);
  }, []);

  useEffect(() => {
    if (payload?.creditClosing === true) {
      replacePage(PAGES.SsdClose, {
        from: 'home',
      });
    }
  }, [payload?.creditClosing]);

  if (creditConsultData && routerPage !== PAGES.SsdHome && routerPage !== PAGES.SsdHomePlus) {
    LinkUtil.locationReplace(routerPage);
    return;
  }

  return (
    <div className={styles.ssdHome}>
      <div className={styles.main}>
        <div className={styles.slogan}>
          <i className={styles.logo} />
          <p className={styles.title}>随身贷</p>
        </div>
        <div className={styles.description}>蚂蚁智信及其合作伙伴为你提供服务</div>
        <Notice />
        {renderHome()}
      </div>
      <BottomTextBar customClassName={styles.bottom} />
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '借钱',
  spm: {
    spmB: PAGES.SsdHome,
  },
}));
