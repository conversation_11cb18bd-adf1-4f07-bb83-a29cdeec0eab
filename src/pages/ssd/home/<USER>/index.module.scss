.qualified {
  .overdueTitle {
    font-weight: 500;
    font-size: 28rpx;
    text-align: center;
  }
  .overdueTitleLight {
    font-weight: 500;
    font-size: 28rpx;
    text-align: center;
    color: rgba(#000, 60%);
  }
  .overdueDesc {
    font-size: 24rpx;
  }
  .icon {
    display: block;
    margin-left: 9rpx;
    font-size: 22rpx;
    color: #888;
    width: 22rpx;
    height: 22rpx;
    background-image: url('https://gw.alicdn.com/imgextra/i2/O1CN016HnmaT1IOv93xBOJw_!!6000000000884-2-tps-200-200.png');
    background-size: cover;
    background-position: 50% 50%;
    background-repeat: no-repeat;
  }
  .interesRateDesc {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    padding-top: 28rpx;
    .limitTime {
      display: block;
      background-color: #ff6430;
      color: #fff;
      font-size: 20rpx;
      padding: 2rpx 10rpx;
      margin-right: 8rpx;
      border-radius: 4rpx;
    }
    .desc {
      font-size: 24rpx;
      color: rgba(#000, 80%);
      .light {
        font-weight: 500;
        font-size: 24rpx;
        color: #ff6200;
        padding-left: 8rpx;
      }
      .line {
        text-decoration: line-through;
        font-weight: 400;
      }
    }
  }
  .amount {
    font-family: 'AlibabaSans102Ver2';
    font-size: 108rpx;
    height: 138rpx;
    color: rgba(#000, 80%);
    text-align: center;
  }
  .text {
    font-weight: 500;
    font-size: 72rpx;
    color: rgba(#000, 80%);
    text-align: center;
  }
  .contentNormal {
    font-size: 28rpx;
    color: rgba(#000, 40%);
  }
  .contentDue {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 28rpx;
    color: #000;
    .dot {
      height: 20rpx;
      width: 20rpx;
      background-color: #ff411c;
      border-radius: 50%;
      margin-right: 8rpx;
    }
  }
  .button {
    padding: 22rpx 17rpx;
    span {
      font-size: 36rpx;
      font-weight: 600;
    }
  }
  .disabled {
    opacity: 0.4;
  }
}

.promotionContent {
  .promotionTitle1 {
    color: var(--primary);
    font-size: 28rpx;
    padding-bottom: 8rpx;
    font-weight: 500;
  }
  .promotionTitle {
    font-size: 28rpx;
    padding-bottom: 8rpx;
    font-weight: 500;
  }
  .promotionContent {
    font-size: 24rpx;
    padding-bottom: 32rpx;
    &:last-child {
      padding-bottom: 0;
    }
  }
}

:global(.adm-toast-mask .adm-toast-main) {
  max-width: unset;
}

.descInfo {
  font-size: 28rpx;
  color: #333;
}
