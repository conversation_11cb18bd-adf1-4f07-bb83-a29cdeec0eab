/**
 * @file 非清退状态
 */
import { useCallback } from 'react';
import { includes } from 'lodash-es';
import { number } from '@ali/iec-dtao-utils';
import { Button, Toast, Dialog } from 'antd-mobile';
import classNames from 'classnames';
import { log } from '@alife/dtao-iec-spm-log';

import BillBanner from '@/components/SSD/BillBanner';
import { MainPanel, MainPanelProps } from '@/components/SSD/MainPanel';
import type { LoanSchemaInfoRes } from '@/store/center/actions';
import LinkUtil from '@/utils/link';
import { PAGES } from '@/common/constant';
import { getMD, isGreaterThan, _, isEqual0, getYMD_CN } from '@/utils';
import { checkLoanLimit } from '@/store/home-plus/ssd/format';

import styles from './index.module.scss';

interface QualifiedProps {
  payload?: LoanSchemaInfoRes;
}

export default function Qualified(props: QualifiedProps) {
  const { payload } = props;
  const { amountFormat, numberFormat } = number;

  // 逾期标题
  const renderOverDueHeader = () => {
    if (payload?.overdueDays && payload?.overdueDays > 3) {
      return (
        <p className={styles.overdueTitle}>
          你已逾期{payload?.overdueDays}天，严重影响个人信用<br />
          请及时还款
        </p>
      );
    } else {
      return (
        <p className={styles.overdueTitleLight}>
          你已逾期，暂无法借款，请及时还款
        </p>
      );
    }
  };

  // 逾期描述
  const renderOverDueDesc = () => {
    if (isEqual0(payload?.surplusPenalty)) {
      return null;
    }
    return (
      <p className={styles.overdueDesc}>
        含罚息{amountFormat(payload?.surplusPenalty)}
      </p>
    );
  };

  // 点击限时优惠
  const handlePromotionClick = () => {
    Dialog.show({
      title: '利率说明',
      closeOnAction: true,
      content: (
        <div className={styles.promotionContent}>
          <p className={styles.promotionTitle1}>
            限时降价中
          </p>
          <p className={styles.promotionContent}>
            {getYMD_CN(payload?.interestRate?.tempInterestRateEndTime)}
            前借款均可享受利率优惠，活动结束后，已发生的借款可持续享受活动，不会涨价
          </p>
          <p className={styles.promotionTitle}>
            利息怎么算
          </p>
          <p className={styles.promotionContent}>
            借款1千元仅需{amountFormat(payload?.interestRate?.tempDailyInterestPerThousand)}
            ，按天算利息，提前还款无手续费
          </p>
          <p className={styles.promotionTitle}>
            日利率
          </p>
          <p className={styles.promotionContent}>
            单利指仅按贷款本金计算利息，本金所产生的利息不会再算利
          </p>
        </div>
      ),
      actions: [{
        key: 'cancel',
        text: '我知道了',
      }],
    });
  };

  // 普通点击
  const handleClick = () => {
    Dialog.show({
      closeOnAction: true,
      content: <p className={styles.descInfo}>单利指仅按贷款本金计算利息，本金所产生的利息不会再计算利息</p>,
      actions: [{
        key: 'cancel',
        text: '我知道了',
      }],
    });
  };

  // 去还款
  const toRepay = useCallback(() => {
    log.addClickLog('ssd-home-to-batch-apply');
    LinkUtil.pushPage(PAGES.SsdRepayBatchApply);
  }, [payload]);

  // 非逾期按钮是否可以点击
  const normalButtonDisable = () => {
    if (payload?.loanAdmitted === false) {
      log.addShowLog('ssd-home-loan-limited');
      return {
        disabled: true,
        content: `借款上限${payload?.loanRejectReason?.extMap?.maxLoanContractCount || '--'}笔，暂无法借钱`,
      };
    }
    if (payload?.surplusQuotaStatus === 'INSUFFICIENT') {
      log.addShowLog('ssd-home-insufficient');
      return {
        disabled: true,
        content: '最低起借金额1元',
      };
    }
    if (_.includes(['NORMAL', 'SETTLED'], payload?.surplusQuotaStatus)) {
      return {
        disabled: false,
      };
    }
    return {
      disabled: true,
    };
  };

  // 去支用
  const toLoan = () => {
    const buttonRes = normalButtonDisable();
    if (buttonRes?.disabled) {
      if (buttonRes?.content) {
        Toast.show({
          content: buttonRes?.content,
        });
      }
      return;
    }
    log.addClickLog('ssd-home-to-loan-apply');
    LinkUtil.pushPage(PAGES.SsdLoanApply);
  };

  // 非逾期头部
  const normalTitle = () => {
    if (payload?.surplusQuotaStatus === 'EXHAUSTED') {
      return '';
    } else {
      return '你可以借(元)';
    }
  };

  // 逾期金额
  const renderOverDueContent = () => {
    return (
      <p className={classNames(styles.amount, styles.overdue)}>
        {numberFormat(payload?.surplusTotalAmount)}
      </p>
    );
  };

  // 非逾期金额部分
  const normalContent = () => {
    if (payload?.surplusQuotaStatus === 'EXHAUSTED') {
      log.addShowLog('ssd-home-exhausted');
      return <p className={styles.text}>额度已用完</p>;
    }
    return (
      <p className={styles.amount}>
        {numberFormat(payload?.quota?.surplusQuota)}
      </p>
    );
  };

  const normalButtonText = () => {
    if (checkLoanLimit(payload)) {
      log.addShowLog('ssd-home-reject', {
        rejectCode: payload?.loanRejectReason?.rejectCode,
      });
      return '借钱笔数已达上限';
    }
    return '借钱';
  };

  // 渲染非预期情况下的费率
  const renderNormalDesc = () => {
    if (payload?.interestRate) {
      const {
        interestRatePercent, tempInterestRatePercent, dailyInterestPerThousand,
        tempDailyInterestPerThousand,
      } = payload.interestRate;
      if (
        interestRatePercent &&
        tempInterestRatePercent &&
        isGreaterThan(interestRatePercent, tempInterestRatePercent)
      ) {
        return (
          <div
            className={styles.interesRateDesc}
            onClick={handlePromotionClick}
          >
            <i className={styles.limitTime}>限时</i>
            <p className={styles.desc}>
              年利率(单利)&nbsp;
              <b className={styles.light}>
                {amountFormat(tempInterestRatePercent)}%
              </b>
              <b className={styles.line}>
                {amountFormat(interestRatePercent)}%
              </b>，1千元借1天仅
              <b className={styles.light}>
                {amountFormat(tempDailyInterestPerThousand)}元
              </b>
            </p>
            <i className={styles.icon} />
          </div>
        );
      } else {
        return (
          <div className={styles.interesRateDesc} onClick={handleClick}>
            <p className={styles.desc}>
              年利率(单利)&nbsp;{amountFormat(interestRatePercent)}%，1千元借1天仅{amountFormat(dailyInterestPerThousand)}元
            </p>
            <i className={styles.icon} />
          </div>
        );
      }
    }
    return null;
  };

  // 主体内容
  const getMainOptions = (): MainPanelProps => {
    if (payload) {
      const { surplusBillStatus, surplusQuotaStatus, overdueDays } = payload;
      const OVER_DUE_STATUS = ['OVERDUE_AND_DUE', 'OVERDUE_ONLY'];
      // 先判断逾期
      const isOverDue = includes(OVER_DUE_STATUS, surplusBillStatus);
      // 是否可借
      const buttonRes = normalButtonDisable();
      log.addShowLog('ssd-home-normal', {
        surplusBillStatus,
        surplusQuotaStatus,
      });
      if (isOverDue) {
        log.addShowLog('ssd-home-overdue', {
          overdueDays: payload?.overdueDays,
          daysMoreThan3: payload?.overdueDays && payload?.overdueDays > 3,
        });
        return {
          title: '逾期应还(元)',
          type: overdueDays && overdueDays > 3 ? 'warning' : 'normal',
          renderHeader: renderOverDueHeader,
          renderDesc: renderOverDueDesc,
          renderContent: renderOverDueContent,
          renderButton: () => (
            <Button
              color="primary"
              onClick={toRepay}
              className={styles.button}
              block
            >
              立即还款
            </Button>
          ),
        };
      }
      // 再处理非逾期
      log.addShowLog('ssd-home-loanbtn', {
        surplusBillStatus,
        surplusQuotaStatus,
        disabled: buttonRes?.disabled,
        loanAdmitted: payload?.loanAdmitted,
        isINSUFFICIENT: payload?.surplusQuotaStatus === 'INSUFFICIENT',
      });
      return {
        title: normalTitle(),
        renderDesc: renderNormalDesc,
        renderContent: normalContent,
        renderButton: () => (
          <Button
            color="primary"
            className={classNames(styles.button, buttonRes.disabled && styles.disabled)}
            onClick={toLoan}
            block
          >
            {normalButtonText()}
          </Button>
        ),
      };
    }
    return {
      title: '',
    };
  };

  const renderBillContent = () => {
    if (payload) {
      const { surplusBillStatus, installmentEndDate, surplusTotalAmount } = payload;
      const surplusTotalAmountValue = amountFormat(surplusTotalAmount);
      log.addShowLog('ssd-home-bill', {
        surplusBillStatus,
        surplusTotalAmount,
      });
      if (surplusBillStatus === 'NORMAL') {
        return (
          <p className={styles.contentNormal}>
            {getMD(installmentEndDate)}待还{surplusTotalAmountValue}
          </p>
        );
      }
      if (surplusBillStatus === 'DUE_TOMORROW') {
        return (
          <p className={styles.contentNormal}>
            明天应还{surplusTotalAmountValue}
          </p>
        );
      }
      if (surplusBillStatus === 'DUE') {
        return (
          <p className={styles.contentDue}>
            <i className={styles.dot} />
            今天应还{surplusTotalAmountValue}
          </p>
        );
      }
    }
    return null;
  };

  return (
    <div className={styles.qualified}>
      <MainPanel
        {...getMainOptions()}
      />
      <BillBanner
        title="查账还款"
        renderContent={renderBillContent}
      />
    </div>
  );
}
