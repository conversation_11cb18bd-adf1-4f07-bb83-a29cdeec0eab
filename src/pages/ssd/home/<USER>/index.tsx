/**
 * @file 清退状态
 */

import { includes } from 'lodash-es';
import { Button } from 'antd-mobile';
import { number } from '@ali/iec-dtao-utils';

import BillBanner from '@/components/SSD/BillBanner';
import { MainPanel, MainPanelProps } from '@/components/SSD/MainPanel';
import type { LoanSchemaInfoRes } from '@/store/center/actions';
import LinkUtil from '@/utils/link';
import { PAGES } from '@/common/constant';
import { ArrowIcon, RejectReason, RejectReasonRef, Settled } from '@/components';
import { getMD, isEqual0 } from '@/utils';
import { log } from '@alife/dtao-iec-spm-log';

import styles from './index.module.scss';
import { useRef } from 'react';

interface ClosedProps {
  payload?: LoanSchemaInfoRes;
}

export default function Closed(props: ClosedProps) {
  const { payload } = props;
  const { amountFormat } = number;
  const rejectReasonRef = useRef<RejectReasonRef>();

  const handleClick = () => {
    rejectReasonRef.current?.show();
  };

  // 清退状态头部
  const renderClosedHeader = () => {
    return (
      <p className={styles.closedTitle} onClick={handleClick}>
        暂无法为你提供服务，了解原因
        <ArrowIcon className={styles.icon} />
      </p>
    );
  };

  // 逾期描述
  const renderOverDueDesc = () => {
    if (isEqual0(payload?.surplusPenalty)) {
      return null;
    }
    return (
      <p className={styles.overdueDesc}>
        含罚息{amountFormat(payload?.surplusPenalty)}
      </p>
    );
  };

  // 去还款
  const toRepay = () => {
    log.addClickLog('ssd-home-close-to-bill');
    LinkUtil.pushPage(PAGES.SsdRepayBill);
  };

  // 去还款
  const toBill = () => {
    log.addClickLog('ssd-home-close-to-bill');
    LinkUtil.pushPage(PAGES.SsdRepayBill);
  };

  // 渲染待还金额
  const renderSurplusTotalAmount = () => {
    return (
      <p className={styles.amount}>
        {amountFormat(payload?.surplusTotalAmount)}
      </p>
    );
  };

  // 标题
  const getTitle = () => {
    if (payload?.surplusBillStatus === 'DUE') {
      return '今日应还';
    }
    if (payload?.surplusBillStatus === 'DUE_TOMORROW') {
      return '明日应还';
    }
    // TODO，看看怎么更合理
    return '';
  };

  const getMainOptions = (): MainPanelProps => {
    if (payload) {
      const { surplusBillStatus, surplusQuotaStatus } = payload;
      const OVER_DUE_STATUS = ['OVERDUE_AND_DUE', 'OVERDUE_ONLY'];
      log.addShowLog('ssd-closed-home', {
        surplusBillStatus,
        surplusQuotaStatus,
      });
      if (surplusBillStatus === 'SETTLED') {
        return {
          renderContent: () => <Settled />,
        };
      }
      log.addShowLog('ssd-closed-home-quota');
      // 逾期
      const isOverDue = includes(OVER_DUE_STATUS, surplusBillStatus);
      if (isOverDue) {
        log.addShowLog('ssd-closed-home-overdue', {
          overdueDays: payload?.overdueDays,
          daysMoreThan3: payload?.overdueDays && payload?.overdueDays > 3,
        });
        return {
          title: '逾期应还(元)',
          type: 'warning-text',
          renderDesc: renderOverDueDesc,
          renderContent: renderSurplusTotalAmount,
          renderHeader: renderClosedHeader,
          renderButton: () => (
            <Button
              color="primary"
              block
              onClick={toRepay}
            >
              立即还款
            </Button>
          ),
        };
      }
      // 未到期
      if (surplusBillStatus === 'NORMAL') {
        log.addShowLog('ssd-closed-home-bill-normal');
        return {
          title: `${getMD(payload?.installmentEndDate)}应还(元)`,
          renderContent: renderSurplusTotalAmount,
          renderHeader: renderClosedHeader,
          renderButton: () => (
            <Button
              color="primary"
              block
              onClick={toBill}
              className={styles.button}
            >
              查账还款
            </Button>
          ),
        };
      }
      // 临近到期未结清（还款日当天、还款日前天）
      log.addShowLog('ssd-closed-home-bill-due');
      return {
        title: getTitle(),
        renderContent: renderSurplusTotalAmount,
        renderHeader: renderClosedHeader,
        renderButton: () => (
          <Button
            color="primary"
            block
            onClick={toRepay}
          >
            立即还款
          </Button>
        ),
      };
    }
    return {
      title: '',
    };
  };

  return (
    <div className={styles.closed}>
      <MainPanel
        {...getMainOptions()}
      />
      <BillBanner title="查账还款" />
      <RejectReason ref={rejectReasonRef} />
    </div>
  );
}
