/**
 * @file 通用弹层
 */

import { forwardRef, useCallback, useImperativeHandle, useRef } from 'react';
import { CommonPopup } from '@/components';

import styles from './index.module.scss';
import { Button } from 'antd-mobile';

interface ModalProps {
  title: string;
  icon: string;
  desc: string;
  onClick: () => void;
  buttonText: string;
}

export interface ModalRef {
  show: () => void;
  close: () => void;
}

export const Modal = forwardRef((props: ModalProps, ref) => {
  const { title, icon, desc, onClick, buttonText } = props;
  const popupRef = useRef<any>();

  const handlePopupShow = useCallback(() => {
    popupRef.current.toggleVisible(true);
  }, []);

  const handlePopupClose = useCallback(() => {
    popupRef.current.toggleVisible(false);
  }, []);


  useImperativeHandle(ref, () => ({
    show: handlePopupShow,
    close: handlePopupClose,
  }));

  return (
    <CommonPopup ref={popupRef}>
      <div className={styles.bd}>
        <img className={styles.icon} src={icon} />
        <p className={styles.title}>
          {title}
        </p>
        <p className={styles.desc}>
          {desc}
        </p>
        <Button className={styles.button} block color="primary" onClick={onClick}>
          {buttonText}
        </Button>
      </div>
    </CommonPopup>
  );
});
