/**
 * @file 协议沙盒
 */

import { useState, useEffect, useMemo } from 'react';
import { definePageConfig } from 'ice';
import { log, aes } from '@alife/dtao-iec-spm-log';
import { DotLoading } from 'antd-mobile';
import { isEmpty, map } from 'lodash-es';

import { PAGES } from '@/common/constant';

import styles from './index.module.scss';

export const pageConfig = definePageConfig(() => ({
  spm: {
    spmB: PAGES.SssdAgreementSandbox,
  },
  title: '',
}));

export default function SandboxAliyun() {
  const [payload, setPayload] = useState<any>();

  const handleAnchor = (index: number) => {
    const element = document.getElementById(`anchor-${index}`);
    if (element) {
      element?.scrollIntoView();
    }
  };

  const handlePostMessage = (message) => {
    window?.parent?.postMessage(message, '*');
  };

  const handlePreviewMessage = (event?: any) => {
    const data = event?.data;
    if (data.module === 'xfd-preview-sandbox') {
      log.addShowLog('xfd-agreement-sandbox-preview', data);
      log.addOtherLog('ssd-agreement-sandbox-preview', {
        action: 'getMessage',
      });
      setPayload(data?.data);
      // 通知父容器成功接收到协议内容
      handlePostMessage({
        module: 'xfd-agreement-sandbox-preview-received',
      });
    } else if (data.module === 'xfd-preview-sandbox-anchor') {
      log.addOtherLog('xfd-agreement-sandbox-anchor', data);
      handleAnchor(data?.data?.index);
    }
  };

  const handleScroll = () => {
    handlePostMessage({
      module: 'xfd-preview-sandbox-scroll',
    });
  };


  // 劫持a标签跳转事件
  const handleATarget = (id: string) => {
    const compulsoryContainer = document.getElementById(id);
    if (compulsoryContainer) {
      compulsoryContainer.addEventListener('click', (e) => {
        e.preventDefault();
        const target = e?.target;
        // @ts-ignore
        if (target && target?.localName === 'a') {
        // 获取到a标签上的链接
        // @ts-ignore
          const url = target?.getAttribute('href');
          if (url) {
            handlePostMessage({
              module: 'xfd-preview-sandbox-click',
              data: {
                url,
              },
            });
          }
        }
      });
    }
  };

  useEffect(() => {
    aes.init({
      pid: 'YFcRiF',
      user_type: '0',
    });
    log.addVisitLog('xfd-agreement-sandbox');
    handleATarget('preview-sandbox-container');
    window.addEventListener('message', handlePreviewMessage);
    window.addEventListener('scroll', handleScroll);
    // 通知父页面 sandbox 准备完成
    window?.parent?.postMessage({
      module: 'xfd-preview-sandbox-ready',
    }, '*');
    return () => {
      window.removeEventListener('message', handlePreviewMessage);
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const renderPreContents = useMemo(() => {
    if (!payload?.preContent) {
      return null;
    }

    return (
      <div className={styles.preContent}>{payload?.preContent}</div>
    );
  }, [payload]);

  const renderContents = useMemo(() => {
    if (isEmpty(payload?.previewList)) {
      return (
        <div className={styles.loading}>
          <DotLoading />
        </div>
      );
    }
    if (payload?.previewList?.length) {
      return map(payload?.previewList, (content, index) => {
        return (
          <div
            id={`anchor-${index}`}
            className={styles.content}
            key={`compulsory-agreement-${index}`}
            dangerouslySetInnerHTML={{ __html: content?.content }}
          />
        );
      });
    }
    return null;
  }, [payload]);

  return (
    <div className={styles.previewSandbox} id="preview-sandbox-container">
      {renderPreContents}
      {renderContents}
    </div>
  );
}

