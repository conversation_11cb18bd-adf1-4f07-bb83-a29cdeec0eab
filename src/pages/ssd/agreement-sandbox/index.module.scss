/* 隐藏滚动条的伪元素（Chrome 和 Safari） */
html::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
}

html, body {
  padding: 0 !important;
  margin: 0 !important;
}

.previewSandbox {
  width: 100%;
  padding-bottom: 500rpx;

  .preContent {
    padding-bottom: 24rpx;
    font-weight: 500;
    font-size: 24rpx;
    color: rgba(#000, 60%);
    line-height: 36rpx;
  }

  .loading {
    margin: auto;
    text-align: center;
    padding-top: 50%;
  }

  .content {
    padding-bottom: 68rpx;

    p {
      text-align: left !important;
      font-size: 24rpx !important;
      text-indent: 0 !important;
      line-height: 36rpx !important;
      color: rgba(#000, 60%);

      &:first-child {
        padding-bottom: 12rpx !important;

        span {
          font-weight: 500 !important;
          font-size: 28rpx !important;
          color: rgba(#000, 80%) !important;
        }
      }
    }

    span {
      font-size: 24rpx !important;
      text-indent: 0 !important;
    }
  }
}
