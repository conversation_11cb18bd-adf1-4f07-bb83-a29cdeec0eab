.loanContract {
  padding: 0 !important;
  .loanContractContainer {
    padding: 32rpx 32rpx 150rpx;
  }
  .surplusPrincipal {
    text-align: center;
    .surplusPrincipalAmount {
      margin-top: 12rpx;
      font-size: 60rpx;
      font-weight: 500;
      font-family: "ALIBABA NUMBER FONT MD";
      margin-bottom: 16rpx;
    }
  }
  .loanContractCount {
    color: #7c889c;
    margin-bottom: 24rpx;
    font-size: 26rpx;
    span {
      color: #111;
    }
  }
  .errorSummaryText {
    font-size: 24rpx;
    color: #f00;
  }
  .contractItem {
    display: flex;
    .contractItemIcon {
      width: 36rpx;
      height: 36rpx;
      margin-right: 24rpx;
    }
    .contractItemDateAndAmount {
      display: flex;
      align-items: center;
      font-size: 26rpx;
      line-height: 26rpx;
      color: #50607a;
      font-family: "ALIBABA NUMBER FONT RG";
      display: flex;
      align-items: center;
      margin-bottom: 24rpx;
    }
    .contractItemTerm {
      margin-left: 8rpx;
      color: #50607a;
    }
    .contractItemSurplusPrincipalTitle {
      font-size: 26rpx;
    }
    .contractItemSurplusPrincipalAmount {
      margin-top: 8rpx;
      font-family: "ALIBABA NUMBER FONT MD";
      font-size: 36rpx;
      font-weight: 500;
    }
  }
}
