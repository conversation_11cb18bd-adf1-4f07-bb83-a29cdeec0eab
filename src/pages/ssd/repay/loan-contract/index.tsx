import { useState, useEffect, Fragment } from 'react';
import { definePageConfig } from 'ice';
import FullLoading from '@/components/FullLoading';
import { Button, Modal, NoticeBar } from 'antd-mobile';
import {
  dayjsYYMD,
  amountFormat,
  getRejectMsgFromMsgMap,
  getErrorCodeFromRejectRes,
} from '@/utils';
import styles from './index.module.scss';
import classnames from 'classnames';
import { queryLoanContractListAndSummary, queryRepayBatchPreAdmit } from '@/store/repay/actions';
import { PAGES, REPAYMENT_METHOD } from '@/common/constant';
import LinkUtil from '@/utils/link';
import { useVisibilityChangeRefresh } from '@/hooks';
import CommonFixedNavBar from '@/components/CommonFixedNavBar';
import _ from 'lodash';
import { log } from '@alife/dtao-iec-spm-log';
import { checkHmRejectAlert } from '@/components';
import { loanContractPageLog } from '@/utils/goc';

const ContractIconMap = {
  selected:
    'https://gw.alicdn.com/imgextra/i4/O1CN01aNhzvZ1jmjMPVskUC_!!6000000004591-2-tps-88-88.png',
  disabled:
    'https://gw.alicdn.com/imgextra/i1/O1CN011DyO4N1qw4jhSfsyP_!!6000000005559-2-tps-72-72.png',
  unselect:
    'https://gw.alicdn.com/imgextra/i3/O1CN01ijDoyh277YW2fxCYI_!!6000000007750-2-tps-72-72.png',
};

export default function LoanContract() {
  const [loanContractSummary, setLoanContractSummary] = useState<any>(null);
  const [loanContractListData, setLoanContractListData] = useState<any>(null);
  const [repayAdmit, setRepayAdmit] = useState<any>(false);
  const [noticeMsg, setNoticeMsg] = useState<any>(null);
  const [selectedContractObj, setSelectedContractObj] = useState({});

  const queryBatchAdmit = async () => {
    const admitRes = await queryRepayBatchPreAdmit({
      repaymentScene: 'REPAY_BY_LOAN_CONTRACT',
    });
    setRepayAdmit(admitRes);
    if (admitRes.admitted === false) {
      setNoticeMsg(getRejectMsgFromMsgMap(admitRes));
    }
  };

  const handleSubmit = () => {
    log.addClickLog('repay-early-apply');
    if (checkHmRejectAlert(PAGES.SsdLoanContract)) {
      return;
    }
    LinkUtil.pushPage(PAGES.SsdRepayEarlyApply, {
      loanOrderIdList: encodeURIComponent(JSON.stringify(Object.keys(selectedContractObj))),
    });
  };

  const handleContractItemClick = (item) => {
    setSelectedContractObj((prev) => {
      if (selectedContractObj[item.loanOrderId]) {
        delete prev[item.loanOrderId];
        return { ...prev };
      }
      return {
        ...prev,
        [item.loanOrderId]: item,
      };
    });
  };

  const pageInit = async () => {
    try {
      const [res] = await Promise.all([queryLoanContractListAndSummary(), queryBatchAdmit()]);
      loanContractPageLog({
        success: true,
        creditPlatform: 'MAYI_ZHIXIN',
      });
      setLoanContractSummary(res);
      setLoanContractListData(res?.loanContractList || []);

      // 处理一下返回到该页面的时候，部分借据已还清的情况，把选中的已还清借据给删掉
      setSelectedContractObj((prev) => {
        const selectedLoanOrderIds = Object.keys(prev);
        _.forEach(selectedLoanOrderIds, (id) => {
          if (!_.find(res?.loanContractList, { loanOrderId: id })) {
            delete prev[id];
          }
        });
        return { ...prev };
      });
    } catch (error) {
      loanContractPageLog({
        success: false,
        creditPlatform: 'MAYI_ZHIXIN',
        message: error?.message,
      });
      Modal.alert({
        title: '服务器开小差',
        content: '请稍后重试',
      });
    }
  };

  useVisibilityChangeRefresh(pageInit);

  useEffect(() => {
    pageInit();
  }, []);

  if (!loanContractSummary || !loanContractListData) {
    return <FullLoading visible />;
  }

  const showRepayBtn = Object.keys(selectedContractObj).length > 0;

  return (
    <div className={classnames([styles.loanContract])}>
      <CommonFixedNavBar bgColor="#f3f6f8" />
      {noticeMsg && <NoticeBar content={noticeMsg} color="alert" />}

      <div className={classnames([styles.loanContractContainer])}>
        <div className={classnames([styles.surplusPrincipal, 'common-card'])}>
          <div>剩余未还本金 (元)</div>
          <div className={styles.surplusPrincipalAmount}>
            {amountFormat(loanContractSummary.surplusPrincipal)}
          </div>
          {getErrorCodeFromRejectRes(repayAdmit) === 'PREPAY_REJECT_IF_OVERDUE' && (
            <div className={styles.errorSummaryText}>你已逾期，提前还款前需先还清逾期金额</div>
          )}
        </div>
        <div className={styles.loanContractCount}>
          共含
          <span>{loanContractListData?.length}笔</span>借款
        </div>
        <Fragment>
          {loanContractListData.map((item) => {
            const isOverdue = item.status === 'OVERDUE';
            const disabled = !repayAdmit.admitted;
            const selected = selectedContractObj[item.loanOrderId];
            let status = 'unselect';
            if (disabled) {
              status = 'disabled';
            } else {
              status = selected ? 'selected' : 'unselect';
            }

            return (
              <div
                key={item.loanOrderId}
                className={classnames([styles.contractItem, 'common-card'])}
                onClick={() => {
                  if (disabled) return;
                  handleContractItemClick(item);
                }}
              >
                <div>
                  <img className={styles.contractItemIcon} src={ContractIconMap[status]} />
                </div>
                <div>
                  <div>
                    <span className={styles.contractItemDateAndAmount}>
                      {dayjsYYMD(item.loanStartDate)}借{amountFormat(item.loanedAmount)}元
                      <span className="common-divide-line-vertical" />
                      {REPAYMENT_METHOD[item.repaymentMethod]}
                      {isOverdue && <div className={classnames(['common-tag red'])}>逾期</div>}
                    </span>
                  </div>
                  <div className={styles.contractItemSurplusPrincipalTitle}>
                    未还本金
                    <span className={styles.contractItemTerm}>
                      (剩余{item.surplusInstallmentCount}期)
                    </span>
                  </div>
                  <div className={styles.contractItemSurplusPrincipalAmount}>
                    ¥{amountFormat(item.surplusPrincipal)}
                  </div>
                </div>
              </div>
            );
          })}
        </Fragment>
      </div>
      {showRepayBtn && (
        <div className="page-bottom-bar">
          <Button onClick={handleSubmit} block color="primary">
            去还款
          </Button>
        </div>
      )}
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '使用中的借款',
  spm: {
    spmB: PAGES.SsdLoanContract,
  },
}));
