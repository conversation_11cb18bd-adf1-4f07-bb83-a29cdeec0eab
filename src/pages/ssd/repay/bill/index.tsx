import { definePageConfig } from 'ice';
import { useState, useEffect, useCallback } from 'react';
import { Button, Modal, NoticeBar } from 'antd-mobile';
import styles from './index.module.scss';
import classnames from 'classnames';
import { dayjsFormat, amountFormat, isGreaterThan0, isEqual0, getRejectMsgFromMsgMap, getMsgByErrorCode, isMtopErrorCreditContractNotExist } from '@/utils';
import { queryInstallmentBillConsult, queryRepayBatchPreAdmit } from '@/store/repay/actions';
import ArrowIcon from '@/components/ArrowIcon';
import FullLoading from '@/components/FullLoading';
import SsdCommonResult from '@/components/SSD/CommonResult';
import { useVisibilityChangeRefresh } from '@/hooks';
import { PAGES } from '@/common/constant';
import LinkUtil from '@/utils/link';
import { log } from '@alife/dtao-iec-spm-log';
import { checkHmRejectAlert } from '@/components';
import { repayBillPageLog } from '@/utils/goc';

export default function RepayBill() {
  const [installmentBillConsult, setInstallmentBillConsult] = useState<any>(null);
  const [repayBatchAdmit, setRepayBatchAdmit] = useState<any>({});
  const [inited, setInited] = useState(false);
  const [noticeMsg, setNoticeMsg] = useState<any>(null);

  const handleRightBtnClick = useCallback(() => {
    if (checkHmRejectAlert(PAGES.SsdRepayBill)) {
      return;
    }
    log.addClickLog('repay-batch-apply');
    LinkUtil.pushPage(PAGES.SsdRepayBatchApply, {
      defaultAmount: installmentBillConsult.surplusTotalAmount,
    });
  }, [installmentBillConsult]);

  const pageInit = async () => {
    queryInstallmentBillConsult()
      .then((consultRes) => {
        repayBillPageLog({
          success: true,
          creditPlatform: 'MAYI_ZHIXIN',
          surplusBillStatus: consultRes?.surplusBillStatus,
        });
        setInstallmentBillConsult(consultRes);
        // 如果是未支用过或者已结清，不继续查
      }).catch((e) => {
        repayBillPageLog({
          success: false,
          message: e?.message,
          creditPlatform: 'MAYI_ZHIXIN',
        });
        if (isMtopErrorCreditContractNotExist(e)) {
          LinkUtil.resetToPage(PAGES.SsdCreditLpSSR);
        } else {
          Modal.alert({
            title: '查询失败',
            content: getMsgByErrorCode(e.message),
            onConfirm: LinkUtil.popPage,
          });
        }
      })
      .finally(() => {
        setInited(true);
      });
    try {
      const admitRes = await queryRepayBatchPreAdmit({
        repaymentScene: 'REPAY_BY_INSTALLMENT_BILL',
      });
      setRepayBatchAdmit(admitRes);
      if (admitRes.admitted === false) {
        setNoticeMsg(getRejectMsgFromMsgMap(admitRes));
      }
    } catch (e) {
      log.addErrorLog('ssd-repay-bill-admit', {
        code: e.message,
      });
    }
  };

  useEffect(() => {
    pageInit();
  }, []);

  const handleLoanContractBtnClick = useCallback(() => {
    log.addClickLog('loan-contract');
    LinkUtil.pushPage(PAGES.SsdLoanContract);
  }, []);
  const handleRepayPlanBtnClick = useCallback(() => {
    log.addClickLog('repay-plan');
    LinkUtil.pushPage(PAGES.SsdRepayPlan);
  }, []);

  const handleLoanRecordBtnClick = useCallback(() => {
    log.addClickLog('ssd-loan-record');
    LinkUtil.pushPage(PAGES.RecordList, { tabKey: 'LOAN', isSSD: '1' });
  }, []);
  const handleRepayRecordBtnClick = useCallback(() => {
    log.addClickLog('ssd-repay-record');
    LinkUtil.pushPage(PAGES.RecordList, { tabKey: 'REPAY', isSSD: '1' });
  }, []);

  useVisibilityChangeRefresh(pageInit);

  if (!installmentBillConsult && !inited) {
    return <FullLoading visible />;
  }

  const {
    surplusBillStatus,
    surplusTotalAmount,
    surplusOverdueTotalAmount,
    installmentEndDate,
    loanContractCount,
    instalmentBillCount,
  } = installmentBillConsult || {};

  const isNew = surplusBillStatus === 'NEW';
  const isDue = surplusBillStatus === 'DUE';
  const isDueTomorrow = surplusBillStatus === 'DUE_TOMORROW';
  const isNormal = surplusBillStatus === 'NORMAL'; // 未到期
  const isOverDueOnly = surplusBillStatus === 'OVERDUE_ONLY';
  const isOverDueAndDue = surplusBillStatus === 'OVERDUE_AND_DUE';
  const isSettled = surplusBillStatus === 'SETTLED';

  const RepayIndexPageWraper = (props) => {
    return (
      <div className={styles.repayIndex}>
        {props.children}
        <div className="page-bottom-text">
          <span onClick={handleLoanRecordBtnClick}>借款记录</span>
          <span className="common-divide-line-vertical" />
          <span onClick={handleRepayRecordBtnClick}>还款记录</span>
        </div>
      </div>
    );
  };


  if (isNew || isSettled) {
    return (
      <RepayIndexPageWraper>
        <div className={styles.withMarginTop}>
          <SsdCommonResult
            customImg={
              <img
                className={styles.resultImg}
                src="https://gw.alicdn.com/imgextra/i2/O1CN01xVucth1nw7w2bRYX2_!!6000000005153-2-tps-192-192.png"
              />
            }
            title="当前无需还款"
            description={
              <span>
                还款日有提醒
                <span className={styles.divideLineVertical} />
                到期自动扣款
                <span className={styles.divideLineVertical} />
                支持提前还款
              </span>
            }
          />
        </div>
      </RepayIndexPageWraper>
    );
  }

  const renderCardTitle = () => {
    if (isDue || isOverDueAndDue) {
      return '今日应还 (元)';
    }
    if (isOverDueOnly) {
      return '逾期应还 (元)';
    }
    return `${dayjsFormat(installmentEndDate, 'MM月DD日')}应还 (元)`;
  };

  const renderAmount = () => {
    if (isEqual0(surplusTotalAmount)) {
      return '当期已还清';
    }
    return amountFormat(surplusTotalAmount);
  };

  const renderAmountTips = () => {
    if (isOverDueOnly) {
      return <span className={styles.repayTipError}>您已逾期，请尽快还款，以免影响信用</span>;
    }
    if (isOverDueAndDue) {
      return (
        <span className={styles.repayTipError}>
          {`含已逾期${amountFormat(surplusOverdueTotalAmount)}元，请尽快还款，以免影响信用`}
        </span>
      );
    }

    return isGreaterThan0(surplusTotalAmount) ? '还款日会自动扣款，当天也可主动还' : null;
  };


  return (
    <RepayIndexPageWraper>
      {noticeMsg && <NoticeBar content={noticeMsg} color="alert" />}
      <div className={styles.repayBillWrap}>
        <div className="common-card">
          <div className={styles.repayIndexCardTitle}>{renderCardTitle()}</div>
          <div className={styles.repayAmountBlock}>
            <div className={styles.repayIndexCardLeft}>
              <div className={styles.repayAmountValue}>{renderAmount()}</div>
              <div className={styles.repayTip}>{renderAmountTips()}</div>
            </div>
            {isGreaterThan0(surplusTotalAmount) && !isNormal && !isNew && !isDueTomorrow && (
              <div className={styles.repayIndexCardRight}>
                <Button
                  onClick={handleRightBtnClick}
                  className={styles.repayIndexCardRightBtn}
                  color="primary"
                  disabled={!repayBatchAdmit.admitted}
                >
                  去还款
                </Button>
              </div>
            )}
          </div>
          <div className="common-divide-line" />
          <div onClick={handleRepayPlanBtnClick} className={styles.repayIndexCardRepayPlan}>
            <div className={styles.repayIndexCardRepayPlanLeft}>还款计划</div>
            <div className={classnames([styles.repayIndexCardRepayPlanRight, 'flex-align-center'])}>
              {instalmentBillCount && `剩余${instalmentBillCount}期`}
              <ArrowIcon />
            </div>
          </div>
        </div>
        <div onClick={handleLoanContractBtnClick} className="common-card flex-space-between">
          <div className={styles.loanCountText}>{loanContractCount}笔借款使用中</div>
          <div className={classnames([styles.loanTip, 'flex-align-center'])}>
            可查看借款详情 {!isDue && !isOverDueAndDue && !isOverDueOnly ? '，也可提前还款' : ''}
            <ArrowIcon />
          </div>
        </div>
      </div>
    </RepayIndexPageWraper>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '查账还款',
  spm: {
    spmB: PAGES.SsdRepayBill,
  },
}));
