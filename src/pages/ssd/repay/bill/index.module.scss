.repayIndex {
  padding: 0 !important;
  .withMarginTop {
    margin-top: 400rpx;
  }
  .repayBillWrap {
    padding: 32rpx 32rpx 210rpx;
  }
  .repayIndexCardTitle {
    font-size: 24rpx;
    color: #111;
    margin-bottom: 16rpx;
  }
  .repayAmountBlock {
    display: flex;
    justify-content: space-between;
    margin-bottom: 32rpx;
    .repayIndexCardLeft {
      .repayAmountValue {
        font-size: 60rpx;
        font-weight: 500;
        line-height: 72rpx;
        margin-bottom: 8rpx;
        font-family: "ALIBABA NUMBER FONT MD";
      }
      .repayTip {
        color: #7c889c;
        font-size: 24rpx;
        &Error {
          color: #f00;
        }
      }
    }
    .repayIndexCardRight {
      text-align: right;
      .repayIndexCardRightBtn {
        width: 152rpx;
        height: 72rpx;
        padding: 0;
      }
    }
  }
  .repayIndexCardRepayPlan {
    display: flex;
    justify-content: space-between;
    margin-top: 32rpx;
    align-items: center;
    height: 80rpx;
    .repayIndexCardRepayPlanLeft {
      font-size: 26rpx;
    }
    .repayIndexCardRepayPlanRight {
      color: #7c889c;
    }
  }
  .loanCountText {
    font-size: 26rpx;
    color: #111;
  }
  .loanTip {
    font-size: 26rpx;
    color: #7c889c;
  }
  .divideLineVertical {
    display: inline-block;
    margin: 0 16rpx;
    width: 2rpx;
    height: 20rpx;
    opacity: 0.2;
    background-color: #000;
    transform: scaleX(0.5);
  }
  .resultImg {
    width: 96rpx;
    height: 96rpx;
  }
}
