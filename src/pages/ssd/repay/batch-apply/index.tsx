import { definePageConfig } from 'ice';
import { useEffect, useState, useCallback, useRef } from 'react';
import { Button, Modal, Form, Toast } from 'antd-mobile';
import styles from './index.module.scss';
import {
  idempotentId,
  _,
  getMsgByErrorCode,
  getErrorCodeFromRejectRes,
  isGreaterThan,
  isGreaterThan0,
  amountFormat,
  getTimeStamp,
  isMtopErrorCreditContractNotExist,
} from '@/utils';
import {
  repayBatchConsult,
  repayBatchApply,
  RepayConsultRes,
  repayBatchTrial,
  RepayTrialDTO,
} from '@/store/repay/actions';
import { FullLoading, DetailItemList, AmountInput } from '@/components';
import { useDebounceFn } from 'ahooks';
import { PAGES } from '@/common/constant';
import { log } from '@alife/dtao-iec-spm-log';
import { pay } from '@/utils/pay';
import classnames from 'classnames';
import LinkUtil from '@/utils/link';

export default function RepayApply() {
  const [repayConsultData, setRepayConsultData] = useState<null | RepayConsultRes>(null);
  const [initialTrialResult, setInitialTrialResult] = useState<RepayTrialDTO | any>({});
  const [dynamicTrialResult, setDynamicTrialResult] = useState<RepayTrialDTO | any>({});
  const [loading, setLoading] = useState(true);
  const [trailing, setTrailing] = useState(false);
  const [payStatus, setPayStatus] = useState('');
  const latestTrialResult = useRef<any>(null);
  const initialConsultDataRef = useRef<any>(null);
  const [form] = Form.useForm();

  const renderTrialResult = useCallback(
    (trialResult) => {
      if (!latestTrialResult.current) {
        return null;
      }

      // 如果最近一次没有触发试算 || 没有在试算中且没有试算结果，这种情况一般是试算异常
      if (!trailing && !trialResult) {
        return null;
      }

      return (
        <div className={styles.dynamicTrialResult}>
          <DetailItemList
            data={_.filter(
              [
                {
                  label: '本金',
                  value: trailing ? '计算中...' : `¥${trialResult?.trialPrincipal}`,
                },
                {
                  label: '利息',
                  value: trailing ? '计算中...' : `¥${trialResult?.trialInterest}`,
                },
                isGreaterThan0(trialResult?.trialPenalty) && {
                  label: '罚息',
                  value: `¥${trialResult?.trialPenalty}`,
                },
              ],
              (item) => !!item,
            )}
          />
        </div>
      );
    },
    [dynamicTrialResult, trailing, latestTrialResult.current],
  );

  const { run: handleSubmit } = useDebounceFn(
    async () => {
      log.addClickLog('submit_click');

      if (_.isEmpty(dynamicTrialResult)) return;

      setLoading(true);
      try {
        const applyRes: any = await repayBatchApply({
          institution,
          channel: 'APP',
          applyTime: Date.now(),
          requestId: idempotentId(),
          repayTrialResult: dynamicTrialResult,
          repaymentScene: 'REPAY_BY_INSTALLMENT_BILL',
          passThroughInfo: dynamicTrialResult.passThroughInfo,
        });

        if (applyRes.repayOrderId && applyRes.paymentParams) {
          const verifyRes = await pay({
            signStr: applyRes.paymentParams,
          });
          setPayStatus(verifyRes.payStatus);
          if (verifyRes.payStatus === 'SUCCESS') {
            // Toast.show('支付成功');
            setTimeout(() => {
              LinkUtil.replacePage(PAGES.SsdRepayResult, {
                repayOrderId: applyRes.repayOrderId,
              });
            }, 1000);
          }
          if (verifyRes.payStatus === 'FAIL') {
            Toast.show('支付失败');
          }
        } else {
          throw new Error(getErrorCodeFromRejectRes(applyRes));
        }
      } catch (e) {
        log.addErrorLog('repay-apply-error', { code: e.message });
        Modal.alert({
          title: '还款失败',
          content: getMsgByErrorCode(e.message),
        });
      } finally {
        setLoading(false);
      }
    },
    {
      wait: 800,
      leading: true,
      trailing: false,
    },
  );

  const { run: debounceRepayTrial } = useDebounceFn(
    useCallback(
      (applyAmount, trialKey) => {
        repayBatchTrial({
          institution: repayConsultData?.institution || initialConsultDataRef.current?.institution,
          applyAmount,
          repayPhase: 'UNDUE',
          settleRepayTrialResult: initialTrialResult,
          repaymentScene: 'REPAY_BY_INSTALLMENT_BILL',
        })
          .then((res) => {
            if (!res.admitted) {
              throw new Error(getErrorCodeFromRejectRes(res));
            }
            if (!latestTrialResult.current) return;
            if (trialKey === latestTrialResult.current) {
              setTrailing(false);
              setDynamicTrialResult(res);
            }
          })
          .catch((e) => {
            log.addErrorLog('repay-trial-error', { code: e.message });
            if (trialKey === latestTrialResult.current) {
              setTrailing(false);
              setDynamicTrialResult(null);
              Modal.alert({
                title: '还款计算失败',
                content: getMsgByErrorCode(e.message),
              });
            }
          });
      },
      [repayConsultData],
    ),
    {
      wait: 300,
      leading: true,
      trailing: true,
    },
  );

  const doRepayTrial = useCallback(
    (applyAmount) => {
      const key = getTimeStamp();
      latestTrialResult.current = key;
      if (!applyAmount) return;
      setDynamicTrialResult(null);
      setTrailing(true);
      debounceRepayTrial(applyAmount, key);
    },
    [repayConsultData],
  );

  const clearSimulateResult = () => {
    // 执行一下试算 拦掉之前的执行
    setTrailing(false);
    latestTrialResult.current = null;
    setDynamicTrialResult(null);
  };
  const checkAmountValidator = (rules, value) => {
    // 输入金额不能为0的判断
    if (!value) {
      clearSimulateResult();
      return Promise.reject();
    }

    // 是否大于最小还款金额
    if (
      initialTrialResult.minApplyAmount &&
      isGreaterThan(initialTrialResult.minApplyAmount, value)
    ) {
      clearSimulateResult();
      return Promise.reject(new Error(`单笔最小需还${initialTrialResult.minApplyAmount}元`));
    }

    doRepayTrial(String(Number(value)));

    return Promise.resolve();
  };

  const handleTotalRepayBtnClick = () => {
    if (_.isEmpty(initialTrialResult)) return;
    form.setFieldsValue({
      applyAmount: initialTrialResult.trialTotalAmount,
    });

    doRepayTrial(initialTrialResult.trialTotalAmount);
  };

  useEffect(() => {
    log.addVisitLog(PAGES.SsdRepayBatchApply);
    repayBatchConsult({
      repaymentScene: 'REPAY_BY_INSTALLMENT_BILL',
    })
      .then((res) => {
        if (!res.admitted) {
          throw new Error(getErrorCodeFromRejectRes(res));
        } else {
          setRepayConsultData(res);
          initialConsultDataRef.current = res;
          setInitialTrialResult(res.repayTrialResult);
          form.setFieldsValue({
            applyAmount: res.repayTrialResult?.trialTotalAmount,
          });
          doRepayTrial(res.repayTrialResult?.trialTotalAmount);
          // 需要全部还款
          if (res.needSettleRepay) {
            setDynamicTrialResult(res.repayTrialResult);
          }
        }
      })
      .catch((e) => {
        log.addErrorLog('repay-consult-error', { code: e.message });
        if (isMtopErrorCreditContractNotExist(e)) {
          LinkUtil.resetToPage(PAGES.SsdCreditLpSSR);
        } else {
          Modal.alert({
            title: '暂无法还款',
            content: getMsgByErrorCode(e.message),
            onConfirm: () => {
              LinkUtil.popPage();
            },
          });
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  if (!repayConsultData) {
    return <FullLoading visible={loading} />;
  }

  const { institution } = repayConsultData;

  return (
    <div className={styles.repayApply}>
      <div className={classnames([styles.loanContractInfo])}>
        <div className={styles.loanContractInfoSurplus}>应还金额</div>
        <div className="flex-align-center">
          <span className={styles.loanContractInfoAmount}>
            ¥{amountFormat(initialTrialResult.trialTotalAmount)}
          </span>
          <span className="alibaba-font-rg color-text-1">
            含本金{amountFormat(initialTrialResult.trialPrincipal)}元，利息
            {amountFormat(initialTrialResult.trialInterest)}元
            {isGreaterThan0(initialTrialResult.trialPenalty)
              ? `，罚息${amountFormat(initialTrialResult.trialPenalty)}元`
              : ''}
          </span>
        </div>
      </div>
      <Form form={form}>
        <Form.Item
          rules={[
            {
              validator: checkAmountValidator,
            },
          ]}
          name="applyAmount"
        >
          <AmountInput
            renderAppend={() => (
              <span onClick={handleTotalRepayBtnClick} className={styles.settleRepayBtn}>
                全额还
              </span>
            )}
            autoFocus
            disabled={_.isEmpty(initialTrialResult)}
            max={initialTrialResult.trialTotalAmount}
          />
        </Form.Item>
      </Form>
      {renderTrialResult(dynamicTrialResult)}
      <div className="page-bottom-bar">
        <Button
          disabled={_.isEmpty(initialTrialResult) || _.isEmpty(dynamicTrialResult) || trailing || loading || payStatus === 'SUCCESS'}
          onClick={handleSubmit}
          block
          color="primary"
        >
          确认还款
        </Button>
      </div>
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '还款',
  spm: {
    spmB: PAGES.SsdRepayBatchApply,
  },
  window: {
    navBarImmersive: false,
    navBarBgColor: '#f3f6f8',
    pageBgColor: '#f3f6f8',
  },
}));
