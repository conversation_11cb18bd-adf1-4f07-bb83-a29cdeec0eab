.repayResult {
  padding: 32rpx;
  &.hasPaddingTop {
    padding-top: 300rpx!important;
  }
  .actionBtn {
    margin-top: 32rpx;
    width: 240rpx;
  }
  .resultSucceeded {
    width: 100%;
    font-size: 26rpx;
    .resultSucceededItem {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 70rpx;
    }
    .resultSucceededItemVal {
      font-family: 'ALIBABA NUMBER FONT RG';
      color: #7c889c;
    }
  }
  .twiceRepayment {
    display: flex;
    justify-content: space-between;
    margin-top: 32rpx;
    .twiceRepaymentLeft {
      font-size: 26rpx;
      .twiceRepaymentLeftTitle {
        font-weight: 500;
        margin-bottom: 12rpx;
      }
    }
    .twiceRepaymentRight {
      display: flex;
      align-items: center;

      .twiceRepaymentBtn {
        padding: 16rpx 32rpx;
        background-color: #fff;
      }
    }
  }
}
