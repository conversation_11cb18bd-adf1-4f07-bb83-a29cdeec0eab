/**
 * @file 首支一体页
 */

import { useEffect, useRef, useState } from 'react';
import { Dialog } from 'antd-mobile';
import { defineDataLoader, definePageConfig } from 'ice';
import { includes } from 'lodash-es';

import LinkUtil, { replacePage, toIndex } from '@/utils/link';
import { PAGES } from '@/common/constant';
import BottomTextBar from '@/components/SSD/BottomTextBar';
import { SsdHomePlusSkeleton } from '@/components/SSD/Skeleton';
import Notice from '@/components/SSD/Notice';
import { checkMayizhixinCreditRouter } from '@/store/credit/model';
import { contractAsync } from '@/store/loan/actions';
import { ssdHomePlusCSR, SsdHomePlusPData } from '@/services/ssd-home-plus';
import { useVisibilityChangeRefresh } from '@/hooks';
import { useFSPData } from '@/hooks/useFSPData';
import { addInitLog } from '@/utils/log';
import { checkPushPage, removePushPage } from '@/utils/session';
import { PROCESS_ACTION } from '@/store/loan';
import { LOAN_VERSION } from '@/store/types';
import { getParseExtension, _, getQueryParams } from '@/utils';
import { checkHmRejectAlert } from '@/components/HmReject';
import { homeMayizhixinPageLog } from '@/utils/goc';
import { log } from '@alife/dtao-iec-spm-log';

import Qualified from './Qualified';
import Closed from './Closed';
import styles from './index.module.scss';

// export const serverDataLoader = defineServerDataLoader(async () => {
//   const data = await ssdHomePlusSSR();
//   return data;
// });

const MayiZhixinRedirectPageMap = {
  center: PAGES.SsdCenter,
  recordlist: PAGES.RecordList,
  repaybill: PAGES.SsdRepayBill,
  coupon: PAGES.SsdDiscountCoupon,
};

export const dataLoader = defineDataLoader(async () => {
  const data = await ssdHomePlusCSR('PREFETCH');
  return data;
});

export default function SsdHomePlus() {
  const { fspData, doReInit } = useFSPData<SsdHomePlusPData>({
    init: ssdHomePlusCSR,
  });
  const [reloadTag, setReloadTag] = useState(0);
  const [clearTag, setClearTag] = useState(0);
  const processActionRef = useRef<PROCESS_ACTION>();

  const {
    creditApplyConsult,
    loanSchemaInfo,
  } = fspData?.data || {};

  // 同步支用的processAction状态
  const asyncProcessAction = (actionValue: PROCESS_ACTION) => {
    processActionRef.current = actionValue;
  };

  // 重载页面方法
  const doHomePlusReload = async () => {
    if (includes(['AUTENTICATING', 'SUBMITTING'], processActionRef.current)) {
      return;
    }
    setClearTag((pre) => {
      return pre + 1;
    });
    await doReInit();
    setReloadTag((cur) => {
      return cur + 1;
    });
  };

  // push页面返回后，专用重载
  const pushHomePlusReInit = async () => {
    if (includes(['AUTENTICATING', 'SUBMITTING'], processActionRef.current)) {
      return;
    }
    if (checkPushPage()) {
      removePushPage();
      await doReInit();
    }
  };

  const doContractAsync = async () => {
    await contractAsync();
  };

  const doInit = async () => {
    if (!fspData) {
      return;
    }
    addInitLog(fspData);
    if (fspData?.success === false) {
      setTimeout(() => {
        homeMayizhixinPageLog({});
      }, 300);
      Dialog.show({
        content: '系统开小差，请稍后重试',
        closeOnAction: true,
        actions: [[{
          text: '我知道了',
          key: 'confirm',
          bold: true,
        }]],
      });
      return;
    }

    if (creditApplyConsult && !loanSchemaInfo) {
      setTimeout(() => {
        homeMayizhixinPageLog({
          message: 'ssd-home-plus-uncredit',
        });
        toIndex();
      }, 300);
      return;
    }

    if (!creditApplyConsult || !loanSchemaInfo) {
      return;
    }
    // 先处理退出中的跳转
    if (loanSchemaInfo?.creditClosing === true) {
      replacePage(PAGES.SsdClose, {
        from: 'home',
      });
      return;
    }
    log.setGlobalExtra({ institution: loanSchemaInfo?.institution });
    // 未签约/主动退出的用户 回跳签约页
    const nextRouter = checkMayizhixinCreditRouter({
      ...creditApplyConsult,
      // 授信合约接口中的合约状态滞后不准确，需使用用户信息中实时查询的合约状态
      creditContractStatus: loanSchemaInfo?.creditContractStatus,
    });
    setTimeout(() => {
      homeMayizhixinPageLog({ routerPage: nextRouter });
    }, 300);
    if (nextRouter !== PAGES.SsdHomePlus && nextRouter !== PAGES.SsdHome) {
      replacePage(nextRouter);
    }
  };

  const renderHome = () => {
    const extensionObj = getParseExtension(creditApplyConsult?.extension);
    // 使用用户信息中实时查询的合约状态
    switch (loanSchemaInfo?.creditContractStatus) {
      case 'QUALIFIED':
      case 'FROZEN':
        return (
          <Qualified
            pageVersion={extensionObj?.loanVersion as LOAN_VERSION}
            reload={doHomePlusReload}
            reloadTag={reloadTag}
            clearTag={clearTag}
            payload={loanSchemaInfo}
            asyncProcessAction={asyncProcessAction}
          />
        );
      case 'CLEARED':
        return <Closed payload={loanSchemaInfo} />;
      default:
        return <SsdHomePlusSkeleton />;
    }
  };

  useEffect(() => {
    // 处理到了home页去指定页面，就是url上的to参数
    const toParam = _.get(getQueryParams(), 'to');
    const targetPage = _.get(MayiZhixinRedirectPageMap, `${toParam}`);

    doContractAsync();

    if (targetPage) {
      LinkUtil.pushPage(targetPage);
    }
  }, []);

  // 初始化
  useEffect(() => {
    checkHmRejectAlert(PAGES.SsdHomePlus);
    doInit();
  }, [fspData]);

  useVisibilityChangeRefresh(pushHomePlusReInit);

  return (
    <div className={styles.ssdHome}>
      <div className={styles.main}>
        <div className={styles.slogan}>
          <i className={styles.logo} />
          <p className={styles.title}>随身贷</p>
        </div>
        <Notice />
        {renderHome()}
      </div>
      <BottomTextBar customClassName={styles.bottom} />
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '借钱',
  spm: {
    spmB: PAGES.SsdHomePlus,
  },
}));
