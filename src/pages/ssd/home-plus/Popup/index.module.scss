:global {
  .adm-modal-body, .adm-modal-content {
    max-height: 100vh;
  }
}

.packet {
  font-family: PingFangSC;
  text-align: center;
  transform: translateY(36rpx);

  .packetContent {
    height: 674rpx;
    padding-top: 179rpx;
    background-size: 100% 100%;
    background-repeat: no-repeat;

    .packetTitle {
      opacity: 0.8;
      font-weight: 500;
      font-size: 36rpx;
      color: #000;
    }

    .packetAmount {
      margin-top: 1rpx;
      font-family: AlibabaSans102Ver2;
      font-size: 108rpx;
      color: #ff0020;
      letter-spacing: 0;

      .packetSymbol {
        display: inline-block;
        font-size: 72rpx;
        font-weight: 700;
        transform: scaleX(0.9) translateX(-20rpx);
        width: 42rpx;
      }
    }

    .packetBtn {
      margin: 91rpx auto 0;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 96rpx;
      width: 352rpx;
      background-color: #fff;
      background-image: linear-gradient(180deg, rgba(255, 10, 40, 0.03) 0%, rgba(255, 255, 255, 0.03) 57%);
      border-radius: 48rpx;
      font-weight: 500;
      font-size: 36rpx;
      color: #ff0020;
      text-align: center;
    }

    .packetNote {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 24rpx;
      font-size: 24rpx;
      color: rgba(255, 255, 255, .8);

      .packetNoteArrow {
        width: 20rpx;
        height: 20rpx;
        background-image: url('https://gw.alicdn.com/imgextra/i4/O1CN01HIJt3R1HAR2eAz3x6_!!6000000000717-2-tps-40-40.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
      }
    }
  }

  .packetClose {
    margin: 64rpx auto 0;
    width: 60rpx;
    height: 60rpx;
    background-image: url('https://gw.alicdn.com/imgextra/i2/O1CN013UhV6x27obnjhONYf_!!6000000007844-2-tps-120-120.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
}