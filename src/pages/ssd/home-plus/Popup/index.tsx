import { ReactNode, useEffect, useRef } from 'react';
import { Modal } from 'antd-mobile';
import type { ModalShowHandler } from 'antd-mobile/es/components/modal';
import { log } from '@alife/dtao-iec-spm-log';

import { navigatorOpenURL } from '@/utils/link';
import { queryPopMessage, exposePopMessage } from '@/store/center/actions';

import styles from './index.module.scss';

const redPacketBg = 'https://gw.alicdn.com/imgextra/i4/O1CN01BcMZ4Z1fhSNQQCIMk_!!6000000004038-2-tps-1116-1348.png';

const Popup = () => {
  const modalHandleRef = useRef<ModalShowHandler>();

  const handleClose = () => {
    log.addClickLog('ssd-home-plus-pop-red-packet-close');
    modalHandleRef.current?.close();
  };

  const handleReceive = () => {
    log.addClickLog('ssd-home-plus-pop-red-packet-receive');
    modalHandleRef.current?.close();
  };

  const handleJump = (url: string) => {
    if (!url) return;
    log.addClickLog('ssd-home-plus-pop-red-packet-detail');
    navigatorOpenURL(url);
  };

  const imgLoaded = async (src: string) => {
    const img = new Image();
    img.src = src;

    return new Promise((resolve, reject) => {
      img.onload = () => {
        img.onload = null;
        img.onerror = null;
        log.addSuccessLog('ssd-home-plus-modal-red-packet-img-load');
        resolve(src);
      };

      // 监听加载错误
      img.onerror = () => {
        img.onload = null;
        img.onerror = null;
        log.addErrorLog('ssd-home-plus-modal-red-packet-img-load');
        reject();
      };
    });
  };

  // 查询红包弹窗
  const doInit = async () => {
    try {
      const res = await queryPopMessage({ popMessagePosition: 'HOME_PAGE' });

      const popMessage = res.popMessageList[0];

      let content = null as ReactNode;

      switch (popMessage.popMessageType) {
        case 'TAOBAO_RED_PACKET_SEND_SUCCESS':
          // eslint-disable-next-line no-case-declarations
          const { description, amount, url } = JSON.parse(popMessage.content);
          content = (
            <div className={styles.packet}>
              <div
                className={styles.packetContent}
                style={{
                  backgroundImage: `url(${redPacketBg})`,
                }}
              >
                <div className={styles.packetTitle}>已获得购物红包</div>
                <div className={styles.packetAmount}>
                  <span className={styles.packetSymbol}>￥</span>
                  {amount}
                </div>
                <div className={styles.packetBtn} onClick={handleReceive}>收到</div>
                <div onClick={() => handleJump(url)} className={styles.packetNote}>
                  <span className={styles.packetNoteMessage}>{description}</span>
                  <i className={styles.packetNoteArrow} />
                </div>
              </div>
              <div className={styles.packetClose} onClick={handleClose} />
            </div>
          );
          await imgLoaded(redPacketBg);
          break;
        default:
          break;
      }

      if (content) {
        log.addShowLog('ssd-home-plus-pop-message', {
          popMessageType: popMessage.popMessageType,
        });
        // 展示红包弹窗
        modalHandleRef.current = Modal.show({
          content,
          actions: [],
          bodyStyle: {
            padding: 0,
            background: 'none',
          },
          maskStyle: {
            background: 'rgba(0, 0, 0, .75)',
          },
        });

        // 弹窗展示后调用曝光接口
        exposePopMessage({ popMessageId: popMessage.popMessageId });
      }
    } catch (error) {
      log.addErrorLog('ssd-home-plus-pop-message', {
        error,
      });
    }
  };

  useEffect(() => {
    doInit();
  }, []);

  return null;
};

export default Popup;
