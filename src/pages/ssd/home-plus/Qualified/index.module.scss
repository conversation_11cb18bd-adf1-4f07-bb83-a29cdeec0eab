.qualified {
  .overdueTitle {
    font-weight: 500;
    font-size: 28rpx;
    text-align: center;
  }
  .overdueTitleLight {
    font-weight: 500;
    font-size: 28rpx;
    text-align: center;
    color: rgba(#000, 60%);
  }
  .overdueDesc {
    text-align: center;
    margin-top: 24rpx;
    font-size: 24rpx;
  }
  .amount {
    font-family: 'AlibabaSans102Ver2';
    font-size: 108rpx;
    height: 138rpx;
    color: rgba(#000, 80%);
    text-align: center;
  }
  .contentNormal {
    font-size: 28rpx;
    color: rgba(#000, 40%);
  }
  .contentDue {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 28rpx;
    color: #000;
    .dot {
      height: 20rpx;
      width: 20rpx;
      background-color: #ff411c;
      border-radius: 50%;
      margin-right: 8rpx;
    }
  }
  .button {
    padding: 22rpx 17rpx;
    span {
      font-weight: 600;
    }
  }
}

:global(.adm-toast-mask .adm-toast-main) {
  max-width: unset;
}
