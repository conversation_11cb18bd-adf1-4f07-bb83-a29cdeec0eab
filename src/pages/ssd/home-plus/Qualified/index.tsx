/**
 * @file 非清退状态
 */
import { useCallback } from 'react';
import { includes } from 'lodash-es';
import { Button } from 'antd-mobile';
import classNames from 'classnames';
import { log } from '@alife/dtao-iec-spm-log';

import { PAGES } from '@/common/constant';
import type { LoanSchemaInfoRes } from '@/store/center/actions';
import LinkUtil from '@/utils/link';
import { amountFormat, getMD, isEqual0 } from '@/utils';
import BillBanner from '@/components/SSD/BillBanner';
import { MainPanel } from '@/components/SSD/MainPanel';
import { setPushPage } from '@/utils/session';
import { LOAN_VERSION } from '@/store/types';

import Loan from '../components/Loan';
import styles from './index.module.scss';

interface QualifiedProps {
  pageVersion: LOAN_VERSION;
  payload?: LoanSchemaInfoRes;
  reload: any;
  reloadTag: number;
  clearTag: number;
  asyncProcessAction: any;
}

export default function Qualified(props: QualifiedProps) {
  const { pageVersion, payload, reload, reloadTag, clearTag, asyncProcessAction } = props;

  // 逾期标题
  const renderOverDueHeader = () => {
    if (payload?.overdueDays && payload?.overdueDays > 3) {
      return (
        <p className={styles.overdueTitle}>
          你已逾期{payload?.overdueDays}天，严重影响个人信用<br />
          请及时还款
        </p>
      );
    } else {
      return (
        <p className={styles.overdueTitleLight}>
          你已逾期，暂无法借款，请及时还款
        </p>
      );
    }
  };

  // 逾期描述
  const renderOverDueDesc = () => {
    if (isEqual0(payload?.surplusPenalty)) {
      return null;
    }
    return (
      <p className={styles.overdueDesc}>
        含罚息{amountFormat(payload?.surplusPenalty)}
      </p>
    );
  };

  // 去还款
  const toRepay = useCallback(() => {
    log.addClickLog('ssd-home-to-batch-apply');
    setPushPage();
    LinkUtil.pushPage(PAGES.SsdRepayBatchApply);
  }, [payload]);

  // 逾期金额
  const renderOverDueContent = () => {
    return (
      <p className={classNames(styles.amount, styles.overdue)}>
        {amountFormat(payload?.surplusTotalAmount)}
      </p>
    );
  };

  const renderMain = () => {
    if (!payload) {
      return null;
    }

    const { surplusBillStatus, surplusQuotaStatus, overdueDays } = payload;
    const OVER_DUE_STATUS = ['OVERDUE_AND_DUE', 'OVERDUE_ONLY'];
    // 先判断逾期
    const isOverDue = includes(OVER_DUE_STATUS, surplusBillStatus);
    log.addShowLog('ssd-home-normal', {
      surplusBillStatus,
      surplusQuotaStatus,
    });
    if (isOverDue) {
      log.addShowLog('ssd-home-overdue', {
        overdueDays: payload?.overdueDays,
        daysMoreThan3: payload?.overdueDays && payload?.overdueDays > 3,
      });
      return (
        <MainPanel
          title="逾期应还(元)"
          type={overdueDays && overdueDays > 3 ? 'warning' : 'normal'}
          renderHeader={renderOverDueHeader}
          renderDesc={renderOverDueDesc}
          renderContent={renderOverDueContent}
          renderButton={() => (
            <Button
              color="primary"
              onClick={toRepay}
              className={styles.button}
              block
            >
              立即还款
            </Button>
          )}
        />
      );
    }

    // 再处理非逾期
    log.addShowLog('ssd-home-loan', {
      surplusBillStatus,
      surplusQuotaStatus,
      // disabled: buttonRes?.disabled,
      loanAdmitted: payload?.loanAdmitted,
      isINSUFFICIENT: payload?.surplusQuotaStatus === 'INSUFFICIENT',
    });

    return (
      <Loan
        pageVersion={pageVersion}
        reload={reload}
        reloadTag={reloadTag}
        clearTag={clearTag}
        payload={payload}
        asyncProcessAction={asyncProcessAction}
      />
    );
  };

  const renderBillContent = () => {
    if (payload) {
      const { surplusBillStatus, installmentEndDate, surplusTotalAmount } = payload;
      const surplusTotalAmountValue = amountFormat(surplusTotalAmount);
      log.addShowLog('ssd-home-bill', {
        surplusBillStatus,
        surplusTotalAmount,
      });
      if (surplusBillStatus === 'NORMAL') {
        return (
          <p className={styles.contentNormal}>
            {getMD(installmentEndDate)}待还{surplusTotalAmountValue}
          </p>
        );
      }
      if (surplusBillStatus === 'DUE_TOMORROW') {
        return (
          <p className={styles.contentNormal}>
            明天应还{surplusTotalAmountValue}
          </p>
        );
      }
      if (surplusBillStatus === 'DUE') {
        return (
          <p className={styles.contentDue}>
            <i className={styles.dot} />
            今天应还{surplusTotalAmountValue}
          </p>
        );
      }
    }
    return null;
  };


  return (
    <div className={styles.qualified}>
      {renderMain()}
      <BillBanner
        title="查账还款"
        renderContent={renderBillContent}
      />
    </div>
  );
}
