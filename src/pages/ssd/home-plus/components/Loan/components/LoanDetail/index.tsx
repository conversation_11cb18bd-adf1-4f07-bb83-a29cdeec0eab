import { forwardRef, useCallback, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { includes } from 'lodash-es';
import { Button } from 'antd-mobile';
import classnames from 'classnames';
import { log } from '@alife/dtao-iec-spm-log';

import CommonPopup, { ICommonPopupRef } from '@/components/CommonPopup';
import { BankInfoTip } from '@/components';
import { CommonResult, DetailItemList } from '..';
import { QueryResponse, TrialResponse } from '@/store/loan';
import { getPromotionOfferData } from '@/store/lib/format';
import { amountFormat, getFailedCodeFromOrder, getMD, _ } from '@/utils';
import { toLoanSign, navigatorOpenURL } from '@/utils/link';

import styles from './index.module.scss';

const noop = () => {};

const MSG_MAP = {
  BANK_CARD_ABNORMAL: '您的银行卡状态异常，请使用正常服务中的银行卡收款',
  ID_CARD_INVALID: '您的身份证已过期，请联系客服处理',
  LOAN_APPLICATION_INFO_EXPIRED: '当前借款被拒绝，请重试',
  FACE_FILE_EMPTY: '当前借款被拒绝，请重试',
  FACE_PHOTO_CHECK_ERROR: '当前借款被拒绝，请重试',
  RETRY_MAX_LIMITS: '当前借款被拒绝，请重试',
  ILLEGAL_LOAN_APPLY_NO: '当前借款被拒绝，请重试',
  RISK_REFUSE: '当前借款被拒绝，请重试',
  OTHER: '当前借款被拒绝，请重试',
  OCCUPY_QUOTA_FAILED: '占用额度失败，请重试',
  L_RISK_REJECT: '请保持良好信用，过段时间再试',
  L_LOAN_COUNT_EXCEED: '借款笔数已达上限，可还几笔后再试',
  L_LOAN_FAIL: '可尝试更换收款账户后重新借一笔',
  L_DEDUCT_RISK_EVENT: '当前操作可能存在风险，请稍后再试',
  L_DEDUCT_BANLANCE_ACC_CLOSE: '支付宝余额支付功能关闭，请重新开通后再试',
};

interface LoanDetailProps {
  onReInit?: () => void;
}

interface LoanDetailPayload {
  loanOrder?: QueryResponse;
  trialStore?: TrialResponse;
}

const LoanDetail = forwardRef((props: LoanDetailProps, ref) => {
  const [loanOrder, setLoanOrder] = useState<QueryResponse>();
  const [trialStore, setTrialStore] = useState<TrialResponse>();
  const popupRef = useRef<ICommonPopupRef>(null);

  const isProcess = includes(['AUTHENTICATED', 'LOANING'], loanOrder?.status);
  const isFailed = includes(['CANCELLED', 'FAILED'], loanOrder?.status);
  const isSuccess = includes(['SUCCEEDED'], loanOrder?.status);

  const show = (payload: LoanDetailPayload) => {
    popupRef.current?.toggleVisible(true);
    setLoanOrder(payload?.loanOrder);
    setTrialStore(payload?.trialStore);
  };

  const close = () => {
    popupRef.current?.toggleVisible(false);
  };

  useImperativeHandle(ref, () => {
    return {
      show,
      close,
    };
  });

  const addReloadLog = () => {
    if (isProcess) {
      log.addClickLog('result-loading-close');
    } else if (isSuccess) {
      log.addClickLog('result-succeeded-close');
    } else if (isFailed) {
      log.addClickLog('result-failed-close');
    }
  };

  const handleClose = () => {
    popupRef.current?.toggleVisible(false);
    addReloadLog();
    props?.onReInit?.();
  };

  const renderProcess = () => {
    return (
      <CommonResult
        title="正在放款中"
        status="PROCESSING"
      />
    );
  };

  const renderSuccess = () => {
    return (
      <CommonResult
        title="借款已到账"
        status="SUCCESS"
      />
    );
  };

  const renderFail = useCallback(() => {
    const desc = MSG_MAP[getFailedCodeFromOrder(loanOrder)] || '系统异常，请重试';
    return (
      <CommonResult
        title="放款失败"
        status="FAILED"
        description={desc}
      />
    );
  }, [loanOrder]);

  const renderHeader = () => {
    switch (loanOrder?.status) {
      case 'AUTHENTICATED':
      case 'LOANING':
        log.addOtherLog('result-loading');
        return renderProcess();
      case 'CANCELLED':
      case 'FAILED':
        log.addOtherLog('result-failed');
        return renderFail();
      case 'SUCCEEDED':
        log.addOtherLog('result-succeeded');
        return renderSuccess();
      case 'INIT':
        log.addOtherLog('result-error');
        toLoanSign(loanOrder?.loanOrderId);
        return null;
      default:
        return renderProcess();
    }
  };

  const renderBankIcon = useMemo(() => {
    if (loanOrder?.receiveBankCard?.bankCode) {
      const { bankCode, bankCardNo, bankName } = loanOrder.receiveBankCard;
      return (
        <BankInfoTip
          code={bankCode}
          name={bankName}
          cardNo={bankCardNo}
          source="mycdn"
          className={styles.bankInfoTip}
        />
      );
    }
    return null;
  }, [loanOrder]);

  const amount = isSuccess ? loanOrder?.loanedAmount : loanOrder?.loanApplyAmount;

  const renderContent = () => {
    if (isFailed) {
      return (
        <DetailItemList
          className={styles.failedList}
          data={[
            {
              label: '借款金额',
              value: `${amountFormat(amount)}元`,
            },
            {
              label: '收款账户',
              value: renderBankIcon,
            },
          ]}
        />
      );
    }

    const first = trialStore?.installmentPlanList?.[0];

    return (
      <DetailItemList
        className={styles.commonList}
        data={[
          {
            label: '借款金额',
            value: `${amountFormat(amount)}元`,
          },
          !!first && {
            label: '首期还款',
            value: `${getMD(first?.endDate)}待还${amountFormat(first?.totalAmount)}元`,
          },
          {
            label: '收款账户',
            value: renderBankIcon,
          },
          {
            customRender: () => {
              const promotionOfferData = getPromotionOfferData(loanOrder?.platformPromotionOfferList);
              if (!promotionOfferData || !promotionOfferData?.offerSendStatus) return null;
              const { offerSendStatus } = promotionOfferData;
              log.addShowLog('loandetail-redpacket-bar', { status: offerSendStatus });
              const IconUrlMap = {
                SEND_SUCCEED: 'https://gw.alicdn.com/imgextra/i4/O1CN01s1y7Ej1foKIcblcno_!!*************-2-tps-64-64.png',
                SENDING: 'https://gw.alicdn.com/imgextra/i4/O1CN01s1y7Ej1foKIcblcno_!!*************-2-tps-64-64.png',
                SEND_FAILED: 'https://gw.alicdn.com/imgextra/i3/O1CN01zdI4OD1PP6dTNbslQ_!!*************-2-tps-64-64.png',
              };
              const ArrowUrlMap = {
                SEND_SUCCEED: 'https://gw.alicdn.com/imgextra/i2/O1CN01ka9Jla1XEaT3H0qWl_!!*************-2-tps-48-48.png',
                SENDING: 'https://gw.alicdn.com/imgextra/i2/O1CN01ka9Jla1XEaT3H0qWl_!!*************-2-tps-48-48.png',
                SEND_FAILED: 'https://gw.alicdn.com/imgextra/i3/O1CN013yQ06A1VZzkUSpxa4_!!6000000002668-2-tps-48-48.png',
              };

              const ExtraTextMap = {
                SEND_SUCCEED: '，可在「我的淘宝-红包」查看',
                SENDING: '，稍后可在「我的淘宝-红包」查看',
                SEND_FAILED: '，请联系客服处理',
              };

              const handleLoanDetailRedPacketClick = () => {
                log.addClickLog('loandetail-redpacket-bar', { status: offerSendStatus });
                if (offerSendStatus === 'SEND_FAILED') {
                  navigatorOpenURL('https://render.alipay.com/p/yuyan/180020010001253760/index.html?caprMode=sync&scene=app_ssd_sycjky&commonParams=%7B%22channels%22%3A%22taobao%22%7D');
                  return;
                }
                if (!promotionOfferData.url) return;

                navigatorOpenURL(promotionOfferData.url);
              };

              return (
                <div onClick={handleLoanDetailRedPacketClick} className={classnames([styles.redPacketBar, offerSendStatus === 'SEND_FAILED' ? styles.redPacketBarFailed : styles.redPacketBarSuccess])}>
                  <img className={styles.redPacketIcon} src={IconUrlMap[offerSendStatus]} />
                  {promotionOfferData?.promotionOfferDescription}{_.get(ExtraTextMap, `${offerSendStatus}`) || ''}
                  {offerSendStatus !== 'SENDING' && <img className={styles.redPacketArrow} src={ArrowUrlMap[offerSendStatus]} />}
                </div>);
            },
          },
          {
            customRender: () => {
              return (
                <Button
                  className={styles.button}
                  block
                  color="primary"
                  size="large"
                  onClick={handleClose}
                >
                  知道了
                </Button>
              );
            },
          },
        ]}
      />
    );
  };

  return (
    <CommonPopup
      ref={popupRef}
      disableClose
      hideCloseIcon={!isFailed}
      onClose={handleClose}
      onMaskClick={noop}
      bodyClassName={styles.popup}
    >
      {renderHeader()}
      {renderContent()}
    </CommonPopup>
  );
});

export interface ILoanDetailRef {
  show: (payload: LoanDetailPayload) => void;
  close: () => void;
}

export default LoanDetail;
