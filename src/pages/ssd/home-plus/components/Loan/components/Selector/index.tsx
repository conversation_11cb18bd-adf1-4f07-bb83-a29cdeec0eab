/**
 * @file 选择组件
 * <AUTHOR>
 */

import classNames from 'classnames';
import useSelector, { type InitOptions, type Option } from '@/hooks/useSelector';

import styles from './index.module.scss';

interface CustomerOption extends Option {
  desc?: string;
}

const Selector = <T extends CustomerOption>(props: InitOptions<T>) => {
  const {
    options,
    className,
    isActive,
    handleOnChange,
  } = useSelector(props);

  const renderItem = (option: T) => {
    return (
      <div
        key={option.label}
        className={classNames(styles.option, isActive(option) && styles.act)}
        onClick={handleOnChange(option)}
      >
        <div className={styles.label}>{option.label}</div>
        {!!option.desc && <div className={styles.desc}>{option.desc}</div>}
      </div>
    );
  };

  const renderSelector = () => {
    if (options?.length) {
      return (
        <div className={classNames(styles.selector, className)}>
          {options.map(renderItem)}
        </div>
      );
    }
    return null;
  };

  return renderSelector();
};

export default Selector;
