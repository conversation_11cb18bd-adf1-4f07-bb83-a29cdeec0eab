import { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { CommonPopup } from '@/components';
import { ICommonPopupRef } from '@/components/CommonPopup';
import { LoanSchemaInfoRes } from '@/store/center/actions';
import { ClientOnly } from 'ice';

import styles from './index.module.scss';

export type InterestRate = LoanSchemaInfoRes['interestRate'];

export interface InterestDescRef {
  show?: (interestRate?: InterestRate) => void;
}

const InterestDesc = forwardRef((props, ref: React.Ref<InterestDescRef>) => {
  const [interestRate, setInterestRate] = useState<InterestRate>();
  const commonPopupRef = useRef<ICommonPopupRef>();

  const handleShow = (rate: InterestRate) => {
    setInterestRate(rate);
    commonPopupRef.current?.toggleVisible(true);
  };

  useImperativeHandle(ref, () => ({
    show: handleShow,
  }));

  const renderItem = (item) => (
    <div key={item.title} className={styles.item}>
      {
        !!item.title && (
        <div className={styles.title}>
          {item.title}
        </div>
        )
      }
      {
        !!item.desc?.length && item.desc.map((descItem) => (
          <div key={descItem} className={styles.desc}>
            {descItem}
          </div>
        ))
      }
      {
        !!item.note && (
        <div className={styles.note}>
          {item.note}
        </div>
        )
      }
    </div>
  );

  const list = [
    {
      title: '等额本息计息方式',
      desc: [
        `月利息=当月剩余本金x日利率(${interestRate?.dailyInterestRatePercent}%)x当月天数`,
        '总利息=每月利息相加-优惠金额',
      ],
      note: '(按实际使用天数计算)',
    },
    {
      title: '先息后本计息方式',
      desc: [
        `总利息=本金x日利率(${interestRate?.dailyInterestRatePercent}%)x借款天数-优惠金额`,
      ],
      note: '(按实际使用天数计算)',
    },
    {
      note: `年化利率=日利率${interestRate?.dailyInterestRatePercent}%×${interestRate?.days}天=${interestRate?.interestRatePercent}%`,
    },
  ];

  const renderPopup = () => {
    return (
      <CommonPopup ref={commonPopupRef} transparentMask title="总利息计算方式" position="right" bodyClassName={styles.right}>
        <div className={styles.container}>
          {list.map(renderItem)}
        </div>
      </CommonPopup>
    );
  };

  return (
    <ClientOnly>
      {renderPopup}
    </ClientOnly>
  );
});

export default InterestDesc;
