.wrapper {
  position: relative;
}

.title {
  font-size: 28rpx;
  line-height: 39rpx;
  color: rgba(#000, 80%);
  text-align: right;
}

.fold {
  padding: 0 8rpx 64rpx 8rpx;
  height: calc(60vh - 62rpx);
  display: flex;
  flex-direction: column;

  .repaymentMethodSingle {
    margin-top: -12rpx;
    margin-bottom: 7rpx;
    text-align: center;
    font-size: 24rpx;
    color: rgba(#000, 0.4);
  }

  .repaymentMethod, .line {
    margin-bottom: 40rpx;
  }

  .repaymentMethod {
    padding-top: 13rpx;
  }

  .line {
    width: 100%;
    height: 1rpx;
    background: rgba(#000, 0.06);
  }

  .loanTerm {
    margin-bottom: 50rpx;
  }

  .loading {
    display: flex;
    text-align: center;
    justify-content: center;
    align-items: center;
    padding-top: 318rpx;

    .text {
      font-size: 32rpx;
      color: #7c889c;
    }
  }

  .confirm {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    padding: 24rpx 40rpx 65rpx 40rpx;
    line-height: 1.3;
    font-family: PingFang SC;
    background-color: #fff;
    border-top: 1rpx solid rgba(#000, 0.06);
  }
}

.dot {
  color: #1677ff;
}

.hide {
  visibility: hidden;
}

.arrow {
  margin-left: 8rpx;
}

.uninitialized {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  height: 100%;
  background-color: #fff;
}