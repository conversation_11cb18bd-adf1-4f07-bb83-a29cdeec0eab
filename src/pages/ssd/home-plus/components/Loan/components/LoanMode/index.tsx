/**
 * @file 还款方式组件
 * <AUTHOR>
 */

import { Fragment, useCallback, useRef } from 'react';
import { first, get } from 'lodash-es';
import { Button, DotLoading } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';
import classnames from 'classnames';

import type { LoanTermDTO, RepaymentMethodDTO } from '@/store/types';
import type { PROCESS_ACTION, TrialResponse } from '@/store/loan/types';
import { formatRepaymentMethod } from '@/store/lib/format';
import { _, amountFormat } from '@/utils';
import { ArrowIcon, PopupPanelField, type PopupPanelRef } from '@/components';

import InterestDesc, { type InterestDescRef, type InterestRate } from '../InterestDesc';
import Selector from '../Selector';
import RepayPlan from '../RepayPlan';
import styles from './index.module.scss';

interface ILoanMode {
  repaymentMethod?: string;
  loanTerm?: LoanTermDTO;
}

interface LoanModeProps {
  value?: ILoanMode;
  initialized: boolean;
  loanTermOptions?: LoanTermDTO[];
  repaymentMethodOptions?: RepaymentMethodDTO[];
  trialStore?: TrialResponse;
  interestRate?: InterestRate;
  processAction?: PROCESS_ACTION;
  onChange?: (value: ILoanMode) => void;
}

export default function LoanMode(props: LoanModeProps) {
  const {
    initialized,
    loanTermOptions,
    repaymentMethodOptions,
    trialStore,
    interestRate,
    processAction,
    onChange,
    ...other
  } = props;

  const popupPanelFieldRef = useRef<PopupPanelRef>(null);
  const interestDescRef = useRef<InterestDescRef>(null);

  const checkActive = useCallback((option: LoanTermDTO, loanTerm: LoanTermDTO) => {
    if (loanTerm?.loanTermUnit && loanTerm?.value) {
      if (loanTerm?.loanTermUnit === option?.loanTermUnit && loanTerm?.value === option?.value) {
        return true;
      }
    }
    return false;
  }, []);

  const formatOnChange = useCallback((loanTerm: LoanTermDTO) => {
    return loanTerm;
  }, []);

  const handleRepayInfoClick = () => {
    log.addClickLog('loan-mode-repay-info');
    interestDescRef.current?.show?.(interestRate);
  };

  const handleConfirm = () => {
    log.addClickLog('loan-mode-confirm');
    popupPanelFieldRef.current?.close?.();
  };

  const renderRepayPlan = () => {
    if (processAction === 'TRIALING') {
      return (
        <div className={styles.loading}>
          <p className={styles.text}>计算中</p>
          <DotLoading color="primary" />
        </div>
      );
    }

    return <RepayPlan trialStore={trialStore} onRepayInfoClick={handleRepayInfoClick} />;
  };

  const renderFold = useCallback((foldProps: any) => {
    const onlyOne = repaymentMethodOptions?.length === 1;
    const item = first(repaymentMethodOptions);
    return (
      <Fragment>
        <div className={styles.fold}>
          {onlyOne
            ? (
              <div className={styles.repaymentMethodSingle}>{item?.label}，{item?.desc}</div>
            ) : (
              <Selector
                options={repaymentMethodOptions}
                logKey="repayment-method"
                className={styles.repaymentMethod}
                {...foldProps}
                value={get(other, 'value.repaymentMethod')}
                onChange={(repaymentMethod: string) => onChange?.({
                  ...get(other, 'value'),
                  repaymentMethod,
                })}
              />
            )}
          <div className={classnames(styles.line, { [styles.hide]: onlyOne })} />
          <Selector
            options={loanTermOptions}
            checkActive={checkActive}
            formatOnChange={formatOnChange}
            logKey="loan-term"
            className={styles.loanTerm}
            {...foldProps}
            value={get(other, 'value.loanTerm')}
            onChange={(loanTerm: LoanTermDTO) => onChange?.({
              ...get(other, 'value'),
              loanTerm,
            })}
          />
          {renderRepayPlan()}
          <div className={styles.confirm}>
            <Button block color="primary" size="large" onClick={handleConfirm}>
              确定
            </Button>
          </div>
        </div>
        <InterestDesc ref={interestDescRef} />
      </Fragment>
    );
  }, [loanTermOptions, repaymentMethodOptions, trialStore, processAction, onChange, other]);

  const getDesc = useCallback(() => {
    if (processAction === 'TRIALING' || !trialStore?.installmentPlanList) {
      return (
        <Fragment>
          首期还
          <span className={styles.dot}>...</span>
        </Fragment>
      );
    }
    if (trialStore?.installmentPlanList) {
      const firstInstallmentPlan = _.first(trialStore.installmentPlanList);
      if (firstInstallmentPlan?.endDate) {
        return `首期还${amountFormat(firstInstallmentPlan?.totalAmount)}`;
      }
    }
    return '';
  }, [trialStore, processAction]);

  const renderPanel = useCallback((loanMode: ILoanMode) => {
    return (
      <span className={styles.title}>
        {loanMode?.loanTerm?.value}期&nbsp;|&nbsp;
        {formatRepaymentMethod({ value: loanMode?.repaymentMethod })}
        &nbsp;
        {getDesc()}
      </span>
    );
  }, [getDesc]);

  return (
    <div className={styles.wrapper}>
      <PopupPanelField<ILoanMode>
        ref={popupPanelFieldRef}
        renderPanel={renderPanel}
        renderFold={renderFold}
        popupProps={{
          title: '怎么还',
        }}
        logKey="loan-mode"
        arrowType="right-dark"
        {...other}
      />
      {!initialized && (
        <div className={styles.uninitialized}>
          <span>利息按天算，用1天算1天</span>
          <ArrowIcon type="right-dark" className={styles.arrow} />
        </div>
      )}
    </div>
  );
}
