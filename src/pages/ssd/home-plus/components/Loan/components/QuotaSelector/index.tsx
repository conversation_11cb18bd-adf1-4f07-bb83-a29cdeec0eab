/**
 * @file 额度选择组件
 * <AUTHOR>
 */
import { useEffect } from 'react';
import { log } from '@alife/dtao-iec-spm-log';
import classNames from 'classnames';

import type { RecommendAmount } from '@/store/center/actions';
import styles from './index.module.scss';

interface QuotaSelectorProps {
  options?: RecommendAmount[];
  customClassName?: any;
  logKey?: string;
  onChange?: (value?: string, index?: number, coupon?: boolean) => void;
}

export default function QuotaSelector(props: QuotaSelectorProps) {
  const {
    customClassName, options, logKey, onChange,
  } = props;

  useEffect(() => {
    log.addShowLog('quota-selector');
  }, []);

  const handleChange = (option: RecommendAmount, index: number, coupon: boolean) => {
    onChange?.(option?.amount, index, coupon);

    log.addChangeLog(`${logKey}-change`, {
      option,
    });
  };

  const renderItem = (option: RecommendAmount, index: number) => {
    return (
      <div
        key={option.amountDisplay}
        className={classNames(styles.option, {
          [styles.couponOption]: option.useCoupon,
        })}
        onClick={() => handleChange(option, index, !!option.useCoupon)}
      >
        {option.amountDisplay}
      </div>
    );
  };

  const renderSelector = () => {
    if (options?.length) {
      return (
        <div className={classNames(styles.selector, customClassName && customClassName)}>
          {options.map(renderItem)}
        </div>
      );
    }
    return null;
  };

  return renderSelector();
}
