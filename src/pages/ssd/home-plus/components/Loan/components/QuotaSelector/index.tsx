/**
 * @file 额度选择组件
 * <AUTHOR>
 */
import { useEffect } from 'react';
import { log } from '@alife/dtao-iec-spm-log';
import classNames from 'classnames';

import type { RecommendAmount } from '@/store/center/actions';
import styles from './index.module.scss';
import { _ } from '@/utils';

interface QuotaSelectorProps {
  options?: RecommendAmount[];
  customClassName?: any;
  logKey?: string;
  onChange?: Function;
}

export default function QuotaSelector(props: QuotaSelectorProps) {
  const {
    customClassName, options, logKey, onChange,
  } = props;

  const handleChange = (option: RecommendAmount, index: number, coupon: boolean) => {
    log.addClickLog('quota-selector-btn', {
      ..._.pick(option, ['amount', 'amountDisplay', 'useCoupon']),
      length: options?.length,
      index,
    });
    log.addClickLog('test-bury-1', {
      value: `${!!option.useCoupon}.${option?.amount}.${option?.amountDisplay}.${options?.length}.${index}`,
    });
    log.addOtherLog('test-bury-2', {
      a: !!option.useCoupon,
      b: option?.amount,
      c: option?.amountDisplay,
    });
    log.addClickLog('test-bury-3', {
      d: options?.length,
      e: index,
    });
    onChange?.(option?.amount, index, coupon, option);
  };

  useEffect(() => {
    if (!options?.length) {
      return;
    }

    _.forEach(options, (option) => {
      log.addShowLog(`${logKey}-amount-btn-expose`, option);
    });
  }, [options]);

  const renderItem = (option: RecommendAmount, index: number) => {
    return (
      <div
        key={option.amountDisplay}
        className={classNames(styles.option, {
          [styles.couponOption]: option.useCoupon,
        })}
        onClick={() => handleChange(option, index, !!option.useCoupon)}
      >
        {option.amountDisplay}
      </div>
    );
  };

  const renderSelector = () => {
    if (options?.length) {
      log.addShowLog('quota-selector', {
        length: options?.length,
      });
      return (
        <div className={classNames(styles.selector, customClassName && customClassName)}>
          {options.map(renderItem)}
        </div>
      );
    }
    return null;
  };

  return renderSelector();
}
