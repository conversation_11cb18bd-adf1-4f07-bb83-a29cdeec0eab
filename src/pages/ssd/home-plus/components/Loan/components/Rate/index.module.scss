$primary: #1677ff;

.rate {
  display: flex;
  flex-direction: column;

  .main {
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .limitTime {
    display: block;
    background-color: #ff6430;
    color: #fff;
    font-size: 20rpx;
    padding: 2rpx 10rpx;
    margin-right: 8rpx;
    border-radius: 4rpx;
  }

  .desc {
    font-size: 28rpx;
    color: rgba(#000, 80%);

    .light {
      font-weight: 500;
      font-size: 28rpx;
      color: #ff6200;
    }

    .line {
      padding-left: 8rpx;
      text-decoration: line-through;
      font-weight: 400;
    }
  }

  .icon {
    display: block;
    margin-left: 9rpx;
    font-size: 22rpx;
    color: #888;
    width: 22rpx;
    height: 22rpx;
    background-image: url('https://gw.alicdn.com/imgextra/i2/O1CN016HnmaT1IOv93xBOJw_!!6000000000884-2-tps-200-200.png');
    background-size: cover;
    background-position: 50% 50%;
    background-repeat: no-repeat;
  }

  .mark {
    font-weight: 500;
    color: $primary;
  }
}

// 限时利率介绍弹窗
.promotionContent {
  .promotionTitle1 {
    color: var(--primary);
    font-size: 28rpx;
    padding-bottom: 8rpx;
    font-weight: 500;
  }
  .promotionTitle {
    font-size: 28rpx;
    padding-bottom: 8rpx;
    font-weight: 500;
  }
  .promotionContent {
    font-size: 24rpx;
    padding-bottom: 32rpx;
    &:last-child {
      padding-bottom: 0;
    }
  }
}

// 普通利率弹窗介绍
.descInfo {
  font-size: 28rpx;
  color: #333;
}
