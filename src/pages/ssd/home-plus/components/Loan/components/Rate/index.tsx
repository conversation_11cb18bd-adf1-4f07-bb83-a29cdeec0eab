/**
 * @file 利率展示
 * @description 影响利率展示的因素：降价、优惠券、实时计息（根据用户支用金额计算的利息）
 * <AUTHOR>
 */
import classnames from 'classnames';
import { isEmpty } from 'lodash-es';
import { Dialog } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';

import { LoanSchemaInfoRes } from '@/store/center/actions';
import { PROCESS_ACTION, TrialResponse } from '@/store/loan';
import { amountFormat, getYMD_CN, isGreaterThan } from '@/utils';
import styles from './index.module.scss';

export interface IRateProps {
  initialized: boolean;
  processAction?: PROCESS_ACTION;
  loanApplyAmountValue?: string;
  loanSchemaInfo?: LoanSchemaInfoRes;
  trialStore?: TrialResponse;
  classNames?: string;
}

const map = {
  DAY: '天',
  MONTH: '月',
  YEAR: '年',
};

const Rate = (props: IRateProps) => {
  const {
    initialized, processAction, loanApplyAmountValue, loanSchemaInfo, trialStore, classNames,
  } = props;

  if (!loanSchemaInfo?.interestRate) return null;

  const {
    interestRatePercent, tempInterestRatePercent,
  } = loanSchemaInfo.interestRate;
  // 是否有优惠利率
  const hasPromotion = interestRatePercent &&
    tempInterestRatePercent &&
    isGreaterThan(interestRatePercent, tempInterestRatePercent);
  // 是否有免息券
  const hasFreeCoupon = trialStore?.couponList?.some((item) => item.type === 'INTEREST_FREE');

  // 点击限时优惠
  const handlePromotionClick = () => {
    Dialog.show({
      title: '利率说明',
      closeOnAction: true,
      content: (
        <div className={styles.promotionContent}>
          <p className={styles.promotionTitle1}>
            限时降价中
          </p>
          <p className={styles.promotionContent}>
            {getYMD_CN(loanSchemaInfo?.interestRate?.tempInterestRateEndTime)}
            前借款均可享受利率优惠，活动结束后，已发生的借款可持续享受活动，不会涨价
          </p>
          <p className={styles.promotionTitle}>
            利息怎么算
          </p>
          <p className={styles.promotionContent}>
            借款1千元仅需{amountFormat(loanSchemaInfo?.interestRate?.tempDailyInterestPerThousand)}
            ，按天算利息，提前还款无手续费
          </p>
          <p className={styles.promotionTitle}>
            日利率
          </p>
          <p className={styles.promotionContent}>
            单利指仅按贷款本金计算利息，本金所产生的利息不会再算利
          </p>
        </div>
      ),
      actions: [{
        key: 'cancel',
        text: '我知道了',
      }],
    });
  };

  // 普通点击
  const handleClick = () => {
    Dialog.show({
      closeOnAction: true,
      content: <p className={styles.descInfo}>单利指仅按贷款本金计算利息，本金所产生的利息不会再计算利息</p>,
      actions: [{
        key: 'cancel',
        text: '我知道了',
      }],
    });
  };

  const renderPromotionThousandRate = () => {
    const {
      tempDailyInterestPerThousand,
    } = loanSchemaInfo.interestRate;
    return (
      <div className={styles.main}>
        <i className={styles.limitTime}>限时</i>
        <p className={styles.desc}>
          年利率
          <b className={styles.light}>
            {amountFormat(tempInterestRatePercent)}%
          </b>
          <b className={styles.line}>
            {amountFormat(interestRatePercent)}%
          </b>，1千借1天
          <b className={styles.light}>
            {amountFormat(tempDailyInterestPerThousand)}
          </b>元
        </p>
        <i className={styles.icon} onClick={handlePromotionClick} />
      </div>
    );
  };

  // 限时降价利率展示
  const renderPromotionRateContent = () => {
    if (!loanApplyAmountValue) {
      return renderPromotionThousandRate();
    }

    if (!initialized || processAction === 'TRIALING' || (loanApplyAmountValue && isEmpty(trialStore))) {
      return (
        <div className={styles.main}>
          <i className={styles.limitTime}>限时</i>
          <p className={styles.desc}>
            年利率
            <b className={styles.light}>
              {amountFormat(tempInterestRatePercent)}%
            </b>
            <b className={styles.line}>
              {amountFormat(interestRatePercent)}%
            </b>，借1天
            <b className={styles.light}>
              ...
            </b>元
          </p>
        </div>
      );
    }

    if (!trialStore || isEmpty(trialStore)) {
      return renderPromotionThousandRate();
    }

    const { dailyInterest } = trialStore;
    return (
      <div className={styles.main}>
        <i className={styles.limitTime}>限时</i>
        <p className={styles.desc}>
          年利率
          <b className={styles.light}>
            {amountFormat(tempInterestRatePercent)}%
          </b>
          <b className={styles.line}>
            {amountFormat(interestRatePercent)}%
          </b>，借1天
          <b className={styles.light}>
            {amountFormat(dailyInterest)}
          </b>元
        </p>
      </div>
    );
  };

  const renderNormalThousandRate = () => {
    const {
      dailyInterestPerThousand,
    } = loanSchemaInfo.interestRate;

    return (
      <div className={styles.main}>
        <p className={styles.desc}>
          年利率{amountFormat(interestRatePercent)}%，1千借1天{amountFormat(dailyInterestPerThousand)}元
        </p>
        <i className={styles.icon} onClick={handleClick} />
      </div>
    );
  };

  // 普通利率展示
  const renderNormalRateContent = () => {
    if (!loanApplyAmountValue) {
      return renderNormalThousandRate();
    }

    if (!initialized || processAction === 'TRIALING' || (loanApplyAmountValue && isEmpty(trialStore))) {
      return (
        <div className={styles.main}>
          <p className={styles.desc}>
            年利率{amountFormat(interestRatePercent)}%，借1天
            <span className={styles.mark}>...元</span>
          </p>
        </div>
      );
    }

    if (!trialStore || isEmpty(trialStore)) {
      return renderNormalThousandRate();
    }

    const { dailyInterest } = trialStore;
    return (
      <div className={styles.main}>
        <p className={styles.desc}>
          年利率{amountFormat(interestRatePercent)}%，借1天
          <span className={styles.mark}>{amountFormat(dailyInterest)}元</span>
        </p>
      </div>
    );
  };

  const renderFreeCouponThousandRate = () => {
    if (hasPromotion) {
      return renderPromotionThousandRate();
    }
    return renderNormalThousandRate();
  };

  const renderFreeCouponRateContent = () => {
    if (!loanApplyAmountValue) {
      return renderFreeCouponThousandRate();
    }

    log.addShowLog('free-coupon-rate');
    const { freeQuota, couponUsableRule } = trialStore?.couponList?.find((item) => item.type === 'INTEREST_FREE') || {};
    const { limitTerms, limitTermUnit } = couponUsableRule || {};

    const exceedCoupon = isGreaterThan(loanApplyAmountValue, freeQuota);

    // 免息 + 限时降价
    if (hasPromotion) {
      return (
        <div className={styles.main}>
          <i className={styles.limitTime}>限时</i>
          <p className={styles.desc}>
            年利率
            <b className={styles.light}>
              {amountFormat(tempInterestRatePercent)}%
            </b>
            <b className={styles.line}>
              {amountFormat(interestRatePercent)}%
            </b>，前{limitTerms}{map[limitTermUnit!]}
            <b className={styles.light}>
              {exceedCoupon && `${freeQuota}元`}
              0利息
            </b>
          </p>
        </div>
      );
    }

    // 仅免息
    return (
      <div className={styles.main}>
        <p className={styles.desc}>
          年利率{amountFormat(interestRatePercent)}%，前{limitTerms}{map[limitTermUnit!]}
          <b className={styles.light}>
            {exceedCoupon && `${freeQuota}元`}
            0利息
          </b>
        </p>
      </div>
    );
  };

  // 利率文案分发
  const renderRateContent = () => {
    if (hasFreeCoupon && processAction !== 'TRIALING') {
      return renderFreeCouponRateContent();
    } else if (hasPromotion) {
      return renderPromotionRateContent();
    }

    return renderNormalRateContent();
  };

  const rateCls = classnames(
    styles.rate,
    classNames,
  );

  return (
    <div className={rateCls}>
      {renderRateContent()}
    </div>
  );
};

export default Rate;
