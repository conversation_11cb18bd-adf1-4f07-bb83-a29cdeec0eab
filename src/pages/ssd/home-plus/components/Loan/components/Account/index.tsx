/**
 * @file 收款账户组件
 * <AUTHOR>
 */
import type { ReceiveBankCard } from '@/store/loan';
import { ArrowIcon, IconInstitution } from '@/components';
import { CardList } from '@alipay/mayijie';
import { refreshAlipayCookie } from '@/utils/bridges';
import { navigatorOpenURL } from '@/utils/link';
import { isEmpty } from 'lodash-es';
import { log } from '@alife/dtao-iec-spm-log';
import { ClientOnly } from 'ice';

import styles from './index.module.scss';

interface LoanAccountProps {
  value?: ReceiveBankCard;
  onChange?: any;
  initialized: boolean;
  onReady: () => void;
}

export default function Account(props: LoanAccountProps) {
  const { value, onChange, initialized, onReady } = props;

  const formatValue = () => {
    if (!isEmpty(value)) {
      const { bindCardNo, bankCardNo, bankCode, bankName } = value;
      return {
        bankCode,
        bankContractId: bindCardNo,
        instName: bankName,
        showCardNo: bankCardNo,
      };
    }
    return null;
  };

  const onSelect = (data: any) => {
    onChange && onChange({
      bindCardNo: data?.bankContractId,
      bankCardNo: data?.showCardNo,
      bankCode: data?.instId,
      bankName: data?.instName,
    });
  };

  const renderCardList = () => {
    return (
      <CardList
        platform="TAOTIAN"
        // @ts-ignore
        value={formatValue()}
        url="https://pcloanpromobff.alipay.com/h5/api/suishenLoan/acc/queryAccList.json"
        env="m"
        wrapClassName={initialized ? '' : styles.hide}
        log={log.addClickLog}
        onReady={onReady}
        onSelect={onSelect}
        openURL={navigatorOpenURL}
        refreshAlipayCookie={refreshAlipayCookie}
      />
    );
  };

  return (
    <div className={styles.cardListBox}>
      {
        !!value?.bankCode && (
          <IconInstitution source="mycdn" className={styles.logo} type={value?.bankCode} />
        )
      }

      <ClientOnly>
        {renderCardList}
      </ClientOnly>

      <ArrowIcon type="right-dark" className={styles.arrow} />

      <div
        className={styles.uninitialized}
        style={{
          display: initialized ? 'none' : 'flex',
        }}
      >
        <span>支付宝实时保护中</span>
        <ArrowIcon type="right-dark" className={styles.arrow} />
      </div>
    </div>
  );
}
