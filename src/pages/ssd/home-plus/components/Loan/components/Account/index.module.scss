
.account {
  justify-content: flex-end;
  --bank-info-tip-font-color: #111;
}

.cardListBox {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;

  .logo {
    margin-right: 8rpx;
    width: 32rpx;
    height: 32rpx;
  }

  .arrow {
    margin-left: 8rpx;
  }
}

:global {
  .mayijie-bankInfoTip .name,
  .mayijie-bankInfoTip .no {
    font-weight: 400;
    font-size: 28rpx;
    color: rgba(#000, 0.8);
  }
}

.hide {
  visibility: hidden;
  pointer-events: none;
}

.uninitialized {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #fff;
}
