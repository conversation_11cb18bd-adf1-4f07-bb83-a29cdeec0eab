.repayInfoWrapper {
  text-align: center;
  margin-bottom: 40rpx;

  .repayInfo {
    display: inline-block;
    padding: 0 24rpx;
    line-height: 64rpx;
    text-align: center;
    background-color: rgba(245, 245, 245, 0.5);
    border-radius: 32rpx;
    color: #333;
    font-size: 26rpx;

    &::after {
      position: relative;
      content: "";
      display: inline-block;
      width: 13rpx;
      height: 26rpx;
      background: url("https://mdn.alipayobjects.com/huamei_epybi5/afts/img/A*ZNkvRblU8j0AAAAAAAAAAAAADsGRAQ/original")
        no-repeat;
      background-size: 100% 100%;
      margin-left: 16rpx;
      top: 4rpx;
    }

    .promotion {
      font-weight: 500;
      font-size: 26rpx;
      color: #ff6430;
    }
  }
}

.replayPlan {
  flex: 1;
  margin-bottom: 15rpx;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
  background: #fff;

  &::-webkit-scrollbar {
    display: none;
  }

  .line {
    display: flex;

    & > div {
      padding-bottom: 48rpx;
    }

    &:last-child {
      .ico::before {
        width: 0;
      }
    }
  }

  .date {
    text-align: right;
    width: 220rpx;
    flex: none;
    color: #333;
    font-size: 28rpx;
    line-height: 42rpx;
    font-weight: 500;
    padding-right: 10rpx;

    p {
      margin: 0;
      font-size: 20rpx;
      color: #999;
      line-height: 28rpx;
      font-weight: normal;
    }
  }

  .ico {
    position: relative;
    width: 80rpx;
    flex: none;

    &::after {
      content: "";
      position: absolute;
      width: 10rpx;
      height: 10rpx;
      border: 5rpx solid #1677ff;
      outline: 5rpx solid #fff;
      border-radius: 999rem;
      background-color: #fff;
      top: 22rpx;
      left: 50%;
      margin: -10rpx 0 0 -10rpx;
    }

    &::before {
      content: "";
      position: absolute;
      width: 2rpx;
      top: 22rpx;
      left: 50%;
      bottom: 0;
      margin-left: -1rpx;
      background-color: #d8d8d8;
      opacity: 0.2;
    }
  }

  .money {
    margin-left: 4rpx;
    width: 100%;
    color: #333;
    font-size: 28rpx;
    line-height: 42rpx;
    font-weight: 500;

    .detail {
      font-size: 20rpx;
      color: #999;
      line-height: 28rpx;
      font-weight: 400;

      .originInterest {
        text-decoration: line-through;
        font-weight: 400;
      }
    }
  }
}
