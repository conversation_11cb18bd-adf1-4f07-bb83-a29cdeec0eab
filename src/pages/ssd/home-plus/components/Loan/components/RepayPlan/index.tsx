import { Fragment, useRef } from 'react';
import { number } from '@ali/iec-dtao-utils';
import { TrialResponse } from '@/store/loan';
import { amountFormat, getMD, getYMD_CN, isGreaterThan } from '@/utils';
import { InstallmentPlanDTO } from '@/store/types';

import styles from './index.module.scss';

interface RepayPlanProps {
  trialStore?: TrialResponse;
  onRepayInfoClick?: () => void;
}

const RepayPlan = (props: RepayPlanProps) => {
  const { trialStore, onRepayInfoClick } = props;
  const now = useRef(new Date());

  const renderPromotion = () => {
    if (!trialStore?.promotionAmount) {
      return null;
    }

    const promotionAmountValue = number.getNumber(trialStore?.promotionAmount);
    if (promotionAmountValue !== '--' && promotionAmountValue > 0) {
      return (
        <span className={styles.promotion}>省{promotionAmountValue}元</span>
      );
    }

    return null;
  };

  const renderRepayInfoTip = () => {
    return (
      <span>
        借满{trialStore?.installmentPlanList?.length}个月，总利息
        <span>
          {amountFormat(trialStore?.interest)}元
        </span>
        &nbsp;
        {renderPromotion()}
      </span>
    );
  };

  const renderOriginInterest = (item: InstallmentPlanDTO) => {
    if (!item.originInterest) {
      return null;
    }

    const value = number.getNumber(item?.originInterest);
    if (value === '--' || value <= 0 || !isGreaterThan(item?.originInterest, item.interest)) {
      return null;
    }

    return (
      <Fragment>
        &nbsp;
        <b className={styles.originInterest}>{amountFormat(item.originInterest)}</b>
      </Fragment>
    );
  };

  return (
    <Fragment>
      <div className={styles.repayInfoWrapper}>
        <div className={styles.repayInfo} onClick={onRepayInfoClick}>
          {renderRepayInfoTip()}
        </div>
      </div>
      <div className={styles.replayPlan}>
        {trialStore?.installmentPlanList?.map((item, index) => {
          const date = new Date(item.endDate!);
          const isOverTime = date.getFullYear() !== now.current.getFullYear() && date.getMonth() === 0;
          return (
            <div key={item.endDate} className={styles.line}>
              <div className={styles.date}>
                {index === 0 ? '首' : index + 1}期
                <p>{isOverTime ? getYMD_CN(item.endDate) : getMD(item.endDate)}</p>
              </div>
              <div className={styles.ico} />
              <div className={styles.money}>
                {amountFormat(item.totalAmount)}
                <p className={styles.detail}>
                  含本金 {amountFormat(item.principal)} + 利息 {amountFormat(item.interest)}
                  {renderOriginInterest(item)}
                </p>
              </div>
            </div>
          );
        })}
      </div>
    </Fragment>
  );
};

export default RepayPlan;
