.detailAndAgreements {
  padding-top: 6rpx;
  text-align: center;
  font-size: 24rpx;

  .note {
    color: rgba(#000, 40%);
  }

  .mark {
    color: #4b6b99;
  }
}

.hidden {
  display: none;
}

.popupContent {
  height: calc(67vh - 8rpx);
  .contentBox {
    padding: 0 8rpx;
    padding-top: 16rpx;
    .item {
      display: flex;
      justify-content: space-between;
      padding-bottom: 25rpx;
      .label {
        font-weight: 400;
        font-size: 28rpx;
        color: rgba(#000, 40%);
      }
      .value {
        font-size: 28rpx;
        color: rgba(#000, 80%);
        text-align: right;
        span {
          font-size: 28rpx;
          color: rgba(#000, 80%);
        }
      }
      .link {
        font-size: 28rpx;
        color: #1677ff;
        text-align: right;
      }
    }
  }
}
