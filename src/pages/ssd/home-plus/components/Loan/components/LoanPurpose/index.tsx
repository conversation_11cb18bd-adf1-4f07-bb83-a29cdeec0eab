/**
 * @file 借款用途
 * <AUTHOR>
 */

import { useCallback, useRef, forwardRef, useImperativeHandle } from 'react';
import { log } from '@alife/dtao-iec-spm-log';
import { ClientOnly } from 'ice';

import { CommonPopup, CheckList } from '@/components';

import styles from './index.module.scss';

interface LoanPurposeProps {
  options?: any[];
}

export interface LoanPurposeRef {
  show: () => void;
  close: () => void;
}

export const LoanPurpose = forwardRef((props: LoanPurposeProps, ref) => {
  const { options, ...other } = props;
  const popupRef = useRef<any>();

  const handlePopupShow = useCallback(() => {
    popupRef?.current?.toggleVisible(true);
    log.addClickLog('loan-purpose-open');
  }, []);

  const handlePopupClose = useCallback(() => {
    popupRef?.current?.toggleVisible(false);
    log.addClickLog('loan-purpose-close');
  }, []);

  useImperativeHandle(ref, () => ({
    show: handlePopupShow,
    close: handlePopupClose,
  }));

  const renderPopup = () => {
    return (
      <CommonPopup
        ref={popupRef}
        onClose={handlePopupClose}
        onMaskClick={handlePopupClose}
        bodyClassName={styles.right}
        position="right"
        title="借款用途"
        transparentMask
      >
        <div className={styles.container}>
          <p className={styles.tips}>
            请选择实际资金用途，禁止用于购房、投资及各种非消费场景
          </p>
          <CheckList
            handleChangeClick={handlePopupClose}
            logKey="loan-purpose"
            itemClassName={styles.listItem}
            options={options}
            {...other}
          />
        </div>
      </CommonPopup>
    );
  };

  return (
    <ClientOnly>
      {renderPopup}
    </ClientOnly>
  );
});
