$primary: #1677ff;

.amountInput {

  .inputMain {
    display: flex;
    justify-content: center;
    align-items: baseline;
    margin-bottom: 22rpx;

    .inputInner {
      position: relative;
      display: flex;
      align-items: baseline;
      color: rgba(0, 0, 0, 0.8);

      .inputPrefix {
        position: absolute;
        left: -68rpx;
        bottom: 14rpx;
        font-size: 28rpx;
        color: rgba(0, 0, 0, 0.8);
        transform: translateY(-10rpx);
      }

      .moneyIcon {
        margin-right: 10rpx;
        font-size: 72rpx;
        font-family: AlibabaSans102Ver2;
        font-weight: 700;
        line-height: 107rpx;
        transform: translateY(-4rpx);
      }

      .virtualInputWrap {
        margin-right: 10rpx;

        .virtualInput {
          --font-size: 108rpx;
          --placeholder-color: #ccc;
          line-height: 1.2;

          :global {
            .adm-virtual-input-placeholder {
              position: static;
              font-size: 60rpx;
              padding-top: 30rpx;
              overflow: visible;
            }

            .adm-virtual-input-content {
              color: rgba(#000, 80%);
              font-weight: 500;
              font-family: 'AlibabaSans102Ver2';
            }

            .adm-virtual-input-caret-container {
              height: 84rpx;
              vertical-align: baseline;
            }

            .adm-virtual-input-caret {
              border-radius: 2rpx;
            }

            .adm-virtual-input-clear {
              margin-left: 40rpx;
              margin-top: 8rpx;
              width: 36rpx;
              height: 36rpx;

              .antd-mobile-icon {
                font-size: 36rpx;
              }
            }
          }
        }
      }
    }
  }

  .recommends {
    margin-bottom: 44rpx;
  }

  .recommendsDisable {
    opacity: 0.5;
  }
}

:global(.adm-number-keyboard-popup .adm-popup-body-position-bottom) {
  padding-bottom: var(--safe-area-inset-bottom);
}

:global(.adm-number-keyboard-main) {
  * {
    &:not(:global(.adm-number-keyboard-key-number), :global(.adm-number-keyboard-key-sign)) {
      display: none;
    }
  }
}

.close {
  position: relative;
  color: #ccc;
  font-size: 34rpx;
  top: -14rpx;
  left: 18rpx;
}