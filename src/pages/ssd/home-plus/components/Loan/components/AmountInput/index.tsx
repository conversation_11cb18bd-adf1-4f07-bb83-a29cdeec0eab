/**
 * @file 金额输入组件
 * <AUTHOR>
 */

import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef } from 'react';
import { Toast } from 'antd-mobile';
import classNames from 'classnames';
import { log } from '@alife/dtao-iec-spm-log';
import { includes, toString } from 'lodash-es';
import { ClientOnly } from 'ice';

import { noop, isGreaterThan } from '@/utils';
import { SurplusQuotaStatus } from '@/store/center/actions';
import type { LOAN_VERSION } from '@/store/types';
import { HOME_LOAN_CONSISTENT_V3 } from '@/common/constant';

import QuotaSelector from '../QuotaSelector';
import VirtualInput, { type VirtualInputRef } from '../VirtualInput';
import styles from './index.module.scss';

interface AmountInputProps {
  value?: string;
  recommends?: any;
  inputMode?: 'numeric' | 'decimal';
  max?: string | number;
  surplusQuotaStatus?: SurplusQuotaStatus;
  autoFocus?: boolean;
  disabled?: boolean;
  onChange?: (value: string) => void;
  pageVersion: LOAN_VERSION;
}

export interface AmountInputRef {
  focus?: () => void;
  doRecommendChange: (val: string) => void;
}

export const AmountInput = forwardRef((
  props: AmountInputProps,
  ref: React.RefObject<AmountInputRef>,
) => {
  const {
    value = '', recommends, inputMode = 'numeric', disabled,
    onChange = noop, surplusQuotaStatus, max, autoFocus, pageVersion,
  } = props;
  const inputRef = useRef<VirtualInputRef>(null);

  const handleFocus = () => {
    inputRef?.current?.focus();
  };

  const doRecommendChange = (val: string) => {
    if (disabled) {
      return;
    }
    onChange?.(val);
    log.addChangeLog('amount-input', {
      value: val,
    });
  };

  useImperativeHandle(ref, () => ({
    focus: handleFocus,
    doRecommendChange,
  }));

  const handleRecommendChange = (val: string, index: number, coupon: boolean, option: any) => {
    if (disabled) {
      return;
    }
    onChange?.(val);
    log.addClickLog('amount-input-recommend', {
      value: val,
      length: recommends.length,
      index,
      coupon,
      ...option,
    });
    log.addChangeLog('amount-input', {
      value: val,
    });
  };

  useEffect(() => {
    if (autoFocus && inputRef.current) {
      handleFocus();
    }
  }, []);

  const handleOnChange = useCallback((inputValue: string) => {
    if (inputValue) {
      // 仅数字的数字键盘模式
      if (inputMode === 'numeric') {
        // 非正整数
        if (!/^[1-9]\d*$/.test(inputValue)) {
          return;
        }
        // 首字母非0
        if (!value && (inputValue === '0')) {
          return;
        }
      }
      // 数字+小数点的数字键盘模式
      if (inputMode === 'decimal') {
        // 非正两位小数
        if (!/^\d+(\.\d{0,2})?$/.test(inputValue)) {
          return;
        }
        // 0开头的数字
        if (/^0\d\d*$/.test(inputValue)) {
          return;
        }
      }
      // 最大金额
      if (max && isGreaterThan(inputValue, max)) {
        Toast.show({
          content: `输入金额不能大于${max}元`,
        });
        onChange(toString(max));
        return;
      }
      onChange(inputValue);
    } else {
      onChange(inputValue);
    }
    log.addChangeLog('amount-input', {
      value: inputValue,
    });
  }, [onChange, inputMode, max, value]);

  const handleOnClick = useCallback(() => {
    log.addClickLog('amount-input');

    if (surplusQuotaStatus === 'INSUFFICIENT') {
      Toast.show({
        content: '最低起借金额1元',
      });
    }
  }, [surplusQuotaStatus]);

  const renderRecommends = () => {
    if (!recommends?.length || !!value) {
      return null;
    }

    return (
      <QuotaSelector
        customClassName={classNames(styles.recommends, {
          [styles.recommendsDisable]: disabled,
        })}
        options={recommends}
        onChange={handleRecommendChange}
        logKey="amount-input"
      />
    );
  };

  const defaultFill = pageVersion === HOME_LOAN_CONSISTENT_V3;
  const placeholder = surplusQuotaStatus === 'EXHAUSTED' ? '额度已用完' : '你想借多少';
  const inputDisabled = includes(['INSUFFICIENT', 'EXHAUSTED'], surplusQuotaStatus);

  const renderVirtualInput = () => {
    return (
      <VirtualInput
        ref={inputRef}
        value={value}
        className={styles.virtualInput}
        placeholder={placeholder}
        onChange={handleOnChange}
        onClick={handleOnClick}
        disabled={inputDisabled || disabled}
        clearable
        defaultCursorVisible={!defaultFill}
      />
    );
  };

  return (
    <div className={styles.amountInput}>
      <div className={styles.inputMain}>
        <div className={styles.inputInner} onClick={handleFocus}>
          {defaultFill && !!value && (
            <span className={styles.inputPrefix}>想借</span>
          )}
          <div className={styles.moneyIcon}>¥</div>
          <div className={styles.virtualInputWrap}>
            <ClientOnly>
              {renderVirtualInput}
            </ClientOnly>
          </div>
        </div>
      </div>
      {renderRecommends()}
    </div>
  );
});
