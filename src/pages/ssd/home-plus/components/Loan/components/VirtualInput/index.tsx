/**
 * @file 改版金额输入框组件
 * <AUTHOR>
 */

import {
  useCallback,
  createRef,
  useLayoutEffect,
  useState,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { NumberKeyboard } from 'antd-mobile';
import classNames from 'classnames';
import { log } from '@alife/dtao-iec-spm-log';

import { noop } from '@/utils';
import useDocumentEvent from '@/components/NewAmountInput/useDocumentEvent';
import EventInside from '@/components/NewAmountInput/EventInside';

import styles from './index.module.scss';

export enum SPECIAL_KEY {
  confirm = 'confirm',
  delete = 'delete',
}

interface VirtualInputProps {
  value?: string;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
  clearable?: boolean;
  defaultCursorVisible?: boolean;
  onClick?: () => void;
  onChange?: (value: string) => void;
}

export interface VirtualInputRef {
  blur: () => void;
  focus: () => void;
}

const VirtualInput = (props: VirtualInputProps, ref: React.Ref<VirtualInputRef>) => {
  const {
    value = '',
    className,
    placeholder = '',
    disabled,
    clearable,
    defaultCursorVisible,
    onClick,
    onChange = noop,
  } = props;
  const focusRef = createRef<HTMLDivElement>();
  const inputRef = createRef<HTMLDivElement>();
  const [focus, setFocus] = useState(false);
  const [cursorVisible, setCursorVisible] = useState(defaultCursorVisible || false);

  const doBlur = () => {
    if (focus) {
      focusRef.current?.blur();
      setFocus(false);
    }
  };

  useDocumentEvent(doBlur, focus);

  const doFocus = () => {
    setCursorVisible(false);
    if (disabled) {
      return;
    }
    if (!focus) {
      setFocus(true);
      focusRef.current?.focus();
    }
  };

  useImperativeHandle(ref, () => {
    return {
      blur: doBlur,
      focus: doFocus,
    };
  });

  useLayoutEffect(() => {
    log.addShowLog('customer-virtualInput');
    inputRef.current?.addEventListener('touchstart', () => {
      // 在这里处理touchstart事件的逻辑
      doFocus();
    });
  }, []);

  // 清除按钮点击
  const handleClear = () => {
    onChange('');
  };

  const queryFakeInputEl: any = useCallback(() => {
    return document.querySelector('#fake-input-el')!;
  }, []);

  // 数字键盘输入
  const onKeypadPress = (v: string) => {
    let valueAfterChange;

    log.addClickLog('amount-input-keypadPress', { v });

    if (v === SPECIAL_KEY.delete) {
      valueAfterChange = value.substring(0, value.length - 1);
      onChange(valueAfterChange);
    } else if (v === SPECIAL_KEY.confirm) {
      valueAfterChange = value;
      onChange(valueAfterChange);
      doBlur();
    } else {
      onChange(value + v);
    }
  };

  return (
    <EventInside>
      <div ref={inputRef} className={classNames(styles.amountInput, className)} onClick={onClick}>
        <div
          id="fake-input-el"
          role="textbox"
          ref={focusRef}
          tabIndex={-1}
          aria-label={value || placeholder}
          className={classNames(styles.fakeInput, {
            [styles.focus]: focus,
            'fake-input-disabled': disabled,
            'fake-input-with-clear': false,
          })}
        >
          {value}
          <div className={styles.caretContainer}>
            {((cursorVisible && !disabled && !value) || focus) && <div className={styles.caret} />}
          </div>
          {focus && (
            <NumberKeyboard
              getContainer={queryFakeInputEl}
              onInput={onKeypadPress}
              onDelete={() => onKeypadPress(SPECIAL_KEY.delete)}
              onConfirm={() => onKeypadPress(SPECIAL_KEY.confirm)}
              visible={focus}
              confirmText="确定"
              showCloseButton={false}
            />
          )}
        </div>
        {value === '' && (
          <div className={styles.fakeInputPlaceholder}>{placeholder}</div>
        )}
        {clearable && value.length && focus ? (
          <img
            onClick={handleClear}
            className={styles.clearIcon}
            src="https://gw.alicdn.com/imgextra/i2/O1CN01qSLaSf1IxjEa75M6b_!!6000000000960-2-tps-55-55.png"
          />
        ) : null}
        {value.length && !focus ? (
          <span className={styles.editBtn} onClick={doFocus}>修改</span>
        ) : null}
      </div>
    </EventInside>
  );
};

export default forwardRef(VirtualInput);
