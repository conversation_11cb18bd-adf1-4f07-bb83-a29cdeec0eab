.amountInput {
  position: relative;
  display: flex;
  align-items: center;

  :global(.adm-number-keyboard-popup .adm-popup-body-position-bottom) {
    padding-bottom: var(--safe-area-inset-bottom);
  }

  :global(.adm-number-keyboard-main) {
    * {
      &:not(:global(.adm-number-keyboard-key-number), :global(.adm-number-keyboard-key-sign)) {
        display: none;
      }
    }
  }
}

.fakeInputPlaceholder {
  padding-top: 30rpx;
  width: 100%;
  height: 100%;
  font-size: 60rpx;
  color: rgba(#000, 0.25);
}

.fakeInput {
  height: 100%;
  font-size: 108rpx;
  color: rgba(#000, 80%);
  font-family: "AlibabaSans102Ver2";

  &.focus {
    outline: none;
  }
}

.caretContainer {
  display: inline-block;
  width: 4rpx;
  height: 84rpx;
  vertical-align: baseline;
  margin-right: 1rpx;
}

.caret {
  width: 100%;
  height: 100%;
  background-color: #1677ff;
  position: relative;
  top: 5%;
  animation: keyboard-cursor 1s step-start infinite;
}

.clearIcon {
  position: absolute;
  right: -73rpx;
  margin-top: 22rpx;
  width: 36rpx;
  height: 36rpx;
}

.editBtn {
  position: absolute;
  right: -64rpx;
  font-size: 28rpx;
  color: #1677ff;
  transform: translateY(22rpx);
}

@keyframes keyboard-cursor {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}
