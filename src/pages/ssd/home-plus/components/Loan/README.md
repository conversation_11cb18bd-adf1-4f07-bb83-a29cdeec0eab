# QuotaSelector 推荐金额功能实现

## 功能说明

实现了在点击推荐金额按钮后，将完整的 `RecommendAmount` 对象传递到 form 的 onChange 触发的 doUpdate 方法中。

## 实现流程

### 1. 数据流向
```
QuotaSelector 点击 → AmountInput onChange → Loan 组件 onChange → Form onChange → doUpdate
```

### 2. 关键修改

#### QuotaSelector 组件
- 修改 `onChange` 回调函数签名，传递完整的 `RecommendAmount` 对象
- 在 `handleChange` 方法中将 `option` (RecommendAmount) 作为第四个参数传递

#### AmountInput 组件
- 修改 `AmountInputProps` 接口，`onChange` 支持接收 `RecommendAmount` 参数
- 修改 `handleRecommendChange` 方法，接收并传递 `RecommendAmount` 对象
- 更新类型定义，`recommends` 使用 `RecommendAmount[]` 类型

#### Loan 组件
- 添加 `currentRecommendAmount` 状态来临时存储推荐金额对象
- 修改 `handleValuesChangeFn` 方法，在调用 `doUpdate` 时传递 `recommendAmount`
- 为 `AmountInput` 添加自定义 `onChange` 处理器

### 3. 使用示例

```tsx
// 在 doUpdate 方法中可以接收到 RecommendAmount 对象
const doUpdate = async (params) => {
  const { name, data, updateField, recommendAmount } = params;
  
  if (name === 'loanApplyAmount' && recommendAmount) {
    console.log('用户点击了推荐金额:', recommendAmount);
    // recommendAmount 包含以下信息：
    // {
    //   amount: "5000",           // 推荐额度
    //   amountDisplay: "5000元",  // 推荐额度文案展示
    //   useCoupon: true          // 营销标记
    // }
  }
  
  // 继续原有的试算逻辑...
};
```

### 4. RecommendAmount 对象结构

```typescript
interface RecommendAmount {
  /** 推荐额度 */
  amount: string;
  /** 推荐额度文案展示 */
  amountDisplay: string;
  /** 营销标记 */
  useCoupon?: boolean;
}
```

### 5. 埋点信息

点击推荐金额按钮时会触发以下埋点：
- `quota-selector-btn`: QuotaSelector 组件的点击埋点
- `amount-input-recommend`: AmountInput 组件的推荐金额点击埋点
- `loan-loanApplyAmount`: Form 字段变更埋点

### 6. 注意事项

1. **状态管理**: `currentRecommendAmount` 在使用后会被清空，避免影响后续的手动输入
2. **类型安全**: 所有相关接口都已更新类型定义，确保类型安全
3. **向后兼容**: 修改保持了向后兼容性，不影响现有的手动输入功能
4. **错误处理**: 保持了原有的错误处理逻辑

## 测试验证

可以通过以下方式验证功能：

1. 在 `doUpdate` 方法中添加 console.log 查看 `recommendAmount` 参数
2. 检查埋点数据是否包含推荐金额的相关信息
3. 验证点击推荐金额和手动输入的行为差异
