import { ClientOnly } from 'ice';
import { Fragment, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button, Dialog, Form, Toast } from 'antd-mobile';
import { useDebounceFn } from 'ahooks';
import classNames from 'classnames';
import { first, includes, isEmpty, keys } from 'lodash-es';
import { log } from '@alife/dtao-iec-spm-log';
import { number } from '@ali/iec-dtao-utils';

import { ssdModels } from '@/store/home-plus';
import { LoanSchemaInfoRes } from '@/store/center/actions';
import { ApplyFormData, ApplyResponse, FieldObj, HOME_PLUS_APPLY_END_STATUS, LoanApplyRejectReason, QueryResponse, ssdFormat, useQueryPoll } from '@/store/loan';
import { VerifyErrorCode, VerifyStatus } from '@/components/SSD/VerifyIdentity/libs/types';
import { checkLoanLimit, formatApplyFormData, HomeLoanApplyFormData } from '@/store/home-plus/ssd/format';
import { FIELD_NAME } from '@/store/home-plus/ssd/type';
import { getSecureToken } from '@/utils/umid-token';
import { isGreaterThan, numberFormat, isGreaterThan0, isEqual0, _ } from '@/utils';
import { addRejectLog, getRejectReason } from '@/utils/reject';
import { resetToSsdHomePlus } from '@/utils/link';
import { ErrorMessageMap, HOME_LOAN_CONSISTENT_V2, HOME_LOAN_CONSISTENT_V3, HOME_LOAN_POLL_TIME, PAGES } from '@/common/constant';
import { UnsignedAgreementGroup, UnsignedAgreementsSSD, UnsignedAgreementsSSDRef, useVerifyIdentity, checkHmRejectAlert } from '@/components';
import useCountDown from '@/hooks/useCountDown';
import type { LOAN_VERSION } from '@/store/types';
import RedPacketPopover from '@/components/SSD/RedPacketPopover';
import { genApplyExposePlatformPromotionOfferStr } from '@/store/lib/format';
import { loanMayizhixinPageLog } from '@/utils/goc';

import { Account, AmountInput, AmountInputRef, DetailAndAgreements, Institution, LoanPurpose, LoanPurposeRef, LoanMode, LoanDetail, ApplyReject, Rate } from './components';
import type { ApplyRejectRef, ILoanDetailRef } from './components';
import { institution, loanPurpose, receiveBankCard, loanMode } from './fields';
import styles from './index.module.scss';

const loanApplyAmountKey = 'loanApplyAmount';
const submitBtnText = '同意协议并确认';

interface LoanProps {
  pageVersion: LOAN_VERSION;
  payload?: LoanSchemaInfoRes;
  reload?: any;
  reloadTag?: number;
  clearTag: number;
  asyncProcessAction: any;
}

export default function Loan(props: LoanProps) {
  const {
    pageVersion, payload, reload: doHomePlusReload, reloadTag, clearTag, asyncProcessAction,
  } = props;
  const {
    schemeStore, trialStore, processAction, loanAgreements, doInit,
    doApplyAdmit, doApply, doUpdate, doClear, checkSubmitDisabled, changeProcessAction,
  } = ssdModels.useSsdLoan(payload);
  const { getEnvData, startVerify } = useVerifyIdentity();
  const { payload: loanOrder, doPoll } = useQueryPoll({
    time: 1000,
    times: HOME_LOAN_POLL_TIME,
  });
  const [form] = Form.useForm<ApplyFormData>();

  const loanApplyAmountValue = Form.useWatch(loanApplyAmountKey, form);
  const amountInputRef = useRef<AmountInputRef>(null);
  const loanAgreementRef = useRef<UnsignedAgreementsSSDRef>();
  const loanPurposeRef = useRef<LoanPurposeRef>();
  const loanDetailRef = useRef<ILoanDetailRef>();
  const applyRejectRef = useRef<ApplyRejectRef>(null);
  const actionRef = useRef(false);

  // 冻结/额度已用完/额度不足1元/已达支用最大笔数时，无需贷款方案咨询
  const unNeedLoanSchema = payload?.creditContractStatus === 'FROZEN' ||
    includes(['INSUFFICIENT', 'EXHAUSTED'], payload?.surplusQuotaStatus) ||
    checkLoanLimit(payload);

  // 正在请求贷款方案/无需贷款方案咨询时，输入框禁用（额度推荐同）
  const inputDisabled = unNeedLoanSchema || isEmpty(schemeStore);

  // 是否并行试算（主要校验试算相关要素是否缺失）
  const parallelTrial = useMemo(() => {
    const {
      loanAmount, loanTerm, loanTermUnit, repaymentMethod, type,
    } = payload?.preFillAmount || {};
    return !!(loanAmount &&
      isGreaterThan0(loanAmount) &&
      loanTerm &&
      loanTermUnit &&
      repaymentMethod &&
      type);
  }, [payload]);

  const [formReady, setFormReady] = useState(false);
  const [accountReady, setAccountReady] = useState(false);
  const [trialReady, setTrialReady] = useState(false);
  const initialized = useMemo(() => {
    if (pageVersion === HOME_LOAN_CONSISTENT_V3) {
      if (parallelTrial) {
        return formReady && accountReady;
      }
      return formReady && accountReady && trialReady;
    }
    return formReady;
  }, [pageVersion, formReady, parallelTrial, accountReady, trialReady]);

  const handleCountDownEnd = () => {
    log.addOtherLog('loan-query-poll-countdown-end');
    // 先展示半屏，再切换页面状态，避免试算数据丢失
    loanDetailRef.current?.show({ loanOrder, trialStore });
    changeProcessAction('NULL');
  };

  const { count, start, end } = useCountDown({
    duration: 1000,
    onEnd: handleCountDownEnd,
  });

  const handlePollEnd = (order: QueryResponse) => {
    log.addOtherLog('loan-query-poll-end');
    // 结束倒计时
    end();
    // 先展示半屏，再切换页面状态，避免试算数据丢失
    loanDetailRef.current?.show({ loanOrder: order, trialStore });
    changeProcessAction('NULL');
  };

  const handleClearLoan = () => {
    // 1. 关闭当前弹出的半屏
    applyRejectRef.current?.close();
    loanDetailRef.current?.close();
    // 2. 清空表单
    form.resetFields();
    // 3. 清空现有的值
    doClear();
  };

  const doPageAction = () => {
    try {
      if (!schemeStore?.admitted || isEmpty(schemeStore)) {
        return;
      }
      if (isEqual0(payload?.maxLoanAmount)) {
        return;
      }

      const defaultFillAmount = payload?.preFillAmount?.loanAmount;
      if (pageVersion === HOME_LOAN_CONSISTENT_V2) {
        amountInputRef.current?.focus?.();
      } else if (
        // NOTE: 兼容预填模式下服务端未返回试算所需预填数据的情况，必要数据缺失时仍旧串行试算
        pageVersion === HOME_LOAN_CONSISTENT_V3 &&
        !parallelTrial &&
        defaultFillAmount &&
        isGreaterThan0(defaultFillAmount)
      ) {
        amountInputRef.current?.doRecommendChange?.(defaultFillAmount);
      }
      actionRef.current = true;
    } catch (e) {}
  };

  const handleInitTrial = async () => {
    try {
      const {
        loanAmount, loanTerm, loanTermUnit, repaymentMethod, type,
      } = payload?.preFillAmount || {};
      const applyFormData = {
        repaymentMethod,
        loanApplyAmount: loanAmount,
        loanVersion: pageVersion,
        institution: payload?.institution,
        loanTerm: {
          value: loanTerm,
          loanTermUnit,
        },
        preFillAmountType: type,
      };
      return await doUpdate({
        name: loanApplyAmountKey,
        data: applyFormData,
        updateField: handleUpdateField,
      }, true);
    } catch (e) {
      log.addErrorLog('init-trial');
      initErrorDialog(e);
      return null;
    }
  };

  const handleInitFetch = async () => {
    const secureTokenRes = await getSecureToken();
    if (pageVersion === HOME_LOAN_CONSISTENT_V3 && parallelTrial) {
      log.addOtherLog('init-parallel-trial');
      const result = await Promise.all([doInit({
        ...secureTokenRes,
        loanVersion: pageVersion,
        preFillAmount: payload?.preFillAmount,
      }), handleInitTrial()]);
      return result[0];
    } else {
      return await doInit({
        ...secureTokenRes,
        loanVersion: pageVersion,
        preFillAmount: payload?.preFillAmount,
      });
    }
  };

  const handleInit = useCallback(async () => {
    const res: any = await handleInitFetch();
    if (isEmpty(res?.initLoanScheme)) {
      return;
    }
    form.setFieldsValue(res.initLoanScheme);
    setFormReady(true);
    log.addSuccessLog('init-consult', {
      pageVersion,
    });
    loanMayizhixinPageLog({
      scheme: res,
    });
  }, [form, pageVersion, doInit]);

  useEffect(() => {
    if (!actionRef?.current) {
      doPageAction();
    }
  }, [schemeStore]);

  useEffect(() => {
    if (unNeedLoanSchema) {
      return;
    }
    handleInit();
  }, [reloadTag]);

  // 仅用于清空
  useEffect(() => {
    if (!clearTag) {
      return;
    }
    // 先清空
    handleClearLoan();
  }, [clearTag]);

  useEffect(() => {
    asyncProcessAction(processAction);
  }, [processAction]);

  useEffect(() => {
    if (schemeStore && !schemeStore?.admitted && schemeStore.rejectReason?.rejectCode === 'REFRESH') {
      Dialog.show({
        content: '啊呀开了个小差，请刷新',
        closeOnAction: true,
        actions: [[
          {
            text: '刷新页面',
            key: 'confirm',
            bold: true,
            onClick: resetToSsdHomePlus,
          }],
        ],
      });
    }
  }, [schemeStore]);

  useEffect(() => {
    if (isEqual0(payload?.maxLoanAmount)) {
      return;
    }
    const defaultFillAmount = payload?.preFillAmount?.loanAmount;
    if (
      pageVersion === HOME_LOAN_CONSISTENT_V3 &&
      defaultFillAmount &&
      isGreaterThan0(defaultFillAmount)
    ) {
      form.setFieldValue(loanApplyAmountKey, defaultFillAmount);
    }
  }, []);

  const onAccountReady = () => {
    setAccountReady(true);
  };

  const handleAgreementEC = () => {
    changeProcessAction('NULL');
  };

  const handleAgreementCompleted = useCallback(() => {
    form.submit();
  }, [form]);

  const getLoanAgreementExtensionParams = useCallback((currrentGroup: UnsignedAgreementGroup) => {
    return ssdFormat.getAgreementPreviewExtension(currrentGroup, {
      trialStore,
      formData: form.getFieldsValue(),
    });
  }, [trialStore, form]);

  const handleLoanAgreementShow = useCallback(() => {
    log.addClickLog('loan-agreement-show');
    loanAgreementRef.current?.show({
      action: 'preview',
      popupProps: {
        transparentMask: true,
        position: 'right',
        bodyClassName: styles.right,
      },
    });
  }, []);

  const handleLoanPurposeShow = useCallback(() => {
    log.addClickLog('loan-purpose-show');
    loanPurposeRef.current?.show();
  }, []);

  const checkLoading = () => {
    return processAction === 'AUTENTICATING' || processAction === 'SUBMITTING';
  };

  const initErrorDialog = useCallback((e?: any) => {
    addRejectLog(e);
    Dialog.show({
      content: getRejectReason(e),
      closeOnAction: true,
      actions: [[
        {
          text: '重新计算',
          key: 'confirm',
          bold: true,
          onClick: resetToSsdHomePlus,
        },
      ]],
    });
  }, []);

  const handleUpdateField = useCallback((name: FIELD_NAME, value: any) => {
    form.setFieldValue(name, value);
  }, [form]);

  const handleValuesChangeFn = useCallback(async (
    fieldObj: FieldObj,
    scheme: HomeLoanApplyFormData,
  ) => {
    try {
      const changeNames = keys(fieldObj);
      if (changeNames?.length === 1) {
        const changeName = first(changeNames) as FIELD_NAME;
        log.addChangeLog(`loan-${changeName}`);

        const applyFormData = formatApplyFormData(scheme);
        if (changeName === 'loanApplyAmount') {
          _.set(applyFormData, 'recommendAmount', amountInputRef.current?.getCurrentRecommendAmount?.());
        }
        await doUpdate({
          name: changeName,
          data: applyFormData,
          updateField: handleUpdateField,
        });
        if (!trialReady) {
          setTrialReady(true);
        }
      } else {
        log.addErrorLog('update-twice');
      }
    } catch (e) {
      log.addErrorLog('update-trial');
      initErrorDialog(e);
    }
  }, [doUpdate, handleUpdateField, initErrorDialog, trialReady]);

  const { run: handleValuesChange } = useDebounceFn(handleValuesChangeFn, {
    wait: 500,
    leading: true,
    trailing: true,
  });

  const handleApplyFailed = (
    rejectReason?: LoanApplyRejectReason,
    extension?: ApplyResponse['extension'],
  ) => {
    applyRejectRef.current?.show?.(rejectReason, extension);
  };

  const resetAgreementCheck = useCallback(() => {
    form.setFieldValue('agreementCheck', false);
    log.addOtherLog('rest-agreement-check');
  }, [form]);

  const handleFinishFailed = useCallback((errorInfo: any) => {
    log.addErrorLog('loan-apply-invalid', errorInfo);
  }, []);

  // 轮询放款结果
  const handleDoPoll = async (loanOrderId: string) => {
    log.addOtherLog('loan-query-poll');
    // 切换至放款中状态
    changeProcessAction('CONSULT_LOAN_RESULTING');
    // 页面开始倒计时
    start(HOME_LOAN_POLL_TIME);
    await doPoll({
      end: HOME_PLUS_APPLY_END_STATUS,
      request: {
        loanOrderId,
      },
      onEnd: handlePollEnd,
    });
  };

  // 支用完核身
  const handleVerifyIdentity = async (applyPayLoad: ApplyResponse) => {
    const { loanOrderId, authenticationToken } = applyPayLoad;
    log.addSuccessLog('loan-apply-verify-identity');
    changeProcessAction('AUTENTICATING');
    const verifyRes = await startVerify({
      authenticationToken,
    });
    if (verifyRes?.status === VerifyStatus.CANCEL) {
      log.addOtherLog('loan-apply-verify-identity-cancel');
      changeProcessAction('NULL');
      return;
    }
    if (verifyRes?.errorCode === VerifyErrorCode.AUTH_HIGH_RISK) {
      log.addOtherLog('loan-apply-verify-identity-high-risk');
      changeProcessAction('NULL');
      return;
    }
    if (verifyRes?.status !== VerifyStatus.PASS) {
      log.addErrorLog('loan-apply-verify-identity-error');
      changeProcessAction('NULL');
      return;
    }
    if (verifyRes?.status === VerifyStatus.PASS) {
      log.addSuccessLog('loan-apply-verify-identity-pass');
      await handleDoPoll(loanOrderId);
    }
  };

  // 发起支用
  const doLoan = async (data: HomeLoanApplyFormData) => {
    const maxLoanAmount = payload?.maxLoanAmount;
    if (maxLoanAmount && isGreaterThan(loanApplyAmountValue, maxLoanAmount)) {
      log.addErrorLog('loan-apply-greater');
      Toast.show({
        content: `输入金额不能大于${maxLoanAmount}元`,
      });
      return;
    }

    log.addSubmitLog('loan-apply');
    changeProcessAction('SUBMITTING');
    const applyFormData = formatApplyFormData(data);
    const envData = await getEnvData();
    const secureTokenRes = await getSecureToken();
    const params = {
      applyFormData,
      envData,
      umidToken: secureTokenRes?.umidToken,
      apdidToken: secureTokenRes?.apdidToken,
      exposePlatformPromotionOfferList: genApplyExposePlatformPromotionOfferStr(payload?.platformPromotionOfferList),
    };
    const admitRes = await doApplyAdmit(params);
    if (!admitRes?.admitted) {
      log.addErrorLog('loan-admit-reject', admitRes);
      handleApplyFailed(admitRes?.rejectReason, admitRes?.extension);
      return;
    }
    log.addSuccessLog('loan-apply-admit');
    // 不在组件内判断强制阅读了，比较麻烦
    if (!applyFormData?.agreementCheck && loanAgreements?.forceRead) {
      log.addShowLog('loan-apply-force');
      loanAgreementRef.current?.show({
        action: 'compulsory',
      });
      return;
    }
    log.addSuccessLog('loan-apply-force');
    try {
      const res = await doApply(params);
      resetAgreementCheck();
      if (!res?.admitted) {
        log.addErrorLog('loan-apply-reject', res);
        handleApplyFailed(res?.rejectReason, res?.extension);
        changeProcessAction('NULL');
        return;
      }
      log.addSuccessLog('loan-apply-create');
      if (res?.loanOrderId && res?.authenticationToken && res?.status === 'AUTHENTICATING') {
        // 支用成功开始核身
        await handleVerifyIdentity(res);
      } else {
        log.addErrorLog('loan-apply-fetch', res);
        throw new Error('LOAN_APPLY_FAILED');
      }
    } catch (e) {
      resetAgreementCheck();
      changeProcessAction('NULL');
      Toast.show({
        content: ErrorMessageMap.BUSY_DEFUALT,
      });
      log.addErrorLog('loan-apply-error', e);
    }
  };

  const { run: doLoanFn } = useDebounceFn(doLoan, {
    wait: 1000,
    leading: true,
    trailing: false,
  });

  const handleHomePlusReload = () => {
    doHomePlusReload();
  };

  const handleSubmit = async (data: HomeLoanApplyFormData) => {
    log.addClickLog('loan-apply');
    if (checkHmRejectAlert(`${PAGES.SsdHomePlus}-LOAN`)) {
      return;
    }

    // 尚未输入支用金额
    if (!loanApplyAmountValue) {
      log.addOtherLog('loan-apply-no-amount');
      amountInputRef.current?.focus?.();
      return;
    }

    doLoanFn(data);
  };

  const renderTitle = () => {
    return (
      <div className={styles.normalTitle}>
        可借
        <span className={styles.normalTitleAmount}>
          {numberFormat(payload?.quota?.surplusQuota)}
        </span>
      </div>
    );
  };

  const handleDisabledClick = () => {
    log.addClickLog('loan-apply-disabled-btn');
    if (payload?.surplusQuotaStatus === 'FROZEN') {
      applyRejectRef.current?.show?.({
        rejectCode: 'QUOTA_STATUS_FROZEN',
      });
      return;
    }
    if (checkLoanLimit(payload)) {
      applyRejectRef.current?.show?.(payload?.loanRejectReason);
      return;
    }
    if (payload?.surplusQuotaStatus === 'INSUFFICIENT') {
      Toast.show({
        content: '最低起借金额1元',
      });
    }
  };

  const normalButtonText = () => {
    if (checkLoanLimit(payload)) {
      return '借款笔数已达上限';
    }
    if (processAction === 'CONSULT_LOAN_RESULTING') {
      return (
        <Fragment>
          正在放款中&nbsp;{count}s
        </Fragment>
      );
    }
    if (loanApplyAmountValue) {
      return submitBtnText;
    }
    return '借钱';
  };

  const renderFooter = () => {
    const isLoading = checkLoading();
    const disabled = checkSubmitDisabled() || !initialized;
    const btnProps = disabled ? {
      className: classNames(styles.confirm, 'adm-button-disabled'),
      onClick: handleDisabledClick,
    } : {
      className: styles.confirm,
      type: 'submit' as const,
    };

    return (
      <Fragment>
        <RedPacketPopover offerList={payload?.platformPromotionOfferList} displayPosition="HOME_PAGE">
          <Button
            block
            loading={isLoading}
            loadingText={submitBtnText}
            color="primary"
            {...btnProps}
          >
            {normalButtonText()}
          </Button>
        </RedPacketPopover>
        <DetailAndAgreements
          form={form}
          initialized={initialized}
          trialStore={trialStore}
          loanAgreements={loanAgreements?.payLoad}
          onAgreementClick={handleLoanAgreementShow}
          onPurposeClick={handleLoanPurposeShow}
        />
      </Fragment>
    );
  };

  const renderPromotion = () => {
    if (trialStore?.promotionAmount && processAction !== 'TRIALING') {
      const promotionAmountValue = number.getNumber(trialStore?.promotionAmount);
      if (promotionAmountValue !== '--' && promotionAmountValue > 0) {
        return (
          <div className={styles.promotion}>总利息省{promotionAmountValue}元</div>
        );
      }
    }
    return null;
  };

  const renderMain = () => {
    const maxLoanAmount = payload?.maxLoanAmount;
    const rateCls = classNames(
      styles.interestRateDesc,
      loanApplyAmountValue && styles.interestRateDescMargin,
    );

    return (
      <Form
        form={form}
        className={styles.loanForm}
        footer={renderFooter()}
        onFinish={handleSubmit}
        onFinishFailed={handleFinishFailed}
        onValuesChange={handleValuesChange}
        layout="horizontal"
        hasFeedback
      >
        <Form.Item className={styles.inputFormItem} name={loanApplyAmountKey}>
          <AmountInput
            ref={amountInputRef}
            recommends={payload?.recommendAmountList}
            max={maxLoanAmount}
            surplusQuotaStatus={payload?.surplusQuotaStatus}
            disabled={inputDisabled}
            inputMode="numeric"
            pageVersion={pageVersion}
          />
        </Form.Item>
        <Rate
          initialized={initialized}
          processAction={processAction}
          loanSchemaInfo={payload}
          trialStore={trialStore}
          loanApplyAmountValue={loanApplyAmountValue}
          classNames={rateCls}
        />
        <div className={classNames(styles.panel, !loanApplyAmountValue && styles.hide)}>
          <Form.Item required={false} rules={loanMode.rules} className={styles.loanMode} label="怎么还" name="loanMode">
            <LoanMode
              initialized={initialized}
              loanTermOptions={schemeStore?.loanTerm?.options}
              repaymentMethodOptions={schemeStore?.repaymentMethod?.options}
              trialStore={trialStore}
              interestRate={payload?.interestRate}
              processAction={processAction}
            />
          </Form.Item>
          {renderPromotion()}
          <Form.Item required={false} rules={receiveBankCard.rules} className={styles.receiveBankCard} label="收款账户" name="receiveBankCard">
            <Account initialized={initialized} onReady={onAccountReady} />
          </Form.Item>
          <Form.Item
            className={styles.hide}
            required={false}
            rules={institution.rules}
            label="放款机构"
            name="institution"
          >
            <Institution capitalInstitution={trialStore?.capitalInstitution} />
          </Form.Item>
          <Form.Item
            className={styles.hide}
            required={false}
            rules={loanPurpose.rules}
            label="借款用途"
            name="loanPurpose"
          >
            <LoanPurpose
              ref={loanPurposeRef}
              options={schemeStore?.loanPurpose?.options}
            />
          </Form.Item>
          <Form.Item
            className={styles.hide}
            label="借款合同"
            name="agreementCheck"
          >
            <UnsignedAgreementsSSD
              ref={loanAgreementRef}
              bizType="LOAN"
              popupTitle="详情及协议"
              creditPlatform="MAYI_ZHIXIN"
              customPayload={loanAgreements?.payLoad}
              onCompleted={handleAgreementCompleted}
              getExtensionParams={getLoanAgreementExtensionParams}
              useInit={false}
              onError={handleAgreementEC}
              onClose={handleAgreementEC}
              buttonText="同意协议并借钱"
              force={false}
            />
          </Form.Item>
          <Form.Item name="loanVersion" noStyle className={styles.hide} />
          <Form.Item name="preFillAmountType" noStyle className={styles.hide} />
        </div>
      </Form>
    );
  };

  // 支用申请期间禁用页面操作
  const renderHover = () => {
    if (
      processAction === 'AUTENTICATING' ||
      processAction === 'SUBMITTING' ||
      processAction === 'CONSULT_LOAN_RESULTING'
    ) {
      return (<div className={styles.hover} />);
    }
    return null;
  };

  const renderApplyReject = () => {
    return <ApplyReject ref={applyRejectRef} />;
  };

  const renderLoanDetail = () => {
    return <LoanDetail ref={loanDetailRef} onReInit={handleHomePlusReload} />;
  };

  return (
    <div className={styles.loan}>
      {renderTitle()}
      {renderMain()}
      {renderHover()}
      <ClientOnly>
        {renderApplyReject}
      </ClientOnly>
      <ClientOnly>
        {renderLoanDetail}
      </ClientOnly>
    </div>
  );
}
