import { useState, useRef, useEffect } from 'react';
import { Toast, Dialog } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';

import { authenticationCreate, closeOrder, closeOrderQuery } from './service';
import { VerifyService } from '@/components/SSD/VerifyIdentity/libs/service';
import { VerifyStatus } from '@/components/SSD/VerifyIdentity/libs/types';
import { requestId } from '@/utils';
import { popPage, reload } from '@/utils/link';
import { queryCreditContract } from '@/store/center/actions';
import { useLoading } from '@/components/Loading';
import { ErrorMessageMap } from '@/common/constant';

export type PAGE_STATUS = 'SUCCEEDED' | 'FAILED' | 'PROCESSING' | 'INIT';

export enum CloseReasonEnum {
  none = 'none',
  interest = 'HIGH_RATE',
  amount = 'LOW_QUOTA',
  zhengxin = 'CREDIT',
  promo = 'MARKETING',
  security = 'SECURITY',
  usage = 'DEMAND',
  other = 'OTHER',
}

const REASONS = [{
  desc: '利息太贵，不划算',
  code: CloseReasonEnum.interest,
  index: Math.random() * 100,
}, {
  desc: '额度太低，不够用',
  code: CloseReasonEnum.amount,
  index: Math.random() * 100,
}, {
  desc: '担心征信受到影响',
  code: CloseReasonEnum.zhengxin,
  index: Math.random() * 100,
}, {
  desc: '当前不需要借钱',
  code: CloseReasonEnum.usage,
  index: Math.random() * 100,
}, {
  desc: '担心安全问题',
  code: CloseReasonEnum.security,
  index: Math.random() * 100,
}, {
  desc: '营销打扰太多了',
  code: CloseReasonEnum.promo,
  index: Math.random() * 100,
}, {
  desc: '其他原因',
  code: CloseReasonEnum.other,
  index: 0,
}];

export default function useCloseModel() {
  const [pageStatus, setPageStatus] = useState<PAGE_STATUS>();
  const [reasons, setReasons] = useState<any>([]);
  const timerRef = useRef<any>();
  const curCreditOrder = useRef<any>();
  const { showLoading, hideLoading } = useLoading();

  const handleFailed = () => {
    Dialog.alert({
      title: '关闭失败，请稍后再试',
      content: '请重试',
      onConfirm: () => {},
      confirmText: '我知道了',
    });
  };

  const loopOne = async () => {
    try {
      const rst = await closeOrderQuery({
        product: 'XFD',
        creditContractId: curCreditOrder?.current?.creditContractId,
      });
      // 4.0 处理各种状态
      const { status } = rst as any;
      setPageStatus(status);
      if (status === 'SUCCEEDED') { // 关闭成功
        log.addSuccessLog('close-ssd-submit');
        clearInterval(timerRef?.current);
      } else if (status === 'FAILED') { // fail
        log.addErrorLog('close-ssd-submit');
        handleFailed();
        clearInterval(timerRef?.current);
      } else if (status === 'PROCESSING' || status === 'INIT') {
        log.addOtherLog('close-ssd-submit-doing');
      } else {
        log.addOtherLog('close-ssd-submit-network');
        clearInterval(timerRef?.current);
      }
      return {
        status,
      };
    } catch (e) {
      clearInterval(timerRef?.current);
      Toast.show({
        content: '网络异常，请稍后重试',
      });
      throw e;
    }
  };

  const loopCloseOrderQuery = async () => {
    if (timerRef?.current) {
      clearInterval(timerRef?.current);
    }
    timerRef.current = setInterval(loopOne, 3000);
  };

  const doInit = async () => {
    showLoading('加载中...');
    try {
      curCreditOrder.current = await queryCreditContract({
        needQuerySurplusQuota: false,
      });
      const res = await loopOne();
      if (res?.status === 'INIT' || res?.status === 'PROCESSING') {
        loopCloseOrderQuery();
      } else {
        setReasons(REASONS.sort((a, b) => { return b.index - a.index; }));
      }
      hideLoading();
    } catch (e) {
      hideLoading();
      Dialog.show({
        content: '啊呀开了个小差，请重试',
        closeOnAction: true,
        actions: [[{
          key: 'cancel',
          onClick: popPage,
          text: '返回',
        }, {
          key: 'retry',
          onClick: reload,
          bold: true,
          text: '点击重试',
        }]],
      });
    }
  };

  const submit = async (code: CloseReasonEnum, reasonText: string) => {
    if (code === CloseReasonEnum.none) {
      return;
    }

    // 1.0 根据 code 中 reasons 获取对应的内容
    let reason;
    if (code === CloseReasonEnum.other) {
      reason = reasonText;
    } else {
      const matchReason = reasons.find((item) => item.code === code);
      reason = matchReason?.desc;
    }

    try {
      const res: any = await authenticationCreate({
        product: 'XFD',
        bizId: requestId(), // 保证唯一
        requestId: requestId(), // 幂等号
        point: 'QUIT_JIEBEI',
        origin: 'APP',
        authType: 'PASSWORD',
        authScene: 'QUIT',
      });
      const authenticationToken = res?.taskToken;

      // 2.0 验证核身
      const verifyRst = await verify(authenticationToken);

      if (!verifyRst) {
        return;
      }
      log.addSuccessLog('ssd-close-auth');
      showLoading('申请中，请稍等...');
      // 3.0 提交
      await closeOrder({
        product: 'XFD',
        requestId: requestId(),
        applyType: 'MANUAL',
        applyTime: Date.now(),
        channel: 'APP',
        closedCode: code, // 退出code
        closedDesc: reason, // 退出原因
        taskToken: authenticationToken,
      });
      log.addSuccessLog('ssd-close-apply');
      setTimeout(() => {
        hideLoading();
      }, 3000);
      loopCloseOrderQuery();
    } catch (err) {
      log.addErrorLog('ssd-close-apply');
      hideLoading();
      Toast.show({
        content: ErrorMessageMap.BUSY_DEFUALT,
      });
    }
  };

  const verify = async (authenticationToken: any): Promise<boolean> => {
    const verifyService = new VerifyService({
      authenticationToken,
    });
    const rst = await verifyService.verify();
    // 取消核身不需要提示异常
    if (rst?.status === VerifyStatus.CANCEL) {
      return false;
    }
    if (rst?.status !== VerifyStatus.PASS) {
      Toast.show('身份验证失败，请重试');
      return false;
    }
    return true;
  };

  useEffect(() => {
    doInit();
  }, []);

  return {
    pageStatus,
    reasons,
    loopCloseOrderQuery,
    submit,
  };
}
