import { MtopGet } from '@/utils/mtop';

export function consultReason(data?): Promise<any> {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.credit.close.consult',
    data,
  });
}

export function authenticationCreate(data: any) {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.authentication.create',
    data,
  });
}

export function closeOrder(data: any) {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.credit.close',
    data,
  });
}

export function closeOrderQuery(data: any) {
  return MtopGet({
    api: 'mtop.alibaba.fin.tao.blp.credit.close.order.query',
    data,
  });
}
