import { useCallback, useEffect } from 'react';
import { Outlet, ClientOnly, useData, defineDataLoader } from 'ice';
import { PAGES } from '@/common/constant';
import { NavBar } from '@ali/uni-api';
import { getPageName, isThemis, isTbApp } from '@/utils';
import LinkUtil from '@/utils/link';
import { isDev } from '@/utils/env';
import { initAes } from '@/utils/aes';
import classnames from 'classnames';

const HomeCsLink =
  'https://render.alipay.com/p/yuyan/180020010001253760/index.html?caprMode=sync&scene=app_ssd_sycjwt&commonParams=%7B%22channels%22%3A%22taobao%22%7D';
const LoanCsLink =
  'https://render.alipay.com/p/yuyan/180020010001253760/index.html?caprMode=sync&scene=app_ssd_sycjky&commonParams=%7B%22channels%22%3A%22taobao%22%7D';
const RepayCsLink =
  'https://render.alipay.com/p/yuyan/180020010001253760/index.html?caprMode=sync&scene=app_ssd_ssyhky&commonParams=%7B%22channels%22%3A%22taobao%22%7D';
const CenterCsLink =
  'https://render.alipay.com/p/yuyan/180020010001253760/index.html?caprMode=sync&scene=app_ssd_wdcjwt&commonParams=%7B%22channels%22%3A%22taobao%22%7D';

const PageCSLinkMap = {
  [PAGES.SsdHomePlus]: HomeCsLink,
  [PAGES.SsdHome]: HomeCsLink,
  [PAGES.SsdCreditLp]: HomeCsLink,
  [PAGES.SsdLoanApply]: LoanCsLink,
  [PAGES.SsdBankList]: LoanCsLink,
  [PAGES.SsdLoanResult]: LoanCsLink,
  [PAGES.SsdRepayEarlyApply]: RepayCsLink,
  [PAGES.SsdRepayBatchApply]: RepayCsLink,
  [PAGES.SsdRepayBill]: RepayCsLink,
  [PAGES.SsdLoanContract]: RepayCsLink,
  [PAGES.SsdRepayPlan]: RepayCsLink,
  [PAGES.SsdRepayResult]: RepayCsLink,
  [PAGES.SsdCenter]: CenterCsLink,
  [PAGES.SsdClose]: CenterCsLink,
  [PAGES.LoanDetail]: CenterCsLink,
  [PAGES.RepayDetail]: CenterCsLink,
  [PAGES.RecordList]: CenterCsLink,
};

const PageClassMap = {
  [PAGES.SsdHomePlus]: 'common-page-ssd',
  [PAGES.SsdHome]: 'common-page-ssd',
  [PAGES.SsdCreditLp]: 'common-page-ssd',
  [PAGES.SsdLoanResult]: 'common-page-bg-white',
  [PAGES.SsdRepayResult]: 'common-page-bg-white',
  [PAGES.SsdLoanApply]: 'common-page-bg-3',
};

const NoSafeAreaPages = [
  PAGES.SsdRepayEarlyApply,
  PAGES.SsdRepayBatchApply,
  PAGES.SsdClose,
  PAGES.SsdLoanApply,
  PAGES.RecordList,
  PAGES.SsdCoupon,
  PAGES.SsdInvalidCoupon,
];

export default function Layout() {
  const data = useData();

  const renderSafe = () => {
    // 如果是开发环境或者不在themis环境或者非通顶的页面，则不渲染
    if (isDev() || !isThemis() || NoSafeAreaPages.includes(getPageName(data?.pathname))) {
      return null;
    }
    return (
      <>
        <div className="safe-area-inset-top" />
        <div className="navbar-height" />
      </>
    );
  };

  const handleSetPageTitleImg = useCallback(async () => {
    try {
      await NavBar.setRightItem({
        lightImageUrl:
          'https://gw.alicdn.com/imgextra/i2/O1CN01Z24CVt1PhsqERPwyz_!!6000000001873-2-tps-112-112.png',
        darkImageUrl:
          'https://gw.alicdn.com/imgextra/i2/O1CN01Z24CVt1PhsqERPwyz_!!6000000001873-2-tps-112-112.png',
        onClick: () => {
          LinkUtil.navigatorOpenURL(PageCSLinkMap[getPageName(data?.pathname)] || HomeCsLink);
        },
      });
    } catch (error) {}
  }, [data]);

  useEffect(() => {
    handleSetPageTitleImg();
  }, []);

  if (!isTbApp()) {
    return null;
  }

  const handleAemLoad = () => {
    initAes();
    return <></>;
  };

  return (
    <div
      className={classnames(
        'layout-ssd',
        PageClassMap[getPageName(data?.pathname)] || 'common-page-bg-gray',
      )}
    >
      {renderSafe()}
      <div className="page-container">
        <Outlet />
      </div>
      <ClientOnly>{handleAemLoad}</ClientOnly>
    </div>
  );
}

export const dataLoader = defineDataLoader(async (ctx) => {
  return ctx;
});
