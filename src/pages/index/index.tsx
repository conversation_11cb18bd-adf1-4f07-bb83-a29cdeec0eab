import { definePageConfig } from 'ice';
// import classNames from 'classnames';
import { useCreditRouter } from '@/hooks';
import LinkUtil from '@/utils/link';
import { PAGES } from '@/common/constant';
import { getQueryParams } from '@/utils';
// import { SsdHomePlusSkeleton } from '@/components/SSD/Skeleton';
import { routerPageLog } from '@/utils/goc';

// import styles from './index.module.scss';

export default function Index() {
  const { creditConsultData, routerPage, inited } = useCreditRouter({
    useOneConfig: true,
  });
  const queryParams = getQueryParams();

  if (!inited) return;

  routerPageLog({
    routerPage,
    creditConsultData,
  });

  if (!creditConsultData) return;

  if (routerPage === PAGES.CreditFallback) {
    LinkUtil.locationReplace(PAGES.CreditFallback);
    return;
  }

  LinkUtil.locationReplace(routerPage, queryParams);

  return null;
  // return routerPage === PAGES.SsdHomePlus ? (
  //   <div className={classNames(styles.ssdHome, 'common-page-ssd')}>
  //     <div className={styles.main}>
  //       <div className={styles.slogan}>
  //         <i className={styles.logo} />
  //         <p className={styles.title}>随身贷</p>
  //       </div>
  //       <SsdHomePlusSkeleton />
  //     </div>
  //   </div>
  // ) : null;
}

export const pageConfig = definePageConfig(() => ({
  title: '借钱',
  spm: {
    spmB: PAGES.Index,
  },
}));
