import { definePageConfig } from 'ice';
import { useCreditRouter } from '@/hooks';
import LinkUtil from '@/utils/link';
import { PAGES } from '@/common/constant';
import { getQueryParams } from '@/utils';
import { routerPageLog } from '@/utils/goc';

export default function Index() {
  const { creditConsultData, routerPage, inited } = useCreditRouter({
    useOneConfig: true,
  });
  const queryParams = getQueryParams();

  if (!inited) return;

  routerPageLog({
    routerPage,
    creditConsultData,
  });

  if (!creditConsultData) return;

  if (routerPage === PAGES.CreditFallback) {
    LinkUtil.locationReplace(PAGES.CreditFallback);
    return;
  }

  LinkUtil.locationReplace(routerPage, queryParams);
}

export const pageConfig = definePageConfig(() => ({
  title: '借钱',
  spm: {
    spmB: PAGES.Index,
  },
}));
