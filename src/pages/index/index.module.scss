.ssdHome {
  min-height: calc(100vh - var(--navbar-height) - var(--safe-area-inset-top) - 4rpx);
  padding: 24rpx 16rpx !important;
  display: flex;
  flex-direction: column;
  font-family: PingFang SC;

  button {
    font-family: <PERSON><PERSON>ang SC;
    line-height: 1.3;
    font-size: 36rpx;
  }

  .main {
    flex: 1;

    .slogan {
      margin-bottom: 40rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;

      .logo {
        display: block;
        background-image: url("https://gw.alicdn.com/imgextra/i3/O1CN01wlOyxv1SEygc0C2cH_!!6000000002216-2-tps-144-144.png");
        width: 44rpx;
        height: 44rpx;
        background-repeat: no-repeat;
        background-size: contain;
      }

      .title {
        font-weight: 500;
        font-size: 36rpx;
        color: #000;
        text-align: center;
        padding-left: 15rpx;
        line-height: 1;
      }
    }

    .description {
      font-weight: 400;
      font-size: 24rpx;
      color: rgba(#000, 40%);
      padding-top: 20rpx;
      padding-bottom: 40rpx;
      letter-spacing: 0;
    }
  }

  .bottom {
    position: static;
    height: 130rpx;
  }
}

.detentionModalContent {
  font-weight: 500;
  font-size: 36rpx;
  color: #000;
  text-align: center;
}
.couponCard {
  background-color: #fffaf7;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 150rpx;
  margin-top: 40rpx;
  margin-bottom: 16rpx;
  font-weight: 600;
  font-size: 48rpx;
  color: #ff6010;
  font-family: "ALIBABA NUMBER FONT MD";
}
