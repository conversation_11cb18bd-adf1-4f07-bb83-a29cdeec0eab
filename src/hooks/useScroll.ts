import { useEffect, useRef, useState } from 'react';
import { _ } from '@/utils';

export default function useScroll({ targetEleId, scrollEleSelector = window, isTotal }) {
  if (!targetEleId) {
    throw new Error();
  }

  const [currentPage, setCurrentPage] = useState(1);
  const containerRef = useRef({
    currentPage: 1,
  });

  const throttleSetCurrentPage = _.throttle(
    () => {
      if (isTotal) return;
      containerRef.current.currentPage += 1;
      setCurrentPage(containerRef.current.currentPage);
    },
    1000,
    { trailing: false },
  );

  const scrollCallBack = () => {
    const scrollTop =
      document.documentElement.scrollTop || document.body.scrollTop || window.pageYOffset;
    const windowClientHeight = document.documentElement.clientHeight;
    const { offsetHeight } = document.getElementById(targetEleId);

    if (scrollTop + windowClientHeight > offsetHeight - 80) {
      throttleSetCurrentPage();
    }
  };

  useEffect(() => {
    scrollEleSelector.addEventListener('scroll', scrollCallBack);

    return () => {
      scrollEleSelector.removeEventListener('scroll', scrollCallBack);
    };
  }, []);

  return {
    currentPage,
  };
}
