/**
 * @file 页面visibilitychange hook
 * <AUTHOR>
 */

import { useEffect, useCallback } from 'react';

export default function useVisibilityChangeRefresh(reFreshCallback) {
  const handleVisibilityChange = useCallback(() => {
    if (!document.hidden) {
      reFreshCallback();
    }
  }, [reFreshCallback]);

  useEffect(() => {
    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);
}
