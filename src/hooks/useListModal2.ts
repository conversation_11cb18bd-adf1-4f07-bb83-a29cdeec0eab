/**
 * @file 列表
 * <AUTHOR>
 */

import { useState, useCallback, useEffect } from 'react';
import { _ } from '@/utils';
import { get, concat } from 'lodash-es';

interface FetchResponse<T> {
  success: boolean;
  responseCode: 'SUCCESS' | 'ERROR';
  response?: T;
  total?: number;
  hasMore: boolean;
}

export type DoFetch<D, T> = (params?: D) => Promise<FetchResponse<T>>;

type Fetch<D, T> = (params?: D) => Promise<T> | undefined;

interface PageParam {
  pageStart: number;
  pageSize: number;
}

export type FetchParams<D> = D extends PageParam ? D : PageParam;

interface ListModalProps<D, T, Q> {
  fetch: Fetch<FetchParams<D>, T>;
  listKey: string;
  totalKey: string;
  renderItem: (option: Q, index: number) => any;
  renderEmpty: () => React.JSX.Element;
  pageParam: FetchParams<D>;
}

export interface ListModalRef<D, T> {
  doFetch: DoFetch<FetchParams<D>, T>;
  doInit: DoFetch<FetchParams<D>, T>;
}

const LIST_MODAL_FETCH_CODE = {
  ERROR: 'ERROR',
  SUCCESS: 'SUCCESS',
};

export const useListModal = <D, T, Q>(params: ListModalProps<D, T, Q>) => {
  const { fetch, listKey, totalKey, renderItem, renderEmpty, pageParam: initPageParam } = params;
  const [list, setList] = useState<Q[]>([]);
  const [isFetching, setFetching] = useState(false);
  const [isListEmpty, setListEmpty] = useState(false);
  const [curPageParam, setCurPageParam] = useState<FetchParams<D> | null>(null);
  const [hasMore, setHasMore] = useState(true);

  // 请求远端数据
  const doRequest = useCallback(
    async (request?: FetchParams<D>) => {
      setFetching(true);
      try {
        const result = await fetch(request);
        // 请求结束
        // 请求异常
        if (!result) {
          throw new Error(LIST_MODAL_FETCH_CODE.ERROR);
        }
        // 获取请求列表、总条目数
        const fetchList = get(result, listKey) as Q[];
        const fetchTotal = get(result, totalKey) || 0;
        // 请求列表字段异常
        if (!fetchList) {
          throw new Error(LIST_MODAL_FETCH_CODE.ERROR);
        }
        return {
          fetchList,
          fetchTotal,
          success: true,
          responseCode: LIST_MODAL_FETCH_CODE.SUCCESS,
        };
      } catch (e: any) {
        return {
          fetchList: [],
          fetchTotal: 0,
          success: false,
          responseCode: e?.message,
        };
      } finally {
        setFetching(false);
      }
    },
    [fetch, listKey, totalKey],
  );

  const loadMoreHandler = useCallback(
    _.throttle(
      async (param) => {
        if (!param) return;
        try {
          const { fetchList, fetchTotal, responseCode, success } = await doRequest(param);
          // 请求异常直接返回不可翻页
          if (!success) {
            throw new Error();
          }
          // 当前列表长度+请求列表列表长度 = 实际列表长度
          const newList = concat(list, fetchList);
          const currentLen = newList.length;

          // 设置列表，且告知请求放是否翻页
          setList(newList);
          setHasMore(currentLen < fetchTotal);
          setListEmpty(fetchTotal === 0);
          return {
            hasMore,
            responseCode,
            success,
          };
        } catch (e) {
          // 异常的兜底
          setListEmpty(list.length === 0); // 如果查询报错，当前为空展示空状态
          setHasMore(false);
          return {
            hasMore: false,
            responseCode: LIST_MODAL_FETCH_CODE.ERROR,
            success: false,
          };
        }
      },
      1000,
      { leading: true, trailing: false },
    ),
    [list],
  );

  useEffect(() => {
    loadMoreHandler(curPageParam);
  }, [curPageParam]);
  // 继续查询
  const doFetch = useCallback(
    (request?: FetchParams<D>) => {
      if (isFetching) return;
      if (!curPageParam) {
        setCurPageParam({ ...initPageParam });
      } else {
        setCurPageParam({
          ...curPageParam,
          pageStart: curPageParam?.pageStart + 1,
          ...request,
        });
      }
    },
    [doRequest, list.length],
  );

  // 初始化查询
  const doInit = () => {
    setHasMore(true);
    setListEmpty(false);
    setList([]);
    setCurPageParam({
      ...initPageParam,
    });
  };

  const renderList = () => {
    if (isListEmpty) {
      return renderEmpty();
    }
    return list.map(renderItem);
  };

  return {
    renderList,
    doInit,
    doFetch,
    isFetching,
    hasMore,
    isListEmpty,
  };
};
