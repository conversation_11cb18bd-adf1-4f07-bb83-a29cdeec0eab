/**
 * @file 授信子路由
 * <AUTHOR>
 */

import { useState, useEffect, useCallback } from 'react';
import { getQueryParams, _ } from '@/utils';
import { Toast } from 'antd-mobile';
import { queryCreditOrder } from '@/store/credit/actions';
import { CREDIT_APPLY_ORDER_DTO } from '@/store/types';
import { PAGES } from '@/common/constant';
import { log } from '@alife/dtao-iec-spm-log';

interface useCreditSubRouterRes {
  creditApplyOrderData: CREDIT_APPLY_ORDER_DTO | null;
  subRouterPage: PAGES;
  inited: boolean;
  queryCreditOrderFn: (creditApplyOrderId?: string) => Promise<void>;
}


export const decideRouterPage = (applyOrder) => {
  if (!applyOrder) return PAGES.CreditLp;

  const { subStatus, status } = applyOrder;

  // 授信成功或者授信冻结跳到首页
  if (status === 'SUCCEEDED') {
    return PAGES.Home;
  }

  // 填写基础资料
  if (subStatus === 'WAIT_TO_FILL_BASIC_INFO' || subStatus === 'WAIT_TO_AUTH') {
    return PAGES.CreditIdentity;
  }

  // 待核身
  // if (subStatus === 'WAIT_TO_AUTH') {
  //   return PAGES.CreditSign;
  // }

  // 待选择机构
  if (subStatus === 'WAIT_TO_SELECT_INSTITUTION') {
    return PAGES.CreditInstitution;
  }

  // 机构授信中
  if (_.includes(['INSTITUTION_CREDITING', 'FAILED'], subStatus)) {
    return PAGES.CreditResult;
  }

  return PAGES.CreditLp;
};

const useCreditSubRouter = (creditApplyOrderId?): useCreditSubRouterRes => {
  const [creditApplyOrderData, setCreditApplyOrderData] = useState<null | CREDIT_APPLY_ORDER_DTO>(
    null,
  );
  const urlCreditApplyOrderId = _.get(getQueryParams(), 'creditApplyOrderId');
  const [inited, setInited] = useState(false);

  const subCreditRouterRet = useCallback((page?) => {
    return {
      creditApplyOrderData,
      subRouterPage: page,
      inited,
      queryCreditOrderFn,
    };
  }, [inited, creditApplyOrderData]);

  const queryCreditOrderFn = async (orderId?) => {
    queryCreditOrder({
      creditApplyOrderId: orderId || urlCreditApplyOrderId,
    })
      .then((res) => {
        setCreditApplyOrderData(res);
      })
      .catch(() => {
        log.addErrorLog('sub-router-query-credit-order');
        Toast.show('查询授信单失败');
        // 如果在主路由查询失败，则跳到兜底页
      })
      .finally(() => {
        setInited(true);
      });
  };

  // 用url上的查一遍
  useEffect(() => {
    if (!urlCreditApplyOrderId) {
      Toast.show('授信单号不能为空');
      return;
    }

    queryCreditOrderFn(urlCreditApplyOrderId);
  }, []);

  // 如果有传进来，重新查
  useEffect(() => {
    if (!creditApplyOrderId) return;

    queryCreditOrderFn(creditApplyOrderId);
  }, [creditApplyOrderId]);

  // 未发送查询请求 未初始化
  if (!inited) return subCreditRouterRet();

  return subCreditRouterRet(decideRouterPage(creditApplyOrderData));

  // 没有授信申请单
};

export default useCreditSubRouter;
