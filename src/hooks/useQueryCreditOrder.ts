/**
 * @file 查询授信单
 * <AUTHOR>
 */

import { useState, useEffect } from 'react';
import { getQueryParams, _ } from '@/utils';
import { Toast } from 'antd-mobile';
import { queryCreditOrder } from '@/store/credit/actions';
import { CREDIT_APPLY_ORDER_DTO } from '@/store/types';
import { log } from '@alife/dtao-iec-spm-log';

interface useQueryCreditOrderRes {
  creditApplyOrderData: CREDIT_APPLY_ORDER_DTO | null;
  inited: boolean;
  queryCreditOrderFn: (creditApplyOrderId?: string, otherParams?: any) => Promise<undefined | CREDIT_APPLY_ORDER_DTO>;
}

const useQueryCreditOrder = (defaultOrderData?): useQueryCreditOrderRes => {
  const [creditApplyOrderData, setCreditApplyOrderData] = useState<null | CREDIT_APPLY_ORDER_DTO>(
    null,
  );
  const urlCreditApplyOrderId = _.get(getQueryParams(), 'creditApplyOrderId');
  const [inited, setInited] = useState(false);

  const queryCreditOrderFn = async (orderId?, otherParams?) => {
    try {
      const res = await queryCreditOrder({
        creditApplyOrderId: orderId || urlCreditApplyOrderId,
        ...otherParams,
      });
      setCreditApplyOrderData(res);
      return res;
    } catch (error) {
      log.addErrorLog('sub-router-query-credit-order');
      Toast.show('查询授信单失败');
      // 如果在主路由查询失败，则跳到兜底页
    } finally {
      setInited(true);
    }
  };

  // 用url上的查一遍
  useEffect(() => {
    if (!urlCreditApplyOrderId) return;

    queryCreditOrderFn(urlCreditApplyOrderId);
  }, []);

  return {
    creditApplyOrderData: creditApplyOrderData || defaultOrderData,
    inited,
    queryCreditOrderFn,
  };
};

export default useQueryCreditOrder;
