/**
 * @file 列表
 * <AUTHOR>
 */

import { useState, useRef, useCallback } from 'react';
import { get, concat } from 'lodash-es';

interface FetchResponse<T> {
  success: boolean;
  responseCode: 'SUCCESS' | 'ERROR';
  response?: T;
  total?: number;
  hasMore: boolean;
}

export type DoFetch<D, T> = (params?: D) => Promise<FetchResponse<T>>;

type Fetch<D, T> = (params?: D) => Promise<T> | undefined;

interface PageParam {
  pageStart: number;
  pageSize: number;
}

export type FetchParams<D> = D extends PageParam ? D : PageParam;

interface ListModalProps<D, T, Q> {
  fetch: Fetch<FetchParams<D>, T>;
  listKey: string;
  totalKey: string;
  renderItem: (option: Q, index: number) => any;
  renderEmpty: () => React.JSX.Element;
  pageParam: FetchParams<D>;
  onSuccess?: any;
  onError?: any;
}

export interface ListModalRef<D, T> {
  doFetch: DoFetch<FetchParams<D>, T>;
  doInit: DoFetch<FetchParams<D>, T>;
}

const LIST_MODAL_FETCH_CODE = {
  ERROR: 'ERROR',
  SUCCESS: 'SUCCESS',
};

export const useListModal = <D, T, Q>(params: ListModalProps<D, T, Q>) => {
  const { fetch, listKey, totalKey, renderItem, renderEmpty, pageParam, onSuccess, onError } = params;
  const [list, setList] = useState<Q[]>([]);
  const [isFetching, setFetching] = useState(false);
  const [isListEmpty, setListEmpty] = useState(false);
  const fetchParamsRef = useRef<FetchParams<D>>(pageParam);

  // 请求远端数据
  const doRequest = useCallback(async (request?: FetchParams<D>) => {
    setFetching(true);
    try {
      const result = await fetch(request);
      // 请求结束
      // setFetching(false);
      // 请求异常
      if (!result) {
        throw new Error(LIST_MODAL_FETCH_CODE.ERROR);
      }
      // 获取请求列表、总条目数
      const fetchList = get(result, listKey) as Q[];
      const fetchTotal = get(result, totalKey) || 0;
      // 请求列表字段异常
      if (!fetchList) {
        throw new Error(LIST_MODAL_FETCH_CODE.ERROR);
      }
      onSuccess && onSuccess();
      return {
        fetchList,
        fetchTotal,
        success: true,
        responseCode: LIST_MODAL_FETCH_CODE.SUCCESS,
      };
    } catch (e: any) {
      onError && onError();
      return {
        fetchList: [],
        fetchTotal: 0,
        success: false,
        responseCode: e?.message,
      };
    } finally {
      setFetching(false);
    }
  }, [fetch, listKey, totalKey]);

  // 继续查询
  const doFetch = useCallback(async (request?: FetchParams<D>) => {
    try {
      let hasMore = false;
      fetchParamsRef.current = {
        ...fetchParamsRef.current,
        ...request || {},
      };
      const {
        fetchList,
        fetchTotal,
        responseCode,
        success,
      } = await doRequest(fetchParamsRef.current);
      // 请求异常直接返回不可翻页
      if (!success) {
        throw new Error();
      }
      // 当前列表长度+请求列表列表长度 = 实际列表长度
      const currentLen = list.length + fetchList.length;
      // 实际列表长度 < 请求列表总长度，继续翻页
      if (currentLen < fetchTotal) {
      // 继续翻页
        fetchParamsRef.current.pageStart++;
        hasMore = true;
      }
      // 设置列表，且告知请求放是否翻页
      setList((preVal) => {
        const concatList = concat(preVal, fetchList);
        return concatList || [];
      });
      return {
        hasMore,
        responseCode,
        success,
      };
    } catch (e) {
      // 异常的兜底
      return {
        hasMore: false,
        responseCode: LIST_MODAL_FETCH_CODE.ERROR,
        success: false,
      };
    }
  }, [doRequest, list.length]);

  // 初始化查询
  const doInit = useCallback(async (request?: D) => {
    try {
      let hasMore = false;
      setListEmpty(false);
      // 初始化页码，可能是切换查询条件再查询
      fetchParamsRef.current = {
        ...pageParam,
        ...request || {},
      };
      if (list?.length) {
        // 清空列表
        setList([]);
      }
      // 可能有问题，list还没有置为空，结果已经返回，但是基本不会有这个问题
      const {
        success,
        responseCode,
        fetchList,
        fetchTotal,
      } = await doRequest(fetchParamsRef.current);
      const defaultRes = {
        success,
        responseCode,
      };
      // 首次请求失败
      if (!success) {
        throw new Error();
      }
      // 列表有数据，且请求列表的长度 < 列表总长度
      if (
        fetchList.length > 0 &&
        fetchTotal > 0 &&
        fetchList.length < fetchTotal
      ) {
        fetchParamsRef.current.pageStart++;
        hasMore = true;
      }
      // 列表有数据，且请求列表的长度 = 列表总长度 = 0
      if (fetchList.length === 0 && fetchTotal === 0) {
        setListEmpty(true);
      }
      setList(fetchList);
      return {
        ...defaultRes,
        total: fetchTotal,
        hasMore,
      };
    } catch (e) {
      setListEmpty(true);
      return {
        hasMore: false,
        responseCode: LIST_MODAL_FETCH_CODE.ERROR,
        success: false,
      };
    }
  }, [pageParam, list, doRequest]);

  const renderList = () => {
    if (isListEmpty) {
      return renderEmpty();
    }
    return list.map(renderItem);
  };

  return {
    renderList,
    doInit,
    doFetch,
    isFetching,
  };
};
