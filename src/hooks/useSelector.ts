import { useCallback } from 'react';
import { log } from '@alife/dtao-iec-spm-log';
import { isObject, isString } from 'lodash-es';

export type Value<T> = string | T;

export interface Option {
  label?: string;
  value: string;
}

export interface InitOptions<T> {
  options?: Array<Value<T>>;
  value?: Value<T>;
  color?: 'default' | 'primary';
  size?: 'normal' | 'large';
  columns?: number;
  logKey?: string;
  className?: string;
  onChange?: (value?: Value<T>) => void;
  checkActive?: (option: T, value?: Value<T>) => boolean;
  formatOnChange?: (option: T) => any;
}

const useSelector = <T extends Option>(initOptions: InitOptions<T>) => {
  const {
    options, color = 'normal', value, size = 'normal', className,
    columns = 4, logKey = 'selector', onChange, checkActive, formatOnChange,
  } = initOptions;

  const handleCheckActive = useCallback((option: T) => {
    if (checkActive && isObject(value)) {
      return checkActive(option, value);
    }
    if (isString(value)) {
      return option?.value === value;
    }
    return false;
  }, [value, checkActive]);

  const handleOnChange = useCallback((option: T) => () => {
    if (handleCheckActive(option)) {
      return;
    }
    if (formatOnChange) {
      onChange && onChange(formatOnChange(option));
    } else {
      onChange && onChange(option?.value);
    }
    log.addChangeLog(`${logKey}-change`, {
      option,
    });
  }, [logKey, handleCheckActive, onChange, formatOnChange]);

  const isActive = useCallback((option: T) => {
    return handleCheckActive(option);
  }, [handleCheckActive]);

  const isFirst = useCallback((index: number) => {
    return index % columns === 0;
  }, [columns]);

  return {
    value,
    options,
    color,
    size,
    columns,
    logKey,
    className,
    isActive,
    isFirst,
    handleOnChange,
  };
};

export default useSelector;
