/**
 * @file 倒计时
 */

import { useState, useEffect, useCallback, useRef } from 'react';

interface UseCountDownProps {
  duration?: number;
  onEnd?: () => void;
}

export default function useCountDown(props: UseCountDownProps) {
  const { duration = 1000, onEnd } = props;
  const [count, setCount] = useState<number>(0);
  const tmpRef = useRef(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (count > 0) {
        setCount((c) => c - 1);
      }
    }, duration);
    if (count === 0 && tmpRef.current) {
      tmpRef.current = false;
      onEnd && onEnd();
    }
    return () => clearTimeout(timer);
  }, [count, duration]);

  const start = useCallback((times: number) => {
    setCount(times);
    tmpRef.current = true;
  }, []);

  const end = () => {
    tmpRef.current = false;
  };

  return {
    count,
    start,
    end,
  };
}
