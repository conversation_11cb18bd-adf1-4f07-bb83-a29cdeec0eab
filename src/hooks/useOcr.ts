import { useState, useEffect } from 'react';
import { ocrIdCardOfFace, ocrIdCardOfBack, ocrBankCard } from '@/store/common/actions';
import { _ } from '@/utils';

export interface OcrConfig {
  enable?: boolean;
  withSign?: boolean;
  ocrType: OCR_TYPE;
}

export type OCR_TYPE = 'front' | 'back' | 'business-license' | 'bankcard';

interface FileValueObject {
  url?: string;
  type?: string;
  name?: string;
  suffix?: string;
  fileFactoryNo?: string;
}

export default function useOcr(fileItemValue: FileValueObject | null, ocrConfig: OcrConfig) {
  const [ocrResult, setOcrResult] = useState(null);
  const [error, setError] = useState<any>(null);

  useEffect(() => {
    const { ocrType, enable = true, withSign = true } = ocrConfig || {};
    const { fileFactoryNo, url } = fileItemValue || {};

    if (_.isEmpty(fileItemValue) || !ocrType || !enable || !fileFactoryNo) {
      setError(null);
      setOcrResult(null);
      return;
    }
    const ocrApi = _.get(
      {
        front: ocrIdCardOfFace,
        back: ocrIdCardOfBack,
        bankcard: ocrBankCard,
      },
      ocrType,
    );

    if (!ocrApi) {
      throw new Error('unsupported ocrType');
    }

    ocrApi({
      fileNo: fileFactoryNo,
      fileServerFileName: url,
      withSign,
    })
      .then((res: any) => {
        if (res) {
          setOcrResult(res);
          setError(null);
        } else {
          throw new Error('OCR_FAILED');
        }
      })
      .catch((e) => {
        setError(e);
        setOcrResult(null);
      });
  }, [fileItemValue]);

  return {
    ocrResult,
    error,
  };
}
