import { useState, useEffect, useRef } from 'react';

interface CountData {
  smsBtnDisabled?: boolean;
  smsBtnTxt: string;
  count: number;
}

const useSmsCodeCount = (
  countParams?,
) => {
  const { defaultSmsBtnTxt = '发送验证码', defaultCount = 60, reSendTxt = '重新发送', countingTextRender = (count) => `${count}s后重新发送` } = countParams || {};
  const [countData, setCountData] = useState<CountData>({
    smsBtnDisabled: false,
    smsBtnTxt: defaultSmsBtnTxt,
    count: defaultCount,
  });
  const countRef = useRef<any>(null);
  const startCount = () => {
    setCountData({
      ...countData,
      smsBtnDisabled: true,
      smsBtnTxt: countingTextRender(countRef.current),
    });

    const _smsInterval = setInterval(() => {
      if (countRef.current <= 0) {
        clearInterval(_smsInterval);
        countRef.current = defaultCount;
        setCountData({
          count: countRef.current,
          smsBtnDisabled: false,
          smsBtnTxt: reSendTxt,
        });
        return;
      }
      const newCount = countRef.current - 1;
      setCountData({
        count: newCount,
        smsBtnDisabled: true,
        smsBtnTxt: countingTextRender(newCount),
      });
    }, 1000);
  };
  useEffect(() => {
    countRef.current = countData.count;
  }, [countData]);

  return { countValue: countRef.current, ...countData, startCount };
};

export default useSmsCodeCount;
