/**
 * @file useUnsignedAgreement
 * <AUTHOR>
 */

import { useState } from 'react';
import { Modal } from 'antd-mobile';
import LinkUtil from '@/utils/link';
import { log } from '@alife/dtao-iec-spm-log';
import { queryUnSignAgreementList, QueryUnSignAgreementListResponse } from '@/store/agreement/actions';

interface Option {
  default?: QueryUnSignAgreementListResponse;
  requestParams?: any;
}

export default function useUnsignedAgreement(option: Option) {
  const [
    unsignedAgreementData,
    setUnsignedAgreementData,
  ] = useState<null | QueryUnSignAgreementListResponse>(null);

  const queryUnsignedAgreement = async () => {
    try {
      const res = await queryUnSignAgreementList({
        ...(option.requestParams || {}),
      });
      setUnsignedAgreementData(res);
      return res;
    } catch (e) {
      log.addErrorLog('query-unsigned-agreement-error', { jsonError: JSON.stringify(e), code: e.message, mtopErrorCode: e.errorMessage });
      Modal.alert({
        title: '服务器开小差',
        content: '请稍后重试',
        onConfirm: LinkUtil.popPage,
      });
      return null;
    }
  };

  return {
    queryUnsignedAgreement,
    unsignedAgreementData: unsignedAgreementData || option?.default,
  };
}
