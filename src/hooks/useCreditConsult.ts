/**
 * @file creditConsult
 * <AUTHOR>
 */

import { useState } from 'react';
import { queryCreditApplyConsult } from '@/store/credit/actions';
import { CREDIT_APPLY_CONSULT_DTO } from '@/store/types';
import { Modal } from 'antd-mobile';
import LinkUtil from '@/utils/link';
import { log } from '@alife/dtao-iec-spm-log';

interface Option {
  default?: CREDIT_APPLY_CONSULT_DTO;
  requestParams?: any;
}

export default function useCreditConsult(option: Option) {
  const [creditConsultData, setCreditConsultData] = useState<null | CREDIT_APPLY_CONSULT_DTO>(null);

  const queryConsult = async () => {
    try {
      const res = await queryCreditApplyConsult({
        ...(option.requestParams || {}),
      });
      setCreditConsultData(res);
      return res;
    } catch (e) {
      log.addErrorLog('credit-router-error', { jsonError: JSON.stringify(e), code: e.message, mtopErrorCode: e.errorMessage });
      Modal.alert({
        title: '服务器开小差',
        content: '请稍后重试',
        onConfirm: LinkUtil.popPage,
      });
      return null;
    }
  };

  return {
    queryConsult,
    creditConsultData: creditConsultData || option?.default,
  };
}
