/// <reference types="@ice/app/types" />
declare interface Window {
  WindVane: any;
  ap: any;
  Pan: any;
  baxiaCommon: any;
  goldlog?: any;
  WXEnvironment?: any;
  pha?: any;
  AES?: any;
  AES_CONFIG?: any;
  AESPluginEvent?: any;
  DTAO_IEC_ADDRESS?: any;
  __windvane__: {
    call: any;
  };
}

declare interface BaseRequest {
  product?: string;
}

declare interface FileObject {
  url?: string;
  suffix: string;
  fileFactoryNo: string;
}

declare interface MtopRequest<D> {
  api: string;
  data: D;
  noResponse?: boolean;
}

declare interface PollingParams {
  times?: number;
  delay?: number;
  onPollingFinished?: () => void;
}

declare interface Option {
  label?: string;
  value: string;
  unit?: string;
}
