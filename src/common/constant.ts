/**
 * @file 各种常量
 * <AUTHOR>
 */

export const PRODUCT = 'XFD';

export const ArrowIconSrc =
  'https://gw.alicdn.com/imgextra/i1/O1CN01YcX1Qm1XRrtqQ2dmf_!!6000000002921-2-tps-22-22.png';

export const ZZFW_TB_MARKET_LINK_PRE = 'https://market.m.taobao.com/app/starlink/wakeup-transit/pages/download?slk_force_set_request=true&star_id=7601&targetUrl=';

export const AGREEMENT_PDF = 'https://b2bfin.taobao.com/web/XFD/h5/agreement';

export const AGREEMENT_PDF_PRE = 'https://b2bfin.taobao.com/web/XFD/h5/agreement';

export const AGREEMENT_PREVIEW = 'https://b2bfin.taobao.com/web/XFD/h5/sandbox';

export const AGREEMENT_PREVIEW_ALIYUN = 'https://b2bfin.taobao.com/web/XFD/h5/sandbox-aliyun';

export const AGREEMENT_PREVIEW_SSD = 'https://pages-fast.m.taobao.com/wow/z/uniapp/1100010/b2b-finance-web/dtao-xfd-uniapp/ssd-agreement-sandbox';

export const TENANT = 'DTAO';

export const PRIVACY_ID = 'tb_borrow';

export const PHONE_REG = /^1[0-9]{10}$/;

export const SMSCODE_REG = /^[0-9]{6}$/;

export const ID_CARD_REG = /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/;

export const XFD_ONECONFIG_DATAID = 'dtao-xfd-fe';

export const REPAYMENT_METHOD = {
  AVERAGE_CAPITAL_PLUS_INTEREST: '等额本息',
  BALLOON_MORTGAGE: '先息后本',
};

export const REPAYMENT_METHOD_DESC = {
  AVERAGE_CAPITAL_PLUS_INTEREST: '每月还款金额相同',
  BALLOON_MORTGAGE: '先还利息，到期还本金',
};

export const LOAN_TERM_UNIT = {
  MONTH: '个月',
};

export enum PAGES {
  Index = '/',

  CreditLp = 'credit-lp',
  CreditLpSSR = 'lp-ssr',
  SsdCreditLpSimple = 'lp-ssd-simple',
  CreditSimpleLp = 'lp-simple',
  CreditSign = 'credit-sign',
  CreditIdentity = 'credit-identity',
  CreditNewIdentity = 'credit-new-identity',
  CreditInstitution = 'credit-institution',
  CreditResult = 'credit-result',
  CreditFallback = 'credit-fallback',
  InstList = 'inst-list',

  Home = 'home',

  LoanApply = 'loan-apply',
  BindCard = 'bind-card',
  LoanResult = 'loan-result',
  LoanSign = 'loan-sign',

  RecordList = 'record-list',
  LoanDetail = 'loan-detail',
  RepayDetail = 'repay-detail',

  RepayEarlyApply = 'repay-early-apply',
  RepayBill = 'repay-bill',
  RepayBatchApply = 'repay-batch-apply',
  RepayPlan = 'repay-plan',
  RepayResult = 'repay-result',
  LoanContract = 'loan-contract',

  CenterCertManage = 'cert-manage',
  CenterCertUpdate = 'cert-update',
  CenterPhoneUpdate = 'phone-update',
  CenterProfileSign = 'profile-sign',
  CenterQuotaRate = 'quota-rate',
  CenterSignedAgreements = 'signed-agreements',
  SignedAgreementPreview = 'signed-agreement-preview',
  CenterDiscountCoupon = 'discount-coupon',
  CenterInvalidCoupon = 'invalid-coupon',
  CenterPhoneManage = 'phone-manage',
  CenterClose = 'close-home',
  CenterBankCard = 'bank-card',

  SsdHomePlus = 'ssd-home-plus',

  SsdHome = 'ssd-home',
  SsdCenter = 'ssd-center',
  SsdMyQuota = 'ssd-my-quota',
  SsdSignedAgreements = 'ssd-signed-agreements',
  SsdDiscountCoupon = 'ssd-discount-coupon',
  SsdCreditLp = 'ssd-credit-lp',
  SsdCreditLpSSR = 'lp-ssd-ssr',
  SsdClose = 'ssd-close-home',
  SsdBankList = 'ssd-bank-list',
  SsdLoanApply = 'ssd-loan-apply',
  SsdLoanResult = 'ssd-loan-result',
  SsdCoupon = 'ssd-coupon',
  SsdInvalidCoupon = 'ssd-invalid-coupon',

  SsdRepayEarlyApply = 'ssd-repay-early-apply',
  SsdRepayBill = 'ssd-repay-bill',
  SsdRepayBatchApply = 'ssd-repay-batch-apply',
  SsdRepayPlan = 'ssd-repay-plan',
  SsdRepayResult = 'ssd-repay-result',
  SsdLoanContract = 'ssd-loan-contract',
  SsdAgreementSandbox = 'ssd-agreement-sandbox',
}

export const BANK_REGX = /^[1-9]\d{11,18}$/;

export const PHONE_REGX = /^1\d{10}$/;

export const VIRTUAL_PHONE_REGX = /^1(70|71|65|62|67|69)\d{8}$/;

export const SMS_BASE_PARAMS = {
  source: 'default',
  reqCode: '*********',
};

export const COUPON_TYPE = {
  DISCOUNT: '折扣',
  INTEREST_FREE: '免息',
};

export const INSTITUTION_NAME_MAP = {
  AI_BANK: '中信百信银行',
  DXM: '度小满',
  XW_BANK: '新网银行',
  LX: '分期乐',
  ZYXJ: '中邮消金',
  XYKD: '小赢卡贷',
};

export const INSTITUTION_NAME_MAP_FOR_HOME = {
  AI_BANK: '中信百信银行',
  DXM: '度小满合作金融机构',
  XW_BANK: '新网银行',
  LX: '分期乐合作金融机构',
  ZYXJ: '中邮消金',
  XYKD: '小赢卡贷合作金融机构',
};

export const ErrorMessageMap = {
  BUSY_DEFUALT: '系统开小差，请稍后重试',
  default: '系统开小差，请稍后重试',
  CREDIT_DEGRADE: '服务受限，暂无法申请',
  SAME_IDENTITY_CARD_EXIST: '您上传的身份证已在其他的淘宝账号申请额度，不可重复申请',
  CUSTOMER_NOT_IN_CROWD: '暂无法为您提供服务',
  CREDIT_APPLY_ORDER_ON_GOING: '您已申请过额度',
  CREDIT_IS_QUALIFIED: '您已获取过额度',
  SUPPLEMENT_DATA_NOT_COMPLETE: '补充资料不完整',
  FAIL_BIZ_SUPPLEMENT_DATA_NOT_COMPLETE: '补充资料不完整',
  IDENTIFY_WILL_BE_EXPIRED_IN_30_DAYS: '身份证将在30天内过期',
  FAIL_BIZ_IDENTIFY_WILL_BE_EXPIRED_IN_30_DAYS: '您上传的身份证即将过期，请重新上传',
  INSTITUTION_SELECTION_IS_EMPTY: '系统开小差，请稍后重试',
  INSTITUTION_SYSTEM_SHUT_DOWN: '系统开小差，请稍后重试',
  BANK_CARD_ABNORMAL: '您的银行卡状态异常，请使用正常服务中的银行卡收款',
  ID_CARD_INVALID: '您的身份证已过期，请联系客服处理',
  FAIL_BIZ_IDENTIFY_EXPIRED: '您上传的身份证已过期，请重新上传',
  FAIL_BIZ_SAME_IDENTITY_CARD_EXIST: '您上传的身份证已在其他淘宝账号申请额度，不可重复申请',
  MANUAL_REPAY_REJECT_WHEN_CUT_OFF: '机构系统维护中，暂无法主动还款',
  MANUAL_REPAY_REJECT_WHEN_SYSTEM_REPAY: '机构系统维护中，暂无法主动还款',
  MANUAL_REPAY_REJECT_AT_LOAN_START_DATE: '借款当天无法提前还款，请明日再还',
  PREPAY_REJECT_IF_DUE_OR_OVERDUE: '借款有到期或逾期不能提前还款',
  PREPAY_REJECT_IF_OVERDUE: '当前存在逾期，不支持提前还款',
  PREPAY_REJECT_IF_DUE: '还款日暂不支持提前还款',
  EXITS_CLEAR_LOAN: '存在已结清的借款',
  LOAN_CONTRACT_SETTLED: '借款已还清',
  REPAY_AMOUNT_MAX_LIMIT_UNSATISFIED: '还款金额超过最大限制',
  REPAY_AMOUNT_MIN_LIMIT_UNSATISFIED: '还款金额不能小于应还利息和罚息的总和',
  REPAY_ONGOING: '您有一笔进行中的还款申请，请稍后再还款',
  REPAY_AMOUNT_NOT_EQUAL_TRIAL_AMOUNT: '您的应还金额发生变化，请刷新查看最新金额',
  INSUFFICIENT_BALANCE: '您的银行卡余额不足，请确保余额充足后再还款',
  INSUFFICIENT_BALANCE_OVERRUN: '您的银行卡余额不足，请确保余额充足后再还款',
  ACCOUNT_EXCEPTION: '您的银行卡状态异常，请使用正常服务中的银行卡还款',
  CARD_PASSWORD_LOCK: '您的银行卡状态异常，请使用正常服务中的银行卡还款',
  ACCOUNT_NOT_SUPPORTED: '您的银行卡不支持还款，请使用其他卡重试',
  RESTRICTED_CARD_PAYMENT: '您的银行卡不支持还款，请使用其他卡重试',
  AMOUNT_OVERRUN: '您的银行卡已限额',
  ILLEGAL_REPAY_NO: '当前没有处理中的还款申请',
  REPAY_DISABLED: '服务受限，无法还款',
  PROTOCOL_EXCEPTION: '您的银行卡不支持还款，请使用其他卡重试',
  NO_PAYMENT_CHANNEL: '您的银行卡不支持还款，请使用其他卡重试',
  AMOUNT_OVERRUN_ONCE: '您本次还款金额超限，请联系机构客服',
  AMOUNT_OVERRUN_DAY: '您当日累计还款金额超限，请联系机构客服',
  AMOUNT_OVERRUN_MONTH: '您当月累计还款金额超限，请联系机构客服',
  AMOUNT_OVERRUNHANNEL: '当前还款通道已限额，请使用其他卡重试或联系机构客服',
  REPAY_APPLY_ON_WAY: '有进行中的还款申请，请稍后再还款',
  S_LOAN_ON_USING: '有欠款未结清，暂时无法关闭服务',
  S_LEND_ON_WAY: '有处理中的借款申请，暂时无法关闭服务',
  S_PRODUCT_NOT_OPEN: '当前未开通服务，无需关闭',
  CLOSE_CUSTOMER_HAS_LOANS: '您有借款未结清，请先结清',
  ALIPAY_USER_ID_IS_NULL: '请先绑定支付宝账号信息',
  DISPLAY_TRIAL_AMOUNT_NOT_EQUAL_LATEST_TRIAL_AMOUNT: '您的应还金额发生变化，请重新计算最新金额',
  REPAY_AMOUNT_OVERRUN: '还款金额超过应还金额',
  REPAY_AMOUNT_CHANGED: '应还金额发生变化，请重新计算最新金额',
  CLOSE_RISK_REJECT: '经机构评估，您暂不满足关闭条件',
  CLOSE_OTHER: '系统开小差，请稍后重试',
  ONGOING_CUSTOMER_PROFILE_CHANGE: '更新中，请耐心等待',
  CLOSE_ILLEGAL_CUSTOMER_ID: '您的账户信息异常，无法操作',
  OLD_LICENSE_EFFECTIVE: '您的身份证在有效期内，无需更新',
  CHANGE_IDENTITY_CARD_NOT_SUPPORTED_BEFORE_FIRST_LOAN: '暂不支持更新身份证，若需要请在首次借款后更新',
  LICENSE_NAME_CAN_NOT_CHANGE: '您的身份信息不一致，无法更新',
  LICENSE_NO_CAN_NOT_CHANGE: '您的身份信息不一致，无法更新',
  PHONE_NO_SAME: '新手机号与当前手机号一致，无需更新',
  CHANGE_IDENTITY_CARD_NOT_SUPPORTED: '暂不支持更新身份证',
  CHANGE_PHONE_NOT_SUPPORTED: '暂不支持更新手机号',
  INSTITUTION_SHUT_DOWN: '机构升级中',
  IDENTITY_CARD_EXPIRED: '身份证即将或已到期',
  APPLY_QUOTA_EXCEPTION: '借款金额异常，请重新输入',
  LOAN_DISABLED: '机构系统维护中，暂时无法借款，请稍后再试',
  INSTITUTION_SERVICE_SHUT_DOWN: '机构系统维护中，暂时无法借款，请稍后再试',
  HAS_OVERDUE: '当前有逾期中的借款，请先结清欠款',
  HAS_ON_GOING_CREDIT_CLOSE_ORDER: '服务关闭中，无法借款',
  SUPPLEMENT_DATA_VALID_EXCEPTION: '补充资料校验异常',
  SUPPLEMENT_DATA_IS_NULL: '补充资料不能为空',
  CONTRACT_PERSON_CAN_NOT_BE_SELF: '紧急联系人不能为本人信息',
  ON_GOING_CONTRACT_MORE_THAN_UPPER_LIMIT: '借款数量超限，暂无法再借一笔',
  QUOTA_STATUS_FROZEN: '授信状态异常，无法借款',
  L_USER_ACCOUNT_ILLEGAL: '授信状态异常，无法借款',
  L_ELEMENT_CONSULT_FAIL: '系统开小差，暂无法借款',
  NO_AVAILABLE_QUOTA: '无可用额度',
  LOAN_SCHEMA_VALID_EXCEPTION: '系统开小差，请稍后重试',
  LOAN_TRIAL_VALID_EXCEPTION: '系统开小差，请稍后重试',
  BANK_CARD_VERIFICATION_FAILED: '您提交的银行卡信息与您的身份信息或预留手机号不一致，请核对后重试',
  QUANTITY_MORE_THAN_LIMIT: '您绑定的银行卡数量超限',
  BANK_CARD_ALREADY_EXIST: '该银行卡已绑定',
  INST_NOT_SUPPORT_CHANGE_MASTER_CARD: '抱歉，暂不支持切换主卡',
  CURRENT_CARD_ALREADY_MASTER_CARD: '当前卡已为主卡',
  CHANGE_MASTER_CARD_SYSTEM_ERROR: '系统开小差，请稍后重试',
  EXCEED_UPPER_LIMIT_LOAN_QUOTA: '借款金额不能超过20万',
  ONLY_ONE_CARD_CANNOT_UNBIND: '您当前仅绑定了一张银行卡，添加新卡后可解绑',
  MASTER_CARD_CANNOT_UNBIND: '主卡不允许解绑，请先更换主卡',
  UNBIND_AUTH_TASK_CHECK_FAILED: '系统开小差，请稍后重试',
  UNBIND_CARD_LOAN_UNSETTLED_CHECK_FAILED: '您有借款未结清，请先结清',
  INST_NOT_SUPPORT_UNBIND_CARD: '合作机构反馈无法解绑银行卡，请联系机构客服处理',
  CONTRACT_PERSON_NAME_DUPLICATE: '联系人姓名不能重复',
  CONTRACT_PERSON_PHONE_DUPLICATE: '联系人手机号不能重复',
  CONTRACT_PERSON_PHONE_VIRTUAL: '手机号不正确',
  CONTRACT_PERSON_PHONE_CAN_NOT_BE_SELF: '联系人手机号不能为本人手机号',
  CONTRACT_PERSON_PHONE_NUMBER_NOT_ELEVEN: '手机号格式不正确',
  CONTRACT_PERSON_PHONE_LAST_EIGHT_DIGITS_EQUAL: '请提供正确的手机号',
  CONTRACT_PERSON_PHONE_LAST_EIGHT_DIGITS_EQUAL_TO_SELF: '手机号不正确',
  CONTACT_PERSON_PHONE_CAN_NOT_BE_MASTER_CARD_PHONE: '联系人手机号不能为银行卡预留手机号',
  L_ADMIT_CONSULT_FAIL: '服务逐步开放中',
  IDENTIFY_VALIDITY_DATE_ABNORMAL: '身份证国徽页非本人证件，请重新上传',
};

export const CUSTOMER_UPDATE_PROFILE_ERROR_CODE = {
  ONGOING_CUSTOMER_PROFILE_CHANGE: '更新中，请耐心等待',
  OLD_LICENSE_EFFECTIVE: '您的身份证在有效期内，无需更新',
  LICENSE_NO_CAN_NOT_CHANGE: '您的身份信息不一致，无法更新',
  LICENSE_NAME_CAN_NOT_CHANGE: '您的身份信息不一致，无法更新',
  IDENTIFY_VALIDITY_DATE_ABNORMAL: '身份证国徽页非本人证件，请重新上传',
  PHONE_NO_SAME: '新手机号与当前手机号一致，无需更新',
  CHANGE_IDENTITY_CARD_NOT_SUPPORTED: '暂不支持更新身份证',
  CHANGE_PHONE_NOT_SUPPORTED: '暂不支持更新手机号',
  CHANGE_IDENTITY_CARD_NOT_SUPPORTED_BEFORE_FIRST_LOAN: '暂不支持更新身份证，若需要请在首次借款后更新',
  A00001: '系统开小差，请稍后重试',
  A08002: '信息异常，无法更新',
  A08003: '新手机号与当前手机号一致，无需更新',
  A08004: '手机号已被占用，请重新提交',
  A08005: '检测到当前手机号不是您本人的，请重新提交',
  A08006: '信息异常，无法更新',
  A08007: '信息异常，无法更新',
  A08009: '身份证更新失败，请重试',
};

export const BIND_CARD_APPLY_ERROR_CODE = {
  A00001: '您提交的银行卡信息异常，请重试',
  A06001: '您的身份信息异常，无法绑卡',
  A06002: '您的短信验证过于频繁，请稍后重试',
  A06003: '银行系统维护中，请稍后重试',
  A06004: '该卡状态或信息异常，请换卡重试',
  A06005: '持卡人信息有误，请换卡重试',
  A06006: '不支持此银行，请换卡重试',
  A06007: '您的短信验证次数超限',
  A06008: '该银行卡已绑定',
  QUANTITY_MORE_THAN_LIMIT: '您绑定的银行卡数量超限',
  BANK_CARD_ALREADY_EXIST: '该银行卡已绑定',
};

export const BIND_CARD_CONFIRM_ERROR_CODE = {
  A00001: '您提交的银行卡信息异常，请重试',
  A06003: '银行系统维护中，请稍后重试',
  A06004: '该卡状态或信息异常，请换卡重试',
  A06005: '持卡人信息有误，请换卡重试',
  A06006: '不支持此银行，请换卡重试',
  A06008: '该银行卡已绑定',
  A06009: '短信验证码已失效，请重新获取',
  A06010: '短信验证码有误',
  A06011: '暂不支持该类型银行卡，请绑定您本人的一类储蓄卡',
  A06012: '您提供的银行预留手机号错误，请重新提供',
  A06013: '您的短信验证次数超限',
  A06014: '合作机构反馈无法解绑银行卡，请联系机构客服处理',
  SMS_CODE_TIMEOUT: '短信验证码已失效，请重新获取',
};

export const UNBIND_APPLY_ERROR_CODE = {
  ONLY_ONE_CARD_CANNOT_UNBIND: '您当前仅绑定了一张银行卡，添加新卡后可解绑',
  MASTER_CARD_CANNOT_UNBIND: '主卡不允许解绑，请先更换主卡',
  UNBIND_AUTH_TASK_CHECK_FAILED: '系统异常，请稍后重试',
  UNBIND_CARD_LOAN_UNSETTLED_CHECK_FAILED: '您有借款未结清，请先结清',
  INST_NOT_SUPPORT_UNBIND_CARD: '合作机构反馈无法解绑银行卡，请联系机构客服处理',
  A00001: '系统异常，请稍后重试',
  A40401: '系统异常，请稍后重试',
  A41201: '您有借款未结清，请先结清',
  A40901: '主卡不允许解绑，请先更换主卡',
  A41202: '您当前仅绑定了一张银行卡，添加新卡后可解绑',
  A50001: '解绑失败，请稍后重试',
  OTHER: '解绑失败，请稍后重试',
  SYSTEM_UNBIND_CARD_ERROR: '解绑失败，请稍后重试',
  INST_UNBIND_CARD_FAILED: '合作机构反馈无法解绑银行卡，请联系机构客服处理',
};

export const CREDIT_SUPPLEMENT_ERROR_CODE = {
  FAIL_BIZ_4310012: '与当前绑定支付宝认证信息不一致',
  FAIL_BIZ_4310011: '您名下实名的账号数量已达上限，请注销不常用账号后重新提交认证',
  FAIL_BIZ_4310016: '您名下实名的账号数量已达上限',
  FAIL_BIZ_4400001: '认证进行中，请稍后重试',
  // FAIL_BIZ_4400010: '您的账号已认证，无需重复认证',
  FAIL_BIZ_4300021: '您的姓名或身份证信息有误，请重新提交',
  FAIL_BIZ_4300022: '您的姓名或身份证信息有误，请重新提交',
  FAIL_BIZ_4300023: '身份证格式错误，请填写正确身份证号码',
  FAIL_BIZ_4300024: '姓名不能为空，请填写您的姓名',
  FAIL_BIZ_SUPPLEMENT_DATA_NOT_COMPLETE: '补充资料不完整',
  FAIL_BIZ_IDENTIFY_WILL_BE_EXPIRED_IN_30_DAYS: '您上传的身份证即将过期，请重新上传',
  FAIL_BIZ_IDENTIFY_EXPIRED: '您上传的身份证已过期，请重新上传',
  default: '认证异常，请刷新后重试',
};

export const CREDIT_APPLY_ERROR_CODE = {
  CUSTOMER_TYPE_IS_NOT_PERSONAL: '抱歉，您当前不满足服务使用条件',
  LICENSE_TYPE_IS_NOT_ID_CARD: '抱歉，您当前不满足服务使用条件',
  AGE_LESS_THAN_18: '抱歉，您未满18周岁',
  AGE_MORE_THAN_60: '抱歉，您当前不满足服务使用条件',
};

export const SSDcreditLpWaitingSeconds = 15;

export const SSDSimpleCreditLpWaitingSeconds = 15;

export const FAIL_BIZ_CREDIT_CONTRACT_NOT_EXIST_EXCEPTION = 'FAIL_BIZ_CREDIT_CONTRACT_NOT_EXIST_EXCEPTION';

export const HomeCsLink =
  'https://render.alipay.com/p/yuyan/180020010001253760/index.html?caprMode=sync&scene=app_ssd_sycjwt';

export const KFLOGO_LIGHT =
  'https://gw.alicdn.com/imgextra/i2/O1CN01Z24CVt1PhsqERPwyz_!!6000000001873-2-tps-112-112.png';

export const KFLOGO_BLACK =
  'https://gw.alicdn.com/imgextra/i3/O1CN01CJ7Byr1T6Z4ODg218_!!6000000002333-2-tps-88-88.png';


export const yunCustomerServiceLink = 'https://ai.alimebot.taobao.com/intl/index.htm?from=An6wQFBOCr';

/** 首支一体 - 标准版 */
export const HOME_LOAN_CONSISTENT = 'HOME_LOAN_CONSISTENT';
/** 首支一体 - 自动聚焦输入框版 */
export const HOME_LOAN_CONSISTENT_V2 = 'HOME_LOAN_CONSISTENT_V2';
/** 首支一体 - 预填金额版 */
export const HOME_LOAN_CONSISTENT_V3 = 'HOME_LOAN_CONSISTENT_V3';

// 首支一体放款轮询时长（单位：s）
export const HOME_LOAN_POLL_TIME = 15;

// 首支一体支用类型
export const LOAN_VERSION_PLUS = [HOME_LOAN_CONSISTENT, HOME_LOAN_CONSISTENT_V2, HOME_LOAN_CONSISTENT_V3];

// 芭芭农场
export const BABANONGCHANG = 'babanongchang';

// 鸿蒙上传小于这个版本号拦截
export const HM_REJECT_UPLOAD_TB_VERSION = '10.49.10';
// 授信平台
export const CREDIT_PLATFORM = ['MAYI_ZHIXIN', 'ALIYUN'];
