/**
 * @file 金额输入框组件
 * <AUTHOR>
 */

import { forwardRef, useCallback, useImperativeHandle, useMemo, useRef } from 'react';
import { Input, Toast } from 'antd-mobile';
import { number, env } from '@ali/iec-dtao-utils';
import classNames from 'classnames';
import { log } from '@alife/dtao-iec-spm-log';
import { toString } from 'lodash-es';

import { Selector, NewAmountInput } from '@/components';
import { noop, isGreaterThan } from '@/utils';

import styles from './index.module.scss';
import { checkOneConfigGray } from '@/utils/storage';

interface AmountInputProps {
  label?: string;
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  recommends?: any;
  disabled?: boolean;
  inputMode?: 'numeric' | 'decimal';
  max?: string | number;
  autoFocus?: boolean;
  renderAppend?: () => React.JSX.Element;
}

export interface AmountInputRef {
  focus: () => void;
  blur: () => void;
}

function AmountInput(props: AmountInputProps, ref) {
  const {
    label, value = '', placeholder = '', recommends, disabled, inputMode = 'numeric',
    onChange = noop, renderAppend, max, autoFocus,
  } = props;
  const inputRef = useRef<any>();

  useImperativeHandle(ref, () => ({
    focus: () => {
      inputRef.current?.focus?.();
    },
    blur: () => {
      inputRef.current?.blur?.();
    },
  }));

  const unit = useMemo(() => {
    const { getNumber } = number;
    const numberValue = getNumber(value);
    if (numberValue === '--') {
      return '';
    }
    if (numberValue >= 10000 && numberValue < 100000) {
      return '万';
    } else if (numberValue >= 100000 && numberValue < 1000000) {
      return '十万';
    } else if (numberValue >= 1000000 && numberValue < 10000000) {
      return '百万';
    } else if (numberValue >= 10000000) {
      return '千万';
    }
    return '';
  }, [value]);

  const handleOnChange = useCallback((inputValue: string) => {
    if (inputValue) {
      // 仅数字的数字键盘模式
      if (inputMode === 'numeric') {
        // 非正整数
        if (!/^[1-9]\d*$/.test(inputValue)) {
          return;
        }
        // 首字母非0
        if (!value && (inputValue === '0')) {
          return;
        }
      }
      // 数字+小数点的数字键盘模式
      if (inputMode === 'decimal') {
        // 非正两位小数
        if (!/^\d+(\.\d{0,2})?$/.test(inputValue)) {
          return;
        }
        // 0开头的数字
        if (/^0\d\d*$/.test(inputValue)) {
          return;
        }
      }
      // 最大金额
      if (max && isGreaterThan(inputValue, max)) {
        Toast.show({
          content: `输入金额不能大于${max}元`,
        });
        onChange(toString(max));
        return;
      }
      onChange(inputValue);
    } else {
      onChange(inputValue);
    }
    log.addChangeLog('amount-input', {
      value: inputValue,
    });
  }, [onChange, inputMode, max, value]);

  const unitLine = useMemo(() => {
    return (
      <div className={styles.lineC}>
        <div className={styles.line}>
          <div className={classNames(styles.arrow, !unit && styles.arrowNone)}>
            <i className={styles.arrowC} />
            <i className={styles.arrowB} />
          </div>
        </div>
        {unit ? <i className={styles.unit}>{unit}</i> : null}
      </div>
    );
  }, [unit]);

  const handleRecommondChange = useCallback((val: string) => {
    onChange && onChange(val);
    log.addClickLog('amount-input-recommend', {
      value: val,
    });
    log.addChangeLog('amount-input', {
      value: val,
    });
  }, [onChange]);

  const handleOnClick = useCallback(() => {
    log.addClickLog('amount-input');
  }, []);

  const renderRecommonds = useMemo(() => {
    if (recommends?.length) {
      return (
        <Selector
          customClassName={classNames(styles.recommends, !unit && styles.padding)}
          options={recommends}
          onChange={handleRecommondChange}
          logKey="amount-input"
        />
      );
    }
    return null;
  }, [unit, recommends, handleRecommondChange]);

  if (checkOneConfigGray('useNumberInput')) {
    return (
      <NewAmountInput {...props} />
    );
  }

  return (
    <div className={classNames(styles.amountInput, disabled && styles.disabled, env.isIOS() && styles.ios)}>
      <div className={styles.labelWrap}>
        <span className={styles.label}>
          {label}
        </span>
      </div>
      <div className={styles.inputContainer}>
        <i className={styles.yuan} />
        <Input
          onChange={handleOnChange}
          onClick={handleOnClick}
          className={styles.input}
          value={value}
          placeholder={placeholder}
          disabled={disabled}
          ref={inputRef}
          inputMode={inputMode}
          autoFocus={autoFocus}
          clearable
        />
        {renderAppend ? renderAppend() : null}
      </div>
      {unitLine}
      {renderRecommonds}
    </div>
  );
}

export default forwardRef(AmountInput);
