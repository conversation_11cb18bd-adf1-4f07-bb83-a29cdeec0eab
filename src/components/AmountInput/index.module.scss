.amountInput {
  :global(.adm-input) {
    --background-color: transparent !important;
    --placeholder-color: #bfbfbf;
    padding: 0 0 0 0 !important;
    min-height: 72rpx;
  }
  :global(.adm-input-element) {
    color: #111;
    font-family: 'ALIBABA NUMBER FONT MD';
    height: 72rpx;
  }
  .labelWrap {
    .label {
      color: #7c889c;
      font-size: 24rpx;
      line-height: 36rpx;
    }
  }
  .lineC {
    .line {
      position: relative;
      height: 1rpx;
      background-color: #cacfd7;
      .arrow {
        position: absolute;
        top: -18rpx;
        left: 68rpx;
        .arrowC {
          position: absolute;
          left: 50%;
          transform: translate(-50%,0);
          border-left: 10rpx solid transparent;
          border-right: 10rpx solid transparent;
          border-top: 10rpx solid transparent;
          border-bottom: 10rpx solid #cacfd7;
        }
        .arrowB {
          position: absolute;
          top: 1rpx;
          left: 50%;
          transform: translate(-50%,0);
          border-left: 11rpx solid transparent;
          border-right: 11rpx solid transparent;
          border-top: 11rpx solid transparent;
          border-bottom: 11rpx solid white;
        }
      }
      .arrowNone {
        display: none;
      }
    }
    .unit {
      display: inline-block;
      font-size: 20rpx;
      line-height: 20rpx;
      color: #7c889c;
      font-style: normal;
      margin-left: 50rpx;
    }
  }
  .inputContainer {
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    .yuan {
      font-size: 36rpx;
      font-weight: 500;
      color: #111;
      display: inline-flex;
      align-items: flex-end;
      line-height: 1;
      margin-right: 10rpx;
      margin-left: 10rpx;
      padding-bottom: 16rpx;
    }
    .input {
      --font-size: 70rpx;
      --color: #111;
    }
  }
  .recommends {
    padding-top: 6rpx;
    div {
      &:last-child {
        p {
          font-size: 26rpx;
        }
      }
    }
  }
  .padding {
    padding-top: 28rpx;
  }
  input::-webkit-input-placeholder {
    position: relative;
    font-size: 32rpx;
    top: -2rpx;
  }
  .yuan {
    position: relative;
    display: block;
    background-image: url('https://gw.alicdn.com/imgextra/i2/O1CN01wdj4d31UCLf7sS7wY_!!6000000002481-2-tps-200-200.png');
    background-repeat: no-repeat;
    background-position: 50% 50%;
    background-size: cover;
    width: 19rpx;
    height: 36rpx;
    top: -20rpx;
  }
}

.ios {
  input::-webkit-input-placeholder {
    position: relative;
    font-size: 32rpx;
    top: 15rpx;
  }
}
