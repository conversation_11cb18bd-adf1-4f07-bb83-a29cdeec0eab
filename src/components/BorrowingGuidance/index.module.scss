.borrowing-guidance {
  padding: 24rpx 32rpx 12rpx;
  border-radius: 24rpx;
  // background: linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, #fff 100%);
  // border: 1rpx solid #fff;
  position: fixed;
  left: 50%;
  width: calc(100% - 32rpx);
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;

  &.fixed-170 {
    bottom: 170rpx;
  }

  &.fixed-200 {
    bottom: 200rpx;
  }

  .bar-img {
    width: 213rpx;
    height: 44rpx;
  }

  .button-wrap {
    margin-top: 12rpx;
    display: flex;
    justify-content: center;
    .button {
      display: flex;
      align-items: center;
      font-size: 26rpx;
      font-weight: normal;
      line-height: normal;
      color: #50607a;
      .arrow-img {
        width: 24rpx;
        height: 24rpx;
      }
    }
  }
}

.guide-img {
  width: 100%;
}