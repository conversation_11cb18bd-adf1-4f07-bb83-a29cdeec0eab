import { Fragment, useEffect } from 'react';
import { log } from '@alife/dtao-iec-spm-log';
import { BABANONGCHANG } from '@/common/constant';
import { getOrigin } from '@/utils';

import styles from './index.module.scss';
import classNames from 'classnames';

interface BorrowingGuidanceProps {
  isPlatformAgreementSigned: boolean;
}

const BorrowingGuidance = (props: BorrowingGuidanceProps) => {
  const { isPlatformAgreementSigned } = props;
  // const popupRef = useRef<any>(null);

  useEffect(() => {
    if (getOrigin() !== BABANONGCHANG) return;

    log.addShowLog('borrowing-guidance-show');
  }, []);


  if (getOrigin() !== BABANONGCHANG) {
    return null;
  }

  return (
    <Fragment>
      <div className={classNames(styles['borrowing-guidance'], {
        [styles['fixed-200']]: isPlatformAgreementSigned,
        [styles['fixed-170']]: !isPlatformAgreementSigned,
      })}
      >
        <img src="https://gw.alicdn.com/imgextra/i3/O1CN010rAYlM1jneBzMWrFT_!!6000000004593-2-tps-426-88.png" className={styles['bar-img']} />
      </div>
    </Fragment>
  );
};

export default BorrowingGuidance;
