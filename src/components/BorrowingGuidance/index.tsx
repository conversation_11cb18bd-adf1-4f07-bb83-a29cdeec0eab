import { Fragment, useEffect, useRef } from 'react';
import { log } from '@alife/dtao-iec-spm-log';
import { ClientOnly } from 'ice';
import { BABANONGCHANG } from '@/common/constant';
import { CommonPopup } from '@/components';
import { getOrigin } from '@/utils';

import styles from './index.module.scss';
import classNames from 'classnames';

interface BorrowingGuidanceProps {
  isPlatformAgreementSigned: boolean;
}

const BorrowingGuidance = (props: BorrowingGuidanceProps) => {
  const { isPlatformAgreementSigned } = props;
  const popupRef = useRef<any>(null);

  useEffect(() => {
    if (getOrigin() !== BABANONGCHANG) return;

    log.addLog('borrowing-guidance-show', 'show');
  }, []);

  const renderGuidePopup = () => {
    return (
      <CommonPopup ref={popupRef} title="只需两步，轻松找到“借钱”">
        <div className={styles.container}>
          <img src="https://gw.alicdn.com/imgextra/i1/O1CN01MY15kd1GNt3K8paWA_!!6000000000611-2-tps-1500-1304.png" className={styles['guide-img']} />
        </div>
      </CommonPopup>
    );
  };

  const handleClick = () => {
    log.addLog('borrowing-guidance-btn-click', 'click');
    popupRef?.current?.toggleVisible(true);
  };

  if (getOrigin() !== BABANONGCHANG) {
    return null;
  }

  return (
    <Fragment>
      <div className={classNames(styles['borrowing-guidance'], {
        [styles['fixed-246']]: isPlatformAgreementSigned,
        [styles['fixed-218']]: !isPlatformAgreementSigned,
      })}
      >
        <img src="https://gw.alicdn.com/imgextra/i4/O1CN01gmaHsZ1r9ofTBuwYG_!!6000000005589-2-tps-1308-112.png" className={styles['bar-img']} />
        <div className={styles['button-wrap']}>
          <div className={styles.button} onClick={handleClick}>
            <span>查看指引</span>
            <img src="https://gw.alicdn.com/imgextra/i3/O1CN01claiif1roZuw46Ixn_!!6000000005678-2-tps-48-48.png" className={styles['arrow-img']} />
          </div>
        </div>
      </div>
      <ClientOnly>{renderGuidePopup}</ClientOnly>
    </Fragment>
  );
};

export default BorrowingGuidance;
