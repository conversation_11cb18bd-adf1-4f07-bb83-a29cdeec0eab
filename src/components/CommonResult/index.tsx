import { ReactNode } from 'react';
import './index.scss';

interface commonResultProps {
  status?: 'SUCCESS' | 'FAILED' | 'PROCESSING'; // 状态
  title?: string | ReactNode; // 标题
  description?: string | ReactNode; // 描述
  customImg?: ReactNode; // 自定义图片
  isSSD?: boolean;
  tips?: string;
}

const StatusImgSrcMap = {
  SUCCESS:
    'https://gw.alicdn.com/imgextra/i3/O1CN01RVY6vy1EDEYfZeBYn_!!6000000000317-2-tps-560-400.png',
  FAILED:
    'https://img.alicdn.com/imgextra/i3/O1CN012FqHSi1TCyZW4EpPv_!!6000000002347-2-tps-560-400.png',
  PROCESSING:
    'https://img.alicdn.com/imgextra/i3/O1CN01YNF7LL24fxPnBqmvz_!!6000000007419-2-tps-560-400.png',
};

const SsdStatusImgSrcMap = {
  SUCCESS:
    'https://gw.alicdn.com/imgextra/i3/O1CN01GvKPNa1bAF9NvFj3k_!!6000000003424-2-tps-560-400.png',
  FAILED:
    'https://gw.alicdn.com/imgextra/i1/O1CN019gNBXd1FpXJQmlBgi_!!6000000000536-2-tps-560-400.png',
  PROCESSING:
    'https://gw.alicdn.com/imgextra/i3/O1CN01lSr7q31X6LJgvhVA3_!!6000000002874-2-tps-560-400.png',
};


function CommonResult({
  status = 'PROCESSING',
  title = '',
  description = '',
  customImg,
  isSSD,
  tips = '',
}: commonResultProps) {
  const customImgSrc = isSSD ? SsdStatusImgSrcMap[status] : StatusImgSrcMap[status];
  return (
    <div className="common-result">
      {customImg || (
        <img src={customImgSrc} className="common-result-img" />
      )}
      <div className="common-result-title">{title}</div>
      {tips ? <p className="common-result-tips">{tips}</p> : null}
      {description && (
        <div className="common-result-description">{description}</div>
      )}
    </div>
  );
}

export default CommonResult;
