/**
 * @file 通用鸿蒙拦截弹窗
 * <AUTHOR>
 */

import { ExclamationCircleFill } from 'antd-mobile-icons';
import { isHarmonyOS } from '@/utils/bridges';
import { Modal } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';

import styles from './index.module.scss';

function HmRejectAlert() {
  return (
    <div className={styles.hmRejectAlert}>
      <ExclamationCircleFill
        style={{
          fontSize: '72rpx',
          color: 'var(--adm-color-primary)',
        }}
      />
      <div className={styles.hmRejectAlertTitle}>
        <div>
          <span className={styles.highLight}>服务暂未对鸿蒙系统开放</span>
        </div>
        <div>请使用安卓或者苹果IOS</div>
      </div>
    </div>
  );
}

function addHmRejectLog(scene: string) {
  log.addShowLog('hm-reject-log', {
    scene,
  });
}

export const showHmRejectAlert = () => {
  return Modal.alert({
    content: <HmRejectAlert />,
    confirmText: '我知道了',
  });
};


export const checkHmRejectAlert = (scene: string) => {
  if (isHarmonyOS() === true) {
    addHmRejectLog(scene);
    showHmRejectAlert();
    return true;
  }
  return false;
};
