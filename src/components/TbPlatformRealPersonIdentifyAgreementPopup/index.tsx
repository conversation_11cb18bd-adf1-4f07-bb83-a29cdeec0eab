/**
 * @file 淘宝平台账户实名认证协议
 */
import { forwardRef, useEffect, useRef } from 'react';
import { CommonPopup } from '@/components';
import LinkUtil from '@/utils/link';
import styles from './index.module.scss';

export default forwardRef((props, ref) => {
  const containerRef = useRef(null);

  // 处理容器内的点击事件
  const handleClick = (e) => {
    // 阻止默认行为
    e.preventDefault();

    // 获取点击的目标元素
    let { target } = e;

    // 向上查找，检查是否点击了 a 标签或其子元素
    while (target && target !== containerRef.current) {
      if (target.tagName.toLowerCase() === 'a') {
        // 获取 href 属性
        const url = target.getAttribute('href');

        // 如果有 URL，则使用 navigatorToOutside 打开新 webview
        if (url) {
          LinkUtil.pushPage(url);
          return;
        }
        break;
      }
      target = target.parentNode;
    }

    // 如果不是 a 标签或没有 href，则不执行任何操作
  };

  // 添加点击事件监听器
  useEffect(() => {
    const container: any = containerRef.current;
    if (container) {
      container.addEventListener('click', handleClick);
    }

    // 清理函数
    return () => {
      if (container) {
        container.removeEventListener('click', handleClick);
      }
    };
  }, []);

  return (
    <>
      <CommonPopup ref={ref} title="淘宝平台账户实名认证协议">
        <div className={styles.container} ref={containerRef}>
          <div dangerouslySetInnerHTML={{ __html: agreementHTMLStr }} />
        </div>
      </CommonPopup>
    </>
  );
});

export const agreementHTMLStr = `
<div class="agreementpreview agr-content"><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: right;"><strong><span style="font-size: 14px;color: rgb(51, 51, 51)">&nbsp;</span></strong><strong><span style="font-size: 14px;color: rgb(51, 51, 51)">最新</span></strong><span style="font-size: 14px;color: rgb(51, 51, 51)">版本生效日期：2024年10月17日</span></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: right;"><span style="font-size: 14px;color: rgb(51, 51, 51)">&nbsp;</span></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;"><span style="font-size: 14px;color: rgb(51, 51, 51)">请您务必仔细阅读本协议的全部内容（特别是以<strong><span style="text-decoration:underline;">粗体下划线</span></strong>标注的条款，<span>包括但不限于</span>免除或者限制我们责任的条款、对您的权利进行限制的条款、司法管辖的条款等）。<strong><span style="text-decoration:underline;">在充分理解相关内容后再慎重决定是否确认并同意本协议。在您主动勾选、点击同意或以其他方式确认本协议后，本协议将对您发生法律约束力</span></strong>。<strong><span style="text-decoration:underline;">如您对本协议有任何疑问，可向淘宝平台客服咨询，以便我们为您进行解释。届时您不应以未阅读本协议的内容或者未获得对您咨询的解答等理由，主张本协议无效或要求撤销。如果您不同意本协议的任意内容，或者无法准确理解我们对条款的解释，请不要进行任何后续操作。</span></strong><strong><span style="text-decoration:underline;"><br><br></span></strong></span></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;"><span style="font-size: 14px;color: rgb(51, 51, 51)">为符合相关法律法规（包括《中华人民共和国网络安全法》《中华人民共和国数据安全法》《中华人民共和国个人信息保护法》《中华人民共和国未成年人保护法》《未成年人网络保护条例》等）对平台提供服务过程中的用户身份信息管理相关要求，您同意淘宝平台经营者（以下简称“淘宝”或“我们”）收集您的必要身份信息，用于完成您当前淘宝平台账户（以下简称“账户”）的实名认证。我们也将严格按照本协议向您提供基于账户身份信息认证的相关服务。</span></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;"><span style="font-size: 14px;color: rgb(51, 51, 51)">&nbsp;</span></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;"><strong><span style="font-size: 14px;color: rgb(51, 51, 51)">一、服务目的和方式</span></strong></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;"><span style="font-size: 14px;color: rgb(51, 51, 51)">1.1 </span><span style="font-size: 14px;color: rgb(51, 51, 51)">为完成您当前账户的实名认证，您需要<strong><span style="text-decoration:underline;" data-spm-anchor-id="a1zaa.8161610.0.i0.7cc66bb2iVggeB">向我们提供您本人真实有效的姓名、身份证件类型和证件号码等信息（具体以页面提示为准）。您保证提供的所有信息的真实性、合法性、准确性和有效性，不采取任何违法违规、不正当的手段使用本服务。</span></strong>我们将与相关国家权威可信身份认证机构合作进行信息核验，并依法记录、保存您在完成实名认证流程中所提供的信息及认证结果。</span></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;"><span style="font-size: 14px;color: rgb(51, 51, 51)">1.2 </span><span style="font-size: 14px;color: rgb(51, 51, 51)">为了保障您的个人信息及重要权益，您的当前账户仅限绑定您本人的支付宝账户。如果您的当前账户已绑定支付宝账户，<strong><span style="text-decoration:underline;">我们需要向支付宝提供您在当前账户实名认证过程中提供的证件类型、证件号码用于校验</span></strong>，如身份不一致的，您可能无法完成当前账户的实名认证。</span></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;"><span style="font-size: 14px;color: rgb(51, 51, 51)">1.3 </span><span style="font-size: 14px;color: rgb(51, 51, 51)">如您是未满十四周岁的未成年人，请您在监护人的陪同、指导下阅读本协议，并在确保监护人同意本协议内容后使用服务。</span></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;"><span style="font-size: 14px;color: rgb(51, 51, 51)">1.4 </span><span style="font-size: 14px;color: rgb(51, 51, 51)">本协议下的个人信息均是您自愿提供。您有权拒绝提供，但如果您拒绝提供全部或部分个人信息，您将可能无法完成实名认证。</span></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;"><span style="font-size: 14px;color: rgb(51, 51, 51)">&nbsp;</span></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;"><strong><span style="font-size: 14px;color: rgb(51, 51, 51)">二、认证信息使用场景</span></strong></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;"><strong><span style="text-decoration:underline;"><span style="font-size: 14px;color: rgb(51, 51, 51)">您同意我们仅在淘宝平台相关的以下场景使用您的认证信息（包括姓名、证件类型、证件号码、年龄、性别、认证结果等）：</span></span></strong></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;"><strong><span style="font-size: 14px;color: rgb(51, 51, 51)">（1）根据法律法规相关要求</span></strong><strong><span style="font-size: 14px;background-color: white">需确定您必要身份的场景，包括依法保护未成年人权益、打击电信网络诈骗、为您扣缴税费、行政执法或司法诉讼中相关主体认定，</span></strong><strong><span style="font-size: 14px;color: rgb(51, 51, 51)">或按行政、司法机关依法提出的要求；</span></strong></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;"><strong><span style="font-size: 14px;color: rgb(51, 51, 51)">（2）</span></strong><strong><span style="font-size: 14px;background-color: white">为了确保我们是在为您本人提供服务，我们可能会根据您提供的认证信息校验您的身份；</span></strong></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;"><strong><span style="font-size: 14px;color: rgb(51, 51, 51)">（3）<span style="text-decoration:underline;">您主动选择使用认证信息的场景以及平台向您提供相关权益和服务；</span></span></strong></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;"><strong><span style="font-size: 14px;color: rgb(51, 51, 51)">（4）<span style="text-decoration:underline;">您主动同意并明确授权我们向其他第三方提供认证信息。</span></span></strong></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;"><span style="font-size: 14px;color: rgb(51, 51, 51)">&nbsp;</span></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;"><strong><span style="font-size: 14px;color: rgb(51, 51, 51)">三、个人信息权利保障</span></strong></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;"><span style="font-size: 14px">3.1 </span><span style="font-size: 14px">根据相关法律的规定，一旦您完成认证，相应的身份信息和认证结果将不因任何原因被修改或取消；如果您的姓名在完成认证后发生了变更，您可通过认证页面提交经过公安机关审核后的姓名，我们将协助核实并变更认证信息。</span></p><p style=";font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap"><span style="font-size: 14px">3.2 </span><span style="font-size: 14px">如果您需要行使您的其他个人信息权利，可通过淘宝平台客服与我们取得联系。</span></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;"><strong><span style="font-size: 14px">&nbsp;</span></strong></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;"><strong><span style="font-size: 14px">四、信息存储与安全</span></strong></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify"><span style="font-size: 14px">4.1 </span><span style="font-size: 14px">我们在中华人民共和国境内保存</span><span style="font-size: 14px;color: rgb(51, 51, 51)">您认证时提交给我们的认证资料</span><span style="font-size: 14px">。我们会采用行业领先的方案保障技术服务的安全、正常运行，严格设定信息访问权限，防止个人信息遭到未经授权访问、公开披露、使用、修改、损坏或丢失，尽力确保您的个人信息安全。</span></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify"><span style="font-size: 14px">4.2 </span><span style="font-size: 14px">除非经您另行单独同意，或者法律法规有明确要求或本协议有明确约定，或者因发生诉讼、仲裁、行政调查或刑事执法等依法需要确认您身份的必要情形，我们不会向任何第三方提供您在实名认证过程中向我们提供的信息。</span></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify"><span style="font-size: 14px">4.3 </span><span style="font-size: 14px">我们只会在达成本协议所述目的所需的期限内保留您的个人信息，除非法律有强制的留存要求。</span></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify"><span style="font-size: 14px">&nbsp;</span></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify"><strong><span style="font-size: 14px">五、特别声明</span></strong></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify"><span style="font-size: 14px">5.1 </span><span style="font-size: 14px">请您知悉，<strong><span style="text-decoration:underline;">由于网络用户身份识别存在一定技术复杂性，因此，我们对完成认证的用户身份的绝对准确性和真实性<span>不做保证</span>。</span></strong></span></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;"><span style="font-size: 14px;color: rgb(51, 51, 51)">5.2 </span><span style="font-size: 14px;color: rgb(51, 51, 51)">此外，<strong><span style="text-decoration:underline;">淘宝无需为以下情况承担责任，</span></strong><strong>法律另有规定的除外</strong>：</span></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;"><strong><span style="text-decoration:underline;"><span style="font-size: 14px;color: rgb(51, 51, 51)">（1）由于您本人将账户密码告知他人、未妥善保管好账户密码或与他人分享账户等非淘宝过错导致的账户认证信息泄露；</span></span></strong></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;"><strong><span style="text-decoration:underline;"><span style="font-size: 14px;color: rgb(51, 51, 51)">（2）由于黑客攻击、计算机病毒入侵、电信部门技术调整导致的影响，因政府管制而造成的暂时性关闭，由于不可抗力及其他非因淘宝过错而造成的认证信息泄露、丢失、被盗用或被篡改等；</span></span></strong></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;"><strong><span style="text-decoration:underline;"><span style="font-size: 14px;color: rgb(51, 51, 51)">（3）由于您自身提供错误、不完整、不实信息等造成无法通过认证或由此引发的损失或后果。</span></span></strong></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify"><strong><span style="text-decoration:underline;"><span style="font-size: 14px;color: rgb(51, 51, 51)">5.3</span></span></strong><strong><span style="text-decoration:underline;"><span style="font-size: 14px;color: rgb(51, 51, 51)">如果我们接到投诉或我们发现您违反法律法规、本协议约定的，我们有权视情况对您的账号采取<span>包括但不限于</span>暂停淘宝平台服务、取消认证等措施，或者要求您限期改正、补充资料、修改账号名称，并有权按照相关规定追究您的法律责任。</span></span></strong></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify"><strong><span style="text-decoration:underline;"><span style="font-size: 14px;color: rgb(51, 51, 51)">&nbsp;</span></span></strong></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify"><strong><span style="font-size: 14px">六、协议范围及变更</span></strong></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify"><strong><span style="text-decoration:underline;"><span style="font-size: 14px">6.1</span></span></strong><strong><span style="text-decoration:underline;"><span style="font-size: 14px"><span>本协议内容包括协议正文及所有淘宝平台已经发布或后续发布</span>并依法通知您的相关规则、说明等（以下统称“规则”）。前述规则均为本协议不可分割的一部分，与协议正文具有同等法律效力。</span></span></strong></p><p style="margin: 7px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;line-height: 24px"><span style="font-size: 14px">6.2 </span><span style="font-size: 14px">请您理解，在为您提供服务的过程中，<strong><span style="text-decoration:underline;">淘宝可根据法律法规变化、用户权益保护等需要不时修改本协议，并依法以网站提前公示的方式进行公告，相关变更一经生效即作为本协议的重要组成部分。</span></strong><a name="_Hlk174025024" style="color: rgb(0, 0, 0); text-decoration: underline;"><span style="font-size: 14px; color: rgb(0, 0, 0);">如您不同意相关变更，您有权于变更事项确定的生效日前联系我们反馈意见。如反馈意见得以采纳，我们将酌情调整变更事项。</span></a><strong><span style="text-decoration:underline;">如您对已生效的变更事项仍不同意的，应当于变更事项确定的生效之日起停止使用淘宝平台相关服务，变更事项对您不产生效力，但如您继续使用相关服务将被视为接受并同意变更后的协议。</span></strong></span></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify"><span style="font-size: 14px">&nbsp;</span></p><p style="margin: 7px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;line-height: 24px"><strong><span style="font-size: 14px">七、争议解决</span></strong></p><p style="margin: 7px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify;line-height: 24px"><span style="font-size: 14px;color: rgb(51, 51, 51)">与淘宝平台账户实名认证服务有关的争议，由我们与您协商解决。<strong>协商不成时，任何一方均可向被告所在地有管辖权的人民法院提起诉讼。</strong></span></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify"><span style="font-size: 12px;font-family:微软雅黑,Microsoft YaHei,宋体,SimSun,黑体, SimHei;Microsoft YaHei', sans-serif">&nbsp;</span></p><p style="margin: 5px 0;font-size: 16px;font-family:宋体,SimSun;color: rgb(0, 0, 0);text-wrap: wrap;text-align: justify"><strong><span style="font-size: 14px">八、其他</span></strong></p><p><strong style="color: rgb(0, 0, 0);font-size: medium;text-wrap: wrap"><span style="text-decoration:underline;"><span style="font-size: 14px;font-family:微软雅黑,Microsoft YaHei,宋体,SimSun,黑体, SimHei;">本协议作为</span></span></strong><span style="color: rgb(0, 0, 0);text-wrap: wrap;font-size: 16px;font-family:宋体,SimSun;"><a href="https://terms.alicdn.com/legal-agreement/terms/TD/TD201609301342_19559.html?spm=a2145.7268393.0.0.f9aa5d7cyFvg2j" style="color: rgb(5, 99, 193)"><strong><span style="font-size: 14px;font-family:微软雅黑,Microsoft YaHei,宋体,SimSun,黑体, SimHei;Microsoft YaHei', sans-serif;color: black">《淘宝平台服务协议》</span></strong></a></span><strong style="color: rgb(0, 0, 0);font-size: medium;text-wrap: wrap"><span style="text-decoration:underline;"><span style="font-size: 14px;font-family:微软雅黑,Microsoft YaHei,宋体,SimSun,黑体, SimHei;">的补充，相关定义与</span></span></strong><span style="color: rgb(0, 0, 0);text-wrap: wrap;font-size: 16px;font-family:宋体,SimSun;"><a href="https://terms.alicdn.com/legal-agreement/terms/TD/TD201609301342_19559.html?spm=a2145.7268393.0.0.f9aa5d7cyFvg2j" style="color: rgb(5, 99, 193)"><strong><span style="font-size: 14px;font-family:微软雅黑,Microsoft YaHei,宋体,SimSun,黑体, SimHei;Microsoft YaHei', sans-serif;color: black">《淘宝平台服务协议》</span></strong></a></span><strong style="color: rgb(0, 0, 0);font-size: medium;text-wrap: wrap"><span style="text-decoration:underline;"><span style="font-size: 14px;font-family:微软雅黑,Microsoft YaHei,宋体,SimSun,黑体, SimHei;">保持一致。</span></span></strong><span style="color: black;text-wrap: wrap;font-size: 14px;font-family:微软雅黑,Microsoft YaHei,宋体,SimSun,黑体, SimHei;">本协议未约定之内容，以《淘宝平台服务协议》约定为准；本协议与《淘宝平台服务协议》不一致者，以本协议为准。</span></p><p><br></p></div>`;
