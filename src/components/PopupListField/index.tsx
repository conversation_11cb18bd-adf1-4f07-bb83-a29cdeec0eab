/**
 * @File 表单面板
 * <AUTHOR>
 */

import React, { useCallback, useRef } from 'react';
import classNames from 'classnames';

import { CheckList, CheckListProps } from '../CheckList';
import ArrowIcon from '../ArrowIcon';
import FieldPanel from '../FieldPanel';
import CommonPopup from '../CommonPopup';

import styles from './index.module.scss';
import { log } from '@alife/dtao-iec-spm-log';

interface PopupListFieldProps {
  value?: Option;
  onChange?: (value: Option) => void;
  placeholder?: string;
  renderPanel?: (value?: Option) => React.JSX.Element;
  className?: string;
  logKey?: string;
  valueClassName?: string;
  checkListProps?: CheckListProps;
  popupProps: {
    title: string;
    position?: 'right' | 'bottom';
    bodyClassName?: string;
  };
}

export default function PopupListField(props: PopupListFieldProps) {
  const popupRef = useRef<any>();
  const {
    value, popupProps, placeholder, checkListProps, className, valueClassName,
    logKey = 'popup-list', renderPanel, onChange,
  } = props;

  const handlePopupShow = useCallback(() => {
    popupRef?.current?.toggleVisible(true);
    log.addClickLog(`${logKey}-open`);
  }, [logKey]);

  const handlePopupClose = useCallback(() => {
    popupRef?.current?.toggleVisible(false);
    log.addClickLog(`${logKey}-close`);
  }, [logKey]);

  const checkRight = () => {
    if (popupProps?.position === 'right') {
      return true;
    }
    return false;
  };

  return (
    <div className={classNames(styles.popupListField, className)}>
      <div className={styles.panel} onClick={handlePopupShow}>
        <FieldPanel<Option>
          value={value}
          placeholder={placeholder}
          className={valueClassName}
          renderPanel={renderPanel}
        />
        <ArrowIcon className={styles.icon} />
      </div>
      <CommonPopup
        ref={popupRef}
        {...popupProps}
        onClose={handlePopupClose}
        onMaskClick={handlePopupClose}
        bodyClassName={classNames(checkRight() && styles.right)}
        transparentMask={checkRight()}
      >
        <div className={styles.container}>
          <CheckList
            {...checkListProps}
            handleChangeClick={handlePopupClose}
            onChange={onChange}
            value={value}
            logKey={logKey}
          />
        </div>
      </CommonPopup>
    </div>
  );
}
