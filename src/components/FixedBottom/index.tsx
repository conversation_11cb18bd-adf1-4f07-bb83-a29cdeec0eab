/**
 * @file 底部固定区域（带安全组件）
 * <AUTHOR>
 */

import React, { useCallback, useEffect, useState } from 'react';
import { SafeArea } from 'antd-mobile';
import classNames from 'classnames';

import { resize } from '@/utils/life-cycle';
import { isAndroidOrisHarmonyOS } from '@/utils/env';

import styles from './index.module.scss';

interface FixedBottomProps {
  children: React.JSX.Element;
  className?: string;
  useResize?: boolean;
}

export default function FixedBottom(props: FixedBottomProps) {
  const { children, className, useResize = false } = props;
  const [isReduce, setReduce] = useState(false);

  const toggle = useCallback(() => {
    setReduce((cur) => {
      return !cur;
    });
  }, []);

  useEffect(() => {
    if (useResize && isAndroidOrisHarmonyOS()) {
      resize(toggle, toggle);
    }
  }, []);

  return (
    <div className={classNames(
      styles.fixedBottom,
      isReduce && styles.staticBottom,
      className && className,
    )}
    >
      <div className={styles.children}>
        {children}
      </div>
      <SafeArea position="bottom" />
    </div>
  );
}
