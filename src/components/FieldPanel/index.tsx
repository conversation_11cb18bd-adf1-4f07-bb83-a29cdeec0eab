/**
 * @file 支持field展示用的
 * <AUTHOR>
 */

import React from 'react';
import { isEmpty, isNumber, isObject, isString, toString, get } from 'lodash-es';
import classNames from 'classnames';
import { Ellipsis } from 'antd-mobile';

import styles from './index.module.scss';

interface FieldPanelProps<V> {
  value?: V;
  placeholder?: string;
  className?: string;
  renderPanel?: (value?: V) => React.JSX.Element | null;
}

export default function FieldPanel<V>(props: FieldPanelProps<V>) {
  const { value, placeholder, className, renderPanel } = props;

  if ((!value || isEmpty(value)) && placeholder) {
    return (
      <Ellipsis
        className={classNames(styles.text, styles.placeholder, className && className)}
        content={placeholder}
      />
    );
  }
  if (renderPanel) {
    return (
      <div className={classNames(className && className)}>
        {renderPanel(value)}
      </div>
    );
  }
  if (isString(value) || isNumber(value)) {
    return (
      <Ellipsis
        className={classNames(styles.text, className && className)}
        content={toString(value)}
      />
    );
  }
  if (isObject(value) && get(value, 'label')) {
    return (
      <Ellipsis
        className={classNames(styles.text, className && className)}
        content={toString(get(value, 'label'))}
      />
    );
  }
  return <div className={classNames(className && className)} />;
}
