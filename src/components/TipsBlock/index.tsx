/**
 * @file 重要提示
 */

import classNames from 'classnames';
import styles from './index.module.scss';

interface TipsBlockProps {
  children?: any;
  className?: string;
}

export default function TipsBlock(props: TipsBlockProps) {
  const { children, className } = props;

  return (
    <div className={classNames(styles.tips, className && className)}>
      <div className={styles.hd}>
        <i className={styles.icon} />
        <p className={styles.title}>
          温馨提示
        </p>
      </div>
      {children}
    </div>
  );
}
