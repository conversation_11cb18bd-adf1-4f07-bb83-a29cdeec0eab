.tips {
  margin-top: 80rpx;
  border-radius: 12rpx;
  padding: 24rpx;
  background: linear-gradient(270deg, #daebff 0%, #f4f8ff 100%);
  .hd {
    display: flex;
    flex-direction: row;
    align-items: center;
    .title {
      font-size: 30rpx;
      line-height: 45rpx;
      color: var(--primary);
      padding-left: 8rpx;
    }
    .icon {
      display: block;
      background-image: url('https://gw.alicdn.com/imgextra/i3/O1CN01VIVoCq29sqdwZJtGv_!!6000000008124-2-tps-80-80.png');
      background-repeat: no-repeat;
      background-size: contain;
      width: 40rpx;
      height: 40rpx;
    }
  }
}
