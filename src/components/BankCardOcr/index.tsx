/**
 * @file 调起摄像头或相册上传图片
 * <AUTHOR>
 */

import { useCallback, useRef, forwardRef } from 'react';
import { Button, Toast } from 'antd-mobile';
import classNames from 'classnames';
import { get } from 'lodash-es';
import { log } from '@alife/dtao-iec-spm-log';

import Popup from '../CommonPopup';
import UploadCell from '@/components/NewUploadCell';
import { ocrBankCard, fileFactoryReadUrl } from '@/store/common/actions';
import { PermissionKeyType } from '@ali/uniapi-permission';
import { PRIVACY_ID } from '@/common/constant';

import styles from './index.module.scss';

interface UploadImageProps {
  className?: string;
  title?: string;
  product: string;
  tenant: string;
  emptyRender: () => React.JSX.Element;
  onSuccess: (data: any) => void;
  onFail: (data?: any) => void;
}

export const BindCardOcr = forwardRef((props: UploadImageProps) => {
  const { emptyRender, onSuccess, onFail } = props;
  const commonPopupRef = useRef<any>();
  const toggleCommonPopupVisible = () => {
    commonPopupRef.current?.toggleVisible(false);
  };
  const {
    className,
    title = '图片上传',
    product,
    tenant,
  } = props;

  const handleUploadSuccess = useCallback(async (image: any) => {
    log.addSuccessLog('bank-card-ocr-file');
    commonPopupRef.current?.toggleVisible(false);
    try {
      const readUrlRes = await fileFactoryReadUrl({
        product,
        sourceScene: 'FRONT_END',
        fileFactoryNo: image?.fileFactoryNo,
      });
      const res = await ocrBankCard({
        product,
        tenant,
        fileNo: image?.fileFactoryNo,
        fileServerFileName: readUrlRes?.url,
      });
      if (res) {
        const { bank, cardNo } = res;
        if (bank && cardNo) {
          log.addSuccessLog('bank-card-ocr');
          onSuccess({
            cardNo,
            bank,
          });
          return;
        }
      }
      throw new Error('银行卡识别失败');
    } catch (e) {
      log.addErrorLog('bank-card-ocr');
      onFail({
        code: e?.message,
      });
    }
  }, [onSuccess, onFail, product, tenant]);

  const onProcess = () => {
    // setLoadingVisible(true);
  };

  const handleChangeVisible = () => {
    log.addClickLog('band-card-ocr-panel');
    commonPopupRef?.current.toggleVisible(true);
  };

  const handleUploadError = (error: unknown) => {
    const errorCode = get(error, 'errorCode');
    log.addErrorLog('band-card-ocr-upload', {
      error,
    });
    if (errorCode === 'OVER_SIZE') {
      Toast.show({
        content: '图片大小超出限制，请重新上传',
      });
    }
    if (errorCode === 'UPLOAD_ERROR') {
      Toast.show({
        content: '图片上传服务繁忙，请稍后再试',
      });
    }
  };

  return (
    <div className={`${className}`}>
      <div className={styles.uploadImgWrap} onClick={handleChangeVisible}>
        {emptyRender()}
      </div>
      <Popup ref={commonPopupRef} title={title}>
        <div className={styles.UploadCellsWrap}>
          <UploadCell
            className={styles.uploadItem}
            onUploadError={handleUploadError}
            onUploadSuccess={handleUploadSuccess}
            mode="photo"
            onProcess={onProcess}
            needHarmonyOSCompatible
            privacyID={PRIVACY_ID}
            permissionType={PermissionKeyType.READ_IMAGES}
          >
            <i className={classNames(styles.icon, styles.images)} />
            从相册上传
          </UploadCell>
          <UploadCell
            className={styles.uploadItem}
            onUploadError={handleUploadError}
            onUploadSuccess={handleUploadSuccess}
            mode="camera"
            onProcess={onProcess}
            needHarmonyOSCompatible
            privacyID={PRIVACY_ID}
            permissionType={PermissionKeyType.TAKE_CAMERA}
          >
            <i className={classNames(styles.icon, styles.camera)} />
            拍照
          </UploadCell>
        </div>
        <div>
          <Button onClick={toggleCommonPopupVisible} block className="cancel-button">
            取消
          </Button>
        </div>
      </Popup>
    </div>
  );
});
