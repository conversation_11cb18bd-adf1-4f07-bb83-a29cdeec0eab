import classnames from 'classnames';
import styles from './index.module.scss';

interface UploadedImgRenderProps {
  type: string;
}

interface EmptyRenderProps {
  type: string;
}


export const uploadedImgRender = ({ type }: UploadedImgRenderProps) => {
  return (
    <div
      className={classnames([
        styles.uploadedImg,
        type === 'front' ? styles.uploadedImgFront : styles.uploadedImgBack,
      ])}
    >
      <div className={styles.uploadedImgContent}>
        <img src="https://gw.alicdn.com/imgextra/i4/O1CN01Zmo1Nu1K1gGJn1Kom_!!6000000001104-2-tps-72-72.png" />
        上传成功
      </div>
      <div className={styles.uploadItemCorner}>重新上传</div>
    </div>
  );
};

export const emptyRender = ({ type }): EmptyRenderProps => {
  return (
    <div
      className={classnames([
        styles.uploadEmpty,
        type === 'front' ? styles.emptyImgFront : styles.emptyImgBack,
      ])}
    >
      <img src="https://gw.alicdn.com/imgextra/i4/O1CN0170eqkV1VVPkQtTjYM_!!6000000002658-2-tps-144-144.png" />
      {type === 'front' ? '上传人像面' : '上传国徽面'}
    </div>
  );
};


export default {
  uploadedImgRender,
  emptyRender,
};
