/**
 * @file 方法
 */

import { AuthenticationQueryResponse } from '@/store/authentication/actions';
import { UN_INIT_STEP_STATUS } from './constant';
import { first, filter, includes } from 'lodash-es';

export function getCurrentStep(queryRes?: AuthenticationQueryResponse) {
  if (queryRes?.allSteps?.length) {
    const { allSteps } = queryRes;
    const initSteps = filter(allSteps, (step) => {
      if (step?.status) {
        const { status } = step;
        if (status && !includes(UN_INIT_STEP_STATUS, status)) {
          return true;
        }
      }
      return false;
    });
    if (initSteps?.length) {
      return first(initSteps);
    }
  }
  return null;
}
