/**
 * @file 阿里集团人脸
 * <AUTHOR>
 */

import { Button, Toast } from 'antd-mobile';
import { useCallback, useEffect, useRef, useState } from 'react';
import { log } from '@alife/dtao-iec-spm-log';

import { init, AuthenticationStepInfoVO, verify } from '@/store/authentication/actions';
import FixedBottom from '../../FixedBottom';
import LinkUtil from '@/utils/link';
import Agreement from './agreement';
import { PRODUCT, TENANT } from '@/common/constant';

import styles from './index.module.scss';

interface SkyEyeFaceProps {
  stepInfo: AuthenticationStepInfoVO;
  directly?: boolean;
  onSuccess: () => void;
}

function SkyEyeFace(props: SkyEyeFaceProps) {
  const { stepInfo, directly = false, onSuccess } = props;
  const [verified, setVerified] = useState(false);
  const faceRef = useRef({
    isClick: false,
  });

  const checkInitButtonDisable = () => {
    if (stepInfo?.status === 'SUCCESS' || stepInfo?.status === 'FAIL' || verified) {
      log.addOtherLog('sys-eye-face-disabled');
      return true;
    }
    return false;
  };

  const handleVisiblity = () => {
    if (document?.visibilityState === 'visible' || !document.hidden) {
      handlePageShow();
    }
  };

  const handleClick = useCallback((enterUrl: string) => {
    faceRef.current.isClick = true;
    log.addClickLog('sys-eye-face-click');
    LinkUtil.navigatorOpenURL(`${enterUrl}?successRedirect=finish`);
  }, []);

  const doVerify = async () => {
    try {
      if (stepInfo?.token && stepInfo?.method) {
        const verifyRes = await verify({
          product: PRODUCT,
          tenant: TENANT,
          stepToken: stepInfo?.token,
          authenticationMethod: stepInfo?.method,
        });
        if (verifyRes?.pass) {
          setVerified(true);
          log.addSuccessLog('sky-eye-face-verify');
          onSuccess();
        } else {
          throw new Error(`FACE_VERIFY_PASS_FALSE_${verifyRes.status}`);
        }
      } else {
        throw new Error('FACE_VERIFY_PARAMS_ERROR');
      }
    } catch (e) {
      log.addErrorLog('sky-eye-face-verify', { code: e.message });
      Toast.show({
        icon: 'fail',
        content: '认证失败，请重试',
      });
    }
  };

  const doInit = useCallback(async () => {
    try {
      if (
        stepInfo?.token &&
        stepInfo?.method &&
        stepInfo?.status !== 'SUCCESS' &&
        stepInfo?.status !== 'FAIL'
      ) {
        const initRes = await init({
          product: PRODUCT,
          tenant: TENANT,
          stepToken: stepInfo?.token,
          authenticationMethod: stepInfo?.method,
          authenticationChannel: stepInfo?.channel,
          forceInit: true,
        });
        if (initRes?.authenticationAction?.enterUrl) {
          log.addSuccessLog('sky-eye-face-init');
          handleClick(initRes?.authenticationAction?.enterUrl);
        } else {
          throw new Error('SKY_EYE_FACE_ERROR');
        }
      }
    } catch (e) {
      log.addErrorLog('sky-eye-face-init');
      Toast.show({
        icon: 'fail',
        content: '认证失败，请重试',
      });
    }
  }, [stepInfo, handleClick]);

  const handlePageShow = () => {
    log.addShowLog('sky-eye-face-back');
    const { isClick } = faceRef.current;
    if (isClick && !verified) {
      doVerify();
    }
  };


  useEffect(() => {
    log.addShowLog('sky-eye-face');
    if (directly) {
      doInit();
    }
  }, []);

  useEffect(() => {
    document.addEventListener('visibilitychange', handleVisiblity);
    return () => {
      document.removeEventListener('visibilitychange', handleVisiblity);
    };
  }, []);

  if (directly) {
    return null;
  }

  return (
    <div className={styles.skyEyeFace}>
      <p className={styles.title}>
        请确保为
        <span className={styles.bold}>{stepInfo?.context?.name}</span>
        本人操作
      </p>
      <div className={styles.content} />
      <div className={styles.panel}>
        <p className={styles.main}>为便于我们帮助当前业务核实确认您的真实身份，您需要同意</p>
        <p className={styles.sub}>向我们提交并授权我们加密保存您本人信息相关的图像</p>
        <p className={styles.sub}>向合法数据持有者核实您的身份</p>
        <p className={styles.sub}>业务服务提供方保存您提交的信息用于身份核验</p>
      </div>
      <FixedBottom className={styles.fix}>
        <>
          <div className={styles.check}>
            <Agreement />
          </div>
          <Button
            onClick={() => {
              log.addClickLog('auth-start-click');
              doInit();
            }}
            block
            disabled={checkInitButtonDisable()}
            color="primary"
          >
            开始认证
          </Button>
        </>
      </FixedBottom>
    </div>
  );
}


export default SkyEyeFace;
