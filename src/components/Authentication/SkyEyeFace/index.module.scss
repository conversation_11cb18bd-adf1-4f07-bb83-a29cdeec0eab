.skyEyeFace {
  .title {
    font-size: 40rpx;
    font-weight: 500;
    color: #111;
    padding-bottom: 52rpx;
    .bold {
      padding: 0 12rpx;
    }
  }
  .content {
    display: block;
    border-radius: 21rpx;
    overflow: hidden;
    background-image: url('https://img.alicdn.com/imgextra/i2/O1CN01Vzswq11D12OCHIMM7_!!6000000000155-2-tps-1372-1170.png');
    background-repeat: no-repeat;
    background-size: contain;
    width: 686rpx;
    height: 585rpx;
  }
  .panel {
    border-radius: 12rpx;
    padding: 16rpx;
    background: #f3f6f8;
    margin-top: 38rpx;
    .main {
      font-size: 26rpx;
      line-height: 39rpx;
      color: #111;
      padding-bottom: 24rpx;
    }
    .sub {
      font-size: 26rpx;
      line-height: 39rpx;
      color: #7c889c;
      padding-bottom: 24rpx;
      &:last-child {
        padding-bottom: 0;
      }
      &:before {
        content: '·';
        padding-right: 18rpx;
      }
    }
  }
}

.fix {
  background-color: #fff;
  .check {
    --agreement-name-font-size: 20rpx;
    padding-bottom: 8rpx;
    :global(.adm-checkbox .adm-checkbox-icon) {
      --icon-size: 24rpx;
    }
    .prefix {
      font-size: 20rpx;
      line-height: 30rpx;
      color: #7c889c;
    }
    .name {
      color: var(--primary);
      font-size: 20rpx;
      line-height: 30rpx;
    }
  }
}

.link {
  color: var(--primary);
}
