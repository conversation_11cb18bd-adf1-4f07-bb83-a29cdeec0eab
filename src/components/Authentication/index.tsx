/**
 * @file 核身组件
 * <AUTHOR>
 */

import { useCallback, useEffect, useRef, useState, forwardRef, useImperativeHandle } from 'react';
import { _ } from '@/utils';
import { Toast, Modal } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';
import { last } from 'lodash-es';

import { query, AuthenticationQueryResponse } from '@/store/authentication/actions';
import SkyEyeFace from './SkyEyeFace';
import LinkUtil from '@/utils/link';
import { PRODUCT, TENANT, ErrorMessageMap } from '@/common/constant';
import { getCurrentStep } from './lib';
import { STOP_STATUS } from './constant';

import styles from './index.module.scss';

interface AuthenticationProps {
  authenticationToken?: string | null;
  directly?: boolean;
  onSuccess: () => void;
  onFail: () => void;
}


function Authentication(props: AuthenticationProps, ref: any) {
  const [detail, setDetail] = useState<AuthenticationQueryResponse>();
  const { authenticationToken, directly, onSuccess, onFail } = props;
  const timerRef = useRef<any>({
    timer: null,
    times: 99,
  });
  const authenticationExecToken = useRef<any>(null);

  const handleFinished = useCallback((result: AuthenticationQueryResponse) => {
    switch (result?.status) {
      case 'EXPIRE':
      case 'FAIL':
        log.addErrorLog('authentication-failed');
        onFail();
        break;
      case 'SUCCESS':
        log.addSuccessLog('authentication-success');
        onSuccess();
        break;
      default: break;
    }
  }, [onFail, onSuccess]);

  const doQuery = useCallback(async (token?) => {
    const result = await query({
      product: PRODUCT,
      tenant: TENANT,
      authenticationToken: token || authenticationExecToken?.current || authenticationToken,
    });

    return result;
  }, [authenticationToken]);

  const hasNext = useCallback((queryRes?: AuthenticationQueryResponse) => {
    if (detail && queryRes) {
      const currentStep = getCurrentStep(detail);
      const nextStep = getCurrentStep(queryRes);
      if (currentStep && nextStep && currentStep?.token !== nextStep?.token) {
        return true;
      }
    }
    return false;
  }, [detail]);

  // 简易轮询
  const doPoll = useCallback(async () => {
    try {
      const queryRes = await doQuery();
      timerRef.current.times--;
      if (!timerRef.current.times) {
        throw new Error();
      }
      if (!_.includes(STOP_STATUS, queryRes?.status)) {
        timerRef.current.timer = setTimeout(() => {
          doPoll();
        }, 3000);
      } else if (hasNext(queryRes)) {
        // 下一步
        clearTimeout(timerRef.current.timer);
        timerRef.current.times = 99;
        setDetail(queryRes);
      } else {
        clearTimeout(timerRef.current.timer);
        handleFinished(queryRes);
        return;
      }
    } catch (e) {
      clearTimeout(timerRef.current.timer);
      throw e;
    }
  }, [doQuery, handleFinished, hasNext]);

  const doInit = useCallback(async (token?) => {
    try {
      if (token) {
        authenticationExecToken.current = token;
      }
      const queryRes = await doQuery(token);
      setDetail(queryRes);
      // loading.close();
      log.addSuccessLog('authentication-init-fetch');
      if (_.includes(STOP_STATUS, queryRes?.status)) {
        // loading.close();
        handleFinished(queryRes);
      } else if (_.isEmpty(queryRes)) {
        // loading.close();
        throw new Error();
      }
    } catch (e) {
      // loading.close();
      Toast.show({
        icon: 'fail',
        content: ErrorMessageMap.BUSY_DEFUALT,
      });
      log.addErrorLog('authentication-init-fetch');
    }
  }, [doQuery, handleFinished]);

  const handleStepSuccess = useCallback(async () => {
    try {
      log.addOtherLog('authentication-step-poll');
      await doPoll();
    } catch (e) {
      Modal.show({
        content: ErrorMessageMap.BUSY_DEFUALT,
        actions: [{
          key: 'retry',
          primary: true,
          onClick: LinkUtil.reload,
          text: '点击重试',
        }],
      });
    }
  }, [doPoll]);

  const renderStep = useCallback(() => {
    const currentStep = getCurrentStep(detail) || last(detail?.allSteps);
    if (currentStep) {
      const { stepCode } = currentStep;
      switch (stepCode) {
        case 'FACE_LOOSE_NO_REPLACE':
          return (
            <SkyEyeFace
              onSuccess={handleStepSuccess}
              stepInfo={currentStep}
              directly={directly}
            />
          );
        default:
          return null;
      }
    }
    return null;
  }, [detail, directly, handleStepSuccess]);

  useImperativeHandle(ref, () => ({
    exec: (token) => {
      setDetail({});
      doInit(token);
    },
  }));

  useEffect(() => {
    if (authenticationToken) {
      setDetail({});
      doInit();
    }
  }, [authenticationToken]);

  return (
    <div className={styles.authentication}>
      {renderStep()}
    </div>
  );
}


export default forwardRef(Authentication);
