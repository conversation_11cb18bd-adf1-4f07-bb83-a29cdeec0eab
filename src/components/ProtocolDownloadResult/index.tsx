import React, { useState, forwardRef, useImperativeHandle, useEffect } from 'react';
import { Button, Popup } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';

import styles from './index.module.scss';

const ProtocolDownloadResultPopup = forwardRef((_, ref) => {
  const [visible, setVisible] = useState(false);

  const popupContent = (
    <div className={styles.popupContent}>
      <div className={styles.icon} />
      <div className={styles.title}>申请成功</div>
      <div className={styles.subTitle}>协议预计会在30分钟内自动发送到你的邮箱请注意查收</div>
      <div className={styles.deSubTitle}>若未收到协议，请联系客服获取</div>
      <Button
        color="primary"
        shape="rounded"
        className={styles.mainBtn}
        onClick={() => setVisible(false)}
      >
        我知道了
      </Button>
    </div>
  );

  const toggle = () => {
    setVisible(true);
  };

  useEffect(() => {
    log.addVisitLog('ssd-signed-agreement-detail-download-result');
  }, []);

  useImperativeHandle(ref, () => ({
    toggle,
  }));

  return (
    <div className={styles.protocolDownloadPopup}>
      <Popup
        visible={visible}
        showCloseButton
        bodyClassName={styles.popupBody}
        onMaskClick={() => setVisible(false)}
        onClose={() => setVisible(false)}
      >
        {popupContent}
      </Popup>
    </div>
  );
});

export default ProtocolDownloadResultPopup;
