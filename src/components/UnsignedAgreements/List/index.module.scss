.list {
  position: relative;
  width: 100%;
  background-color: #fff;
  box-shadow: 0 -5rpx 2rpx -5rpx rgba(#000, 30%);
  padding: 24rpx 0;
  .container {
  }
  .overflow {
    max-height: 310rpx;
    overflow: auto;
  }
  .item {
    padding-bottom: 24rpx;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    .name {
      font-size: 26rpx;
      line-height: 26rpx;
      text-align: left;
    }
  }
  .current {
    .name {
      color: var(--primary);
    }
  }
  .firstItem {
    .name {
      margin-right: 155rpx;
    }
  }
  .action {
    position: absolute;
    right: -24rpx;
    top: 0;
    display: flex;
    flex-direction: row;
    font-size: 26rpx;
    line-height: 26rpx;
    text-align: right;
    color: #7c889c;
    align-items: center;
    padding: 24rpx;
    .icon {
      position: relative;
      display: inline-block;
      font-size: 10rpx;
      top: 0;
      background-repeat: no-repeat;
      background-size: contain;
      background-position: 50% 50%;
      width: 20rpx;
      height: 12rpx;
      margin-left: 16rpx;
    }
    .up {
      background-image: url('https://gw.alicdn.com/imgextra/i4/O1CN01AUin0428tvz7GG3AD_!!6000000007991-2-tps-353-200.png');
    }
    .down {
      background-image: url('https://gw.alicdn.com/imgextra/i3/O1CN01bWYLuw1spmXd0V2lm_!!6000000005816-2-tps-353-200.png');
    }
  }
  .currentItem {
    padding: 0;
  }
}
