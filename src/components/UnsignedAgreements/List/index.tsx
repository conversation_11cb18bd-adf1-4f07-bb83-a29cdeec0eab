/**
 * @file 可折叠面板
 */

import { useCallback, useState } from 'react';
import classNames from 'classnames';
import { Ellipsis } from 'antd-mobile';

import type { AgreementDTO } from '@/store/types';

import styles from './index.module.scss';

interface ListProps {
  className?: string;
  actionClassName?: string;
  currentIndex: number;
  options: AgreementDTO[];
  onChange: (index: number) => void;
}

export default function List(props: ListProps) {
  const { options = [], currentIndex = 0, className, actionClassName, onChange } = props;
  const [open, setOpen] = useState(false);

  const toggle = (visible: boolean) => {
    setOpen(visible);
  };

  const doHide = () => {
    toggle(false);
  };

  const doShow = () => {
    toggle(true);
  };

  const handleOnChange = useCallback((index: number) => () => {
    toggle(false);
    onChange(index);
  }, [onChange]);

  const renderAction = () => {
    if (open) {
      return (
        <p
          className={classNames(styles.action, actionClassName)}
          onClick={doHide}
        >
          收起
          <i className={classNames(styles.icon, styles.up)} />
        </p>
      );
    } else {
      return (
        <p className={classNames(styles.action, actionClassName)} onClick={doShow}>
          展开全部
          <i className={classNames(styles.icon, styles.down)} />
        </p>
      );
    }
  };

  const renderItem = (index: number, option?: AgreementDTO) => {
    if (option) {
      const useFirst = index === 0 && options?.length > 1;
      return (
        <div className={classNames(styles.item, useFirst && styles.firstItem, index === currentIndex && styles.current)}>
          <p
            className={styles.name}
            onClick={handleOnChange(index)}
          >
            <Ellipsis content={option.name || ''} />
          </p>
        </div>
      );
    }
    return null;
  };

  const renderCurrent = () => {
    if (open) {
      return null;
    }
    if (options && options[currentIndex]) {
      const option = options[currentIndex];
      const useFirst = options?.length > 1;
      return (
        <div className={classNames(styles.item, styles.current, useFirst && styles.firstItem, styles.currentItem)}>
          <p className={styles.name}>
            <Ellipsis content={option.name || ''} />
          </p>
        </div>
      );
    }
    return null;
  };

  const renderList = () => {
    if (options?.length && open) {
      return options.map((option, index) => {
        return renderItem(index, option);
      });
    }
    return null;
  };

  return (
    <div className={classNames(styles.list, className)}>
      {options?.length > 1 ? renderAction() : null}
      {renderCurrent()}
      <div className={classNames(styles.container, options?.length > 5 && styles.overflow)}>
        {renderList()}
      </div>
    </div>
  );
}
