/**
 * @file 协议组件-强制阅读
 * <AUTHOR>
 */

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button, Toast, DotLoading, Tabs } from 'antd-mobile';
import { map, forEach, concat, cloneDeep, first } from 'lodash-es';
import { useThrottleFn } from 'ahooks';
import { log } from '@alife/dtao-iec-spm-log';

import useCountDown from '@/hooks/useCountDown';
import type { UnsignedAgreementList } from '@/store/agreement/actions';
import { preview } from '@/store/agreement/actions';
import { AgreementDTO } from '@/store/types';
import { getMinReadTime } from '../format';
import List from '../List';
import { handleATarget } from '@/utils/hack';
import { ErrorMessageMap, INSTITUTION_NAME_MAP } from '@/common/constant';

import styles from './index.module.scss';

interface CompulsoryProps {
  bizType: string;
  options?: UnsignedAgreementList[];
  onChange?: (value: boolean) => void;
  onCompleted: () => void;
  params?: any;
  onError: any;
  changeTitle: (title: string) => void;
}

export default function Compulsory(props: CompulsoryProps) {
  const { options = [], bizType, params, onChange, onCompleted, onError, changeTitle } = props;
  // const [readed, setReaded] = useState(0);
  const [current, setCurrent] = useState<UnsignedAgreementList>();
  // 滚动、点击强制阅读需要用一份暂存的数据
  const currentItems = useRef<UnsignedAgreementList>({
    unSignedAgreementList: [],
  });
  const [currentIndex, setCurrentIndex] = useState(0);
  const [contents, setContents] = useState<AgreementDTO[]>();
  const { count, start } = useCountDown({
    duration: 1000,
  });
  const completedRef = useRef(false);

  // const hasNext = useMemo(() => {
  //   if (options?.length) {
  //     return readed !== options?.length - 1;
  //   }
  //   return false;
  // }, [options, readed]);

  // 协议中心协议查询
  const previewAgreementCenter = useCallback(async (agreements: AgreementDTO[]) => {
    try {
      const promiseList = agreements.map((item) => {
        return preview({
          bizType,
          code: item.code,
          source: item.source,
          institution: item.institution,
          name: item.name,
          params: params?.agreementCenter,
        });
      });
      const listResult = await Promise.all(promiseList);
      if (listResult?.length) {
        let agreementResults: AgreementDTO[] = [];
        forEach(listResult, (item) => {
          if (item?.previewAgreementList) {
            agreementResults = concat(agreementResults, item?.previewAgreementList);
          }
        });
        log.addSuccessLog('unsigned-agreement-compulsory-center-fetch', agreements);
        return {
          minReadTime: getMinReadTime(agreementResults),
          list: agreementResults,
        };
      }
      return {
        minReadTime: 0,
        list: [],
      };
    } catch (e) {
      Toast.show({
        content: ErrorMessageMap.BUSY_DEFUALT,
      });
      log.addErrorLog('unsigned-agreement-compulsory-center-fetch', agreements);
      onError && onError();
      return {
        minReadTime: 0,
        list: [],
      };
    }
  }, [bizType, params]);

  // 机构协议查询
  const previewInstitution = useCallback(async (agreement: AgreementDTO) => {
    try {
      if (agreement && agreement.institution) {
        const listResult = await preview({
          bizType,
          code: agreement.code,
          source: agreement.source,
          institution: agreement.institution,
          name: agreement.name,
          params: params?.institution,
        });
        if (listResult?.previewAgreementList) {
          log.addSuccessLog('unsigned-agreement-compulsory-institution-fetch', agreement);
          return {
            minReadTime: getMinReadTime(listResult?.previewAgreementList),
            list: listResult?.previewAgreementList,
          };
        }
      }
      return {
        minReadTime: 0,
        list: [],
      };
    } catch (e) {
      Toast.show({
        content: ErrorMessageMap.BUSY_DEFUALT,
      });
      log.addErrorLog('unsigned-agreement-compulsory-institution-fetch', agreement);
      onError && onError();
      return {
        minReadTime: 0,
        list: [],
      };
    }
  }, [bizType, params?.institution]);

  // 点击TAB切换协议，滚动到对应的协议内容下
  const handleTabOnChange = useCallback((index: number) => {
    const element = document.getElementById(`anchor-${index}`);
    setCurrentIndex(index);
    if (element) {
      element?.scrollIntoView();
    }
    log.addChangeLog('unsigned-agreement-compulsory-tab', {
      index,
    });
  }, []);

  // 请求到待强制阅读的协议内容后，需要设置默认的tab，同时把屏幕滚动到待阅读的协议下
  const handleActiveTabDefault = useCallback((agreementList: AgreementDTO[]) => {
    currentItems.current.unSignedAgreementList = cloneDeep(agreementList);
    setTimeout(() => {
      handleTabOnChange(0);
    }, 200);
  }, [handleTabOnChange]);

  const handleStart = () => {
    if (!completedRef.current) {
      start(5);
      completedRef.current = true;
    }
  };

  // 按协议中心协议、机构协议请求preview接口获取待强制阅读的协议列表内容
  const handlePreview = useCallback(async (item: UnsignedAgreementList) => {
    try {
      const { source, unSignedAgreementList } = item;
      if (unSignedAgreementList) {
        if (source === 'AGREEMENT_CENTER') {
          const centerList = await previewAgreementCenter(unSignedAgreementList);
          handleStart();
          setContents(centerList?.list);
          handleActiveTabDefault(centerList?.list);
        } else {
          const institutionList = await previewInstitution(unSignedAgreementList[0]);
          handleStart();
          setContents(institutionList?.list);
          handleActiveTabDefault(institutionList?.list);
        }
      }
    } catch (e) { }
  }, [start, previewAgreementCenter, previewInstitution, handleActiveTabDefault]);

  // 用户滚动协议，自动切换TAB
  const handleContainerScroll = useCallback(() => {
    const tabItems = currentItems?.current?.unSignedAgreementList;
    const container = document.getElementById('compulsory-container');
    if (container && tabItems && tabItems[0]) {
      const containerRect = container.getBoundingClientRect();
      let currentTabIndex = 0;
      for (let i = 0; i < tabItems?.length; i++) {
        const element = document.getElementById(`anchor-${i}`);
        if (!element || !container) continue;
        const elementRect = element.getBoundingClientRect();
        if (elementRect.top - containerRect.top <= 70) {
          currentTabIndex = i;
        } else {
          break;
        }
      }
      setCurrentIndex(currentTabIndex);
    }
  }, []);

  // 对handleContainerScroll进行节流
  const { run: handleScroll } = useThrottleFn(handleContainerScroll, {
    leading: true,
    trailing: true,
    wait: 100,
  });

  // 获取当前需要渲染的协议内容，同时根据是平台协议/机构协议请求preview接口获取协议内容
  const getCurrentAgreements = useCallback((index: number) => {
    const currentGroup = options[index];
    if (currentGroup?.institution) {
      changeTitle && changeTitle(INSTITUTION_NAME_MAP[currentGroup?.institution]);
    }
    if (currentGroup?.unSignedAgreementList) {
      currentItems.current = cloneDeep(currentGroup);
      handlePreview(currentGroup);
    }
  }, [options, handlePreview, changeTitle]);

  // 点击“阅读并同意协议内容”
  const handleRead = useCallback(() => {
    // setReaded((cur) => {
    //   return cur + 1;
    // });
    // if (readed + 1 === options.length || options.length === 0) {
    onChange && onChange(true);
    onCompleted();
    // }
  }, [onChange, onCompleted]);

  // 渲染顶部协议内容，因为会有倒计时，避免协议内容重新渲染加一个memo
  const renderContents = useMemo(() => {
    if (contents?.length) {
      return map(contents, (content, index) => {
        return (
          <div
            id={`anchor-${index}`}
            className={styles.content}
            key={`compulsory-agreement-${index}`}
            dangerouslySetInnerHTML={{ __html: content?.content }}
          />
        );
      });
    }
    return (
      <div className={styles.loading}>
        <DotLoading />
      </div>
    );
  }, [contents]);

  // 渲染顶部协议展示的tab，因为会有倒计时，避免tab重复渲染加一个memo
  const renderAgreementsTab = () => {
    if (contents?.length) {
      return (
        <List
          options={contents}
          currentIndex={currentIndex}
          onChange={handleTabOnChange}
        />
      );
    }
    return null;
  };

  // 强制阅读首次打开，开始对第一个机构的协议预览内容进行查询
  useEffect(() => {
    if (options?.length) {
      getCurrentAgreements(0);
      setCurrent(first(options));
    } else {
      onError && onError();
    }
  }, [options]);

  // 滑动协议内容切换TAB，需要监听页面scroll事件
  useEffect(() => {
    const dom = document.getElementById('compulsory-container');
    dom?.addEventListener('scroll', handleScroll);
    handleATarget('compulsory-container');
    return () => {
      dom?.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const renderTabs = () => {
    if (options?.length > 1 && current) {
      // 多机构
      return renderMulti();
    }
    // 异常
    return null;
  };
  // 渲染多机构协议
  const renderMulti = () => {
    if (options?.length) {
      return (
        <Tabs
          className={styles.tabs}
          defaultActiveKey={0}
          onChange={handleInstitutionChange}
        >
          {options?.map((item, index) => (
            <Tabs.Tab
              className={styles.tabItem}
              key={index}
              title={INSTITUTION_NAME_MAP[item?.institution || 'default']}
            />
          ))}
        </Tabs>
      );
    }
    return null;
  };

  // 切换机构
  const handleInstitutionChange = useCallback(
    (institution: number) => {
      // const lists = filter(options, { institution });
      // if (lists?.length === 1) {
      setCurrent(options[institution]);
      getCurrentAgreements(institution);
      // }
    },
    [options],
  );


  return (
    <div className={styles.agreements}>
      {renderTabs()}
      <div id="compulsory-container" className={styles.container}>
        {renderContents}
      </div>
      <div className={styles.fixedBottom}>
        {renderAgreementsTab()}
        <Button
          className={styles.read}
          color="primary"
          disabled={count < 0 || !contents?.length || count > 0}
          block
          onClick={handleRead}
        >
          同意以上协议{count ? `（${count}s）` : ''}
          {/* {hasNext ? '，下一个' : ''} */}
        </Button>
      </div>
    </div>
  );
}
