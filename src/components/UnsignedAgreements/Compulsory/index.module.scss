.tabs {
  :global(.adm-tabs-content) {
    --content-padding: 0;
  }
}

.container {
  height: 70vh;
  overflow: auto;
  background-color: #f3f6f8;
  padding: 32rpx;
  padding-bottom: 208rpx;
  .content {
    background-color: #fff;
    padding: 32rpx;
    border-radius: 12rpx;
    overflow: hidden;
    margin-top: 24rpx;
    &:first-child {
      margin-top: 0;
    }
    h1, h5, p, span, b, li {
      font-size: 24rpx !important;
      text-indent: unset !important;
    }
  }
}

.bottom {
  background-color: #fff;
  padding: 16rpx 32rpx;
  padding-bottom: 68rpx;
}

.fixedBottom {
  background-color: #fff;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 0 32rpx;
  padding-bottom: 42rpx;
}

.loading {
  margin: auto;
  text-align: center;
  padding-top: 50%;
}
