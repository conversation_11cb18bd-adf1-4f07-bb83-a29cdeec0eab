/**
 * @file 协议预览
 * <AUTHOR>
 */

import { useCallback, useEffect, useState } from 'react';
import { Tabs } from 'antd-mobile';
import { filter, first } from 'lodash-es';

import type { UnsignedAgreementList } from '@/store/agreement/actions';
import AgreementCenter from './AgreementCenter';
import AgreementInstitution from './AgreementInstitution';
import { INSTITUTION_NAME_MAP } from '@/common/constant';

import styles from './index.module.scss';

interface PreviewProps {
  bizType: string;
  options?: UnsignedAgreementList[];
  params?: any;
  currentIndex?: number;
}

export default function Preview(props: PreviewProps) {
  const { options = [], bizType, params, currentIndex = 0 } = props;
  const [current, setCurrent] = useState<UnsignedAgreementList>();

  // 切换机构
  const handleInstitutionChange = useCallback((institution: string) => {
    const lists = filter(options, { institution });
    if (lists?.length === 1) {
      setCurrent(lists[0]);
    }
  }, [options]);

  // 渲染单机构协议
  const renderPreview = () => {
    switch (current?.source) {
      case 'AGREEMENT_CENTER':
        return (
          <AgreementCenter
            bizType={bizType}
            options={current?.unSignedAgreementList}
            params={params}
            defaultIndex={currentIndex}
          />
        );
      case 'INSTITUTION':
        return (
          <AgreementInstitution
            bizType={bizType}
            options={current?.unSignedAgreementList}
            params={params}
            defaultIndex={currentIndex}
          />
        );
      default: return null;
    }
  };

  // 渲染多机构协议
  const renderMulti = () => {
    if (options?.length) {
      const defaultActiveKey = first(options)?.institution || 'default';
      return (
        <Tabs
          className={styles.tabs}
          defaultActiveKey={defaultActiveKey}
          onChange={handleInstitutionChange}
        >
          {options?.map((item) => (
            <Tabs.Tab
              className={styles.tabItem}
              key={item?.institution || 'default'}
              title={INSTITUTION_NAME_MAP[item?.institution || 'default']}
            />
          ))}
        </Tabs>
      );
    }
    return null;
  };

  const renderTabs = () => {
    if (options?.length > 1 && current) {
      // 多机构
      return renderMulti();
    }
    // 异常
    return null;
  };

  useEffect(() => {
    if (options) {
      setCurrent(first(options));
    }
  }, [options]);

  return (
    <div className={styles.agreements}>
      {renderTabs()}
      <div className={styles.preview}>
        {renderPreview()}
      </div>
    </div>
  );
}
