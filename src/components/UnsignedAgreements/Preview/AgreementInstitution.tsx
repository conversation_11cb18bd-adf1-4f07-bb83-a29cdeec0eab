/**
 * @file 机构协议预览
 * <AUTHOR>
 */

import { useCallback, useState, useEffect } from 'react';
import { Toast, DotLoading } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';

import type { AgreementDTO } from '@/store/types';
import { preview } from '@/store/agreement/actions';
import List from '../List';
import { ErrorMessageMap } from '@/common/constant';
import { handleATarget } from '@/utils/hack';

import styles from './index.module.scss';

interface AgreementInstitutionProps {
  bizType: string;
  // 外部的单个协议
  options?: AgreementDTO[];
  params?: any;
  defaultIndex?: number;
}

export default function AgreementInstitution(props: AgreementInstitutionProps) {
  const { options = [], bizType, params, defaultIndex = 0 } = props;
  const [agreementList, setAgreementList] = useState<AgreementDTO[]>([]);
  const [currentIndex, setCurrentIndex] = useState(defaultIndex);

  // 请求预览协议接口
  const handlePreview = useCallback(async (agreement?: AgreementDTO) => {
    try {
      if (agreement?.institution) {
        const result = await preview({
          bizType,
          code: agreement?.code,
          source: agreement?.source,
          institution: agreement?.institution,
          params: params?.institution,
        });
        const formatList = result?.previewAgreementList || [];
        setAgreementList(formatList);
        setCurrentIndex(defaultIndex);
        log.addSuccessLog('unsigned-agreement-preview-insitution-fetch');
      }
    } catch (e) {
      Toast.show({
        content: ErrorMessageMap.BUSY_DEFUALT,
      });
      log.addErrorLog('unsigned-agreement-preview-insitution-fetch', agreement);
    }
  }, [bizType, params, defaultIndex]);

  // 机构切换协议
  const handleOnChange = useCallback((index: number) => {
    const previewContainer = document.getElementById('agreement-institution');
    previewContainer?.scrollIntoView();
    setCurrentIndex(index);
    log.addChangeLog('unsigned-agreement-preview-insitution-tab', {
      index,
    });
  }, []);

  // 渲染协议内容
  const renderContent = useCallback((content?: string) => {
    if (content) {
      return (
        <div
          className={styles.content}
          dangerouslySetInnerHTML={{ __html: content }}
        />
      );
    }
    return (
      <div className={styles.loading}>
        <DotLoading />
      </div>
    );
  }, []);

  // 组件首次打开预览协议
  const doFirstPreview = useCallback(() => {
    if (options?.length) {
      handlePreview(options[defaultIndex]);
    }
  }, [options, defaultIndex, handlePreview]);

  useEffect(() => {
    doFirstPreview();
  }, [doFirstPreview]);

  useEffect(() => {
    handleATarget('agreement-institution');
  }, []);

  return (
    <div className={styles.agreementInstitution}>
      <div id="agreement-institution" className={styles.container}>
        {renderContent(agreementList[currentIndex]?.content)}
      </div>
      <List
        className={styles.fixed}
        actionClassName={styles.fixedAction}
        currentIndex={currentIndex}
        options={agreementList}
        onChange={handleOnChange}
      />
    </div>
  );
}
