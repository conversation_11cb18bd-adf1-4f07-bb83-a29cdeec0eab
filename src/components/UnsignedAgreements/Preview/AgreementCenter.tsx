/**
 * @file 协议中心协议预览
 * <AUTHOR>
 */

import { useCallback, useMemo, useState, useEffect } from 'react';
import { log } from '@alife/dtao-iec-spm-log';
import { DotLoading, Toast } from 'antd-mobile';

import type { AgreementDTO } from '@/store/types';
import { preview } from '@/store/agreement/actions';
import List from '../List';
import { ErrorMessageMap } from '@/common/constant';
import { handleATarget } from '@/utils/hack';

import styles from './index.module.scss';

interface PreviewProps {
  bizType: string;
  options?: AgreementDTO[];
  params?: any;
  defaultIndex?: number;
}

export default function AgreementCenter(props: PreviewProps) {
  const { options = [], bizType, params, defaultIndex = 0 } = props;
  const [previewAgreementList, setPreviewAgreementList] = useState<AgreementDTO[]>();
  const [currentIndex, setCurrentIndex] = useState(defaultIndex);

  // 请求预览协议接口
  const handlePreview = useCallback(async (agreement?: AgreementDTO) => {
    try {
      const result = await preview({
        bizType,
        code: agreement?.code,
        source: agreement?.source,
        institution: agreement?.institution,
        name: agreement?.name,
        params: params?.agreementCenter,
      });
      setPreviewAgreementList(result?.previewAgreementList);
      log.addSuccessLog('unsigned-agreement-preview-center-fetch');
    } catch (e) {
      Toast.show({
        content: ErrorMessageMap.BUSY_DEFUALT,
      });
      log.addErrorLog('unsigned-agreement-preview-center-fetch', agreement);
    }
  }, [bizType, params]);

  // 协议中心切换协议
  const handleOnChange = useCallback((index: number) => {
    const previewContainer = document.getElementById('agreement-center');
    previewContainer?.scrollIntoView();
    setCurrentIndex(index);
    handlePreview(options[index]);
    log.addChangeLog('unsigned-agreement-preview-center-tab', options[index]);
  }, [handlePreview, options]);

  // 渲染协议内容
  const renderContent = useMemo(() => {
    if (previewAgreementList?.length) {
      return previewAgreementList.map((agreement) => {
        return (
          <div
            className={styles.content}
            dangerouslySetInnerHTML={{ __html: agreement?.content }}
          />
        );
      });
    }
    return (
      <div className={styles.loading}>
        <DotLoading />
      </div>
    );
  }, [previewAgreementList]);

  // 组件首次打开预览协议
  const doFirstPreview = useCallback(() => {
    if (options?.length) {
      setCurrentIndex(defaultIndex);
      handlePreview(options[defaultIndex]);
    }
  }, [options, defaultIndex, handlePreview]);

  useEffect(() => {
    doFirstPreview();
  }, [doFirstPreview]);

  useEffect(() => {
    handleATarget('agreement-center');
  }, []);

  return (
    <div className={styles.agreementCenter}>
      <div id="agreement-center">
        {renderContent}
      </div>
      <List
        className={styles.fixed}
        actionClassName={styles.fixedAction}
        onChange={handleOnChange}
        currentIndex={currentIndex}
        options={options}
      />
    </div>
  );
}
