.tabs {
  :global(.adm-tabs-content) {
    --content-padding: 0;
  }
  :global(.adm-tabs-tab-active) {
    --active-title-color: #111;
    font-size: 30rpx;
    font-weight: 600;
  }
  :global(.adm-tabs-tab) {
    --title-font-size: #50607a;
    font-size: 30rpx;
  }
  :global(.adm-tabs-tab-line) {
    --active-line-color: #111;
  }
  .tabItem {
    padding: 0 32rpx;
  }
}

.agreements {
  .preview {
    width: 100%;
    padding: 32rpx;
    padding-bottom: 168rpx;
    height: 60vh;
    background: #f3f6f8;
    overflow-y: auto;
    overflow-x: hidden;
  }
}

.agreementInstitution {
  .content {
    background-color: #fff;
    padding: 32rpx;
    border-radius: 12rpx;
    h1, h5, p, span {
      font-size: 24rpx !important;
      text-indent: unset !important;
    }
  }
}

.agreementCenter {
  .content {
    background-color: #fff;
    padding: 32rpx;
    border-radius: 12rpx;
    h1, h5, p, span {
      font-size: 24rpx !important;
      text-indent: unset !important;
    }
  }
}

.fixed {
  position: fixed;
  bottom: -5rpx;
  padding: 24rpx 32rpx;
  padding-bottom: 42rpx;
  left: 0;
  z-index: 999;
}

.fixedAction {
  right: 42rpx !important;
}

.fixedBottom {
  background-color: #fff;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 0 32rpx;
  padding-bottom: 42rpx;
}

.loading {
  margin: auto;
  text-align: center;
  padding-top: 50%;
}
