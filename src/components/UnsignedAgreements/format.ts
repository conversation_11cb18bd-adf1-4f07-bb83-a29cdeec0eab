/**
 * @file 格式化方法
 * <AUTHOR>
 */

import { AgreementDTO } from '@/store/types';
import { UnsignedAgreementList } from '@/store/agreement/actions';
import { number } from '@ali/iec-dtao-utils';
import { forEach, isUndefined, max } from 'lodash-es';

export function getAgreementNames(groups: UnsignedAgreementList[]) {
  const names: string[] = [];
  if (groups?.length) {
    forEach(groups, (group) => {
      if (group?.unSignedAgreementList?.length) {
        forEach(group.unSignedAgreementList, (agreement) => {
          if (agreement?.name) {
            names.push(`《${agreement?.name}》`);
          }
        });
      }
    });
  }
  return names;
}

export function getMinReadTime(list?: AgreementDTO[]) {
  const times: number[] = [];
  // TODO，是否需要一个兜底的强制阅读时间
  if (!list?.length) {
    return -1;
  }
  forEach(list, (item) => {
    const time = number.getNumber(item?.minReadTime);
    if (time !== '--') {
      times.push(time);
    }
  });
  const maxTime = max(times);
  if (isUndefined(maxTime)) {
    return -1;
  }
  return maxTime;
}
