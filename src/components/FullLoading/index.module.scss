.loading {
  position: fixed;
  z-index: 3000;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  .container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 240rpx;
    width: 240rpx;
    background-color: rgba(#000, 75%);
    border-radius: 32rpx;
    .icon {
      display: block;
      height: 72rpx;
      width: 72rpx;
      background-image: url('https://gw.alicdn.com/imgextra/i3/O1CN01k1GR4G1r6bqxbWoD0_!!6000000005582-2-tps-144-144.png');
      background-repeat: no-repeat;
      background-size: contain;
      animation: rotate 1s linear infinite;
      -webkit-animation: rotate 1s linear infinite;
    }
    .text {
      font-size: 28rpx;
      color: #fff;
      text-align: center;
      padding-top: 16rpx;
    }
  }
}

@keyframes rotate {
  100% {
    transform: rotate(0);
  }
  0% {
    transform: rotate(360deg);
  }
}

@-webkit-keyframes rotate {
  100% {
    transform: rotate(0);
  }
  0% {
    transform: rotate(360deg);
  }
}
