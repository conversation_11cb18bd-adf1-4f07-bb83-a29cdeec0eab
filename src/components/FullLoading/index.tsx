import styles from './index.module.scss';

interface FullLoadingProps {
  visible?: boolean;
  text?: string;
}

function FullLoading(props: FullLoadingProps) {
  const { visible = false, text = '加载中' } = props;

  return (
    visible ? (
      <div className={styles.loading}>
        <div className={styles.container}>
          <i className={styles.icon} />
          <p className={styles.text}>{text}</p>
        </div>
      </div>
    ) : null
  );
}

export default FullLoading;
