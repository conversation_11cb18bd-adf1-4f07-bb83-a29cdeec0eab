.address-selector-wrap {
  position: relative;
  display: flex;
  align-items: center;
  background: #f3f6f8;
  border-radius: 12rpx;
  .arrow-icon {
    font-size: 26rpx;
    color: #111;
    margin-right: 24rpx;
  }
}

.address-selector-popup-wrap {
  height: calc(80vh - 50rpx);
  display: flex;
  flex-direction: column;
  &::-webkit-scrollbar {
    display: none;
  }
  .address-options-title {
    font-size: 36rpx;
    font-weight: 500;
    color: #111;
    margin: 12rpx 0;
  }
  .address-option-item {
    height: 108rpx;
    display: flex;
    align-items: center;
    color: #111;
    .first-char {
      width: 20rpx;
      color: #7c889c;
      margin-right: 24rpx;
    }
  }
  .cur-address-wrap {
    padding-top: 12rpx;
    margin-bottom: 16rpx;
    .left-dots {
      display: inline-block;
      background: var(--primary);
      border-radius: 50%;
      width: 12rpx;
      height: 12rpx;
      margin-right: 24rpx;
    }
    .cur-address-item {
      display: flex;
      align-items: center;
      height: 108rpx;
      &:not(:last-child) {
        .left-dots {
          position: relative;
          &::after {
            content: "";
            display: block;
            position: absolute;
            left: 5rpx;
            top: 12rpx;
            width: 2rpx;
            height: 98rpx;
            background: var(--primary);
            transform: scaleX(0.5);
          }
        }
      }
      &.active {
        .left-dots {
          background: #fff;
          border: 1rpx solid var(--primary);
        }
        .cur-address-name {
          color: var(--primary);
        }
      }
      .cur-address-name {
        flex: 1 1 auto;
        font-size: 30rpx;
        color: #111;
      }
    }
  }
  .address-options {
    flex: 0 1 auto;
    overflow: scroll;
    padding-bottom: 80rpx;
    &::-webkit-scrollbar {
      display: none;
    }
  }
}

.address-selector-popup {
  .common-popup-content {
    height: 80vh;
  }
}

.address-popup-right {
  width: 100%;
  height: 1100rpx;
  bottom: 0;
  top: unset;
  .address-selector-popup-wrap {
    height: calc(1100rpx - 50rpx);
  }
}
