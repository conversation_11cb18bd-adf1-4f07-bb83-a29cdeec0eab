/**
 * @file 省市区选择组件
 */

import { useEffect, useRef, useState } from 'react';
import { Input } from 'antd-mobile';
import CommonPopup from '@/components/CommonPopup';
import ArrowIcon from '@/components/ArrowIcon';
import classnames from 'classnames';
import { _ } from '@/utils';
import { loadAddressScript } from '@/utils/hack';
import './index.scss';
import { cloneDeep, isEmpty } from 'lodash-es';

interface Names {
  EN: string;
  CN: string;
}

interface AddressOption {
  id: string;
  iso: string;
  leaf: boolean;
  level: number;
  names: Names;
  parentId: string;
  firstENChar: string;
  children: AddressOption[];
}

interface CurAddress {
  province?: CurAddress | AddressOption;
  city?: CurAddress | AddressOption;
  county?: CurAddress | AddressOption;
  children?: AddressOption[];
}

interface AddressSelectorProps {
  popupPosition?: 'bottom' | 'right';
  value?: any;
  onChange?: any;
}

const getCNName = (option) => {
  return _.get(option, 'names.CN');
};

const getValue = (option) => {
  return {
    value: option.id,
    label: getCNName(option),
  };
};

export default function AddressSelector(props: AddressSelectorProps) {
  const commonPopupRef = useRef<any>(null);
  const [curAddress, setCurAddress] = useState<CurAddress>({
    province: {},
    city: {},
    county: {},
  });
  const [addressData, setAddressData] = useState<any>([]);
  const [curOptions, setCurOptions] = useState<any>([]);
  const curLevel: number = _.get(_.first(curOptions), 'level', 0); // 取第一个level
  const { onChange, value, popupPosition } = props;

  const scrollToTop = () => {
    const AddressOptionsEl = document.querySelector('#AddressOptions');
    AddressOptionsEl && (AddressOptionsEl.scrollTop = 0);
  };

  const handleOptionClick = (option) => {
    if (option.level === 2) {
      setCurAddress({
        province: { ...option },
        city: {},
        county: {},
      });
      setCurOptions(option.children);
      scrollToTop();
    }
    if (option.level === 3) {
      setCurAddress({
        ...curAddress,
        city: { ...option },
        county: {},
      });
      setCurOptions(option.children);
      scrollToTop();
    }
    if (option.level >= 4) {
      const finalAddress = {
        ...curAddress,
        county: { ...option },
      };
      setCurAddress(finalAddress);
      commonPopupRef.current.toggleVisible(false);
      scrollToTop();
      onChange({
        province: getValue(finalAddress.province),
        city: getValue(finalAddress.city),
        county: getValue(finalAddress.county),
      });
    }
  };

  const handleWrapClick = () => {
    if (isEmpty(curOptions)) {
      return;
    }
    commonPopupRef.current.toggleVisible(true);
  };

  const renderCurAddress = () => {
    return (
      <div className="cur-address-wrap">
        <div
          onClick={() => {
            setCurOptions(addressData);
            scrollToTop();
          }}
          className={classnames(['cur-address-item', curLevel === 2 && 'active'])}
        >
          <div className="left-dots" />
          <div className="cur-address-name">{getCNName(curAddress.province)}</div>
          <ArrowIcon />
        </div>
        {curLevel >= 3 && (
          <div
            onClick={() => {
              setCurOptions(curAddress.province?.children);
              scrollToTop();
            }}
            className={classnames(['cur-address-item', curLevel === 3 && 'active'])}
          >
            <div className="left-dots" />
            <div className="cur-address-name">{getCNName(curAddress.city) || '请选择城市'}</div>
            <ArrowIcon />
          </div>
        )}
        {curLevel >= 4 && (
          <div
            onClick={() => {
              setCurOptions(curAddress.city?.children);
              scrollToTop();
            }}
            className={classnames(['cur-address-item', curLevel >= 4 && 'active'])}
          >
            <div className="left-dots" />
            <div className="cur-address-name">{getCNName(curAddress.county) || '请选择地区'}</div>
            <ArrowIcon />
          </div>
        )}
      </div>
    );
  };

  const renderSelectorTitle = () => {
    return _.get(
      {
        2: '选择省份',
        3: '选择城市',
        4: '选择区县',
        5: '选择区县',
      },
      String(curLevel),
    );
  };

  const addressReady = () => {
    const cdnAdresss = cloneDeep(window?.DTAO_IEC_ADDRESS || []);
    setAddressData(cdnAdresss);
    setCurOptions(cdnAdresss);
    // 清空掉
    window.DTAO_IEC_ADDRESS = null;
  };

  useEffect(() => {
    loadAddressScript(addressReady);
  }, []);

  useEffect(() => {
    const { province, city, county } = value || {};
    if (province && city && county && !isEmpty(addressData)) {
      const tgtProvinceOption = _.find(addressData, (i) => i.id === province.value);
      const tgtCityOption = _.find(tgtProvinceOption.children, (i) => i.id === city.value);
      const tgtCountyOption = _.find(tgtCityOption.children, (i) => i.id === county.value);
      setCurAddress({
        province: tgtProvinceOption,
        city: tgtCityOption,
        county: tgtCountyOption,
      });
      setCurOptions(tgtCityOption.children);
    }
  }, [value, addressData]);

  const { province, city, county } = value || {};

  const inputValue =
    province && city && county ? `${province.label} ${city.label} ${county.label}` : '';

  return (
    <div className="address-selector-wrap" onClick={handleWrapClick}>
      <Input
        value={inputValue}
        readOnly
        placeholder={isEmpty(curOptions) ? '加载中...' : '省、市、区'}
      />
      <ArrowIcon inner />
      <CommonPopup
        position={popupPosition}
        className="address-selector-popup"
        ref={commonPopupRef}
        title="请选择所在地区"
        bodyClassName={classnames(popupPosition === 'right' && 'address-popup-right')}
      >
        <div className="address-selector-popup-wrap">
          <div>
            {!_.isEmpty(curAddress.province) && (
              <>
                {renderCurAddress()}
                <div className="common-divide-line" />
              </>
            )}
            <div className="address-options-title">{renderSelectorTitle()}</div>
          </div>
          <div id="AddressOptions" className="address-options">
            {curOptions.map((option, index) => {
              const showENChar = curOptions[index - 1]?.firstENChar !== option.firstENChar;
              return (
                <div
                  key={option.id}
                  onClick={() => handleOptionClick(option)}
                  className="address-option-item"
                >
                  <span className="first-char">{showENChar && option.firstENChar}</span>
                  {getCNName(option)}
                </div>
              );
            })}
          </div>
        </div>
      </CommonPopup>
    </div>
  );
}
