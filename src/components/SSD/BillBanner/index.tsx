/**
 * @file 账单条
 */

import styles from './index.module.scss';
import { pushPage } from '@/utils/link';
import { PAGES } from '@/common/constant';
import { log } from '@alife/dtao-iec-spm-log';

import { ArrowIcon } from '@/components';
import { setPushPage } from '@/utils/session';

interface BillBannerProps {
  title: string;
  renderContent?: () => React.JSX.Element | null;
}

export default function BillBanner(props: BillBannerProps) {
  const { title = '查账还款', renderContent } = props;

  return (
    <div
      onClick={() => {
        log.addClickLog('ssd-home-repay-bill');
        setPushPage();
        pushPage(PAGES.SsdRepayBill);
      }}
      className={styles.billBanner}
    >
      <p className={styles.title}>{title}</p>
      <div className={styles.content}>
        {renderContent ? renderContent() : null}
        <ArrowIcon type="right-light" className={styles.arrowRight} />
      </div>
    </div>
  );
}
