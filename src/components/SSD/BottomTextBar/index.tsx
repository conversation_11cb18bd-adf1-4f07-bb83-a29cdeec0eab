
import { ClientOnly } from 'ice';
import { useRef, Fragment, useEffect } from 'react';

import CommonPopup from '@/components/CommonPopup';
import classNames from 'classnames';
import LinkUtil from '@/utils/link';
import { PAGES } from '@/common/constant';
import { log } from '@alife/dtao-iec-spm-log';
import { setPushPage } from '@/utils/session';

import styles from './index.module.scss';

export default function BottomTextBar(props: any) {
  const { hideCenter, customClassName, lightColor } = props;
  const popupRef = useRef<any>(null);
  const handleKnowMoreClick = () => {
    log.addClickLog('bottom-text-bar-know-more');
    popupRef?.current?.toggleVisible(true);
  };
  const handleMyCenterClick = () => {
    log.addClickLog('bottom-text-bar-my-center');
    setPushPage();
    LinkUtil.pushPage(PAGES.SsdCenter);
  };
  const Config = [
    {
      icon: 'https://gw.alicdn.com/imgextra/i1/O1CN01cpqf3v20ZJCz8qEJ7_!!6000000006863-2-tps-96-96.png',
      title: '利息按天算',
      desc: '利息用一天算一天，已还本金不再算利息',
    },
    {
      icon: 'https://gw.alicdn.com/imgextra/i3/O1CN018R5eyb1VET5ptju2N_!!6000000002621-2-tps-96-96.png',
      title: '灵活取用',
      desc: '可借款到银行卡，8秒钟到账',
    },
    {
      icon: 'https://gw.alicdn.com/imgextra/i1/O1CN01xNvwEm21APHmLQLgs_!!6000000006944-2-tps-96-96.png',
      title: '随时可还款',
      desc: (
        <div>
          <div>支持提前还，且无手续费</div>
          <div>还款日当天将从支付宝及其绑定的银行卡自动扣款</div>
        </div>
      ),
    },
    {
      icon: 'https://gw.alicdn.com/imgextra/i4/O1CN01ep8lb11INXwVakOPA_!!6000000000881-2-tps-96-96.png',
      title: '安全有保障',
      desc: '正规持牌机构服务，支付宝保障资金安全',
    },
  ];

  const renderPopup = () => {
    return (
      <CommonPopup ref={popupRef} title="了解随身贷">
        <div className={styles.container}>
          {Config.map((item, index) => {
            if (!item) return null;
            return (
              <div className={styles.rateDescItem} key={`bottom-text-bar-${index}`}>
                <div className={styles.left}>
                  <img src={item.icon} />
                </div>
                <div className={styles.right}>
                  <div className={styles.title}>{item.title}</div>
                  <div className={styles.desc}>{item.desc}</div>
                </div>
              </div>
            );
          })}
          <p className={styles.zhixinTitle}>蚂蚁智信及其合作伙伴为你提供服务</p>
        </div>
      </CommonPopup>
    );
  };

  useEffect(() => {
    log.addShowLog('bottom-text-bar', { hideCenter });
  }, []);

  return (
    <div className={classNames(styles.bottomTextBar, customClassName && customClassName, lightColor && styles.lightColor)}>
      <span className={styles.knowSsd} onClick={handleKnowMoreClick}>
        了解随身贷
        {hideCenter && (
          <img
            className={styles.knowArrowIcon}
            src="https://gw.alicdn.com/imgextra/i3/O1CN01ZaDvpb1e3K0uiI7Q1_!!6000000003815-2-tps-48-48.png"
          />
        )}
      </span>
      {!hideCenter && (
        <Fragment>
          <span className="common-divide-line-vertical"> </span>
          <span className={styles.center} onClick={handleMyCenterClick}>个人中心</span>
        </Fragment>
      )}
      <ClientOnly>{renderPopup}</ClientOnly>
    </div>
  );
}
