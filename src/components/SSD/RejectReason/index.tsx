/**
 * @file 拒绝说明
 */

import { useImperativeHandle, forwardRef, useRef } from 'react';
import CommonPopup from '@/components/CommonPopup';

import styles from './index.module.scss';

export interface RejectReasonRef {
  show: () => void;
}

export const RejectReason = forwardRef((props, ref) => {
  const popupRef = useRef<any>();

  const show = () => {
    popupRef.current?.toggleVisible(true);
  };

  useImperativeHandle(ref, () => ({
    show,
  }));

  return (
    <CommonPopup
      ref={popupRef}
      title="了解原因"
      contentClassName={styles.reasonPopupContent}
    >
      <div className={styles.block}>
        <div className={styles.title}>为什么无法借款？</div>
        <div className={styles.content}>
          提示“暂无法为你提供服务”，说明你暂未通过系统评估，暂时无法借款，系统会根据你的信用情况定期评估你的可用额度，建议持续保持良好的使用习惯。
        </div>
      </div>
      <div className={styles.block}>
        <div className={styles.title}>我什么时候能借款？</div>
        <div className={styles.content}>本次评估未通过也不用担心，建议你过段时间再尝试借款，可借款时我们将第一时间通知你。</div>
      </div>
    </CommonPopup>
  );
});
