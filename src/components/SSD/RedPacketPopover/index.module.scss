.redPacketPopoverWrap {
  position: relative;
  z-index: 0;
  .content {
    left: 50%;
    top: -50rpx;
    transform: translateX(-50%);
    position: absolute;
    display: flex;
    align-items: center;
    padding: 11rpx 24rpx;
    height: 50rpx;
    background-image: linear-gradient(0deg, #fffdfd 0%, #fff3f0 100%);
    color: #ff6430;
    border-radius: 8rpx;
    z-index: 2;
    box-sizing: border-box;
    font-size: 24rpx;
    white-space: nowrap;
    &::before {
      display: block;
      content: " ";
      position: absolute;
      background-image: url('https://gw.alicdn.com/imgextra/i4/O1CN01fzjkex1kUF0ecTWwK_!!6000000004686-2-tps-40-12.png');
      background-size: cover;
      width: 20rpx;
      height: 6rpx;
      bottom: -6rpx;
      left: 50%;
      transform: translateX(-50%);
      z-index: 1;
      box-sizing: content-box !important;
    }
  }
}

.redPacketIcon {
  margin-right: 8rpx;
  width: 28rpx;
  height: 28rpx;
  background-image: url('https://gw.alicdn.com/imgextra/i1/O1CN01X3oQb71qpCqnTqgZg_!!6000000005544-2-tps-56-56.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  flex-shrink: 0;
}

.redPacketArrow {
  width: 20rpx;
  height: 20rpx;
  margin-left: 8rpx;
}

.orangeType {
  .content {
    background-image: linear-gradient(-89deg, #ff8600 0%, #ff6430 100%);
    color: #fff;
    top: -54rpx;
    letter-spacing: 0;
    font-family: PingFang SC;
    height: 54rpx;
    &::before {
      background-image: url('https://gw.alicdn.com/imgextra/i2/O1CN01WPzb0T1fwZQruiSIu_!!6000000004071-2-tps-40-10.png');
      width: 20rpx;
      height: 5rpx;
      bottom: -5rpx;
    }
  }
}
