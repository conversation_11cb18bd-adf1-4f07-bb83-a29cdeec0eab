import styles from './index.module.scss';
// import { Modal } from 'antd-mobile';
import classNames from 'classnames';
import { getPromotionOfferData } from '@/store/lib/format';
import { PromotionOffer } from '@/store/types';
// import TaoRedPacketRule from '@/components/TaoRedPacketRule';
import { log } from '@alife/dtao-iec-spm-log';

interface RedPacketPopoverProps {
  offerList: PromotionOffer[] | any;
  children?: any;
  styleType?: string;
  displayPosition: 'LP_PAGE_OPENING_BUTTON_BUBBLE' | 'LP_PAGE' | 'HOME_PAGE';
}

function RedPacketPopover(props: RedPacketPopoverProps) {
  const promotionOfferData = getPromotionOfferData(props.offerList, props.displayPosition);
  const type = promotionOfferData?.type;

  if (!type) {
    return props.children;
  }
  log.addShowLog('red-packet-offer');

  // NOTE：按钮红包气泡不需要点击查看活动规则
  // const handleRedPacketOfferWrapClick = () => {
  //   log.addClickLog('red-packet-rule');
  //   Modal.alert({
  //     title: '活动规则',
  //     content: <TaoRedPacketRule ruleStr={promotionOfferData.offerSendRule} />,
  //   });
  // };

  return (
    <div className={classNames(styles.redPacketPopoverWrap, props?.styleType && styles[props?.styleType])}>
      <div className={styles.content}>
        <div className={styles.redPacketIcon} />
        {promotionOfferData.promotionOfferDescription}
        {/* <img
          className={styles.redPacketArrow}
          src="https://gw.alicdn.com/imgextra/i2/O1CN01ka9Jla1XEaT3H0qWl_!!6000000002892-2-tps-48-48.png"
        /> */}
      </div>
      {props.children}
    </div>
  );
}

export default RedPacketPopover;
