import styles from './index.module.scss';
import { Modal } from 'antd-mobile';
import classNames from 'classnames';
import { getPromotionOfferData } from '@/store/lib/format';
import { PromotionOffer } from '@/store/types';
import TaoRedPacketRule from '@/components/TaoRedPacketRule';
import { _ } from '@/utils';
import { log } from '@alife/dtao-iec-spm-log';

interface RedPacketOfferProps {
  offerList: PromotionOffer[] | any;
  children?: any;
  styleType?: string;
}

function RedPacketOffer(props: RedPacketOfferProps) {
  const promotionOfferData = getPromotionOfferData(props.offerList);
  const type = promotionOfferData?.type;

  if (_.isEmpty(promotionOfferData) || !type) {
    return props.children;
  }
  log.addShowLog('red-packet-offer');

  const handleRedPacketOfferWrapClick = () => {
    log.addClickLog('red-packet-rule');
    Modal.alert({
      title: '活动规则',
      content: <TaoRedPacketRule />,
    });
  };

  return (
    <div className={classNames(styles.redPacketPopoverWrap, props?.styleType && styles[props?.styleType])}>
      <div onClick={handleRedPacketOfferWrapClick} className={styles.content}>
        {promotionOfferData.promotionOfferDescription}
        <img
          className={styles.redPacketArrow}
          src="https://gw.alicdn.com/imgextra/i2/O1CN01ka9Jla1XEaT3H0qWl_!!6000000002892-2-tps-48-48.png"
        />
      </div>
      {props.children}
    </div>
  );
}

export default RedPacketOffer;
