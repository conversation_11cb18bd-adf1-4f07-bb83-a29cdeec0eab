export enum VerifyStatus {
  PASS = 'PASS',
  FAIL = 'FAIL',
  CANCEL = 'CANCEL',
  EXPIRE = 'EXPIRE',
  PENDING = 'PENDING',
}

export enum VerifyErrorCode {
  QUERY_FAILED = 'QUERY_FAILED',
  QUERY_STATUS_ERROR = 'QUERY_STATUS_ERROR',
  QUERY_EXPIRE = 'QUERY_EXPIRE',
  INIT_FAILED = 'INIT_FAILED',
  CANCEL = 'CANCEL',
  VERIFY_ABILITY_ERROR = 'VERIFY_ABILITY_ERROR',
  AUTH_FAILED = 'AUTH_FAILED',
  // 高风险失败
  AUTH_HIGH_RISK = 'AUTH_HIGH_RISK',
  SYSTEM_ERROR = 'SYSTEM_ERROR',
}

export interface VerifyResult {
  status: VerifyStatus;
  errorCode?: VerifyErrorCode;
  errorMessage?: string;
  data?: any;
}

export interface VerifyOptions {
  authenticationToken: string;
  directly?: boolean;
}
