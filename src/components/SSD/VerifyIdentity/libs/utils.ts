interface PollOptions {
  // 轮询间隔时间(ms)
  interval?: number;
  // 最大轮询次数
  maxAttempts?: number;
  // 超时时间(ms)
  timeout?: number;
  // 成功条件判断函数
  shouldStop?: (result: any) => boolean;
}

/**
 * 通用轮询函数
 * @param fn 需要轮询的异步函数
 * @param options 轮询配置选项
 * @returns Promise
 */
export const poll = async <T>(
  fn: () => Promise<T>,
  options: PollOptions = {},
): Promise<T> => {
  const {
    interval = 3000,
    maxAttempts = 99,
    timeout = 30000,
    shouldStop = (result) => Boolean(result),
  } = options;

  let attempts = 0;
  const startTime = Date.now();

  const executePoll = async (): Promise<T> => {
    const result = await fn();

    // 检查是否满足停止条件
    if (shouldStop(result)) {
      return result;
    }

    // 检查是否超过最大尝试次数
    if (maxAttempts && attempts >= maxAttempts) {
      throw new Error('超过最大轮询次数');
    }

    // 检查是否超时
    if (timeout && Date.now() - startTime >= timeout) {
      throw new Error('轮询超时');
    }

    // 增加尝试次数
    attempts++;

    // 等待指定时间后继续轮询
    await new Promise((resolve) => setTimeout(resolve, interval));
    return executePoll();
  };

  return executePoll();
};

// // 示例1：基本使用
// const result = await poll(async () => {
//   const response = await fetch('api/status');
//   return response.json();
// });

// // 示例2：使用配置选项
// const result = await poll(
//   async () => {
//     const response = await fetch('api/status');
//     return response.json();
//   },
//   {
//     interval: 2000,
//     maxAttempts: 5,
//     timeout: 10000,
//     shouldStop: (result) => result.status === 'completed',
//   },
// );

export function delay(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}
