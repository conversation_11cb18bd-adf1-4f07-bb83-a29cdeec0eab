import { PRODUCT, TENANT } from '@/common/constant';
import {
  AuthenticationInitResponse,
  AuthenticationQueryResponse,
  AuthenticationStepInfoVO,
  init,
  query,
  verify,
} from '@/store/authentication/actions';
import { _, isMtopCodeTimeout } from '@/utils';
import { VerifyErrorCode, VerifyOptions, VerifyResult, VerifyStatus } from './types';
import { VerifyError } from './errors';
import { startVerifyIdentity, StartVerifyIdentityCode } from './bridge';
import { getSecureToken } from '@/utils/umid-token';
import { poll, delay } from './utils';
import { Toast } from 'antd-mobile';
import { showHighRiskAlert } from '../components/HighRiskAlert';
import { log } from '@alife/dtao-iec-spm-log';
import { includes, isEmpty, set } from 'lodash-es';

function addVerifyIdentityLog(token: string, status?: VerifyStatus, errorCode?: VerifyErrorCode) {
  try {
    const VERIFY_IDENTITY_PASS = 'verify-identity-pass';
    const VERIFY_IDENTITY_FAIL = 'verify-identity-fail';
    const VERIFY_IDENTITY_EXPIRE = 'verify-identity-expire';
    const VERIFY_IDENTITY_CANCEL = 'verify-identity-cancel';
    const VERIFY_IDENTITY_ERROR = 'verify-identity-error';
    const showToastErrorCode = [
      VerifyErrorCode.INIT_FAILED,
      VerifyErrorCode.QUERY_STATUS_ERROR,
      VerifyErrorCode.SYSTEM_ERROR,
      VerifyErrorCode.AUTH_FAILED,
    ];
    const extraData = {
      token,
    };
    if (errorCode) {
      set(extraData, 'errorCode', errorCode);
    }
    if (status === VerifyStatus.PASS) {
      log.addSuccessLog(VERIFY_IDENTITY_PASS, extraData);
    } else if (status === VerifyStatus.FAIL) {
      log.addErrorLog(VERIFY_IDENTITY_FAIL, extraData);
    } else if (status === VerifyStatus.EXPIRE) {
      log.addErrorLog(VERIFY_IDENTITY_EXPIRE, extraData);
    } else if (status === VerifyStatus.CANCEL) {
      log.addOtherLog(VERIFY_IDENTITY_CANCEL, extraData);
    } else {
      log.addErrorLog(VERIFY_IDENTITY_ERROR);
    }
    if (includes(showToastErrorCode, errorCode)) {
      Toast.show({
        content: '系统开小差，请稍后重试',
      });
    }
  } catch (e) {}
}

function addInitVerifyErrorLog() {
  try {
    Toast.show({
      content: '系统开小差，请稍后重试',
    });
    log.addOtherLog('verify-identity-unknow-error');
  } catch (e) {}
}

function addStartVerifyErrorLog(error?: unknown) {
  try {
    log.addErrorLog('start-verify-unknow', error || {
      code: 'unknow',
    });
  } catch (e) {}
}

function addVerifyShowLog() {
  try {
    log.addShowLog('verify-identity-show');
  } catch (e) {}
}

const queryRequest = async (authenticationToken: string) => {
  const res = await query({
    authenticationToken,
    product: PRODUCT,
    tenant: TENANT,
  });

  if (res) {
    return res;
  }

  throw new VerifyError(VerifyStatus.FAIL, VerifyErrorCode.QUERY_FAILED, 'query 接口请求失败');
};

// 最后轮询核身的终止条件
const STOP_STATUS = ['SUCCESS', 'FAIL', 'EXPIRE'];

const UN_INIT_STEP_STATUS = ['SUCCESS', 'TERMINATED'];

const ERROR_STATUS = ['FAIL', 'EXPIRE', 'SUCCESS'];

const routeQueryResult = (result: AuthenticationQueryResponse): VerifyResult => {
  switch (result?.status) {
    case 'EXPIRE':
    case 'FAIL':
      throw new VerifyError(
        VerifyStatus.FAIL,
        VerifyErrorCode.QUERY_STATUS_ERROR,
        'query 验证失败',
        result,
      );
    case 'SUCCESS':
      return {
        status: VerifyStatus.PASS,
        data: result,
      };
    default:
      break;
  }

  // 基本不存在此情况
  throw new VerifyError(
    VerifyStatus.FAIL,
    VerifyErrorCode.QUERY_STATUS_ERROR,
    'query 接口返回状态错误',
    result,
  );
};

const getCurrentSteps = (queryRes?: AuthenticationQueryResponse) => {
  if (queryRes?.allSteps?.length) {
    const { allSteps } = queryRes;
    const initSteps = _.filter(allSteps, (step) => {
      if (step?.status) {
        const { status } = step;
        if (status && !_.includes(UN_INIT_STEP_STATUS, status)) {
          return true;
        }
      }
      return false;
    });

    return initSteps;
  }
  return null;
};

export interface initVerifyTaskResult {
  currentStep: AuthenticationStepInfoVO;
  initRes: AuthenticationInitResponse;
}

export interface authVerifyTaskOptions {
  currentStep: AuthenticationStepInfoVO;
  authenticationToken: string;
}

export class VerifyService {
  private authenticationToken: string;
  private currentSteps: AuthenticationStepInfoVO[] = [];
  private queryRes?: AuthenticationQueryResponse;
  private initResults: Record<string, AuthenticationInitResponse> = {};
  private status: VerifyStatus = VerifyStatus.PENDING;
  private result?: VerifyResult;
  private retryTimes = 0;

  constructor(options: VerifyOptions) {
    this.authenticationToken = options.authenticationToken;
  }

  async queryVerifyTask(): Promise<void> {
    try {
      const res = await queryRequest(this.authenticationToken);
      if (_.includes(ERROR_STATUS, res?.status)) {
        this.setResult(routeQueryResult(res));
        return;
      }
      this.queryRes = res;

      // 获取所有需要验证的步骤
      const steps = getCurrentSteps(this.queryRes);
      if (!steps?.length) {
        throw new VerifyError(VerifyStatus.FAIL, VerifyErrorCode.SYSTEM_ERROR, '无效的验证步骤');
      }
      this.currentSteps = steps;
    } catch (e) {
      if (e instanceof VerifyError) throw e;
      throw new VerifyError(
        VerifyStatus.FAIL,
        VerifyErrorCode.SYSTEM_ERROR,
        'query 阶段未知错误',
        e?.message,
      );
    }
  }

  async initAndVerifyTask(): Promise<void> {
    if (this.status !== VerifyStatus.PENDING) return;

    try {
      // 串行处理每个步骤的 init 和 verify
      for (const step of this.currentSteps) {
        // eslint-disable-next-line no-await-in-loop
        await this.handleSingleInit(step);
        // eslint-disable-next-line no-await-in-loop
        await this.handleSingleVerify(step);
      }

      // 所有步骤都验证通过后，进行最终轮询
      const finalRes = await poll(() => queryRequest(this.authenticationToken), {
        shouldStop: (result) => _.includes(STOP_STATUS, result?.status),
      });

      this.setResult(routeQueryResult(finalRes));
    } catch (error) {
      if (
        error?.data?.subResponseCode === 'HIGH_RISK' ||
        error?.data?.data?.subResponseCode === 'HIGH_RISK'
      ) {
        await showHighRiskAlert();
        throw new VerifyError(
          VerifyStatus.FAIL,
          VerifyErrorCode.AUTH_HIGH_RISK,
          '高风险提示',
          error,
        );
      }
      if (error instanceof VerifyError) throw error;
      throw new VerifyError(
        VerifyStatus.FAIL,
        VerifyErrorCode.SYSTEM_ERROR,
        'verify 阶段未知错误',
        error?.message,
      );
    }
  }

  async verify(): Promise<VerifyResult> {
    try {
      await this.queryVerifyTask();
      if (this.status !== VerifyStatus.PENDING) {
        return this.result!;
      }

      await this.initAndVerifyTask();
      addVerifyIdentityLog(this.authenticationToken, this.result?.status);
      return this.result!;
    } catch (error) {
      if (error instanceof VerifyError) {
        this.setResult({
          status: error.status,
          errorCode: error.errorCode,
          errorMessage: error.message,
          data: error.data,
        });
        addVerifyIdentityLog(this.authenticationToken, error?.status, error?.errorCode);
      } else {
        this.setResult({
          status: VerifyStatus.FAIL,
          errorCode: VerifyErrorCode.SYSTEM_ERROR,
          errorMessage: error instanceof Error ? error.message : '未知错误',
        });
        addInitVerifyErrorLog();
      }
      return this.result!;
    }
  }

  private setResult(result: VerifyResult) {
    this.status = result.status;
    this.result = result;
  }

  private getInitData(step: AuthenticationStepInfoVO) {
    switch (step?.stepCode) {
      case 'ALIPAY_SDK_PWD':
        return {
          type: 'ALIPAY',
          value: {
            alipayAuthStrategy: {
              alipayEnterType: 'SDK',
            },
          },
        };
      default:
        return null;
    }
  }

  private async handleSingleInit(step: AuthenticationStepInfoVO): Promise<void> {
    if (!step?.token || !step?.method) {
      throw new VerifyError(VerifyStatus.FAIL, VerifyErrorCode.SYSTEM_ERROR, '无效的验证步骤');
    }
    const secureTokenRes = await getSecureToken();
    const { token, method, channel } = step;
    const initParams = {
      product: PRODUCT,
      tenant: TENANT,
      stepToken: token,
      authenticationMethod: method,
      authenticationChannel: channel,
      forceInit: true,
    };
    const initData = this.getInitData(step);
    if (!isEmpty(initData)) {
      _.set(initParams, 'authenticationInitData', initData);
    }
    if (secureTokenRes?.umidToken) {
      _.set(initParams, 'umidToken', secureTokenRes?.umidToken);
    }
    if (secureTokenRes?.apdidToken) {
      _.set(initParams, 'apdidToken', secureTokenRes?.apdidToken);
    }
    const initRes = await init(initParams);

    // 无需核身的情况还存在吗？
    if (initRes?.responseCode === 'SUCCESS' && initRes?.status === 'SUCCESS') {
      this.initResults[token] = initRes;
      return;
    }

    if (!initRes?.authenticationAction?.verifyId) {
      throw new VerifyError(
        VerifyStatus.FAIL,
        VerifyErrorCode.INIT_FAILED,
        'init 阶段接口错误',
        initRes,
      );
    }

    this.initResults[token] = initRes;
  }

  private async verifyRetry(params: any) {
    try {
      // 服务端验证
      const verifyRes = await verify(params);
      if (this.retryTimes !== 0) {
        log.addSuccessLog('verify-identity-pass-retry');
      }
      this.retryTimes = 0;
      return verifyRes;
    } catch (e) {
      if (isMtopCodeTimeout(_.get(e, 'errorList[0].code')) && this.retryTimes < 3) {
        log.addOtherLog('verify-identity-verify-retry');
        this.retryTimes++;
        await delay(500);
        return await this.verifyRetry(params);
      } else {
        this.retryTimes = 0;
        throw new Error('VERIFY_FAIL');
      }
    }
  }

  private async handleSingleVerify(step: AuthenticationStepInfoVO): Promise<void> {
    const initRes = this.initResults[step.token!];

    // 无需核身，直达成功
    if (initRes?.responseCode === 'SUCCESS' && initRes?.status === 'SUCCESS') {
      return;
    }

    if (!initRes?.authenticationAction?.verifyId) {
      throw new VerifyError(VerifyStatus.FAIL, VerifyErrorCode.INIT_FAILED, '无效的 verifyId');
    }

    addVerifyShowLog();

    // 调用端能力
    const startVerifyResult = await startVerifyIdentity(initRes.authenticationAction.verifyId);
    if (startVerifyResult.code === StartVerifyIdentityCode.CANCEL) {
      throw new VerifyError(
        VerifyStatus.CANCEL,
        VerifyErrorCode.CANCEL,
        'startVerifyIdentity 取消核身',
        startVerifyResult,
      );
    }
    if (startVerifyResult.code !== StartVerifyIdentityCode.PASS) {
      addStartVerifyErrorLog(startVerifyResult);
      throw new VerifyError(
        VerifyStatus.FAIL,
        VerifyErrorCode.VERIFY_ABILITY_ERROR,
        'startVerifyIdentity 端能力失败',
        startVerifyResult,
      );
    }

    // 服务端验证
    const verifyRes = await this.verifyRetry({
      product: PRODUCT,
      tenant: TENANT,
      stepToken: step.token!,
      authenticationMethod: step.method!,
    });

    if (!verifyRes?.pass) {
      throw new VerifyError(
        VerifyStatus.FAIL,
        VerifyErrorCode.AUTH_FAILED,
        '服务端 verify 失败',
        verifyRes,
      );
    }
  }
}
