import { Modal } from 'antd-mobile';
import { ExclamationCircleFill } from 'antd-mobile-icons';
import { log } from '@ali/iec-dtao-utils';
import styles from './index.module.scss';


function addVerifyHighRiskLog() {
  try {
    log.addShowLog('verify-high-risk');
  } catch (e) {}
}


const HighRiskAlert = () => {
  return (
    <div className={styles.highRiskAlert}>
      <ExclamationCircleFill
        style={{
          fontSize: '72rpx',
          color: 'var(--adm-color-primary)',
        }}
      />
      <div className={styles.highRiskAlertTitle}>
        <div>
          <span>你当前</span>
          <span className={styles.highLight}>存在被骗风险</span>
        </div>
        <div>建议过段时间申请借款</div>
      </div>
    </div>
  );
};

export const showHighRiskAlert = () => {
  addVerifyHighRiskLog();
  return Modal.alert({
    content: <HighRiskAlert />,
    confirmText: '我知道了',
  });
};
