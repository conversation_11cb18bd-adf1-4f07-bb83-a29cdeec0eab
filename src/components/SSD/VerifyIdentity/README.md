# 核身组件

## 如何使用

```typescript
import { useVerifyIdentity } from './VerifyIdentity';

const YourComponent = () => {
  const { startVerify } = useVerifyIdentity();

  const handleVerify = async () => {
    const result = await startVerify({
      authenticationToken: 'your-token-here'
    });

    if (result.status === VerifyStatus.PASS) {
      // 验证通过
      console.log('验证成功');
    }
    else {
      console.log('验证失败', result.errorMessage);
    }
  };

  return (
    <button onClick={handleVerify}>开始验证</button>
  );
};
```

### 返回值类型

```typescript
interface VerifyResult {
  status: VerifyStatus;        // 验证状态
  errorCode?: VerifyErrorCode; // 错误码（如果有）
  errorMessage?: string;       // 错误信息（如果有）
  data?: any;                  // 成功时的返回数据
}

enum VerifyStatus {
  PASS = 'PASS',         // 验证通过
  FAIL = 'FAIL',         // 验证失败
}
```

### 注意事项

1. 必须提供有效的 `authenticationToken`
2. startVerify 不会进入 catch，都在返回结果中处理

## 详细流程

1. **查询验证任务**：
   - 调用 `queryVerifyTask` 方法，传入 `authenticationToken`。
   - 如果查询结果状态为 `FAIL` 或 `EXPIRE`，抛出 `VerifyError`，流程终止。
   - 如果查询结果状态为 `SUCCESS`，返回 `PASS` 状态。
   - 否则，返回 `PENDING` 状态，继续进行下一步。

2. **初始化验证任务**：
   - 调用 `initVerifyTask` 方法，传入查询结果数据。
   - 获取当前步骤信息，如果没有有效步骤，抛出 `VerifyError`。
   - 调用 `init` 接口进行初始化。
   - 如果初始化结果为 `SUCCESS`，返回 `PASS` 状态。
   - 如果初始化结果为 `ALIPAY_EXTERNAL_ERROR`，抛出 `VerifyError`，提示高风险。
   - 如果初始化成功但需要进一步验证，返回 `PENDING` 状态，继续进行下一步。

3. **调用端能力核身**：
   - 调用 `startVerifyTask` 方法，传入初始化结果数据。
   - 如果 `verifyId` 为空，抛出 `VerifyError`。
   - 调用 `startVerifyIdentity` 接口。
   - 如果结果为 `PASS`，返回 `PENDING` 状态，继续进行下一步。
   - 否则，抛出 `VerifyError`。

4. **服务端二次验证**：
   - 调用 `authVerifyTask` 方法，传入当前步骤和 `authenticationToken`。
   - 如果验证通过，轮询 `query` 接口，直到状态为 `SUCCESS`、`FAIL` 或 `EXPIRE`。
   - 如果轮询结果为 `SUCCESS`，返回 `PASS` 状态。
   - 否则，抛出 `VerifyError`。

5. **错误处理**：
   - 在每个步骤中，如果捕获到 `VerifyError`，将错误信息返回给调用者。
   - 其他异常也会被捕获并返回标准化的错误信息。
