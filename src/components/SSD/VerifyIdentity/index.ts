import { useCallback } from 'react';
import { VerifyService } from './libs/service';
import { VerifyOptions, VerifyResult } from './libs/types';
import { getEnvData } from './libs/bridge';

export const useVerifyIdentity = () => {
  const startVerify = useCallback(async (options: VerifyOptions): Promise<VerifyResult> => {
    const verifyService = new VerifyService(options);
    return verifyService.verify();
  }, []);

  return {
    startVerify,
    getEnvData,
  };
};
