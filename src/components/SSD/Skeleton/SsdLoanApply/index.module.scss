.loanApply {
  padding-top: 45rpx;
  .panel1, .panel2, .item {
    margin: auto;
    border-radius: 8rpx;
    height: 55rpx;
  }
  .panel1 {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    .item {
      flex: 1 1;
      margin-left: 24rpx;
      background-color: #f3f6f8;
      &:first-child {
        margin-left: 0;
      }
    }
  }
  .panel2 {
    width: 100%;
    background-color: #f3f6f8;
    margin-top: 16rpx;
  }
}
