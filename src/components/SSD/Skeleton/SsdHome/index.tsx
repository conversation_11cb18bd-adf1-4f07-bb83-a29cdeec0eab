/**
 * @file 随身贷home骨架屏
 */
import classNames from 'classnames';

import styles from './index.module.scss';

interface SsdHomeSkeletonProps {
  customClassName?: string;
}

export default function SsdHomeSkeleton(props: SsdHomeSkeletonProps) {
  return (
    <div className={classNames(styles.home, props?.customClassName && props?.customClassName)}>
      <div className={styles.panel1} />
      <div className={styles.panel2} />
      <div className={styles.panel3} />
      <div className={styles.panel4} />
    </div>
  );
}
