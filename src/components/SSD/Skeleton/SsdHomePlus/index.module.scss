.home {
  padding-top: 96rpx;
  padding-bottom: 120rpx;
  background-color: #fff;
  border-radius: 24rpx;

  .panel1,
  .panel2,
  .panel3,
  .panel4,
  .panel5,
  .ellipse {
    margin: auto;
    background: linear-gradient(90deg, hsla(0, 0%, 74.5%, .2) 25%, hsla(0, 0%, 50.6%, .24) 37%, hsla(0, 0%, 74.5%, .2) 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease infinite;
    border-radius: 8rpx;
  }

  .panel1 {
    margin-bottom: 92rpx;
    height: 50rpx;
    width: 200rpx;
  }

  .panel2 {
    margin-bottom: 34rpx;
    height: 86rpx;
    width: 316rpx;
  }

  .wrapper {
    margin-bottom: 44rpx;
    display: flex;
    justify-content: center;
    align-items: center;

    .ellipse {
      margin: 0;
      width: 137rpx;
      height: 64rpx;
      border-radius: 32rpx;
    }

    .ellipse+.ellipse {
      margin-left: 24rpx;
    }
  }

  .panel4 {
    height: 32rpx;
    width: 336rpx;
    margin-bottom: 96rpx;
  }

  .panel5 {
    height: 98rpx;
    width: 590rpx;
    border-radius: 49rpx;
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0 50%;
  }
}
