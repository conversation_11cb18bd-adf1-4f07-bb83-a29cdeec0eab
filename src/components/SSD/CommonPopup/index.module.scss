.popup {
  :global(.adm-popup-body) {
    border-radius: 12rpx 12rpx 0 0;
    min-height: 96rpx;
  }
  .content {
    position: relative;
    padding: 0 40rpx 28rpx;
  }
  .hd {
    position: relative;
    padding: 30rpx 32rpx;
    // margin-bottom: 24rpx;
    .title {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 36rpx;
      line-height: 36rpx;
      font-weight: 500;
      text-align: center;
    }
  }

  .close {
    position: absolute;
    top: 18rpx;
    right: 18rpx;
    width: 56rpx;
    height: 56rpx;
    border-radius: 8rpx;
    background: #f3f6f8;
    text-align: center;
    .closeIcon {
      position: relative;
      left: 5rpx;
      display: block;
      width: 44rpx;
      height: 44rpx;
      text-align: center;
      background-position: 50% 50%;
      background-repeat: no-repeat;
      background-size: cover;
      background-image: url('https://gw.alicdn.com/imgextra/i4/O1CN01pIVshk1sbaGHhQrQW_!!6000000005785-2-tps-200-200.png');
    }
  }
}

.bottomSafeArea {
  height: var(--safe-area-inset-bottom);
}
