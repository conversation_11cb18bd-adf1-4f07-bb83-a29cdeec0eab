/**
 * @file 弹窗组件
 */

import { ReactNode, useImperativeHandle, useState, forwardRef } from 'react';
import { Popup, PopupProps } from 'antd-mobile';
import classNames from 'classnames';

import styles from './index.module.scss';

interface CommonPopupProps extends PopupProps {
  title?: string | ReactNode;
  hideCloseIcon?: boolean;
  contentClassName?: string;
}

function CommonPopup(
  {
    title = null,
    className = '',
    hideCloseIcon = false,
    children,
    onMaskClick,
    contentClassName,
    onClose,
    ...otherProps
  }: CommonPopupProps,
  ref,
) {
  const [visible, setVisible] = useState(false);

  const handleClose = () => {
    onClose && onClose();
    setVisible(false);
  };

  const toggleVisible = (value) => {
    setVisible(value);
  };

  useImperativeHandle(ref, () => ({
    toggleVisible,
  }));

  return (
    <Popup
      visible={visible}
      className={classNames(styles.popup, className)}
      onMaskClick={onMaskClick || handleClose}
      {...otherProps}
    >
      <div className={styles.hd}>
        <p className={styles.title}>{title}</p>
      </div>
      {hideCloseIcon ? null : (
        <div className={styles.close} onClick={handleClose}>
          <i className={styles.closeIcon} />
        </div>
      )}
      <div className={classNames(styles.content, contentClassName && contentClassName)}>
        {children}
      </div>
      <div className={styles.bottomSafeArea} />
    </Popup>
  );
}

export default forwardRef(CommonPopup);
