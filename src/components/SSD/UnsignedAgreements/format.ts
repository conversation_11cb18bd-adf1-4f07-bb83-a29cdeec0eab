import { forEach, max, set } from 'lodash-es';
import type {
  QueryUnSignAgreementListResponse,
  UnsignedAgreementList,
  PreviewRequest,
} from '@/store/agreement/actions';
import type { AgreementDTO, CREDIT_TYPE } from '@/store/types';

export function getMinReadTime(queryResponse?: QueryUnSignAgreementListResponse) {
  const unSignedAgreementGroupList = queryResponse?.unSignedAgreementGroupList;
  const minReadTimeList: number[] = [];
  if (unSignedAgreementGroupList?.length) {
    forEach(unSignedAgreementGroupList, (group) => {
      const unSignedAgreementList = group?.unSignedAgreementList;
      if (unSignedAgreementList?.length) {
        forEach(unSignedAgreementList, (agreement) => {
          minReadTimeList.push(agreement.minReadTime);
        });
      }
    });
  }
  return max(minReadTimeList) || 0;
}

export function getForceReadAgreements(queryResponse?: QueryUnSignAgreementListResponse) {
  const forceAgreements: AgreementDTO[] = [];
  const unSignedAgreementGroupList = queryResponse?.unSignedAgreementGroupList;
  if (unSignedAgreementGroupList?.length) {
    forEach(unSignedAgreementGroupList, (group) => {
      const unSignedAgreementList = group?.unSignedAgreementList;
      if (unSignedAgreementList?.length) {
        forEach(unSignedAgreementList, (agreement) => {
          if (agreement?.forceRead) {
            forceAgreements.push(agreement);
          }
        });
      }
    });
  }
  return forceAgreements;
}

interface GetPreviewParamsOptions {
  bizType?: string;
  creditType?: CREDIT_TYPE;
  agreement: AgreementDTO;
  currentGroup: UnsignedAgreementList;
  getExtensionParams?: any;
}

export function getPreviewParams(options: GetPreviewParamsOptions): PreviewRequest {
  if (options) {
    const { bizType, agreement, currentGroup, getExtensionParams, creditType } = options;
    const { source, code, version, name, contractNo, agreementStatus, fundSupplierCode, fundSupplierName } = agreement;
    const baseParams = {
      bizType,
      creditType,
      source,
      code,
      institution: currentGroup?.institution,
      version,
      name,
      contractNo,
      agreementStatus,
      fundSupplierCode,
      fundSupplierName,
    };
    // NOTE: 兼容原链路中从扩展字段取机构信息的逻辑
    if (!fundSupplierCode && currentGroup?.extension?.fundSupplierCode) {
      set(baseParams, 'fundSupplierCode', currentGroup?.extension?.fundSupplierCode);
    }
    if (!fundSupplierName && currentGroup?.extension?.fundSupplierName) {
      set(baseParams, 'fundSupplierName', currentGroup?.extension?.fundSupplierName);
    }
    if (getExtensionParams) {
      const extensionParams = getExtensionParams(currentGroup);
      return {
        ...baseParams,
        ...extensionParams,
      };
    } else if (currentGroup?.extension) {
      return {
        ...baseParams,
        extension: currentGroup?.extension,
      };
    }
    return baseParams;
  }
  return {};
}

export function removeStyleTags(html: string) {
  /**
   * <style[^>]*>：匹配 <style> 标签，[^>]* 表示匹配任意数量的非 > 字符，即匹配 <style> 标签及其所有属性。
   * .*?：非贪婪匹配，匹配 <style> 标签内的所有内容，直到遇到 </style>。
   * <\/style>：匹配 </style> 标签。
   * g：全局匹配，确保匹配所有符合条件的 <style> 标签。
   * i：忽略大小写（虽然 <style> 标签通常是小写，但为了兼容性加上）。
   * s：使 . 匹配包括换行符在内的所有字符，确保跨行的 <style> 标签内容也能被匹配到。
   */
  const styleTagPattern = /<style[^>]*>.*?<\/style>/gis;
  return html?.replace(styleTagPattern, '');
}
