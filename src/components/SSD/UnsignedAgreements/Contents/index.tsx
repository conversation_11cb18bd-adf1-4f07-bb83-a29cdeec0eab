/**
 * @file 协议列表
 */

import { useCallback, useEffect, useMemo, useState } from 'react';
import { DotLoading, Ellipsis } from 'antd-mobile';
import classNames from 'classnames';
import { findIndex, map } from 'lodash-es';
import { useThrottleFn } from 'ahooks';

import type { AgreementDTO } from '@/store/types';
import { navigatorToOutside } from '@/utils/link';

import { removeStyleTags } from '../format';
import styles from './index.module.scss';

interface ContentsProps {
  options?: AgreementDTO[];
  isLoading?: boolean;
  renderButton?: () => React.JSX.Element | null;
  size?: 'l' | 'n';
  activeKey?: string;
  type?: 'compulsoryContent' | 'previewContent';
  useAnchor?: boolean;
}

export default function Contents(props: ContentsProps) {
  const { options = [], isLoading, activeKey, size = 'n', renderButton, type = 'preview', useAnchor = false } = props;
  const [open, setOpen] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);

  const toggelOpen = () => {
    setOpen((cur) => {
      return !cur;
    });
  };

  const checkListHide = () => {
    if (!open && options?.length > 2) {
      return true;
    }
    return false;
  };

  // 协议滚动的同时，下面的列表也跟随滚动
  const scrollList = () => {
    if (options?.length > 2 && currentIndex > 1 && !open) {
      if (currentIndex < options.length - 2) {
        const offsetY = currentIndex * 43;
        return {
          transform: `translateY(-${offsetY.toFixed(2)}rpx)`,
          transition: 'all .3s',
        };
      } else {
        const offsetFix = (options.length - 2) * 43;
        return {
          transform: `translateY(-${offsetFix.toFixed(2)}rpx)`,
          transition: 'all .3s',
        };
      }
    }
    return {};
  };

  const handleClick = useCallback((index) => () => {
    const element = document.getElementById(`anchor-${index}`);
    setCurrentIndex(index);
    if (element) {
      element?.scrollIntoView();
    }
  }, []);

  // 用户滚动协议，自动切换TAB
  const handleContainerScroll = () => {
    const container = document.getElementById('compulsory-container');
    if (container && options && options[0]) {
      const containerRect = container.getBoundingClientRect();
      let currentTabIndex = 0;
      for (let i = 0; i < options?.length; i++) {
        const element = document.getElementById(`anchor-${i}`);
        if (!element || !container) continue;
        const elementRect = element.getBoundingClientRect();
        if (elementRect.top - containerRect.top <= 70) {
          currentTabIndex = i;
        } else {
          break;
        }
      }
      setCurrentIndex(currentTabIndex);
    }
  };

  // 劫持a标签跳转事件
  const handleATarget = () => {
    const compulsoryContainer = document.getElementById('compulsory-container');
    if (compulsoryContainer) {
      compulsoryContainer.addEventListener('click', (e) => {
        e.preventDefault();
        const target = e?.target;
        // @ts-ignore
        if (target && target?.localName === 'a') {
          // 获取到a标签上的链接
        // @ts-ignore
          const url = target?.getAttribute('href');
          if (url) {
            navigatorToOutside(url);
          }
        }
      });
    }
  };

  // 对handleContainerScroll进行节流
  const { run: handleScroll } = useThrottleFn(handleContainerScroll, {
    leading: true,
    trailing: true,
    wait: 100,
  });

  const renderTotal = () => {
    if (options?.length > 2) {
      return (
        <div className={styles.total} onClick={toggelOpen}>
          <p className={styles.text}>
            共{options.length}份协议
          </p>
          <i className={classNames(styles.icon, open && styles.down, !open && styles.up)} />
        </div>
      );
    }
    return null;
  };

  const renderItem = (option: AgreementDTO, index: number) => {
    if (option) {
      const { name = '' } = option;
      return (
        <li className={styles.item} key={`preview-${index}`}>
          <Ellipsis
            className={classNames(
              styles.name,
              index === currentIndex && styles.current,
            )}
            content={name}
            onContentClick={handleClick(index)}
          />
        </li>
      );
    }
    return null;
  };

  const renderContents = useMemo(() => {
    if (isLoading) {
      return (
        <div className={styles.loading}>
          <DotLoading />
        </div>
      );
    }
    if (options?.length) {
      return map(options, (content, index) => {
        const pureContent = removeStyleTags(content?.content);
        return (
          <div
            id={`anchor-${index}`}
            className={styles.content}
            key={`compulsory-agreement-${index}`}
            dangerouslySetInnerHTML={{ __html: pureContent }}
          />
        );
      });
    }
    return null;
  }, [options, isLoading]);

  // 滑动协议内容切换TAB，需要监听页面scroll事件
  useEffect(() => {
    const dom = document.getElementById('compulsory-container');
    dom?.addEventListener('scroll', handleScroll);
    handleATarget();
    return () => {
      dom?.removeEventListener('scroll', handleScroll);
    };
  }, []);

  useEffect(() => {
    if (activeKey && options?.length) {
      const targIndex = findIndex(options, (option) => {
        return option?.code === activeKey;
      });
      if (targIndex > 0) {
        handleClick(targIndex)();
      }
    }
  }, [activeKey, options]);

  return (
    <div className={styles.contents}>
      <div id="compulsory-container" className={classNames(styles.preview, size === 'l' && styles.previewL)}>
        {renderContents}
      </div>
      {useAnchor && (
        <div className={styles.bottom}>
          <div className={styles.mask} />
          <div className={styles.listC}>
            <div
              className={classNames(styles.listWrap, checkListHide() && styles.hide, styles[type])}
            >
              <ul style={scrollList()} className={styles.list}>
                {map(options, renderItem)}
              </ul>
              {renderTotal()}
            </div>
            {renderButton ? renderButton() : null}
          </div>
        </div>
      )}
    </div>
  );
}
