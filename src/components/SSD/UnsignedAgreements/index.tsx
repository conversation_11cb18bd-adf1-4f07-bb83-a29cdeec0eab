/**
 * @file sdd协议专用组件
 */

import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
  useRef,
  useCallback,
  useMemo,
} from 'react';
import { filter, first } from 'lodash-es';
import classNames from 'classnames';
import { ClientOnly } from 'ice';

import type {
  QueryUnSignAgreementListResponse,
  UnsignedAgreementsExtension,
} from '@/store/agreement/actions';
import {
  queryUnSignAgreementList,
  GetExtensionParams,
  UnsignedAgreementList,
} from '@/store/agreement/actions';
import type { CREDIT_PLATFORM, CREDIT_TYPE } from '@/store/types';
import CommonPopup from '@/components/CommonPopup';
import Compulsory from './Compulsory';
import Preview from './Preview';
import { log } from '@alife/dtao-iec-spm-log';

import styles from './index.module.scss';

interface UnsignedAgreementsSSDProps {
  bizType: 'PLATFORM' | 'LOAN' | 'CREDIT' | 'BIND_CARD';
  creditPlatform: CREDIT_PLATFORM;
  popupTitle?: string;
  institutionList?: string[];
  customPayload?: QueryUnSignAgreementListResponse;
  renderName?: (result?: QueryUnSignAgreementListResponse) => React.JSX.Element | null;
  renderCompulsory?: () => React.JSX.Element | null;
  onChange?: (value?: boolean) => void;
  onCompleted?: () => void;
  onError?: (e?: unknown) => void;
  useInit?: boolean;
  getExtensionParams?: GetExtensionParams;
  containerClassName?: string;
  onClose?: () => void;
  force?: boolean;
  creditType?: CREDIT_TYPE;
  buttonText?: string;
  renderPreviewTitle?: () => JSX.Element;
  renderCompulsoryTitle?: () => JSX.Element;
  useContentsNew?: boolean;
}

interface ShowOption {
  action?: 'preview' | 'compulsory' | '';
  activeKey?: string;
  popupProps?: {
    position?: any;
    mask?: boolean;
    bodyClassName?: string;
    title?: string;
    bodyStyle?: any;
    transparentMask?: boolean;
  };
}

export type UnsignedAgreementGroup = UnsignedAgreementList;

export interface UnsignedAgreementsSSDRef {
  show: (option?: ShowOption) => void;
  close: () => void;
  getExtension: () => UnsignedAgreementsExtension;
  doInit: (extra?: any) => void;
}

export const UnsignedAgreementsSSD = forwardRef((props: UnsignedAgreementsSSDProps, ref) => {
  const {
    bizType,
    popupTitle,
    institutionList,
    creditPlatform,
    renderName,
    onChange,
    onCompleted,
    onError,
    renderCompulsory,
    useInit = true,
    getExtensionParams,
    customPayload,
    containerClassName,
    onClose,
    force,
    creditType,
    buttonText,
    renderPreviewTitle,
    renderCompulsoryTitle,
    useContentsNew,
  } = props;
  const [payLoad, setPayLoad] = useState<QueryUnSignAgreementListResponse>();
  const [showOption, setShowOption] = useState<ShowOption>();
  const popupRef = useRef<any>(null);
  const institutionListStr = useMemo(() => {
    try {
      if (institutionList?.length) {
        return JSON.stringify(institutionList);
      }
      return '';
    } catch (e) {
      return '';
    }
  }, [institutionList]);

  // 检查是否支用授信协议强读
  const checkCreditLoan = () => {
    try {
      const checkList = first(payLoad?.unSignedAgreementGroupList);
      if (checkList && bizType === 'LOAN') {
        const target = first(filter(checkList?.unSignedAgreementList, {
          forceRead: true,
        }));
        if (target?.forceRead) {
          return true;
        }
      }
      return false;
    } catch (e) {
      return false;
    }
  };

  // 协议展示
  const show = (option: ShowOption) => {
    if (option.action === 'compulsory') {
      log.addClickLog('ssd-unsigned-agreement-show-compulsory');
      popupRef.current.toggleVisible(true);
      const prePopupProps = option?.popupProps || {};
      setShowOption({
        ...option,
        popupProps: {
          ...prePopupProps,
          bodyStyle: {
            height: '112vw',
            minHeight: 'unset',
          },
        },
      });
    } else if (option.action === 'preview') {
      log.addClickLog('ssd-unsigned-agreement-show-preview');
      popupRef.current.toggleVisible(true);
      setShowOption(option);
    }
  };

  // 协议关闭
  const close = () => {
    popupRef.current.toggleVisible(false);
    onClose && onClose();
    setShowOption({
      popupProps: showOption?.popupProps,
    });
  };

  // 内部协议预览
  const handlePreview = () => {
    show({
      action: 'preview',
    });
  };

  // 获取协议查询结果
  const getExtension = useCallback(() => {
    try {
      if (payLoad?.unSignedAgreementGroupList) {
        const targetList = filter(payLoad.unSignedAgreementGroupList, {
          source: 'MAYI_ZHIXIN',
        });
        if (targetList?.length === 1) {
          const target = first(targetList);
          if (target?.extension) {
            return target.extension;
          }
        }
      }
      // TODO: 这里的异常要如何被外部感知
      throw new Error();
    } catch (e) {
      return null;
    }
  }, [payLoad]);

  // 强制阅读结束
  const handleCompleted = () => {
    close();
    onChange && onChange(true);
    onCompleted && onCompleted();
  };

  // 协议展开
  const handleExpand = () => {
    if (showOption?.popupProps?.bodyStyle) {
      const prePopupProps = showOption?.popupProps || {};
      setShowOption((cur) => {
        return {
          ...cur,
          popupProps: {
            ...prePopupProps,
            bodyStyle: {},
          },
        };
      });
    }
  };

  // 协议列表初始化
  const doInit = async (extra = {}) => {
    try {
      if (bizType && institutionList?.length && creditPlatform) {
        const res = await queryUnSignAgreementList({
          bizType,
          creditPlatform,
          institutionList: institutionListStr,
          ...extra,
        });
        if (res?.unSignedAgreementGroupList?.length) {
          setPayLoad(res);
        } else {
          throw new Error('queryUnSignAgreementList error');
        }
      }
    } catch (e) {
      log.addErrorLog('ssd-unsigned-agreement-fetch');
      setPayLoad({});
      onError && onError(e);
    }
  };

  // 渲染外部的协议名称
  const renderNameWrap = () => {
    if (renderName) {
      return renderName(payLoad);
    }
    return <p onClick={handlePreview}>查看</p>;
  };

  // 渲染协议内容体
  const renderAgreementContent = () => {
    if (payLoad) {
      switch (showOption?.action) {
        case 'compulsory':
          return (
            <Compulsory
              bizType={bizType}
              creditType={creditType}
              queryResponse={payLoad}
              onCompleted={handleCompleted}
              renderCompulsory={renderCompulsory}
              getExtensionParams={getExtensionParams}
              onError={onError}
              force={force}
              buttonText={buttonText}
              useLoanCredit={checkCreditLoan()}
              onExpand={handleExpand}
              renderCompulsoryTitle={renderCompulsoryTitle}
              useContentsNew={useContentsNew}
            />
          );
        case 'preview':
          return (
            <Preview
              bizType={bizType}
              creditType={creditType}
              activeKey={showOption?.activeKey}
              queryResponse={payLoad}
              getExtensionParams={getExtensionParams}
              renderPreviewTitle={renderPreviewTitle}
            />
          );
        default:
          return null;
      }
    }
    return null;
  };

  const renderPopup = () => {
    return (
      <CommonPopup
        ref={popupRef}
        title={showOption?.popupProps?.title || popupTitle}
        contentClassName={styles.popupContent}
        className={styles.zIndex1010}
        onClose={close}
        {...(showOption?.popupProps || {})}
      >
        <div className={classNames(styles.container, containerClassName && containerClassName)}>
          {renderAgreementContent()}
        </div>
      </CommonPopup>
    );
  };

  useEffect(() => {
    if (useInit) {
      doInit();
    }
  }, []);

  useEffect(() => {
    if (!useInit && customPayload) {
      setPayLoad(customPayload);
    }
  }, [customPayload, useInit]);

  useImperativeHandle(ref, () => ({
    show,
    close,
    getExtension,
    doInit,
  }));

  return (
    <>
      <div className={styles.name}>{renderNameWrap()}</div>
      <ClientOnly>{renderPopup}</ClientOnly>
    </>
  );
});
