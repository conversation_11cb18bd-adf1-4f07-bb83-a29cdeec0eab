/**
 * @file 协议列表
 */

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { DotLoading } from 'antd-mobile';
import classNames from 'classnames';
import { findIndex, map } from 'lodash-es';
import { log } from '@alife/dtao-iec-spm-log';
import { ClientOnly } from 'ice';

import { AgreementDTO } from '@/store/types';
import { navigatorToOutside } from '@/utils/link';
import { CommonPopup } from '@/components';

import { removeStyleTags } from '../format';
import styles from './index.module.scss';

interface ContentsNewProps {
  options?: AgreementDTO[];
  isLoading?: boolean;
  renderButton?: () => React.JSX.Element | null;
  activeKey?: string;
  useLoanCredit?: boolean;
  onExpand: () => void;
}

export default function ContentsNew(props: ContentsNewProps) {
  const { options = [], isLoading, activeKey, useLoanCredit, renderButton, onExpand } = props;
  const [isExpand, setExpand] = useState(false);
  const expandRef = useRef(false);
  const popupRef = useRef<any>();

  const handleClick = useCallback((index) => () => {
    const element = document.getElementById(`anchor-${index}`);
    if (element) {
      element?.scrollIntoView();
    }
  }, []);

  // 劫持a标签跳转事件
  const handleATarget = () => {
    const compulsoryContainer = document.getElementById('compulsory-container');
    if (compulsoryContainer) {
      compulsoryContainer.addEventListener('click', (e) => {
        e.preventDefault();
        const target = e?.target;
        // @ts-ignore
        if (target && target?.localName === 'a') {
          // 获取到a标签上的链接
          // @ts-ignore
          const url = target?.getAttribute('href');
          if (url) {
            navigatorToOutside(url);
          }
        }
      });
    }
  };

  const handlePopupShow = useCallback(() => {
    popupRef?.current?.toggleVisible(true);
    log.addClickLog('compulsory-show');
  }, []);

  const handlePopupClose = useCallback(() => {
    popupRef?.current?.toggleVisible(false);
    log.addClickLog('compulsory-close');
  }, []);

  const handlePopupItemClick = useCallback((index) => () => {
    handlePopupClose();
    const element = document.getElementById(`anchor-${index}`);
    if (element) {
      element?.scrollIntoView();
    }
  }, []);

  const renderContents = useMemo(() => {
    if (isLoading) {
      return (
        <div className={styles.loading}>
          <DotLoading />
        </div>
      );
    }
    if (options?.length) {
      return map(options, (content, index) => {
        const pureContent = removeStyleTags(content?.content);
        return (
          <div
            id={`anchor-${index}`}
            className={styles.content}
            key={`compulsory-agreement-${index}`}
            dangerouslySetInnerHTML={{ __html: pureContent }}
          />
        );
      });
    }
    return null;
  }, [options, isLoading]);

  const renderPopupList = () => {
    if (options?.length) {
      return options.map((option, index) => {
        return (
          <div
            className={styles.item}
            key={`popup-item-${index}`}
            onClick={handlePopupItemClick(index)}
          >
            <p className={styles.itemName}>
              {option?.name}
            </p>
            <i className={styles.arrowIcon2} />
          </div>
        );
      });
    }
    return null;
  };

  const renderPopup = () => {
    return (
      <CommonPopup
        ref={popupRef}
        onClose={handlePopupClose}
        onMaskClick={handlePopupClose}
        className={styles.zIndex1020}
        bodyClassName={classNames(styles.right, isExpand && styles.rightLong)}
        position="right"
        transparentMask
      >
        <div className={styles.container}>
          {renderPopupList()}
        </div>
      </CommonPopup>
    );
  };

  const renderCreditLoanTips = () => {
    if (useLoanCredit) {
      return (
        <div className={styles.creditLoanTips}>
          <p className={styles.creditDesc}>
            根据您签署的《授信协议及个人征信查报授权书》，您将在本服务页面确认您向授信机构的借款申请，请仔细阅读如下协议，依据本页面提示完成本次借款申请及其他相关协议确认。
          </p>
        </div>
      );
    }
    return null;
  };

  const handleScroll = () => {
    if (!expandRef?.current) {
      setExpand(true);
      expandRef.current = true;
      onExpand();
    }
  };

  // 滑动协议内容切换TAB，需要监听页面scroll事件
  useEffect(() => {
    const dom = document.getElementById('compulsory-container');
    handleATarget();
    dom?.addEventListener('scroll', handleScroll);
    return () => {
      dom?.removeEventListener('scroll', handleScroll);
    };
  }, []);

  useEffect(() => {
    if (activeKey && options?.length) {
      const targIndex = findIndex(options, (option) => {
        return option?.code === activeKey;
      });
      if (targIndex > 0) {
        handleClick(targIndex)();
      }
    }
  }, [activeKey, options]);

  return (
    <div className={styles.contents}>
      <div id="compulsory-container" className={classNames(styles.preview)}>
        {renderCreditLoanTips()}
        {renderContents}
      </div>
      <div className={styles.bottom}>
        <div className={styles.listC}>
          <div className={styles.enter} onClick={handlePopupShow}>
            <p className={styles.title}>
              {/* 当前文案为 授信、支用 共用，请勿随意修改 */}
              了解全部协议及相关告知
            </p>
            <i className={styles.arrowIcon} />
          </div>
          {renderButton ? renderButton() : null}
        </div>
      </div>
      <ClientOnly>{renderPopup}</ClientOnly>
    </div>
  );
}
