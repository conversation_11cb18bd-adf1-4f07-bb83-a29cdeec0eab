/**
 * @file 强制阅读
 */

import { useEffect, useState } from 'react';
import { concat, forEach, noop } from 'lodash-es';
import { Button, Toast } from 'antd-mobile';

import { AgreementDTO, CREDIT_TYPE } from '@/store/types';
import type { QueryUnSignAgreementListResponse, GetExtensionParams } from '@/store/agreement/actions';
import { preview } from '@/store/agreement/actions';
import Contents from '../Contents';
import useCountDown from '@/hooks/useCountDown';
import { getMinReadTime, getPreviewParams } from '../format';
import { setCompulsoryEnd, checkCompulsoryEnd, doNoop } from '../session';
import ContentsNew from '../ContentsNew';

import styles from './index.module.scss';
import { forceAgreementPageLog } from '@/utils/goc';

interface CompulsoryProps {
  bizType?: string;
  creditType?: CREDIT_TYPE;
  queryResponse?: QueryUnSignAgreementListResponse;
  onCompleted?: () => void;
  renderCompulsory?: () => React.JSX.Element | null;
  getExtensionParams?: GetExtensionParams;
  onError: any;
  force?: boolean;
  buttonText?: string;
  useLoanCredit?: boolean;
  onExpand: () => void;
  renderCompulsoryTitle?: () => JSX.Element;
  useContentsNew?: boolean;
}

export default function Compulsory(props: CompulsoryProps) {
  const {
    queryResponse, bizType, creditType, onCompleted = noop, renderCompulsory,
    getExtensionParams, onError, force = false, buttonText = '同意协议并扫脸',
    useLoanCredit = false, onExpand, renderCompulsoryTitle, useContentsNew,
  } = props;
  // 用于预览的协议
  const [contentList, setContentList] = useState<AgreementDTO[]>([]);
  const { count, start } = useCountDown({
    duration: 1000,
    onEnd: force ? doNoop : setCompulsoryEnd,
  });

  const doPreview = async () => {
    try {
      // 当前协议为空
      if (!queryResponse?.unSignedAgreementGroupList?.length) {
        throw new Error('UNSIGNED_AGREEMENT_NULL');
      }
      const promiseList: any = [];
      const { unSignedAgreementGroupList } = queryResponse;
      const minReadTime = getMinReadTime(queryResponse);
      forEach(unSignedAgreementGroupList, (group) => {
        if (group?.unSignedAgreementList?.length) {
          forEach(group.unSignedAgreementList, (agreement) => {
            if (agreement) {
              const params = getPreviewParams({
                agreement,
                currentGroup: group,
                bizType,
                creditType,
                getExtensionParams,
              });
              const previewPromise = preview(params);
              promiseList.push(previewPromise);
            }
          });
        }
      });
      if (!promiseList.length) {
        throw new Error('UNSIGNED_PROMISE_NULL');
      }
      // 还有等待强制阅读的协议
      let contentResList: any[] = [];
      const promiseRes = await Promise.all(promiseList);
      forceAgreementPageLog({
        success: true,
        message: 'ssd-unsigned-agreement-compulsory-success',
        creditPlatform: 'MAYI_ZHIXIN',
      });
      if (promiseRes?.length) {
        forEach(promiseRes, (previewRes) => {
          if (previewRes?.previewAgreementList) {
            contentResList = concat(contentResList, previewRes?.previewAgreementList);
          }
        });
      }
      if (!contentResList.length) {
        throw new Error('UNSIGNED_CONTENT_LIST_NULL');
      }
      if (minReadTime && !checkCompulsoryEnd()) {
        start(minReadTime);
      }
      setContentList(contentResList);
    } catch (e) {
      forceAgreementPageLog({
        success: false,
        message: 'ssd-unsigned-agreement-compulsory-error',
        creditPlatform: 'MAYI_ZHIXIN',
      });
      onError && onError();
      Toast.show({
        icon: 'fail',
        content: '协议查询失败，请重试',
      });
    }
  };

  const handleForceClick = () => {
    // 强制阅读完成
    onCompleted();
  };

  const renderButton = () => {
    return (
      <div className={styles.force}>
        <Button
          className={styles.button}
          color="primary"
          block
          onClick={handleForceClick}
          disabled={count > 0 || !contentList?.length}
        >
          {buttonText}{ count > 0 ? `(${count}s)` : ''}
        </Button>
      </div>
    );
  };

  const renderTitle = () => {
    if (renderCompulsoryTitle) {
      return renderCompulsoryTitle();
    }
    if (useLoanCredit) {
      return null;
    }
    return <p className={styles.text}>阅读并确认以下协议</p>;
  };

  const renderContents = () => {
    if (useContentsNew) {
      return (
        <ContentsNew
          options={contentList}
          renderButton={renderButton}
          onExpand={onExpand}
        />
      );
    }
    if (useLoanCredit) {
      return (
        <ContentsNew
          options={contentList}
          renderButton={renderButton}
          useLoanCredit
          onExpand={onExpand}
        />
      );
    }
    return (
      <Contents
        options={contentList}
        renderButton={renderButton}
        size={renderCompulsory ? 'l' : 'n'}
        type="compulsoryContent"
      />
    );
  };

  useEffect(() => {
    doPreview();
    return () => {
      setContentList([]);
    };
  }, []);

  return (
    <div className={styles.compulsory}>
      <div className={styles.panel}>
        {renderCompulsory ? renderCompulsory() : null}
        {renderTitle()}
      </div>
      {renderContents()}
    </div>
  );
}
