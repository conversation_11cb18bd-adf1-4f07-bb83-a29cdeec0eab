/**
 * @file session storage
 */

import { SessionKVStorage } from '@ali/uni-api';
import { log } from '@alife/dtao-iec-spm-log';

const SSD_COMPULSORY_END = 'SSD_COMPULSORY_END';

function safeOthorLog(key: string) {
  try {
    log.addOtherLog(key);
  } catch (e) {}
}

export function setWindowSession() {
  try {
    window.sessionStorage.setItem(SSD_COMPULSORY_END, 'true');
  } catch (e) {}
}

export function getWindowSession() {
  try {
    return window.sessionStorage.getItem(SSD_COMPULSORY_END);
  } catch (e) {
    return '';
  }
}

export function setCompulsoryEnd() {
  try {
    safeOthorLog('ssd-compulsory-end-unneed');
    SessionKVStorage.setItem({
      key: SSD_COMPULSORY_END,
      value: 'true',
    });
  } catch (e) {
    setWindowSession();
  }
}


export function getCompulsoryEnd() {
  try {
    const res = SessionKVStorage.getItem({
      key: SSD_COMPULSORY_END,
    });
    return res;
  } catch (e) {
    return getWindowSession();
  }
}

export function checkCompulsoryEnd() {
  try {
    const isEnd = getCompulsoryEnd();
    if (isEnd === 'true') {
      safeOthorLog('check-ssd-compulsory-unneed');
      return true;
    }
    safeOthorLog('check-ssd-compulsory-need');
    return false;
  } catch (e) {
    return false;
  }
}

export function doNoop() {
  safeOthorLog('ssd-compulsory-end-need');
}
