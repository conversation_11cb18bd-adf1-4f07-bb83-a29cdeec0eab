.notice {
  padding: 32rpx 32rpx 32rpx 36rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  background-color: #fff;
  border-radius: 24rpx;

  .dot {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    height: 24rpx;
    width: 24rpx;
    background-color: #cce1ff;
    border-radius: 50%;
    flex: 0 0 auto;

    .inner {
      height: 10rpx;
      width: 10rpx;
      background-color: #1677ff;
      border-radius: 50%;
    }
  }

  .contentWrapper {
    margin-left: 12rpx;
    color: rgba(#000, 60%);

    .content {
      transition-timing-function: linear;
    }
  }

  .scroll {
    position: relative;

    &::before, &::after {
      content: '';
      position: absolute;
      top: 0;
      width: 32rpx;
      height: 100%;
      background-image: linear-gradient(90deg, rgba(#000, 0.00) 0%, #fff 71%);
      z-index: 2;
    }

    &::before {
      left: -2rpx;
      transform: scaleX(-1);
    }

    &::after {
      right: -2rpx;
    }
  }

  .action {
    margin-left: 26rpx;
    color: #1677ff;
    white-space: nowrap;
  }

  .icon {
    margin-left: 24rpx;
    width: 32rpx;
    height: 32rpx;
    background-image: url('https://gw.alicdn.com/imgextra/i4/O1CN01pIVshk1sbaGHhQrQW_!!6000000005785-2-tps-200-200.png');
    background-position: 50% 50%;
    background-repeat: no-repeat;
    background-size: cover;
    opacity: 0.6;
    flex: 0 0 auto;
  }
}
