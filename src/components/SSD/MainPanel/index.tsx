/**
 * @file 主展示区域
 */

import classNames from 'classnames';

import styles from './index.module.scss';

export interface MainPanelProps {
  title?: string;
  renderContent?: () => React.JSX.Element | null;
  renderDesc?: () => React.JSX.Element | null;
  renderHeader?: () => React.JSX.Element | null;
  type?: 'normal' | 'warning' | 'warning-text' | 'warning-light';
  renderButton?: () => React.JSX.Element | null;
}

export function MainPanel(props: MainPanelProps) {
  const {
    title, type = 'NORMAL', renderButton,
    renderContent, renderDesc, renderHeader,
  } = props;

  return (
    <div className={classNames(styles.mainPanel, styles[type], renderHeader && styles.withHeader)}>
      {renderHeader ? (
        <div className={styles.hd}>
          {renderHeader()}
        </div>
      ) : null}
      <div className={classNames(styles.bd)}>
        <p className={styles.title}>
          {title || ''}
        </p>
        {renderContent ? renderContent() : null}
        {renderDesc ? renderDesc() : null}
        <div className={styles.button}>
          {renderButton ? renderButton() : null}
        </div>
      </div>
    </div>
  );
}
