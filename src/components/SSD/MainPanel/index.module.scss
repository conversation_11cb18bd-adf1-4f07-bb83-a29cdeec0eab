.mainPanel {
  position: relative;
  .hd {
    width: 100%;
    padding: 68rpx 0;
  }
  .bd {
    border-radius: 24rpx;
    padding-top: 163rpx;
    padding-bottom: 158rpx;
    background-color: #fff;
    .title {
      font-size: 32rpx;
      color: rgba(#000, 80%);
      text-align: center;
      padding-bottom: 6rpx;
    }
    .button {
      padding: 0 96rpx;
      margin-top: 89rpx;
      font-size: 36rpx;
      font-weight: 500;
    }
  }
}
.withHeader {
  background-color: #e6ebf2;
  border-radius: 24rpx;
  .bd {
    padding-top: 112rpx;
    padding-bottom: 120rpx;
  }
}

.warning-text {
  .bd {
    .title {
      color: #ff3141;
    }
    .content {
      color: #ff3141;
    }
    p {
      color: #ff3141;
    }
  }
}

.warning.withHeader {
  background-color: #fff5f6;
}
.warning {
  .hd {
    p {
      color: #ff3141;
    }
  }
  .amount {
    .title {
      color: #ff3141;
    }
  }
  .bd {
    .title {
      color: #ff3141;
    }
    .content {
      color: #ff3141;
    }
    p {
      color: #ff3141;
    }
  }
}
