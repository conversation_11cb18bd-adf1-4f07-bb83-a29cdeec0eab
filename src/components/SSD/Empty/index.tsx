import styles from './index.module.scss';

interface EmptyResultProps {
  title?: string;
}

function Empty(props: EmptyResultProps) {
  const { title } = props;
  return (
    <div className={styles.emptyResult}>
      <img
        className={styles.emptyImg}
        src="https://gw.alicdn.com/imgextra/i3/O1CN01Wj2b0p1SUYAbrd3ko_!!6000000002250-2-tps-560-560.png"
      />
      <div className={styles.emptyText}>{title}</div>
    </div>
  );
}

export default Empty;
