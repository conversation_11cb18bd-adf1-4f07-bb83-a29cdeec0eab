import { useState } from 'react';
import classnames from 'classnames';
import styles from './index.module.scss';

interface CommonTabsProps {
  tabConfig: Array<{
    key: string;
    title: string;
  }>;
  defaultActiveKey?: string;
  onTabClick?: (key: string) => void;
}

function CommonTabs(props: CommonTabsProps) {
  const { tabConfig, defaultActiveKey, onTabClick } = props;
  const [activeKey, setActiveKey] = useState(defaultActiveKey);

  return (
    <div className={styles.tabs}>
      {tabConfig.map((item) => {
        return (
          <div
            key={item.key}
            onClick={() => {
              setActiveKey(item.key);
              onTabClick && onTabClick(item.key);
            }}
            className={classnames([
              styles['tab-item'],
              item.key === activeKey && styles['tab-item-actived'],
            ])}
          >
            {item.title}
          </div>
        );
      })}
    </div>
  );
}

export default CommonTabs;
