.tabs {
  height: 88rpx;
  display: flex;
  align-items: center;
  .tab-item {
    display: flex;
    flex: 1 1 auto;
    align-items: center;
    justify-content: center;
    font-size: 30rpx;
    font-weight: 500;
    color: #50607a;
    position: relative;
    &-actived {
      color: #000;
      &::after {
        content: "";
        display: block;
        position: absolute;
        bottom: -12rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 48rpx;
        height: 4rpx;
        background: var(--primary);
        border-radius: 2rpx;
      }
    }
  }
}
