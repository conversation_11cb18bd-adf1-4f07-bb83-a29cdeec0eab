

.maskContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  
  :global(.adm-mask-content) {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.contentContainer {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 80%;
  max-width: 660rpx;
}

.contentImage {
  width: 100%;
  height: auto;
}

/* 金额显示区域样式 */
.amountContainer {
  position: absolute;
  top: 45%;
  left: 150rpx;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: baseline;
  color: #fff;
  font-family: "ALIBABA NUMBER FONT MD";
}

.currency {
  font-size: 36rpx;
  font-weight: bold;
}

.amountValue {
  font-size: 72rpx;
  font-weight: bold;
}

/* 我知道了按钮样式 */
.confirmButton {
  position: absolute;
  bottom: 15rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 300rpx;
  height: 80rpx;
  // background-color: #FF4D4F;
  // color: white;
  // padding: 20rpx 60rpx;
  border-radius: 40rpx;
  font-weight: bold;
  cursor: pointer;
  // box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 虚拟div样式 */
.virtualDiv {
  position: absolute;
  bottom: 140rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 400rpx;
  height: 36rpx;
}

.closeButtonContainer {
  position: absolute;
  bottom: -100rpx;
  left: 50%;
  transform: translateX(-50%);
}

.closeButton {
  width: 72rpx;
  height: 72rpx;
  cursor: pointer;
}
