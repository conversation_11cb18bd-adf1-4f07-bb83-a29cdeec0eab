/**
 * @file 蒙层弹窗组件
 */

import { forwardRef, useImperativeHandle, useState } from 'react';
import { Mask } from 'antd-mobile';
import classNames from 'classnames';
import { log } from '@alife/dtao-iec-spm-log';
import styles from './index.module.scss';
import { PromotionOffer } from '@/store/types';
import { getPromotionOfferData } from '@/store/lib/format';

interface CommonMaskPopupProps {
  className?: string;
  onClose?: () => void;
  logParams?: Record<string, any>;
  amount?: string | number;
  offerList: PromotionOffer[] | any;
  descriptionDisplayPosition: string;
}

export interface ICommonMaskPopupRef {
  toggleVisible: (visible: boolean) => void;
}

function CommonMaskPopup(
  { className, onClose, logParams = {}, offerList, descriptionDisplayPosition }: CommonMaskPopupProps,
  ref
) {
  const [visible, setVisible] = useState(false);

  const promotionOfferData = getPromotionOfferData(offerList, descriptionDisplayPosition);

  if (!promotionOfferData) return null;

  const handleClose = () => {
    log.addClickLog('common-mask-popup-close', logParams);
    onClose && onClose();
    setVisible(false);
  };

  const toggleVisible = (value: boolean) => {
    setVisible(value);
    if (value) {
      // 添加曝光埋点
      log.addShowLog('common-mask-popup-show', logParams);
    }
  };

  useImperativeHandle(ref, () => ({
    toggleVisible,
  }));

  return (
    <Mask
      visible={visible}
      onMaskClick={handleClose}
      className={classNames(styles.maskContainer, className)}
    >
      <div className={styles.contentContainer}>
        <img
          src="https://gw.alicdn.com/imgextra/i3/O1CN01UhoYbJ1a9UvhnWFss_!!6000000003287-2-tps-1320-1200.png"
          className={styles.contentImage}
        />

        {/* 金额显示区域 */}
        <div className={styles.amountContainer}>
          <span className={styles.currency}>¥</span>
          <span className={styles.amountValue}>{promotionOfferData.amount}</span>
        </div>

        {/* 我知道了按钮 */}
        <div className={styles.confirmButton} onClick={handleClose}></div>

        <div className={styles.closeButtonContainer}>
          <img
            src="https://gw.alicdn.com/imgextra/i2/O1CN01MyiF1e21HjgBGQtnV_!!6000000006960-2-tps-144-144.png"
            className={styles.closeButton}
            onClick={handleClose}
          />
        </div>
      </div>
    </Mask>
  );
}

export default forwardRef(CommonMaskPopup);
