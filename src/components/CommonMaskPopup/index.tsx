/**
 * @file 蒙层弹窗组件
 */

import { forwardRef, useImperativeHandle, useState } from 'react';
import { Mask } from 'antd-mobile';
import classNames from 'classnames';
import { log } from '@alife/dtao-iec-spm-log';
import styles from './index.module.scss';
import { navigatorOpenURL } from '@/utils/link';

interface CommonMaskPopupProps {
  className?: string;
  onClose?: () => void;
  logParams?: Record<string, any>;
}

export interface ICommonMaskPopupRef {
  toggleVisible: (visible: boolean, data?: any) => void;
}

function CommonMaskPopup(
  { className, onClose, logParams = {} }: CommonMaskPopupProps,
  ref,
) {
  const [visible, setVisible] = useState(false);
  const [data, setData] = useState<any>(null);

  const handleClose = () => {
    log.addClickLog('common-mask-popup-close', logParams);
    onClose && onClose();
    setVisible(false);
  };

  const toggleVisible = (value: boolean, payload: any) => {
    setVisible(value);
    if (value) {
      setData(payload);
      // 添加曝光埋点
      log.addShowLog('common-mask-popup-show', logParams);
    }
  };

  const handleToWotaoKabao = () => {
    log.addClickLog('common-mask-popup-to-wotao', logParams);
    if (data.url) {
      navigatorOpenURL(data.url);
    }
  };

  useImperativeHandle(ref, () => ({
    toggleVisible,
  }));

  if (!data) return null;

  return (
    <Mask
      visible={visible}
      onMaskClick={handleClose}
      className={classNames(styles.maskContainer, className)}
    >
      <div className={styles.contentContainer}>
        <img
          src="https://gw.alicdn.com/imgextra/i3/O1CN013xjB8R1imRXDyaMCW_!!6000000004455-2-tps-660-600.png"
          className={styles.contentImage}
        />

        {/* 金额显示区域 */}
        <div className={styles.amountContainer}>
          <span className={styles.currency}>¥</span>
          <span className={styles.amountValue}>{data?.amount}</span>
        </div>

        {/* 虚拟div */}
        <div className={styles.virtualDiv} onClick={handleToWotaoKabao} />

        {/* 我知道了按钮 */}
        <div className={styles.confirmButton} onClick={handleClose} />

        <div className={styles.closeButtonContainer}>
          <img
            src="https://gw.alicdn.com/imgextra/i2/O1CN01MyiF1e21HjgBGQtnV_!!6000000006960-2-tps-144-144.png"
            className={styles.closeButton}
            onClick={handleClose}
          />
        </div>
      </div>
    </Mask>
  );
}

export default forwardRef(CommonMaskPopup);
