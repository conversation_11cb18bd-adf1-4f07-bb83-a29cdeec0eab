/**
 * @file 协议预览
 * <AUTHOR>
 */

import { useEffect, useRef, useState } from 'react';
import { Button } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';

import { AGREEMENT_PREVIEW } from '@/common/constant';
import type { UnsignedAgreementList } from '@/store/agreement/actions';
import useCountDown from '@/hooks/useCountDown';
import { navigatorToOutside } from '@/utils/link';

import styles from './index.module.scss';
import { forceAgreementPageLog } from '@/utils/goc';

interface CompulsoryProps {
  bizType?: string;
  options?: UnsignedAgreementList[];
  params?: any;
  currentIndex?: number;
  onChange?: any;
  onCompleted?: any;
  showOption?: any;
}

export default function Compulsory(props: CompulsoryProps) {
  const { bizType, options, showOption, params, currentIndex, onChange, onCompleted } = props;
  const { count, start } = useCountDown({
    duration: 1000,
  });
  const [isCompleted, setIsCompleted] = useState(false);
  const completedRef = useRef(false);

  const postMessage = (iframe: any) => () => {
    // @ts-ignore
    iframe?.contentWindow?.postMessage({
      module: 'xfd-preview-sandbox',
      data: {
        bizType,
        options,
        showOption: {
          action: 'compulsory',
          params,
          currentIndex,
        },
      },
    }, '*');
  };

  const handlePreview = () => {
    if (bizType && options && showOption?.action) {
      const previewContainer = document.getElementById('agreementCompulsory');
      if (previewContainer) {
        postMessage(previewContainer)();
      }
    }
  };

  const handleRead = () => {
    log.addClickLog('xfd-preview-sandbox-completed');
    onChange && onChange(true);
    onCompleted();
  };

  const handleCompulsoryMessage = (event?: any) => {
    const module = event?.data?.module;
    if (!completedRef.current && module === 'xfd-preview-sandbox') {
      forceAgreementPageLog({
        success: true,
        creditPlatform: 'ALIYUN',
        message: 'force',
      });
      completedRef.current = true;
      setIsCompleted(true);
      start(5);
    }
    if (module === 'xfd-preview-click') {
      navigatorToOutside(event?.data?.data?.url);
    }
    if (module === 'xfd-preview-error') {
      forceAgreementPageLog({
        success: false,
        creditPlatform: 'ALIYUN',
        message: 'xfd-preview-compulsory-error',
      });
      completedRef.current = false;
      setIsCompleted(false);
    }
    if (module === 'xfd-preview-ready') {
      log.addOtherLog('xfd-preview-compulsory-ready');
      handlePreview();
    }
  };

  useEffect(() => {
    window.addEventListener('message', handleCompulsoryMessage);
    return () => {
      window.removeEventListener('message', handleCompulsoryMessage);
    };
  }, []);

  return (
    <div className={styles.agreements}>
      <iframe
        id="agreementCompulsory"
        className={styles.compulsory}
        src={AGREEMENT_PREVIEW}
      />
      <div className={styles.compulsoryButton}>
        <Button
          className={styles.read}
          color="primary"
          disabled={count < 0 || !options?.length || count > 0 || !isCompleted}
          block
          onClick={handleRead}
        >
          同意以上协议{count ? `（${count}s）` : ''}
        </Button>
      </div>
    </div>
  );
}
