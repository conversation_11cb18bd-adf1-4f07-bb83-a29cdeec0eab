/**
 * @file 协议预览
 * <AUTHOR>
 */

import { useEffect } from 'react';
import { log } from '@alife/dtao-iec-spm-log';

import { AGREEMENT_PREVIEW } from '@/common/constant';
import type { UnsignedAgreementList } from '@/store/agreement/actions';
import { navigatorToOutside } from '@/utils/link';

import styles from './index.module.scss';

interface PreviewProps {
  bizType?: string;
  options?: UnsignedAgreementList[];
  params?: any;
  currentIndex?: number;
  showOption?: any;
}

export default function Preview(props: PreviewProps) {
  const { bizType, options, showOption, params, currentIndex } = props;

  const postMessage = (iframe: any) => () => {
    // @ts-ignore
    iframe?.contentWindow?.postMessage({
      module: 'xfd-preview-sandbox',
      data: {
        bizType,
        options,
        showOption: {
          action: 'preview',
          params,
          currentIndex,
        },
      },
    }, '*');
  };

  const handlePreview = () => {
    if (bizType && options && showOption?.action) {
      const previewContainer = document.getElementById('agreementPreview');
      if (previewContainer) {
        postMessage(previewContainer)();
      }
    }
  };

  const handlePreviewMessage = (event?: any) => {
    const module = event?.data?.module;
    if (module === 'xfd-preview-click') {
      navigatorToOutside(event?.data?.data?.url);
    }
    if (module === 'xfd-preview-error') {
      log.addErrorLog('xfd-preview-preview-error', event?.data);
    }
    if (module === 'xfd-preview-ready') {
      log.addOtherLog('xfd-preview-preview-ready');
      handlePreview();
    }
  };

  useEffect(() => {
    window.addEventListener('message', handlePreviewMessage);
    return () => {
      window.removeEventListener('message', handlePreviewMessage);
    };
  }, []);

  return (
    <div className={styles.agreements}>
      <iframe
        id="agreementPreview"
        className={styles.preview}
        src={AGREEMENT_PREVIEW}
      />
    </div>
  );
}
