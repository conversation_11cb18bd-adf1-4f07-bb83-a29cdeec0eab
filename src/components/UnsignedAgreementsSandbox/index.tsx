/**
 * @file 协议预览
 * <AUTHOR>
 */

import {
  forwardRef, useCallback, useEffect,
  useImperativeHandle, useMemo, useRef, useState,
} from 'react';
import classNames from 'classnames';
import { Toast } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';

import { queryUnSignAgreementList, QueryUnSignAgreementListResponse } from '@/store/agreement/actions';
import { _ } from '@/utils';
import CommonPopup from '../CommonPopup';
import { ErrorMessageMap } from '@/common/constant';
import { getAgreementNames } from '../UnsignedAgreements/format';
import Compulsory from './Compulsory';
import Preview from './Preview';

import styles from './index.module.scss';

interface UnsignedAgreementsProps {
  className?: string;
  institutionList?: string[];
  title?: string;
  prefix?: string;
  bizType: 'PLATFORM' | 'LOAN' | 'CREDIT' | 'BIND_CARD';
  popupTitle?: string;
  renderTitle?: (result?: QueryUnSignAgreementListResponse) => React.JSX.Element | null;
  onChange?: (value?: boolean) => void;
  onCompleted?: () => void;
  onError?: () => void;
  getParams?: () => any;
  onClose?: () => void;
}

interface ShowOption {
  action?: 'preview' | 'compulsory' | '';
  params?: any;
  currentIndex?: number;
}

export interface UnsignedAgreementsSandboxRef {
  show: (option?: ShowOption) => void;
  close: () => void;
}

export const UnsignedAgreementsSandbox = forwardRef((props: UnsignedAgreementsProps, ref) => {
  const {
    prefix, institutionList, bizType, className, title, popupTitle = '查看协议',
    onChange, renderTitle, onCompleted, getParams, onError, onClose,
  } = props;
  const [showOption, setShowOption] = useState<ShowOption>();
  const [listResult, setListResult] = useState<QueryUnSignAgreementListResponse>();
  const [mainTitle, setMainTitle] = useState(popupTitle);
  const popupRef = useRef<any>();
  // institutionList是数组对象，每次重新赋值都会变化，这里转换一下
  const institutionListStr = useMemo(() => {
    try {
      if (institutionList?.length) {
        return JSON.stringify(institutionList);
      }
      return '';
    } catch (e) {
      return '';
    }
  }, [institutionList]);

  const handlePopupShow = useCallback((option?: ShowOption) => {
    setShowOption(option || {
      action: 'preview',
    });
    popupRef?.current?.toggleVisible(true);
    log.addShowLog(`unsigned-agreement-sandbox-${showOption?.action}-show`);
  }, [showOption?.action]);

  const handlePopupClose = useCallback(() => {
    log.addClickLog(`unsigned-agreement-sandbox-${showOption?.action}-close`);
    setShowOption({});
    popupRef?.current?.toggleVisible(false);
    onClose && onClose();
    setMainTitle(popupTitle);
  }, [showOption?.action]);

  const handleCompleted = useCallback(() => {
    popupRef?.current?.toggleVisible(false);
    setShowOption({
      action: '',
    });
    onCompleted && onCompleted();
    log.addClickLog('unsigned-agreement-sandbox-force-completed');
  }, [onCompleted]);

  const handlePreviewClick = useCallback(() => {
    setShowOption({
      action: 'preview',
      params: getParams && getParams(),
    });
    popupRef?.current?.toggleVisible(true);
  }, [getParams]);

  const handlePreviewClickIndex = useCallback((index: number) => {
    setShowOption({
      action: 'preview',
      params: getParams && getParams(),
      currentIndex: index,
    });
    popupRef?.current?.toggleVisible(true);
  }, [getParams]);

  const fetchUnSignAgreementList = useCallback(async () => {
    try {
      if (!bizType) {
        return;
      }
      const result = await queryUnSignAgreementList({
        institutionList: institutionListStr,
        bizType,
      });
      if (result?.unSignedAgreementGroupList) {
        setListResult(result);
        log.addSuccessLog('unsigned-agreeement-sandbox-success');
      } else {
        throw new Error();
      }
    } catch (e) {
      onError && onError();
      Toast.show({
        content: ErrorMessageMap.BUSY_DEFUALT,
      });
      log.addOtherLog('unsigned-agreeement-sandbox-error');
    }
  }, [institutionListStr, bizType]);

  const renderAgreement = () => {
    switch (showOption?.action) {
      case 'compulsory':
        return (
          <Compulsory
            options={listResult?.unSignedAgreementGroupList}
            bizType={bizType}
            onChange={onChange}
            onCompleted={handleCompleted}
            params={showOption.params}
            showOption={showOption}
          />
        );
      case 'preview':
        return (
          <Preview
            bizType={bizType}
            options={listResult?.unSignedAgreementGroupList}
            params={showOption.params}
            currentIndex={showOption?.currentIndex}
            showOption={showOption}
          />
        );
      default:
        return null;
    }
  };

  const renderAgreementsName = useCallback(() => {
    const unSignedAgreementGroupList = listResult?.unSignedAgreementGroupList;
    if (renderTitle && unSignedAgreementGroupList?.length) {
      return (
        <div className={styles.agreementName} onClick={handlePreviewClick}>
          {renderTitle(listResult)}
        </div>
      );
    }
    if (title) {
      return (
        <div className={styles.agreementName} onClick={handlePreviewClick}>
          <span className={styles.name}>{title}</span>
        </div>
      );
    }
    if (unSignedAgreementGroupList?.length) {
      const names = getAgreementNames(unSignedAgreementGroupList);
      return _.map(names, (name, index) => (
        <p className={styles.name} onClick={() => handlePreviewClickIndex(index)}>
          {name}
        </p>
      ));
    }
    return null;
  }, [listResult, title, renderTitle, handlePreviewClick, handlePreviewClickIndex]);

  useEffect(() => {
    fetchUnSignAgreementList();
  }, [institutionListStr]);

  useEffect(() => {
    log.addShowLog('unsigned-agreement-sandbox-show');
  }, []);

  useImperativeHandle(ref, () => ({
    show: handlePopupShow,
    close: handlePopupClose,
  }));

  return (
    <div className={classNames(styles.agreementPreview, className && className)}>
      <div className={styles.title}>
        {prefix ? <div className={styles.prefix}>{prefix}</div> : null}
        {renderAgreementsName()}
      </div>
      <CommonPopup
        ref={popupRef}
        title={mainTitle}
        onClose={handlePopupClose}
        onMaskClick={handlePopupClose}
        contentClassName={styles.content}
      >
        <div className={styles.container}>
          {renderAgreement()}
        </div>
      </CommonPopup>
    </div>
  );
});
