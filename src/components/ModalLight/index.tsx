/**
 * @file 轻弹窗
 */

import { useImperativeHandle, useState, forwardRef, useCallback } from 'react';
import { Button, ButtonProps, CenterPopup } from 'antd-mobile';
import classNames from 'classnames';

import styles from './index.module.scss';

interface ButtonOption extends ButtonProps {
  key: string;
}

interface ModalLightProps {
  actions: ButtonOption[];
  children?: any;
}

export interface ModalLightRef {
  toggle: (visible: boolean) => void;
}

export const ModalLight = forwardRef((props: ModalLightProps, ref) => {
  const [visible, setVisible] = useState(false);
  const { children, actions = [] } = props;

  const toggle = useCallback((val: boolean) => {
    setVisible(val);
  }, []);

  const close = useCallback(() => {
    setVisible(false);
  }, []);

  const handleClick = useCallback((click: any) => () => {
    click && click();
    setVisible(false);
  }, []);

  useImperativeHandle(ref, () => ({
    toggle,
  }));

  return (
    <CenterPopup bodyClassName={styles.modalLight} visible={visible}>
      {children}
      <div className={styles.ft}>
        {actions?.map((option) => (
          <Button
            className={classNames(styles.button, option?.color === 'default' && styles.default)}
            color={option?.color || 'default'}
            onClick={handleClick(option.onClick) || close}
          >
            {option?.children}
          </Button>
        ))}
      </div>
    </CenterPopup>
  );
});
