/**
 * @file 机构图标
 */

import { InstitutionIcon } from '@alife/fui-icon';
import { ClientOnly } from 'ice';
import classnames from 'classnames';
import { toLower, toUpper } from 'lodash-es';

import styles from './index.module.scss';

interface IconInstitutionProps {
  type?: string;
  className?: string;
  source?: string;
}

export default function IconInstitution(props: IconInstitutionProps) {
  const { type, className, source } = props;

  if (source === 'mycdn') {
    return <img className={className} src={`https://mdn.alipayobjects.com/fin_instasset/uri/img/as/instasset/${toUpper(type)}/BANK_LOGO/PURE?zoom=88w_88h.png`} />;
  }

  const defaultIcon = (
    <img
      className={styles.default}
      src="https://gw.alicdn.com/imgextra/i3/O1CN01WDV6B01VoeNpriBZf_!!*************-2-tps-96-96.png"
    />
  );


  if (toLower(type) === 'lx') {
    return (
      <ClientOnly>
        {() => (
          <div
            className={classnames([styles.lxIcon, className])}
          />
        )}
      </ClientOnly>
    );
  }

  if (toLower(type) === 'dxm') {
    return (
      <ClientOnly>
        {() => (
          <div
            className={classnames([styles.dxmIcon, className])}
          />
        )}
      </ClientOnly>
    );
  }

  if (toLower(type) === 'ai_bank') {
    return (
      <ClientOnly>
        {() => (
          <div
            className={classnames([styles.aibankIcon, className])}
          />
        )}
      </ClientOnly>
    );
  }

  if (toLower(type) === 'xykd') {
    return (
      <ClientOnly>
        {() => (
          <div
            className={classnames([styles.xykdIcon, className])}
          />
        )}
      </ClientOnly>
    );
  }

  if (toLower(type) === 'hkbea') {
    return (
      <ClientOnly>
        {() => (
          <InstitutionIcon defaultImg={defaultIcon} type="hkbeab" className={className} />
        )}
      </ClientOnly>
    );
  }

  return (
    <ClientOnly>
      {() => (
        <InstitutionIcon defaultImg={defaultIcon} type={toLower(type)} className={className} />
      )}
    </ClientOnly>
  );
}
