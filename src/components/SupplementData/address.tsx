/**
 * @file 居住地址
 */

import { Form, TextArea } from 'antd-mobile';

import AddressSelector from '../AddressSelector';
import { addressValidator } from './validator';

import styles from './index.module.scss';

export default function Address() {
  return (
    <div className={styles.address}>
      <p className={styles.title}>居住地址</p>
      <Form.Item
        rules={[{
          validator: addressValidator,
        }]}
        name="region"
        label="所在地区"
        required
      >
        <AddressSelector popupPosition="right" />
      </Form.Item>
      <Form.Item
        rules={[{
          required: true,
          message: '请填写详细地址',
        }, {
          pattern: /^.{4,30}$/,
          message: '请输入4~30个汉字',
        }]}
        name="residenceAddress"
        label="详细地址"
        required
      >
        <TextArea
          rows={2}
          placeholder="小区、门牌号等"
          className={styles.addressTextArea}
          showCount={() => <span className={styles.addressTextAreaCount}>4～30个汉字</span>}
          maxLength={40}
        />
      </Form.Item>
    </div>
  );
}
