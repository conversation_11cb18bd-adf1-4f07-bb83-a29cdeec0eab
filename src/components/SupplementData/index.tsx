/**
 * @file 补充信息组件
 * <AUTHOR>
 */

import { useCallback, forwardRef, useImperativeHandle } from 'react';
import { Form, Input, Button } from 'antd-mobile';
import classNames from 'classnames';
import { log } from '@alife/dtao-iec-spm-log';

import { SupplementDataCollection, SupplementDataType, SupplementDataFormData } from '@/store/types';
import * as optionsMap from './options';
import PopupListField from '../PopupListField';
import { _ } from '@/utils';
import Address from './address';
import Contact from './contact';

import styles from './index.module.scss';

interface SupplementDataProps {
  collections?: SupplementDataCollection;
  onFinish: any;
  onChange?: any;
  initData?: SupplementDataFormData;
}

interface ItemOption {
  type: SupplementDataType;
  options: Option[];
  minCount?: number;
  content: {
    title: string;
    label: string;
  };
}

export interface SupplementDataRef {
  form: any;
}

export const SupplementData = forwardRef((props: SupplementDataProps, ref) => {
  const { collections, onFinish, initData, onChange } = props;
  const [form] = Form.useForm<SupplementDataFormData>();

  const handleInputClick = useCallback(() => {
    log.addClickLog('company-name-click');
  }, []);

  const handleValuesChange = useCallback((changedValues: any, allValues: any) => {
    onChange && onChange(allValues);
  }, [onChange]);

  const renderItem = useCallback((option: ItemOption) => {
    const { type, content, options } = option;
    switch (type) {
      case 'profession':
      case 'education':
      case 'maritalStatus':
      case 'monthlyIncome':
        return (
          <Form.Item
            className={styles.item}
            label={content?.label}
            name={type}
            required
            rules={[{
              required: true,
              message: `请选择${content?.label}`,
            }]}
          >
            <PopupListField
              className={classNames(styles.supplementDataField, 'normal-input-field')}
              placeholder={content?.label}
              popupProps={{
                title: content?.title,
                position: 'right',
              }}
              logKey={type}
              checkListProps={{
                options,
              }}
            />
          </Form.Item>
        );
      case 'companyName':
        return (
          <Form.Item
            className={styles.item}
            label={content?.label}
            name={type}
            required
            rules={[{
              required: true,
              message: '请填写公司名称',
            }, {
              min: 2,
              message: '请输入至少2位字符',
            }]}
          >
            <Input
              onClick={handleInputClick}
              placeholder={content?.title}
            />
          </Form.Item>
        );
      default:
        return null;
    }
  }, [handleInputClick]);

  const renderCollection = useCallback((
    type: SupplementDataType,
    index: number,
  ) => {
    if (collections) {
      const collection = collections[type];
      if (!_.isNull(collection)) {
        const options = collection?.options || [];
        const content = optionsMap?.mapContent[type];
        const minCount = collection?.minCount;
        return (
          <div className={styles.itemC} key={`supplement-item-${type}-${index}`}>
            {renderItem({ type, options, content, minCount })}
          </div>
        );
      }
    }
    return null;
  }, [collections, renderItem]);

  const supplementForm = () => {
    if (collections) {
      const keys = Object.keys(collections);
      return _.map(keys, renderCollection);
    }
    return null;
  };

  useImperativeHandle(ref, () => ({
    form,
  }));

  return (
    <Form
      form={form}
      className={styles.supplementForm}
      layout="horizontal"
      onFinish={onFinish}
      initialValues={initData}
      onValuesChange={handleValuesChange}
      footer={(
        <div className={styles.submit}>
          <Button type="submit" color="primary" block>确定</Button>
        </div>
      )}
    >
      {supplementForm()}
      {(initData?.region || initData?.residenceAddress) ? (
        <Address />
      ) : null}
      {collections?.emergencyContact ? (
        <Contact
          options={collections.emergencyContact.options}
          length={collections.emergencyContact.minCount || 0}
          form={form}
        />
      ) : null}
    </Form>
  );
});
