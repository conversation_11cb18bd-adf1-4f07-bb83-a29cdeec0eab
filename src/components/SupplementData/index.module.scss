.supplementDataField {
  --field-panel-text-color: #7c889c;
}

.title {
  font-size: 30rpx;
  font-weight: 600;
  line-height: 30rpx;
  letter-spacing: 0;
  color: #111;
  padding-bottom: 40rpx;
}

.supplementForm {
  height: 962rpx;
  overflow: auto;
  padding-bottom: 102rpx;
  :global(.adm-list-item-content-prefix) {
    --prefix-width: 180rpx;
  }
  :global(.adm-list.adm-list-card) {
    margin: 0;
  }
  :global(.adm-form-footer) {
    position: absolute;
    width: 100%;
    bottom: 0;
    background-color: #fff;
    padding: 0;
  }
}

.submit {
  width: 100%;
  background-color: #fff;
}

.item, .address, .contact {
  padding-bottom: 18rpx;
}

.addressTextArea {
  text-align: right;
  .addressTextAreaCount {
    color: #7c889c;
  }
}
