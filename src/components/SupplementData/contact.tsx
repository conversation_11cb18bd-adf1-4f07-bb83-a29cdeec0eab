/**
 * @file 紧急联系人
 */

import { Form, Input } from 'antd-mobile';
import classNames from 'classnames';

import PopupListField from '../PopupListField';
import PhoneNumberInput from '../PhoneNumberInput';
import { PHONE_REGX, VIRTUAL_PHONE_REGX } from '@/common/constant';

import styles from './index.module.scss';

interface ContactProps {
  length: number;
  options: any;
  form: any;
}

export default function Contact(props: ContactProps) {
  const { length, options, form } = props;

  const validateName = (rule: any, value: string) => {
    return new Promise((resolve, reject) => {
      if (!value) {
        resolve('');
        return;
      }
      if (value.length < 2) {
        reject('至少包含2个汉字');
        return;
      }
      if (value.length > 30) {
        reject('不能超过30个汉字');
        return;
      }
      const chineseCharacterRegex = /^[\u4e00-\u9fa5]+(?:\u00B7[\u4e00-\u9fa5]+)*$/;
      if (!chineseCharacterRegex.test(value)) {
        reject('联系人姓名格式不正确');
        return;
      }
      const contacts = form.getFieldValue('emergencyContact') || [];
      const names = contacts.map((contact: any) => contact.name);
      if (names.filter((name: string) => name === value).length > 1) {
        reject('请提供不同的联系人');
      } else {
        resolve('');
      }
    });
  };

  const validatePhone = (rule: any, value: string) => {
    return new Promise((resolve, reject) => {
      if (!value) {
        resolve('');
        return;
      }

      // 检验重复手机号
      const contacts = form.getFieldValue('emergencyContact') || [];
      const phones = contacts.map((contact: any) => contact.phone);
      if (phones.filter((phone: string) => phone === value).length > 1) {
        reject('联系人手机号不可以重复');
        return;
      }

      // 校验手机格式
      if (!PHONE_REGX.test(value)) {
        reject('手机号格式错误');
      } else if (VIRTUAL_PHONE_REGX.test(value)) {
        reject('手机号不正确');
      } else {
        resolve('');
      }
    });
  };

  return (
    <div className={styles.contact}>
      <Form.Array
        name="emergencyContact"
        renderHeader={({ index }) => (
          <>
            <p className={styles.title}>常用联系人{length > 1 ? index + 1 : ''}</p>
          </>
        )}
      >
        {(fields) =>
          fields.map(({ index }) => (
            <>
              <Form.Item
                name={[index, 'name']}
                label="姓名"
                rules={[
                  {
                    required: true,
                    message: '姓名不能为空',
                  },
                  { validator: validateName },
                ]}
              >
                <Input placeholder="联系人姓名" />
              </Form.Item>
              <Form.Item
                required
                rules={[
                  { required: true, message: '手机号不能为空' },
                  { validator: validatePhone },
                ]}
                name={[index, 'phone']}
                label="手机号"
              >
                <PhoneNumberInput placeholder="手机号" />
              </Form.Item>
              <Form.Item
                required
                name={[index, 'relationship']}
                label="联系人关系"
                rules={[
                  {
                    required: true,
                    message: '请选择联系人关系',
                  },
                ]}
              >
                <PopupListField
                  className={classNames(styles.supplementDataField, 'normal-input-field')}
                  placeholder="您和联系人关系"
                  popupProps={{
                    title: '您和联系人关系',
                    position: 'right',
                  }}
                  checkListProps={{
                    options,
                  }}
                />
              </Form.Item>
            </>
          ))
        }
      </Form.Array>
    </div>
  );
}
