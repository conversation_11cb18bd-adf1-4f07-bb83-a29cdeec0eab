/**
 * @file 改版金额输入框组件
 * <AUTHOR>
 */

import {
  useCallback,
  useMemo,
  createRef,
  useLayoutEffect,
  useState,
  useEffect,
} from 'react';
import { Toast, NumberKeyboard } from 'antd-mobile';
import { number } from '@ali/iec-dtao-utils';
import classNames from 'classnames';
import { log } from '@alife/dtao-iec-spm-log';
import { toString } from 'lodash-es';

import { Selector } from '@/components';
import { noop, isGreaterThan } from '@/utils';
import { blurExcept } from './globalFocus';
import EventInside from './EventInside';
import useGlobalFocus from './useGlobalFocus';
import useDocumentEvent from './useDocumentEvent';

import styles from './index.module.scss';

export enum SPECIAL_KEY {
  confirm = 'confirm',
  delete = 'delete',
}

interface AmountInputProps {
  label?: string;
  value?: string;
  onChange?: (value: string) => void;
  onConfirm?: (value: string) => void;
  onFocus?: (value: string) => void;
  onBlur?: (value: string) => void;
  placeholder?: string;
  recommends?: any;
  disabled?: boolean;
  inputMode?: 'numeric' | 'decimal';
  max?: string | number;
  renderAppend?: () => React.JSX.Element;
  autoFocus?: boolean;
  clearable?: boolean;
}

export default function AmountInput(props: AmountInputProps) {
  const {
    label,
    value = '',
    placeholder = '',
    recommends,
    disabled,
    inputMode = 'decimal',
    onChange = noop,
    renderAppend,
    max,
    autoFocus,
    onConfirm = noop,
    onFocus = noop,
    onBlur = noop,
    clearable = true,
  } = props;
  const focusRef = createRef<any>();
  const inputRef = createRef<any>();
  const [focus, setFocus] = useState(false);

  const unit = useMemo(() => {
    const { getNumber } = number;
    const numberValue = getNumber(value);
    if (numberValue === '--') {
      return '';
    }
    if (numberValue >= 10000 && numberValue < 100000) {
      return '万';
    } else if (numberValue >= 100000 && numberValue < 1000000) {
      return '十万';
    } else if (numberValue >= 1000000 && numberValue < 10000000) {
      return '百万';
    } else if (numberValue >= 10000000) {
      return '千万';
    }
    return '';
  }, [value]);

  const doBlur = () => {
    if (focus) {
      // 两个焦点都要失效
      // forwardRef?.current?.blur()
      focusRef.current?.blur();
      setFocus(false);
      onBlur(value);
    }
  };

  const uuid = useGlobalFocus(doBlur, focus);
  useDocumentEvent(doBlur, focus);
  const doFocus = () => {
    // hack 先把其他 focus 状态的数字键盘关掉
    blurExcept(uuid);
    if (disabled) {
      return;
    }
    if (!focus) {
      setFocus(true);
      focusRef.current?.focus();
      onFocus(value);
    }
  };

  useLayoutEffect(() => {
    autoFocus && doFocus();

    inputRef.current.addEventListener('touchstart', () => {
      // 在这里处理touchstart事件的逻辑
      doFocus();
    });
  }, []);

  // 清除按钮点击
  const handleClear = () => {
    onChange('');
  };

  const queryFakeInputEl: any = useCallback(() => {
    return document.querySelector('#fake-input-el')!;
  }, []);

  // 数字键盘输入
  const onKeypadPress = (v: string) => {
    let valueAfterChange;

    log.addClickLog('amount-input-keypadPress', { v });

    if (v === SPECIAL_KEY.delete) {
      valueAfterChange = value.substring(0, value.length - 1);
      onChange(valueAfterChange);
    } else if (v === SPECIAL_KEY.confirm) {
      valueAfterChange = value;
      onChange(valueAfterChange);
      doBlur();
      onConfirm(valueAfterChange);
    } else {
      valueAfterChange = value + v;

      if (inputMode === 'numeric') {
        // 非正整数
        if (!/^[1-9]\d*$/.test(valueAfterChange)) {
          return;
        }
        // 首字母非0
        if (!value && valueAfterChange === '0') {
          return;
        }
      }
      // 数字+小数点的数字键盘模式
      if (inputMode === 'decimal') {
        // 非正两位小数
        if (!/^\d+(\.\d{0,2})?$/.test(valueAfterChange)) {
          return;
        }
        // 0开头的数字
        if (/^0\d\d*$/.test(valueAfterChange)) {
          return;
        }
      }

      if (max && isGreaterThan(valueAfterChange, max)) {
        Toast.show({
          content: `输入金额不能大于${max}元`,
        });
        onChange(toString(max));
      } else {
        onChange(valueAfterChange);
      }
    }
  };

  const unitLine = useMemo(() => {
    return (
      <div className={styles.lineC}>
        <div className={styles.line}>
          <div className={classNames(styles.arrow, !unit && styles.arrowNone)}>
            <i className={styles.arrowC} />
            <i className={styles.arrowB} />
          </div>
        </div>
        {unit ? <i className={styles.unit}>{unit}</i> : null}
      </div>
    );
  }, [unit]);

  const handleRecommendChange = useCallback(
    (val: string) => {
      onChange && onChange(val);
      log.addClickLog('amount-input-recommend', {
        value: val,
      });
      log.addChangeLog('amount-input', {
        value: val,
      });
    },
    [onChange],
  );

  const renderRecommends = useMemo(() => {
    if (recommends?.length) {
      return (
        <Selector
          customClassName={classNames(styles.recommends, !unit && styles.padding)}
          options={recommends}
          onChange={handleRecommendChange}
          logKey="amount-input"
        />
      );
    }
    return null;
  }, [unit, recommends, handleRecommendChange]);

  useEffect(() => {
    log.addShowLog('new-amount-input');
  }, []);

  return (
    <EventInside>
      <div className={classNames(styles.amountInput, disabled && styles.disabled)}>
        <div className={styles.labelWrap}>
          <span className={styles.label}>{label}</span>
        </div>
        <div className={styles.inputContainer}>
          <i className={styles.yuan} />
          <div ref={inputRef} className={styles.fakeInputWrap}>
            {clearable && value.length ? (
              <img
                onClick={handleClear}
                className={styles.clearIcon}
                src="https://gw.alicdn.com/imgextra/i2/O1CN01qSLaSf1IxjEa75M6b_!!6000000000960-2-tps-55-55.png"
              />
            ) : null}
            {value === '' && (
              <div className={styles.fakeInputPlaceholder}>{placeholder}</div>
            )}
            <div
              id="fake-input-el"
              role="textbox"
              ref={focusRef}
              tabIndex={-1}
              aria-label={value || placeholder}
              className={classNames(styles.fakeInput, {
                [styles.focus]: focus,
                'fake-input-disabled': disabled,
                'fake-input-with-clear': false,
              })}
            >
              {value}
              {focus && (
                <NumberKeyboard
                  getContainer={queryFakeInputEl}
                  onInput={onKeypadPress}
                  onDelete={() => onKeypadPress(SPECIAL_KEY.delete)}
                  onConfirm={() => onKeypadPress(SPECIAL_KEY.confirm)}
                  visible={focus}
                  onClose={() => {}}
                  customKey={inputMode === 'decimal' ? '.' : ''}
                  confirmText="确定"
                  showCloseButton={false}
                />
              )}
            </div>
          </div>

          {renderAppend ? renderAppend() : null}
        </div>
        {unitLine}
        {renderRecommends}
      </div>
    </EventInside>
  );
}
