.amountInput {
  :global(.adm-number-keyboard-popup .adm-popup-body-position-bottom) {
    padding-bottom: var(--safe-area-inset-bottom);
  }
  :global(.adm-number-keyboard-main) {
    * {
      &:not(:global(.adm-number-keyboard-key-number), :global(.adm-number-keyboard-key-sign)) {
        display: none;
      }
    }
  }
  .labelWrap {
    margin-bottom: 16rpx;
    .label {
      color: #7c889c;
      font-size: 24rpx;
      line-height: 36rpx;
    }
  }
  .lineC {
    .line {
      position: relative;
      height: 1rpx;
      background-color: #cacfd7;
      .arrow {
        position: absolute;
        top: -18rpx;
        left: 68rpx;
        .arrowC {
          position: absolute;
          left: 50%;
          transform: translate(-50%, 0);
          border-left: 10rpx solid transparent;
          border-right: 10rpx solid transparent;
          border-top: 10rpx solid transparent;
          border-bottom: 10rpx solid #cacfd7;
        }
        .arrowB {
          position: absolute;
          top: 1rpx;
          left: 50%;
          transform: translate(-50%, 0);
          border-left: 11rpx solid transparent;
          border-right: 11rpx solid transparent;
          border-top: 11rpx solid transparent;
          border-bottom: 11rpx solid white;
        }
      }
      .arrowNone {
        display: none;
      }
    }
    .unit {
      display: inline-block;
      font-size: 20rpx;
      line-height: 20rpx;
      color: #7c889c;
      font-style: normal;
      margin-left: 50rpx;
    }
  }
  .recommends {
    padding-top: 6rpx;
    div {
      &:last-child {
        p {
          font-size: 26rpx;
        }
      }
    }
  }
  .padding {
    padding-top: 28rpx;
  }
}

.inputContainer {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  margin-bottom: 8rpx;
  position: relative;
  .yuan {
    position: relative;
    display: block;
    background-image: url("https://gw.alicdn.com/imgextra/i2/O1CN01wdj4d31UCLf7sS7wY_!!6000000002481-2-tps-200-200.png");
    background-repeat: no-repeat;
    background-position: 50% 50%;
    background-size: cover;
    width: 19rpx;
    height: 36rpx;
    margin-bottom: 6rpx;
    margin-right: 10rpx;
  }
}

@keyframes keyboard-cursor {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

.fakeInputWrap {
  flex: 1 1 auto;
  height: 72rpx;
  position: relative;

  .fakeInputPlaceholder {
    position: absolute;
    left: 0;
    font-size: 40rpx;
    height: 100%;
    color: #bfbfbf;
    font-family: "ALIBABA NUMBER FONT MD";
    display: inline-flex;
    align-items: flex-end;
  }
  .clearIcon {
    position: absolute;
    right: 18rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 28rpx;
    height: 28rpx;
  }
}
.fakeInput {
  position: relative;
  height: 100%;
  display: inline-flex;
  font-size: 70rpx;
  line-height: 70rpx;
  color: #111;
  font-family: "ALIBABA NUMBER FONT MD";
  &.focus {
    outline: none; /* 去除默认的 focus 样式 */
    &:after {
      position: absolute;
      top: 0;
      right: -4rpx;
      height: 100%;
      border-right: 4rpx solid #1677ff;
      -webkit-animation: keyboard-cursor 1s step-start infinite;
      animation: keyboard-cursor 1s step-start infinite;
      content: "";
    }
  }
}
