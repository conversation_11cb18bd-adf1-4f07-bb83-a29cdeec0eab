/**
 * @file 还款方式描述
 * <AUTHOR>
 */

import { useEffect, useRef } from 'react';
import { number } from '@ali/iec-dtao-utils';
import classNames from 'classnames';
import { DotLoading } from 'antd-mobile';

import type { RepaymentMethodDTO, InstallmentPlanDTO, LoanModeValue } from '@/store/types';
import type { PROCESS_ACTION, TrialResponse } from '@/store/loan/types';
import { formatRepaymentMethod } from '@/store/lib/format';
import { CommonPopup, ArrowIcon } from '@/components';
import { _, getYMD, isGreaterThan, isGreaterThan0 } from '@/utils';

import styles from './index.module.scss';
import { log } from '@alife/dtao-iec-spm-log';

interface RepaymentMethodNewProps {
  options?: RepaymentMethodDTO[];
  trialStore?: TrialResponse;
  processAction?: PROCESS_ACTION;
  value?: LoanModeValue;
}

export default function RepaymentMethodDescNew(props: RepaymentMethodNewProps) {
  const { options, trialStore, processAction, value } = props;
  const popupRef = useRef<any>(null);

  const renderPlanItem = (plan: InstallmentPlanDTO, index: number) => {
    const { amountFormat } = number;
    if (plan) {
      const { interest, principal, totalAmount, endDate, promotionAmount } = plan;
      return (
        <div className={styles.planItem} key={`plan-item-${index}`}>
          <div className={styles.left}>
            <p className={styles.title}>
              {plan?.number === 1 ? '首' : plan?.number}期
            </p>
            <p className={styles.desc}>
              {getYMD(endDate)}
            </p>
          </div>
          <i className={styles.icon} />
          <div className={styles.right}>
            <p className={styles.title}>
              ¥{amountFormat(totalAmount)}
            </p>
            <p className={styles.desc}>
              <span>含本金</span>&nbsp;
              <span>¥{amountFormat(principal)}</span>
              <span className={styles.plus}>+</span>
              {
                isGreaterThan0(promotionAmount) ? <span className={styles.promotionText}>优惠后利息&nbsp;</span> : <span>利息</span>
              }
              <span>¥{amountFormat(interest)}</span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  const handleClick = () => {
    popupRef?.current?.toggleVisible(true);
  };

  const renderDescDetail = () => {
    if (!options?.length || !trialStore || !value) {
      return null;
    }
    const showIndex = options.length >= 2;
    return options.map((option, index) => {
      if (option?.value === 'AVERAGE_CAPITAL_PLUS_INTEREST') {
        return (
          <div key={`detail-item-${index}`} className={styles.detailItem}>
            <p className={styles.itemTitle}>
              {showIndex ? <i className={styles.itemIndex}>{index + 1}</i> : null}
              等额本息计息方式
            </p>
            <p className={styles.itemText}>
              <span className={styles.itemT}>每期利息</span>
              =剩余待还本金 x 日利率 x 当期借款天数
            </p>
            <p className={styles.itemText}>
              <span className={styles.itemT}>总利息</span>=每期利息总和-优惠总金额
            </p>
          </div>
        );
      }
      if (option?.value === 'BALLOON_MORTGAGE') {
        return (
          <div key={`detail-item-${index}`} className={styles.detailItem}>
            <p className={styles.itemTitle}>
              {showIndex ? <i className={styles.itemIndex}>{index + 1}</i> : null}
              先息后本计息方式
            </p>
            <p className={styles.itemText}>
              <span className={styles.itemT}>总利息</span>
              =本金 x 日利率 x 借款天数-优惠金额
            </p>
          </div>
        );
      }
      return null;
    });
  };

  const renderDesc = (installmentPlanList?: any[]) => {
    if (!trialStore) {
      return null;
    }
    return (
      <div className={styles.descContainer}>
        <p className={styles.tip}>当前总利息是按借满{installmentPlanList?.length}个月计算，总利息计算方式如下：</p>
        <div className={styles.descDetail}>
          {renderDescDetail()}
          <p className={classNames(styles.itemText, styles.anualInterest)}>
            年化利率=日利率 x 360天
          </p>
        </div>
        <i className={styles.arrowIcon} />
      </div>
    );
  };

  const renderPopup = () => {
    const { amountFormat } = number;
    if (!trialStore) {
      return null;
    }
    const { installmentPlanList, totalAmount, principal, interest, originInterest } = trialStore;
    return (
      <CommonPopup
        position="right"
        ref={popupRef}
        bodyClassName={styles.popupBodyClass}
        title="应还总额说明"
        transparentMask
      >
        <p className={styles.panelTitle}>
          借满{installmentPlanList?.length}个月，应还总额{amountFormat(totalAmount)}元
        </p>
        <div className={styles.panel}>
          <div className={styles.item}>
            <span>还款方式：</span>
            <span className={styles.itemValue}>
              {formatRepaymentMethod({
                value: value?.repaymentMethodValue,
              })}
            </span>
          </div>
          <div className={styles.item}>
            <span>借款本金：</span>
            <span className={styles.itemValue}>￥{amountFormat(principal)}</span>
          </div>
          <div className={styles.item}>
            <span>总利息：</span>
            <span className={styles.itemValue1}>
              <b>￥{amountFormat(interest)}</b>
              {isGreaterThan(originInterest, interest) ? <b className={styles.line}>￥{amountFormat(originInterest)}</b> : null}
            </span>
          </div>
          {renderDesc(installmentPlanList)}
        </div>
      </CommonPopup>
    );
  };

  const renderPlan = () => {
    const { amountFormat } = number;
    if (processAction === 'TRIALING') {
      return (
        <div className={styles.loading}>
          <p className={styles.text}>计算中</p>
          <DotLoading color="primary" />
        </div>
      );
    }
    if (!_.isEmpty(trialStore)) {
      const { installmentPlanList, totalAmount } = trialStore;
      return (
        <div className={styles.installmentPlan}>
          <p className={styles.infoBox} onClick={handleClick}>
            借满{installmentPlanList?.length}个月，应还总额{amountFormat(totalAmount)}元
            <ArrowIcon type="right" />
          </p>
          <div className={classNames(
            styles.planList,
            styles.panel,
          )}
          >
            {installmentPlanList?.map(renderPlanItem)}
          </div>
          {renderPopup()}
        </div>
      );
    }
    return null;
  };

  useEffect(() => {
    log.addShowLog('aliyun-repayment-method-desc-new');
  }, []);

  return renderPlan();
}
