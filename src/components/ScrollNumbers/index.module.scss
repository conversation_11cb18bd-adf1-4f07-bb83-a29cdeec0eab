.signNumber {
  display: flex;
  position: relative;
  height: 92px;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  &::before, &::after {
    content: '';
    display: inline-block;
    width: 100%;
    height: 30px;
    position: absolute;
    z-index: 1;
  }

  &::before {
    left: 0;
    top: 0;
    background-image: linear-gradient(to top, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1) 25%, rgba(255, 255, 255, 1));
  }

  &::after {
    left: 0;
    bottom: 0;
    background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1) 25%, rgba(255, 255, 255, 1));
  }
  .wrap {
    height: 50px;
    overflow: hidden;
    .container {
      @keyframes scroll {
        to {
          transform: translateY(-50%);
        }
      }
      animation: scroll 2s linear infinite;
      .list {
        .number {
          height: 40px;
          line-height: 40px;
          font-size: 54px;
          font-family: 'ALIBABA NUMBER FONT MD';
          color: #333;
          margin-bottom: 4px;
        }
      }
    }
  }
}
