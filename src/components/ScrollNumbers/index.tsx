/**
 * @file 数字滚动组件
 * <AUTHOR>
 */

import React from 'react';
import { shuffle } from 'lodash-es';
import classNames from 'classnames';

import styles from './index.module.scss';

const generateArr = (count: number) => {
  const arr = Array.from(Array(count).keys());
  return arr;
};
const numberArr = generateArr(10);

interface IScrollNumber {
  duration: number;
}

const ScrollNumber: React.FC<IScrollNumber> = (props) => {
  const { duration = 5 } = props;
  const showArr = shuffle(numberArr);
  return (
    <div className={styles.wrap}>
      <div
        className={styles.container}
        style={{
          animationDuration: `${duration}s`,
        }}
      >
        <div className={styles.list}>
          {showArr.map((item, index) => {
            return (
              <div className={styles.number} key={`s-${item}-${index}`}>
                {item}
              </div>
            );
          })}
        </div>
        <div className={styles.list}>
          {showArr.map((item, index) => {
            return (
              <div className={styles.number} key={`n-${item}-${index}`}>
                {item}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

interface IScrollNumbers {
  count?: number;
  duration?: number;
  durationStep?: number;
  className?: string;
}

const ScrollNumbers: React.FC<IScrollNumbers> = (props) => {
  const { count = 5, duration = 2, durationStep = 0.1, className } = props;
  const list = generateArr(count);

  return (
    <div className={classNames(styles.signNumber, className && className)}>
      {list.map((item, index) => {
        const numberDuration = duration - index * durationStep;
        return <ScrollNumber duration={numberDuration} key={index} />;
      })}
    </div>
  );
};

export default ScrollNumbers;
