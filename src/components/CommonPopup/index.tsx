/**
 * @file 弹窗组件
 */

import { ReactNode, useImperativeHandle, useState, forwardRef } from 'react';
import { Popup, PopupProps } from 'antd-mobile';
import classNames from 'classnames';

import styles from './index.module.scss';

interface CommonPopupProps extends PopupProps {
  title?: string | ReactNode;
  hideCloseIcon?: boolean;
  contentClassName?: string;
  transparentMask?: boolean;
  disableClose?: boolean;
}

function CommonPopup(
  {
    title = null,
    className = '',
    hideCloseIcon = false,
    children,
    onMaskClick,
    contentClassName,
    onClose,
    position = 'bottom',
    transparentMask = false,
    disableClose = false,
    ...otherProps
  }: CommonPopupProps,
  ref,
) {
  const [visible, setVisible] = useState(false);

  const handleClose = () => {
    if (disableClose) {
      onClose && onClose();
      return;
    }
    onClose && onClose();
    setVisible(false);
  };

  const toggleVisible = (value) => {
    setVisible(value);
  };

  useImperativeHandle(ref, () => ({
    toggleVisible,
  }));

  return (
    <Popup
      visible={visible}
      className={classNames(styles.popup, className && className)}
      onMaskClick={onMaskClick || handleClose}
      position={position}
      maskClassName={classNames(transparentMask && styles.transparentMask)}
      {...otherProps}
    >
      <div className={styles.hd}>
        <p className={styles.title}>{title}</p>
        {hideCloseIcon ? null : (
          <div
            className={classNames(
              styles.close, position && styles[position],
            )}
            onClick={handleClose}
          >
            <i className={classNames(styles.icon, styles[position])} />
          </div>
        )}
      </div>
      <div className={classNames(styles.content, contentClassName && contentClassName)}>
        {children}
      </div>
    </Popup>
  );
}

export interface ICommonPopupRef {
  toggleVisible: (visible: boolean) => void;
}

export default forwardRef(CommonPopup);
