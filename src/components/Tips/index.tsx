/**
 * @file 提示组件
 * <AUTHOR>
 */

import classNames from 'classnames';

import styles from './index.module.scss';

interface TipsProps {
  type: 'safe';
  content: string;
}

export default function Tips(props: TipsProps) {
  const { type, content } = props;

  return (
    <div className={classNames(styles.tips, styles[type])}>
      <i className={styles.icon} />
      <p className={styles.content}>{content}</p>
    </div>
  );
}
