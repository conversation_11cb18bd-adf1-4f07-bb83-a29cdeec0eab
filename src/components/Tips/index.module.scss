.tips {
  display: flex;
  flex-direction: row;
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  align-items: flex-start;
  justify-content: space-between;
  .icon {
    position: relative;
    top: -4rpx;
    display: block;
    width: 42rpx;
    height: 42rpx;
    background-image: url('https://gw.alicdn.com/imgextra/i2/O1CN01MobvAe1kdrJ62wjAx_!!6000000004707-2-tps-200-200.png');
    background-repeat: no-repeat;
    background-size: contain;
    background-position: 50% 50%;
  }
  .content {
    color: #7c889c;
    padding-left: 8rpx;
  }
}

.safe {
  background-color: rgba(#31cc81, 8%);
}
