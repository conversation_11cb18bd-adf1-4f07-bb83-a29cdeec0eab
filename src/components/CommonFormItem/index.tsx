import { Form, FormItemProps } from 'antd-mobile';
import classnames from 'classnames';
import './index.scss';

interface CommonFormItemProps extends FormItemProps {
  errorMessage?: string;
  label?: string;
  showRequireIcon?: boolean;
  extra?: any;
}

function CommonFormItem(props: CommonFormItemProps) {
  const {
    errorMessage,
    label,
    showRequireIcon,
    children,
    extra,
    ...otherProps
  } = props;
  return (
    <div className="common-form-item">
      <div
        className={classnames([
          'common-form-item-comp-wrap',
          errorMessage && 'error',
        ])}
      >
        {label && (
          <div className="common-form-item-label">
            <span className={classnames([showRequireIcon && 'with-asterisk'])}>
              {label}
            </span>
          </div>
        )}
        <div className="common-form-item-value">
          <Form.Item noStyle {...otherProps}>
            {children}
          </Form.Item>
          {extra && <div className="common-form-item-value-extra">{extra}</div>}
        </div>
      </div>
      {errorMessage && (
        <div className="common-form-item-error-message">{errorMessage}</div>
      )}
    </div>
  );
}

export default CommonFormItem;
