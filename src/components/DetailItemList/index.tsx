import classnames from 'classnames';
import { _ } from '@/utils';
import styles from './index.module.scss';

interface DetailItemProps {
  label?: any;
  value?: any;
  customRender?: () => any;
}

interface DetailItemListProps {
  className?: string;
  data: DetailItemProps[] | any;
  noPadding?: boolean;
}

export function DetailItem(props: DetailItemProps) {
  const { label, value, customRender } = props;
  return customRender ? (
    customRender()
  ) : (
    <div className={styles.detailItem}>
      <div className={styles.detailItemLabel}>{label}</div>
      <div className={styles.detailItemValue}>{_.isFunction(value) ? value() : value}</div>
    </div>
  );
}

export function DetailItemList(props: DetailItemListProps) {
  const { className, data, noPadding } = props;
  return (
    <div
      className={classnames([
        className,
        styles.detailItemList,
        noPadding && styles.detailItemListNoPadding,
      ])}
    >
      {_.map(data, (item, index) => {
        return item ? <DetailItem key={index} {...item} /> : null;
      })}
    </div>
  );
}
