import styles from './index.module.scss';
import { _ } from '@/utils';

export default function TaoRedPacketRule({ ruleStr = '' }) {
  if (ruleStr) {
    return (
      <div className={styles.taoRedPacketRuleWrap} dangerouslySetInnerHTML={{ __html: _.replace(ruleStr, /\n/g, '<br />') }} />
    );
  }
  return null;
  // return (
  //   <div className={styles.taoRedPacketRuleWrap}>
  //     <div className="section">
  //       <div className="section-title">一、活动时间</div>
  //       <div className="subsection">
  //         <div className="subsection-item">
  //           本活动正在进行中，页面可见即表示活动有效。建议您尽快参与，避免错过机会。
  //         </div>
  //       </div>
  //     </div>

  //     <div className="section">
  //       <div className="section-title">二、参与对象</div>
  //       <div className="content">借钱业务特邀用户</div>
  //     </div>

  //     <div className="section">
  //       <div className="section-title">三、红包领取方式</div>
  //       <div className="content">用户在借钱业务中首次授信成功且在授信成功24小时内，单笔支用满1000元及以上，系统在1小时内自动发放一个10元无门槛红包至用户申请支用的淘宝账号。</div>
  //     </div>
  //     <div className="section">
  //       <div className="section-title">四、红包有效期</div>
  //       <div className="content">红包发放后即刻生效，自发放成功后有效期365天。</div>
  //     </div>
  //     <div className="section">
  //       <div className="section-title">五、红包查看方式及使用规则</div>
  //       <div className="content">手机淘宝-我的淘宝-红包。具体的红包使用规则以红包详情页为准。</div>
  //     </div>
  //   </div>
  // );
}
