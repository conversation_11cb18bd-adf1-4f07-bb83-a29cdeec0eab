/**
 * @file 短信验证码输入框组件
 * <AUTHOR>
 */

import { useCallback } from 'react';
import { Input } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';
import { replace } from 'lodash-es';

interface SmsCodeInputProps {
  className?: string;
  onChange?: (value: string) => void;
  value?: string;
  placeholder?: string;
  logKey?: string;
  disabled?: boolean;
  autoFocus?: boolean;
}

export default function SmsCodeInput(props: SmsCodeInputProps) {
  const { onChange, logKey = 'sms-code-input', placeholder, value = '', disabled = false, className, autoFocus = false } = props;

  const handleInputClick = useCallback(() => {
    log.addClickLog(`${logKey}-click`);
  }, [logKey]);

  const handleOnChange = (val: any) => {
    if (val) {
      // 非正整数
      if (!/^[0-9]\d*$/.test(val)) {
        return;
      }
      onChange && onChange(replace(val, /[^0-9]/g, ''));
    } else {
      onChange && onChange(val);
    }
  };

  return (
    <Input
      autoFocus={autoFocus}
      className={className}
      onChange={handleOnChange}
      onClick={handleInputClick}
      placeholder={placeholder}
      value={value}
      disabled={disabled}
      type="text"
      maxLength={6}
      inputMode="numeric"
    />
  );
}
