.checkList {
  max-height: 60vh;
  padding-bottom: 100rpx;
  overflow: auto;
  p {
    margin: 0;
  }
  .label {
    font-size: 32rpx;
    line-height: 45rpx;
    padding: 21rpx 0;
    color: #111;
  }
  .item {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
  .active {
    color: var(--adm-color-primary);
    .label {
      color: var(--adm-color-primary);
      font-weight: 500;
    }
  }
  .check {
    display: block;
    width: 36rpx;
    height: 36rpx;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: 50% 50%;
    background-image: url('https://gw.alicdn.com/imgextra/i4/O1CN01uHPivF1qhsTWQYwyd_!!6000000005528-2-tps-200-200.png');
  }
}
