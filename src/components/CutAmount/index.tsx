
import React from 'react';
import { _ } from '@/utils';
import styles from './index.module.scss';

interface CutAmountProps {
  amount?: string | number | null | undefined;
  className?: string;
}

const CutAmount: React.FC<CutAmountProps> = ({ amount: amountValue, className }) => {
  // 处理输入值的逻辑
  const processAmount = (value: string | number | undefined | null): string => {
    if (!value) {
      return '';
    }

    // 如果是数值，转为字符串
    if (_.isNumber(value)) {
      return String(value);
    }

    // 如果是字符串，先判断是否是数值字符串
    if (_.isString(value)) {
      // 检查是否是有效的数值字符串
      if (isNaN(Number(value))) {
        return '';
      }

      return value;
    }

    // 其他情况默认返回 ""
    return '';
  };

  const processedAmount = processAmount(amountValue);

  // 分割整数和小数部分
  const [integer, cent] = processedAmount.split('.');

  // 如果没有小数部分，只显示整数
  if (!cent) {
    return <span className={className}>{integer}</span>;
  }

  // 使用传入的 centClassName，如果没有传入则使用默认的 "cut-amount-cent"
  return (
    <span className={className}>
      {integer}<span className={styles.centClass}>.{cent}</span>
    </span>
  );
};

export default CutAmount;