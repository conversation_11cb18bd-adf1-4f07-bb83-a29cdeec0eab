
import React from 'react';
import { money, _ } from '@/utils';

interface CutAmountProps {
  amount?: string | number | null | undefined;
  className?: string;
  centClassName?: string; // 小数部分的样式类名
}

const CutAmount: React.FC<CutAmountProps> = ({ amount: amountValue, className, centClassName }) => {
  // 处理输入值的逻辑
  const processAmount = (value: string | number | undefined | null): string => {
    // 如果没有值，默认为 "0"
    if (!value) {
      return '';
    }

    // 如果是数值，转为字符串
    if (_.isNumber(value)) {
      return String(value)
    }

    // 如果是字符串，先判断是否是数值字符串
    if (_.isString(value)) {
      // 检查是否是有效的数值字符串
      if (isNaN(Number(value))) {
        return '';
      }

      return value;
    }

    // 其他情况默认返回 "0"
    return '0';
  };

  const processedAmount = processAmount(amountValue);

  // 分割整数和小数部分
  const [integer, cent] = processedAmount.split('.');

  // 如果没有小数部分，只显示整数
  if (!cent) {
    return <span className={className}>{integer}</span>;
  }

  // 使用传入的 centClassName，如果没有传入则使用默认的 "cut-amount-cent"
  const centClass = centClassName || "cut-amount-cent";

  return (
    <span className={className}>
      {integer}<span className={centClass}>.{cent}</span>
    </span>
  );
};

export default CutAmount;

// 保留原函数以保持向后兼容性
export const renderCutedAmount = (amountValue: string | number) => {
  let amount: string | number = amountValue;
  if (_.isEmpty(amount)) {
    amount = '0.00';
  }

  const [integer, cent] = money(amount).split('.');
  if (!cent) return integer;

  return (
    <>
      {integer}.<span className="cut-amount-cent">{cent}</span>
    </>
  );
};