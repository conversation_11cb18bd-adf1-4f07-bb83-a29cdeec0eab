
import React from 'react';
import { money, _ } from '@/utils';

interface CutAmountProps {
  amount?: string | number;
  className?: string;
}

const CutAmount: React.FC<CutAmountProps> = ({ amount: amountValue, className }) => {
  let amount: string | number = amountValue || '0.00';
  if (_.isEmpty(amount)) {
    amount = '0.00';
  }

  const [integer, cent] = money(amount).split('.');
  if (!cent) return <span className={className}>{integer}</span>;

  return (
    <span className={className}>
      {integer}.<span className="cut-amount-cent">{cent}</span>
    </span>
  );
};

export default CutAmount;

// 保留原函数以保持向后兼容性
export const renderCutedAmount = (amountValue: string | number) => {
  let amount: string | number = amountValue;
  if (_.isEmpty(amount)) {
    amount = '0.00';
  }

  const [integer, cent] = money(amount).split('.');
  if (!cent) return integer;

  return (
    <>
      {integer}.<span className="cut-amount-cent">{cent}</span>
    </>
  );
};