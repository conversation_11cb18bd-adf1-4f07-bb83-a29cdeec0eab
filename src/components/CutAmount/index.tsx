
import React from 'react';
import { money, _ } from '@/utils';

interface CutAmountProps {
  amount?: string | number;
  className?: string;
  centClassName?: string; // 小数部分的样式类名
}

const CutAmount: React.FC<CutAmountProps> = ({ amount: amountValue, className, centClassName }) => {
  const amount: string | number = amountValue || '0';
  
  const [integer, cent] = money(amount).split('.');


  if (!cent) return <span className={className}>{integer}</span>;

  // 使用传入的 centClassName，如果没有传入则使用默认的 "cut-amount-cent"
  const centClass = centClassName || "cut-amount-cent";

  return (
    <span className={className}>
      {integer}.<span className={centClass}>{cent}</span>
    </span>
  );
};

export default CutAmount;

// 保留原函数以保持向后兼容性
export const renderCutedAmount = (amountValue: string | number) => {
  let amount: string | number = amountValue;
  if (_.isEmpty(amount)) {
    amount = '0.00';
  }

  const [integer, cent] = money(amount).split('.');
  if (!cent) return integer;

  return (
    <>
      {integer}.<span className="cut-amount-cent">{cent}</span>
    </>
  );
};