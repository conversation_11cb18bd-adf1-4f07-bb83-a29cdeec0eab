# CutAmount 组件

用于显示金额的组件，会将小数部分以较小的字体显示。

## 使用方式

### 作为组件使用（推荐）

```tsx
import CutAmount from '@/components/CutAmount';

// 基本使用
<CutAmount amount="1234.56" />

// 带自定义样式
<CutAmount amount="1234.56" className="my-amount" />

// 处理空值
<CutAmount amount="" /> // 显示 0.00
<CutAmount amount={null} /> // 显示 0.00
<CutAmount amount={undefined} /> // 显示 0.00
```

### 作为函数使用（向后兼容）

```tsx
import { renderCutedAmount } from '@/components/CutAmount';

// 在 JSX 中使用
{renderCutedAmount("1234.56")}
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| amount | string \| number | - | 要显示的金额 |
| className | string | - | 自定义样式类名 |

## 样式

组件会自动为小数部分添加 `cut-amount-cent` 类名，你可以通过 CSS 来自定义小数部分的样式：

```css
.cut-amount-cent {
  font-size: 0.8em;
  opacity: 0.8;
}
```

## 示例

- `1234.56` → `1234.56` (小数部分会有特殊样式)
- `1234` → `1234` (没有小数部分)
- `""` → `0.00`
- `null` → `0.00`
