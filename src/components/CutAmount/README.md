# CutAmount 组件

用于显示金额的组件，会将小数部分以较小的字体显示。

## 输入处理逻辑

1. **没有值**：`undefined`、`null`、`''` → 默认显示 `0`
2. **数值类型**：
   - 整数：只显示整数部分
   - 小数：显示完整的 `整数.小数` 格式
3. **字符串类型**：
   - 先判断是否是有效的数值字符串
   - 无效字符串：显示 `0`
   - 有效数值字符串：按数值逻辑处理

## 使用方式

### 作为组件使用（推荐）

```tsx
import CutAmount from '@/components/CutAmount';

// 基本使用
<CutAmount amount="1234.56" />

// 带自定义样式
<CutAmount amount="1234.56" className="my-amount" />

// 自定义小数部分样式
<CutAmount amount="1234.56" centClassName="my-cent-style" />

// 同时自定义整体和小数部分样式
<CutAmount
  amount="1234.56"
  className="my-amount"
  centClassName="my-cent-style"
/>

// 处理各种输入情况
<CutAmount amount={123} />          // 显示: 123
<CutAmount amount={123.45} />       // 显示: 123.45 (小数部分有特殊样式)
<CutAmount amount="123.45" />       // 显示: 123.45
<CutAmount amount="abc" />          // 显示: 0 (无效字符串)
<CutAmount amount="" />             // 显示: 0
<CutAmount amount={null} />         // 显示: 0
<CutAmount amount={undefined} />    // 显示: 0
```

### 作为函数使用（向后兼容）

```tsx
import { renderCutedAmount } from '@/components/CutAmount';

// 在 JSX 中使用
{renderCutedAmount("1234.56")}
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| amount | string \| number \| null \| undefined | - | 要显示的金额，支持各种类型输入 |
| className | string | - | 整体容器的自定义样式类名 |
| centClassName | string | "cut-amount-cent" | 小数部分的自定义样式类名 |

## 样式

### 默认样式
组件会自动为小数部分添加 `cut-amount-cent` 类名（如果没有传入 `centClassName`），你可以通过 CSS 来自定义小数部分的样式：

```css
.cut-amount-cent {
  font-size: 0.8em;
  opacity: 0.8;
}
```

### 自定义小数部分样式
你可以通过 `centClassName` 属性传入自定义的样式类名：

```tsx
<CutAmount amount="1234.56" centClassName="my-custom-cent" />
```

```css
.my-custom-cent {
  font-size: 0.6em;
  color: #999;
  vertical-align: super;
}
```

## 示例

### 数值输入
- `123` → `123` (整数，无小数点)
- `123.45` → `123.45` (小数部分会有特殊样式)
- `123.0` → `123` (JavaScript 自动处理为整数)

### 字符串输入
- `"123"` → `123` (有效数值字符串)
- `"123.45"` → `123.45` (有效小数字符串)
- `"abc"` → `0` (无效字符串)
- `"123abc"` → `0` (无效字符串)

### 空值处理
- `""` → `0`
- `null` → `0`
- `undefined` → `0`
