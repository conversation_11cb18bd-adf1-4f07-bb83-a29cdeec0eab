.selector {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  .option {
    flex: 1 1;
    border-radius: 12rpx;
    border: 1rpx solid #cacfd7;
    padding: 13rpx 0;
    margin-left: 12rpx;
    &:first-child {
      margin-left: 0;
    }
    .label {
      margin: 0;
      font-family: 'ALIBABA NUMBER FONT RG';
      line-height: 30rpx;
      font-size: 30rpx;
      color: #111;
      text-align: center;
    }
  }
  .columns3 {
    flex: 0 0 calc(33% - 6rpx);
    margin-left: 12rpx;
    margin-top: 12rpx;
  }
  .columns4 {
    flex: 0 0 calc(25% - 12rpx);
    margin-left: 12rpx;
    margin-top: 12rpx;
  }
  .marginLeft0 {
    margin-left: 0;
  }
  .marginTop0 {
    margin-left: 0;
  }
  .paddingBottom {
    padding-bottom: 0;
  }
  .disabled {
    opacity: .5;
  }
}

/* 颜色 */
.selector.default {}

.selector.primary {
  .option {
    background-color: #f3f6f8;
    border-color: #f3f6f8;
    .label {
      color: #111;
    }
  }
  .active {
    background-color: var(--primary);
    border-color: var(--primary);
    .label {
      color: #fff;
    }
  }
}
/* 颜色 */


/* 大小 */
.selector.normal {

}
.selector.large {
  .option {
    padding: 16rpx 0;
    .label {
      line-height: 39rpx;
    }
  }
}
/* 大小 */
