/**
 * @file 选择组件
 * <AUTHOR>
 */

import { useCallback } from 'react';
import classNames from 'classnames';
import { isObject, isString } from 'lodash-es';
import { log } from '@alife/dtao-iec-spm-log';

import styles from './index.module.scss';

type Value = string | Option;

interface Option {
  label?: string;
  value: string;
  unit?: string;
  disabled?: boolean;
}

interface SelectorProps {
  options?: Value[];
  value?: Value;
  color?: 'default' | 'primary';
  size?: 'normal' | 'large';
  onChange?: (value?: Value) => void;
  checkActive?: (option: Option, value?: Value) => boolean;
  formatOnChange?: (option: Option) => any;
  columns?: number;
  customClassName?: any;
  logKey?: string;
}

export default function Selector(props: SelectorProps) {
  const {
    options, color = 'normal', value, customClassName, size = 'normal',
    columns = 4, logKey = 'selector', onChange, checkActive, formatOnChange,
  } = props;

  const handleCheckActive = useCallback((option: Option) => {
    if (checkActive && isObject(value)) {
      return checkActive(option, value);
    }
    if (isString(value)) {
      return option?.value === value;
    }
    return false;
  }, [value, checkActive]);

  const handleOnChange = useCallback((option: Option, isActive: boolean) => () => {
    if (isActive) {
      return;
    }
    if (option?.disabled) {
      return;
    }
    if (formatOnChange) {
      onChange && onChange(formatOnChange(option));
    } else {
      onChange && onChange(option?.value);
    }
    log.addChangeLog(`${logKey}-change`, {
      option,
    });
  }, [logKey, onChange, formatOnChange]);

  const renderItem = useCallback((option: Option, index: number) => {
    if (option) {
      const { label, disabled } = option;
      const isActive = handleCheckActive(option);
      const columnsValue = columns > 4 ? 4 : columns;
      const isFirst = index % columnsValue === 0;
      return (
        <div
          onClick={handleOnChange(option, isActive)}
          className={classNames(
            styles.option,
            isActive && styles.active,
            isFirst && styles.marginLeft0,
            disabled && styles.disabled,
            columns && styles[`columns${columnsValue}`],
          )}
          key={`selector-item-${index}`}
        >
          <p className={styles.label}>{label}</p>
        </div>
      );
    }
    return null;
  }, [columns, handleOnChange, handleCheckActive]);

  const renderSelector = useCallback(() => {
    if (options?.length) {
      return (
        <div
          className={classNames(
            styles.selector, styles[color], styles[size], customClassName && customClassName,
          )}
        >
          {options.map(renderItem)}
        </div>
      );
    }
    return null;
  }, [options, color, size, customClassName, renderItem]);

  return renderSelector();
}
