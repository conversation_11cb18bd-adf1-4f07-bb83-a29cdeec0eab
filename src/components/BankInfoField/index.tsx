/**
 * @file 银行选择组件
 * <AUTHOR>
 */

import { useCallback, useRef } from 'react';
import classNames from 'classnames';

import { BankCardDTO } from '@/store/types';
import FieldPanel from '../FieldPanel';
import IconInstitution from '../IconInstitution';

import styles from './index.module.scss';

import CommonPopup from '../CommonPopup';

interface BankDTO {
  bankCode?: string;
  bankName?: string;
}

interface BankInfoFieldProps {
  className?: string;
  options?: BankDTO[];
  value?: BankCardDTO;
}

export default function BankInfoField(props: BankInfoFieldProps) {
  const { options = [], value, className } = props;
  const listRef = useRef<any>();

  const renderPanel = useCallback((val?: BankCardDTO) => {
    if (val && val?.bankCode) {
      return (
        <div className={styles.item}>
          <IconInstitution className={styles.icon} type={val?.bankCode} />
          <p className={styles.name}>{value?.bankName}</p>
        </div>
      );
    }
    return null;
  }, [value]);

  return (
    <div className={classNames(styles.bankInfoField, className && className)}>
      <div className={styles.panel}>
        <FieldPanel<BankCardDTO>
          value={value}
          placeholder=""
          renderPanel={renderPanel}
        />
      </div>
      <CommonPopup ref={listRef} title="支持银行">
        <ul className={styles.bankList}>
          {options.map((option) => (
            <li key={`field-${option}`} className={styles.option}>
              <div className={styles.bank}>
                <IconInstitution className={styles.bankIcon} type={option?.bankCode} />
                <p className={styles.name}>{option?.bankName}</p>
              </div>
              {value?.bankCode === option ? <i className={styles.check} /> : null}
            </li>
          ))}
        </ul>
      </CommonPopup>
    </div>
  );
}
