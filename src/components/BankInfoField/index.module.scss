.bankInfoField {
  p {
    margin: 0;
    padding: 0;
  }
  .panel {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    line-height: inherit;
  }
  .right {
    padding-left: 12rpx;
    font-size: 32rpx;
  }
  .item {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 24rpx 0;
    .name {
      font-size: 26rpx;
      line-height: 39rpx;
      color: #111;
      padding-left: 8rpx;
    }
  }
  .option {
    display: flex;
    align-items: center;
    justify-content: space-around;
  }
  .check {
    display: block;
    background-image: url('https://gw.alicdn.com/imgextra/i3/O1CN01jt5vtt1UDGSL51iiI_!!*************-2-tps-305-200.png');
    width: 32rpx;
    height: 32rpx;
    background-position: 50% 50%;
    background-repeat: no-repeat;
    background-size: cover;
  }
}

.container {
  padding: 0 24rpx;
}
