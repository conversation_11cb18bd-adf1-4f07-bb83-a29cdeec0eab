.coupon {
  padding: 36rpx 26rpx;
  border-radius: 24rpx;
  background: #fff;
  margin-bottom: 24rpx;
  &:last-child {
    margin-bottom: 0;
  }
  p,span {
    margin: 0;
    padding: 0;
  }
  .main {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    .left {
      display: flex;
      border-right: 2px dashed rgba(243, 104, 90, 0.15);
      width: 150rpx;
      .value {
        font-size: 48rpx;
        font-weight: 500;
        line-height: 48rpx;
        text-align: center;
        color: #ff6a5b;
        font-family: 'ALIBABA NUMBER FONT MD';
      }
      .unit {
        position: relative;
        font-size: 28rpx;
        font-weight: 500;
        line-height: 28rpx;
        color: #ff6a5b;
        padding-top: 18rpx;
        padding-left: 3rpx;
      }
    }
    .right {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      flex: 1 1;
      .content {
        padding-left: 20rpx;
        .title {
          font-size: 28rpx;
          font-weight: 500;
          line-height: 28rpx;
          letter-spacing: 0;
          color: #111;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          width: 300rpx;
        }
        .expiredTime {
          font-size: 24rpx;
          font-weight: normal;
          line-height: 24rpx;
          letter-spacing: 0;
          color: #7c889c;
          padding-top: 18rpx;
        }
      }
    }
  }
  .selector {
    width: 36rpx;
    height: 36rpx;
    border: 2px solid #cacfd7;
    border-radius: 50%;
  }
  .selected {
    background-color: #ff6a5b;
    border-color: #ff6a5b;
    .checked {
      position: relative;
      color: #fff;
      top: 0;
      left: 2rpx;
    }
  }
  .sub {
    margin-top: 24rpx;
    .open {
      font-size: 20rpx;
    }
  }
  .rule {
    font-size: 24rpx;
    line-height: 32rpx;
    letter-spacing: 0;
    color: #7c889c;
  }
  .rules {
    padding-top: 6rpx;
    .rule {
      line-height: 36rpx;
    }
  }
}

.clear {
  padding: 24rpx;
  .main {
    justify-content: space-between;
  }
  .text {
    position: relative;
    top: 3rpx;
    font-size: 26rpx;
    line-height: 26rpx;
    color: #3d3d3d;
  }
}

.disabled {
  background: #e5e8ec;
  .main {
    .left {
      border-color: #cacfd7;
      .value {
        color: #cacfd7;
      }
    }
    .right {
      .content {
        .title {
          color: #cacfd7;
        }
      }
    }
  }
}
