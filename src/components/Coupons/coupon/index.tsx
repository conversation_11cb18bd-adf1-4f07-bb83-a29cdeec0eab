/**
 * @file 优惠券
 * <AUTHOR>
 */

import { useCallback, useMemo } from 'react';
import { moment } from '@ali/iec-dtao-utils';
import classNames from 'classnames';
import { Ellipsis } from 'antd-mobile';
import { CheckOutline, RightOutline, UpOutline } from 'antd-mobile-icons';

import { LoanCouponDTO } from '@/store/types';
import { COUPON_TYPE } from '@/common/constant';

import styles from './index.module.scss';

interface CouponPrpos {
  selected?: string[];
  option: LoanCouponDTO;
  onClick: (options: LoanCouponDTO) => void;
  onClear: () => void;
}

export default function Coupons(props: CouponPrpos) {
  const { option, onClick, selected = [], onClear } = props;
  const isSelected = selected[0] === option?.value;

  const handleClick = useCallback(() => {
    if (option?.usable) {
      onClick(option);
    }
  }, [option, onClick]);

  const renderNormal = useCallback(() => {
    const { label, value, type, expireTime, describe, usable } = option;
    return (
      <div className={classNames(styles.coupon, !usable && styles.disabled)} key={value}>
        <div className={styles.main} onClick={handleClick}>
          <div className={styles.left}>
            <p className={styles.value}>{type ? COUPON_TYPE[type] : ''}</p>
          </div>
          <div className={styles.right}>
            <div className={styles.content}>
              <p className={styles.title}>
                {label}
              </p>
              <p className={styles.expiredTime}>
                {expireTime ? `有效期至${moment.YMD(expireTime)}` : ''}
              </p>
            </div>
            <div className={classNames(styles.selector, isSelected && styles.selected)}>
              {isSelected ? <CheckOutline className={styles.checked} /> : null}
            </div>
          </div>
        </div>
        {describe ? (
          <div className={styles.sub}>
            <Ellipsis
              className={styles.rule}
              content={describe}
              expandText={<RightOutline className={styles.open} />}
              collapseText={<UpOutline className={styles.open} />}
            />
          </div>
        ) : null}
      </div>
    );
  }, [option, isSelected, handleClick]);

  const renderClear = useCallback(() => {
    const isClear = !selected?.length;
    return (
      <div className={classNames(styles.coupon, styles.clear)} key="coupons-clear">
        <div className={styles.main} onClick={onClear}>
          <p className={styles.text}>不适用任何优惠</p>
          <div className={classNames(styles.selector, isClear && styles.selected)}>
            {isClear ? <CheckOutline className={styles.checked} /> : null}
          </div>
        </div>
      </div>
    );
  }, [onClear, selected]);

  const renderMain = useMemo(() => {
    if (option?.value) {
      return renderNormal();
    } else if (option?.clear) {
      return renderClear();
    }
    return null;
  }, [renderNormal, renderClear, option]);

  return renderMain;
}
