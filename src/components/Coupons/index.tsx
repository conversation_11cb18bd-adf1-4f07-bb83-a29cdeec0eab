/**
 * @file 优惠券
 * <AUTHOR>
 */

import { useCallback } from 'react';
import { isEmpty, cloneDeep } from 'lodash-es';

import Coupon from './coupon';

import styles from './index.module.scss';
import { LoanCouponDTO } from '@/store/types';

interface CouponPrpos {
  options: LoanCouponDTO[];
  value?: string[];
  onChange?: (couponCodes: string[]) => void;
  close?: any;
}

export default function Coupons(props: CouponPrpos) {
  const { options, value, onChange, close } = props;

  const handleClick = useCallback((option: Option) => {
    close && close();
    if (option?.value) {
      onChange && onChange([option?.value]);
    } else {
      onChange && onChange([]);
    }
  }, [onChange, close]);

  const handleClear = useCallback(() => {
    close && close();
    onChange && onChange([]);
  }, [onChange, close]);

  const renderItem = useCallback((option: Option) => {
    if (option) {
      return (
        <Coupon
          option={option}
          onClick={handleClick}
          selected={value}
          onClear={handleClear}
        />
      );
    }
    return null;
  }, [handleClick, handleClear, value]);

  const renderMain = useCallback(() => {
    if (options && !isEmpty(options)) {
      const _options = cloneDeep(options);
      _options.push({
        clear: true,
      });
      return _options.map(renderItem);
    }
    return null;
  }, [options, renderItem]);


  return (
    <div className={styles.coupons}>
      {renderMain()}
    </div>
  );
}
