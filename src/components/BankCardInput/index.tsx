/**
 * @file 银行卡输入框组件
 * <AUTHOR>
 */

import { useCallback } from 'react';
import { Input, Toast } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';
import { replace } from 'lodash-es';

import { BindCardOcr } from '../BankCardOcr';
import { PRODUCT, TENANT } from '@/common/constant';

import styles from './index.module.scss';
import { noop } from '@/utils';

interface BankCardInputProps {
  onChange?: (value: string) => void;
  value?: string;
  placeholder?: string;
  onFail: any;
  onSuccess: any;
}

export default function BankCardInput(props: BankCardInputProps) {
  const { onChange = noop, placeholder, value = '', onFail, onSuccess } = props;

  const renderIcon = useCallback(() => {
    return (
      <div className={styles.camera}>
        <i className={styles.icon} />
      </div>
    );
  }, []);

  const handleSuccess = useCallback((res: any) => {
    Toast.clear();
    onChange && onChange(res?.cardNo);
    onSuccess();
    log.addSuccessLog('bank-card-ocr');
  }, [onChange, onSuccess]);

  const handleFail = useCallback((res: any) => {
    Toast.clear();
    onFail(res);
    log.addErrorLog('bank-card-ocr');
  }, [onFail]);

  const handleInputClick = useCallback(() => {
    log.addClickLog('bank-input-click');
  }, []);

  const handleOnChange = useCallback((val: string) => {
    if (val) {
      // 首字母非0
      if (!value && (val === '0' || val === '.')) {
        return;
      }
      onChange(replace(val, /[^0-9]/g, ''));
    } else {
      onChange(val);
    }
  }, [onChange, value]);

  return (
    <div className={styles.bankCardInput}>
      <Input
        className={styles.input}
        onChange={handleOnChange}
        onClick={handleInputClick}
        placeholder={placeholder}
        value={value}
        inputMode="numeric"
      />
      <BindCardOcr
        product={PRODUCT}
        tenant={TENANT}
        title="识别银行卡"
        emptyRender={renderIcon}
        onSuccess={handleSuccess}
        onFail={handleFail}
      />
    </div>
  );
}
