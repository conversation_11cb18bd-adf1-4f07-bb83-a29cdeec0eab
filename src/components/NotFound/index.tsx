/**
 * @file 404页面
 */

import { Button } from 'antd-mobile';
import classNames from 'classnames';

import styles from './index.module.scss';
import { reload, popPage } from '@/utils/link';

export default function NotFound() {
  return (
    <div className={styles.notFound}>
      <img
        className={styles.img}
        src="https://gw.alicdn.com/imgextra/i2/O1CN01B0VfUj1GG6GkSpy0i_!!6000000000594-2-tps-510-282.png"
      />
      <p className={styles.title}>网络无法连接</p>
      <p className={styles.desc}>请刷新页面或检查网络设置</p>
      <Button color="primary" onClick={reload} className={styles.button}>
        刷新重试
      </Button>
      <Button color="default" onClick={popPage} className={classNames(styles.button, styles.default)}>
        返回
      </Button>
    </div>
  );
}
