.redPacketPopoverWrap {
  position: relative;
  z-index: 0;
  .content {
    &.contentRight {
      left: unset!important;
      transform: unset!important;
      right: 17rpx!important;
    }
    left: 50%;
    top: -40rpx;
    transform: translateX(-50%);
    position: absolute;
    display: inline-flex;
    align-items: center;
    padding: 8rpx;
    height: 40rpx;
    background: #ffefe5;
    border-radius: 8rpx;
    color: #ff6200;
    z-index: 1;
    font-weight: 600;
    white-space: nowrap;
    box-sizing: border-box;
    &::before {
      display: block;
      content: " ";
      position: absolute;
      background-image: url('https://gw.alicdn.com/imgextra/i2/O1CN01PyJ3Nu1PPZ38RXxwo_!!6000000001833-2-tps-32-8.png');
      background-size: cover;
      width: 32rpx;
      height: 8rpx;
      bottom: -6rpx;
      left: 50%;
      transform: translateX(-50%);
      z-index: 1;
      box-sizing: content-box !important;
    }
    img {
      width: 24rpx;
      height: 24rpx;
      font-size: 24rpx;
      margin-right: 4rpx;
    }
  }
}
