import classnames from 'classnames';
import styles from './index.module.scss';
import { getPromotionOfferData } from '@/store/lib/format';
import { PromotionOffer } from '@/store/types';
import { _ } from '@/utils';

interface RedPacketOfferProps {
  offerList: PromotionOffer[] | any;
  children?: any;
  descriptionDisplayPosition: string;
  position?: 'center' | 'right';
}

function RedPacketOffer(props: RedPacketOfferProps) {
  const promotionOfferData = getPromotionOfferData(props.offerList, props.descriptionDisplayPosition);
  const type = promotionOfferData?.type;

  if (_.isEmpty(promotionOfferData) || !type) {
    return props.children;
  }

  return (
    <div className={styles.redPacketPopoverWrap}>
      <div className={classnames([styles.content, props.position === 'right' && styles.contentRight])}>
        <img src="https://gw.alicdn.com/imgextra/i4/O1CN01s1y7Ej1foKIcblcno_!!6000000004053-2-tps-64-64.png" />
        {promotionOfferData.promotionOfferDescription}
      </div>
      {props.children}
    </div>
  );
}

export default RedPacketOffer;
