.repaymentMethodPopup {
  padding: 0 24rpx;
}

.title {
  font-size: 26rpx;
  font-weight: 500;
  line-height: 39rpx;
  color: #111;
  text-align: right;
  font-family: 'ALIBABA NUMBER FONT MD';
}

.planList {
  overflow: auto;
  max-height: 32vh;
}

.planList15 {
  max-height: 15vh;
}

.planList25 {
  max-height: 20vh;
}

.desc {
  font-size: 24rpx;
  line-height: 36rpx;
  text-align: right;
  color: #7c889c;
  font-family: 'ALIBABA NUMBER FONT MD';
}

.installmentPlan {
  padding-top: 24rpx;
  p, span {
    font-family: 'ALIBABA NUMBER FONT MD';
  }
  .info {
    position: relative;
    display: inline-block;
    font-weight: 500;
    font-size: 26rpx;
    line-height: 39rpx;
    letter-spacing: 0;
    color: #111;
    font-family: 'ALIBABA NUMBER FONT MD';
    &:after {
      position: absolute;
      content: ' ';
      width: 100%;
      height: 22rpx;
      background: #ffefe5;
      bottom: -1rpx;
      left: 0;
      z-index: -1;
    }
  }
  .panel {
    border-radius: 18rpx;
    padding: 16rpx;
    background: #f3f6f8;
    margin-top: 20rpx;
    margin-bottom: 36rpx;
    .item {
      padding-bottom: 12rpx;
      &:last-child {
        padding-bottom: 0;
      }
    }
  }
  .planItem {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    padding-bottom: 42rpx;
    margin-bottom: 9rpx;
    &:last-child {
      padding-bottom: 0;
    }
    .title, .desc {
      line-height: 1;
      font-family: 'ALIBABA NUMBER FONT MD';
    }
    .title {
      padding-bottom: 12rpx;
    }
    .right {
      flex: 1 1;
      .title, .desc {
        text-align: left;
      }
      .plus {
        padding: 0 6rpx;
      }
    }
    .icon {
      position: relative;
      display: block;
      width: 18rpx;
      height: 18rpx;
      background: #fff;
      border: 3rpx solid var(--primary);
      border-radius: 50%;
      margin-left: 40rpx;
      margin-right: 28rpx;
      &:after {
        position: absolute;
        display: block;
        content: ' ';
        width: 1rpx;
        height: 72rpx;
        background-color: #cacfd7;
        left: 6rpx;
        top: 23rpx;
      }
    }
    &:last-child {
      .icon {
        &:after {
          display: none;
        }
      }
    }
  }
}

.descContainer {
  border-radius: 12rpx;
  padding: 15rpx 18rpx;
  background: #fff;
  border: 1rpx dashed #e5e8ec;
  .tip {
    font-size: 24rpx;
    line-height: 36rpx;
    color: #7c889c;
    padding-bottom: 24rpx;
    text-align: left;
  }
  .more {
    text-align: center;
    width: 60rpx;
    height: 36rpx;
    border-radius: 18rpx;
    border: 1px solid #e5e8ec;
    margin: auto;
    .icon {
      position: relative;
      display: inline-block;
      font-size: 10rpx;
      top: 0;
      background-repeat: no-repeat;
      background-size: contain;
      background-position: 50% 50%;
      width: 20rpx;
      height: 12rpx;
    }
    .up {
      background-image: url('https://gw.alicdn.com/imgextra/i4/O1CN01AUin0428tvz7GG3AD_!!6000000007991-2-tps-353-200.png');
    }
    .down {
      background-image: url('https://gw.alicdn.com/imgextra/i3/O1CN01bWYLuw1spmXd0V2lm_!!6000000005816-2-tps-353-200.png');
    }
  }
  .descDetail {
    padding-bottom: 24rpx;
    .detailItem {
      margin-top: 32rpx;
      &:first-child {
        margin-top: 0;
      }
      .itemTitle {
        display: flex;
        font-size: 24rpx;
        font-weight: 500;
        line-height: 36rpx;
        color: #50607a;
        align-items: center;
      }
      .itemIndex {
        position: relative;
        display: block;
        width: 28rpx;
        height: 28rpx;
        border-radius: 50%;
        margin-top: 12rpx;
        background: #e5e8ec;
        font-family: PingFang SC;
        font-size: 20rpx;
        line-height: 30rpx;
        color: #50607a;
        text-align: center;
        margin-right: 12rpx;
        top: -3rpx;
      }
    }
  }
  .itemText {
    font-size: 24rpx;
    font-weight: normal;
    line-height: 36rpx;
    color: #7c889c;
    margin-top: 12rpx;
    .itemT {
      color: #50607a;
    }
  }
  .itemTextT {
    font-size: 24rpx;
    font-weight: normal;
    line-height: 36rpx;
    color: #7c889c;
    margin-top: 32rpx;
    .itemT {
      color: #50607a;
    }
  }
}

.loading {
  display: flex;
  text-align: center;
  justify-content: center;
  align-items: center;
  padding-top: 318rpx;
  .text {
    font-size: 32rpx;
    color: #7c889c;
  }
}

.originInterest {
  text-decoration: line-through;
  margin-left: 8rpx;
  color: #7c889c;
  font-weight: normal;
}

.promotionText {
  color: #ff6200;
}