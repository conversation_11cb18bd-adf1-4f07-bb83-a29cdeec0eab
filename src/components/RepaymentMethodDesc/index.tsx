/**
 * @file 还款方式描述
 * <AUTHOR>
 */

import { useCallback, useState } from 'react';
import { number } from '@ali/iec-dtao-utils';
import classNames from 'classnames';
import { DotLoading } from 'antd-mobile';

import type { RepaymentMethodDTO, InstallmentPlanDTO } from '@/store/types';
import type { PROCESS_ACTION, TrialResponse } from '@/store/loan/types';
import { formatRepaymentMethod } from '@/store/lib/format';
import { _, getYMD, isGreaterThan0 } from '@/utils';

import styles from './index.module.scss';

interface RepaymentMethodProps {
  options?: RepaymentMethodDTO[];
  trialStore?: TrialResponse;
  processAction?: PROCESS_ACTION;
  value?: string;
}

export default function RepaymentMethod(props: RepaymentMethodProps) {
  const { options, trialStore, processAction, value } = props;
  const [show, setShow] = useState(false);

  const renderPlanItem = useCallback((plan: InstallmentPlanDTO, index: number) => {
    const { amountFormat } = number;
    if (plan) {
      const { interest, principal, totalAmount, endDate, promotionAmount } = plan;
      return (
        <div className={styles.planItem} key={`plan-item-${index}`}>
          <div className={styles.left}>
            <p className={styles.title}>
              {plan?.number === 1 ? '首' : plan?.number}期
            </p>
            <p className={styles.desc}>
              {getYMD(endDate)}
            </p>
          </div>
          <i className={styles.icon} />
          <div className={styles.right}>
            <p className={styles.title}>
              ¥{amountFormat(totalAmount)}
            </p>
            <p className={styles.desc}>
              <span>含本金</span>&nbsp;
              <span>¥{amountFormat(principal)}</span>
              <span className={styles.plus}>+</span>
              {
                isGreaterThan0(promotionAmount) ? <span className={styles.promotionText}>优惠后利息&nbsp;</span> : <span>利息</span>
              }
              <span>¥{amountFormat(interest)}</span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  }, []);

  const toggle = useCallback(() => {
    setShow((cur) => {
      return !cur;
    });
  }, []);

  const renderDescDetail = useCallback(() => {
    if (!options?.length || !trialStore) {
      return null;
    }
    const showIndex = options.length >= 2;
    return options.map((option, index) => {
      if (option?.value === 'AVERAGE_CAPITAL_PLUS_INTEREST') {
        return (
          <div key={`detail-item-${index}`} className={styles.detailItem}>
            <p className={styles.itemTitle}>
              {showIndex ? <i className={styles.itemIndex}>{index + 1}</i> : null}
              等额本息计息方式
            </p>
            <p className={styles.itemText}>
              <span className={styles.itemT}>月利息</span>
              =当月剩余本金 x 日利率 x 当月天数
            </p>
            <p className={styles.itemText}>
              <span className={styles.itemT}>总利息</span>=每月利息相加-优惠金额
            </p>
          </div>
        );
      }
      if (option?.value === 'BALLOON_MORTGAGE') {
        return (
          <div key={`detail-item-${index}`} className={styles.detailItem}>
            <p className={styles.itemTitle}>
              {showIndex ? <i className={styles.itemIndex}>{index + 1}</i> : null}
              先息后本计息方式
            </p>
            <p className={styles.itemText}>
              <span className={styles.itemT}>总利息</span>
              =本金 x 日利率 x 借款天数-优惠金额
            </p>
          </div>
        );
      }
      return null;
    });
  }, [options, trialStore]);

  const renderDesc = useCallback((installmentPlanList?: any[]) => {
    if (!trialStore) {
      return null;
    }
    return (
      <div className={styles.descContainer}>
        <p className={styles.tip}>当前总利息按借满{installmentPlanList?.length}个月计算，总利息计算方式如下：</p>
        {show ? (
          <div className={styles.descDetail}>
            {renderDescDetail()}
          </div>
        ) : null}
        <div className={styles.more} onClick={toggle}>
          <i className={classNames(styles.icon, show && styles.up, !show && styles.down)} />
        </div>
      </div>
    );
  }, [trialStore, show, toggle, renderDescDetail]);

  const renderPlan = useCallback(() => {
    const { amountFormat } = number;
    if (processAction === 'TRIALING') {
      return (
        <div className={styles.loading}>
          <p className={styles.text}>计算中</p>
          <DotLoading color="primary" />
        </div>
      );
    }
    if (!_.isEmpty(trialStore)) {
      const { installmentPlanList, totalAmount, repaymentDay, principal, interest, originInterest, promotionAmount } = trialStore;
      return (
        <div className={styles.installmentPlan}>
          <p className={styles.info}>
            借满{installmentPlanList?.length}个月，应还总额{amountFormat(totalAmount)}元
          </p>
          <div className={styles.panel}>
            <div className={styles.item}>
              <span>还款方式：</span>
              <span>
                {formatRepaymentMethod({
                  value,
                })}
              </span>
            </div>
            <div className={styles.item}>
              <span>借款本金：</span>
              <span>¥{amountFormat(principal)}</span>
            </div>
            <div className={styles.item}>
              <span>总利息：</span>
              <span>¥{amountFormat(interest)}{isGreaterThan0(promotionAmount) && <span className={styles.originInterest}>¥{amountFormat(originInterest)}</span>} </span>
            </div>
            {renderDesc(installmentPlanList)}
          </div>
          <p className={styles.info}>
            每月{repaymentDay}日还款
          </p>
          <div className={classNames(
            styles.planList,
            styles.panel,
            (show && options?.length === 1) && styles.planList25,
            (show && options?.length === 2) && styles.planList15,
          )}
          >
            {installmentPlanList?.map(renderPlanItem)}
          </div>
        </div>
      );
    }
    return null;
  }, [show, trialStore, value, options, processAction, renderPlanItem, renderDesc]);

  return renderPlan();
}
