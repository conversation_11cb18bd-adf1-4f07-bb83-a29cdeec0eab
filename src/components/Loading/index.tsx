import { ToastHandler } from 'antd-mobile/es/components/toast';
import { useRef } from 'react';
import { Toast } from 'antd-mobile';
import styles from './index.module.scss';

export const useLoading = () => {
  const handler = useRef<ToastHandler>();

  const showLoading = (content?: string) => {
    if (handler.current) {
      handler.current.close();
    }

    handler.current = Toast.show({
      content: (
        <div className={styles.loadingContainer}>
          <div className={styles.container}>
            <i className={styles.icon} />
            {content || '加载中'}
          </div>
        </div>
      ),
      duration: 0,
      maskClickable: false,
    });
  };

  const hideLoading = () => {
    handler.current?.close();
  };

  return {
    showLoading,
    hideLoading,
  };
};
