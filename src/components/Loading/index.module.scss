.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 36rpx;

  font-size: 32rpx;
  line-height: 48rpx;
}

.container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .icon {
    display: block;
    height: 72rpx;
    width: 72rpx;
    background-image: url('https://gw.alicdn.com/imgextra/i3/O1CN01k1GR4G1r6bqxbWoD0_!!6000000005582-2-tps-144-144.png');
    background-repeat: no-repeat;
    background-size: contain;
    animation: rotate 1s linear infinite;
    -webkit-animation: rotate 1s linear infinite;
    margin-bottom: 16rpx;
  }
  .text {
    font-size: 28rpx;
    color: #fff;
    text-align: center;
    padding-top: 16rpx;
  }
}

.spin {
  margin-bottom: 12rpx;
}

@keyframes rotate {
  100% {
    transform: rotate(0);
  }
  0% {
    transform: rotate(360deg);
  }
}

@-webkit-keyframes rotate {
  100% {
    transform: rotate(0);
  }
  0% {
    transform: rotate(360deg);
  }
}
