import { useState, useCallback, useEffect } from 'react';
import styles from './index.module.scss';
import { _, getPageName } from '@/utils';
import { PAGES } from '@/common/constant';
// import { popPage } from '@/utils/link';

const WHITE_COLOR = '#ffffff';

const PageColorMap = {
  [PAGES.CreditResult]: WHITE_COLOR,
  [PAGES.RepayResult]: WHITE_COLOR,
  // [PAGES.LoanResult]: WHITE_COLOR,
  [PAGES.CenterSignedAgreements]: WHITE_COLOR,
  [PAGES.LoanApply]: WHITE_COLOR,
  [PAGES.CenterClose]: WHITE_COLOR,
  [PAGES.CenterPhoneUpdate]: WHITE_COLOR,
  [PAGES.BindCard]: WHITE_COLOR,
  [PAGES.CreditIdentity]: WHITE_COLOR,
};

const CommonNavBar = (props) => {
  const [transparency, setTransparency] = useState('0');
  const doColor = useCallback((scrollTop) => {
    let molecule = scrollTop;
    if (scrollTop === 0) {
      molecule = 0;
    }
    if (scrollTop > 40) {
      molecule = 40;
    }

    setTransparency(Number(molecule / 40).toFixed(2));
  }, []);

  const onScroll = useCallback(() => {
    doColor(window.scrollY || window.pageYOffset);
  }, []);

  // const handleBack = () => {
  //   popPage();
  // };

  useEffect(() => {
    window.addEventListener('scroll', onScroll);
    return () => {
      window.removeEventListener('scroll', onScroll);
    };
  }, []);
  const opacityBgStyle = {
    opacity: transparency,
    backgroundColor: _.get(PageColorMap, getPageName(props.pathname), '#f3f6f8'),
  };


  return (
    <div className={styles.commonNavBar}>
      <div className={styles.safeAreaInsetTop} style={opacityBgStyle} />
      <div className={styles.navbarHeight}>
        <div className={styles.navbarHeightBackground} style={opacityBgStyle} />
        <div className={styles.realNavBarWrap}>
          {/* <img
            onClick={handleBack}
            className={classnames([styles.backIcon, styles.icon])}
            src="https://gw.alicdn.com/imgextra/i4/O1CN01elrJ9b1KIAWFLR02h_!!6000000001140-2-tps-116-116.png"
          />
          <div className={styles.documentTitle}>{document.title || '借钱'}</div>
          <img
            className={classnames([styles.icon])}
            src="https://gw.alicdn.com/imgextra/i4/O1CN01elrJ9b1KIAWFLR02h_!!6000000001140-2-tps-116-116.png"
          /> */}
        </div>
      </div>
    </div>
  );
};

export default CommonNavBar;
