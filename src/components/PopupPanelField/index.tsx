/**
 * @File 弹窗面板表单项
 * <AUTHOR>
 */

import React, { forwardRef, useCallback, useImperativeHandle, useRef } from 'react';
import { log } from '@alife/dtao-iec-spm-log';
import classNames from 'classnames';
import { ClientOnly } from 'ice';

import CommonPopup, { type ICommonPopupRef } from '../CommonPopup';
import FieldPanel from '../FieldPanel';
import ArrowIcon, { type ArrowIconProps } from '../ArrowIcon';

import styles from './index.module.scss';

interface PopupPanelProps<V> {
  value?: V;
  onChange?: (value?: V) => void;
  placeholder?: string;
  renderPanel?: (value?: V) => React.JSX.Element | null;
  renderFold: (props?: any) => React.JSX.Element | null;
  contentClassName?: string;
  logKey?: string;
  disabled?: boolean;
  containerClassName?: string;
  popupProps: {
    title?: string;
    forceRender?: boolean;
    bodyClassName?: string;
  };
  arrowType?: ArrowIconProps['type'];
}

export interface PopupPanelRef {
  show: () => void;
  close: () => void;
}

export const PopupPanelField = forwardRef(<V extends any>(
  props: PopupPanelProps<V>,
  ref: React.MutableRefObject<PopupPanelRef>,
) => {
  const popupRef = useRef<ICommonPopupRef>();
  const {
    value, placeholder, contentClassName, popupProps, logKey = 'popup-panel',
    disabled = false, arrowType, containerClassName, onChange, renderFold, renderPanel,
  } = props;

  const handlePopupShow = useCallback(() => {
    if (disabled) {
      return;
    }
    popupRef?.current?.toggleVisible(true);
    log.addClickLog(`${logKey}-open`);
  }, [logKey, disabled]);

  const handlePopupClose = useCallback(() => {
    popupRef?.current?.toggleVisible(false);
    log.addClickLog(`${logKey}-close`);
  }, [logKey]);

  useImperativeHandle(ref, () => ({
    show: handlePopupShow,
    close: handlePopupClose,
  }));

  const renderFoldAction = () => {
    if (renderFold) {
      return (
        <CommonPopup
          ref={popupRef}
          {...popupProps}
          onClose={handlePopupClose}
          onMaskClick={handlePopupClose}
          contentClassName={contentClassName}
        >
          <div className={classNames(styles.container, containerClassName && containerClassName)}>
            {renderFold({ value, onChange, close: handlePopupClose })}
          </div>
        </CommonPopup>
      );
    }
    return null;
  };

  return (
    <div className={styles.popupPanelField} onClick={handlePopupShow}>
      <div className={styles.panel}>
        <FieldPanel
          className={styles.fieldPanel}
          value={value}
          placeholder={placeholder}
          renderPanel={renderPanel}
        />
        <ArrowIcon type={arrowType} className={styles.icon} />
      </div>
      <ClientOnly>
        {renderFoldAction}
      </ClientOnly>
    </div>
  );
}) as <V>(
  props: PopupPanelProps<V> & { ref?: React.ForwardedRef<PopupPanelRef> },
) => JSX.Element;
