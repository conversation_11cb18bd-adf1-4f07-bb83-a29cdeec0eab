/**
 * @file 调起摄像头或相册上传图片
 * <AUTHOR>
 */

import { memo, useCallback, useRef, useState, forwardRef, useEffect } from 'react';
import { Button, Toast } from 'antd-mobile';
import classNames from 'classnames';
import dayjs from 'dayjs';

import { noop, _, analyzeIDCardIsOver18 } from '@/utils';
import CommonPopup from '@/components/CommonPopup';
import UploadCell from '@/components/NewUploadCell';
import FullLoading from '@/components/FullLoading';
import {
  fileFactoryReadUrl,
  ocrIdCardOfFace,
  ocrIdCardOfBack,
  ocrBankCard,
  licensePreCheck,
  checkCifSamePerson,
} from '@/store/common/actions';
import { log } from '@alife/dtao-iec-spm-log';
import { PRIVACY_ID } from '@/common/constant';
import { PermissionKeyType } from '@ali/uniapi-permission';

import styles from './index.module.scss';

interface UploadImageProps {
  className?: string;
  title?: string;
  value?: any;
  onChange?: Function;
  onOcrSuccess?: Function;
  onOcrFailed?: Function;
  ocrConfig?: any;
  emptyRender: Function;
  uploadedImgRender: Function;
  needHarmonyOSCompatible?: boolean;
}

function UploadImage(props: UploadImageProps) {
  const commonPopupRef = useRef<any>(null);
  const [loadingVisible, setLoadingVisible] = useState(false);
  const selectedPhotoRef = useRef<any>(null);
  const curFileFactoryNoRef = useRef<any>(null);

  const {
    className,
    value,
    onChange = noop,
    emptyRender = noop,
    uploadedImgRender = noop,
    onOcrSuccess = noop,
    onOcrFailed = noop,
    ocrConfig = {},
    title = '身份证上传',
    needHarmonyOSCompatible = false,
  } = props;

  const imageUrl = value?.url;

  const toggleCommonPopupVisible = () => {
    commonPopupRef.current?.toggleVisible(false);
  };

  const doLicensePreCheck = async (ocrRes: any) => {
    try {
      const { name, num } = ocrRes;
      if (!name || !num) {
        throw new Error('OCR_NAME_NUM_NULL');
      }
      const checkRes = await licensePreCheck({
        personName: name,
        personIdCardNo: num,
      });
      if (checkRes?.isValidLicense === true) {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  };

  const doOcr = async (fileObjValue) => {
    const ocrApi = _.get(
      {
        front: ocrIdCardOfFace,
        back: ocrIdCardOfBack,
        bankcard: ocrBankCard,
      },
      ocrConfig.ocrType,
    );
    try {
      const ocrRes: any = await ocrApi({
        fileNo: fileObjValue.fileFactoryNo,
        fileServerFileName: fileObjValue.url,
        withSign: true,
      });
      if (ocrRes) {
        setLoadingVisible(false);
        // 是否复印件
        if (ocrConfig.ocrType !== 'bankcard' && ocrRes?.photocopy) {
          Toast.show({ content: '识别到您上传的可能不是身份证原件，建议上传原件', duration: 3000 });
          return;
        }

        // 是否翻拍
        if (ocrConfig.ocrType !== 'bankcard' && ocrRes?.reshoot) {
          Toast.show({ content: '识别到您上传的可能不是身份证原件，建议上传原件', duration: 3000 });
          return;
        }

        // 判断有效期
        if (ocrConfig.ocrType === 'back' && ocrRes.endDate !== '长期') {
          const minDate = dayjs().add(30, 'days');
          if (!dayjs(ocrRes.endDate).isAfter(minDate)) {
            onOcrFailed({ code: 'EXPIRE', msg: '身份证有效期必须在1个月以上' });
            return;
          }
        }
        if (ocrConfig.ocrType === 'front') {
          // 是否满18岁的判断
          if (!analyzeIDCardIsOver18(ocrRes.num)) {
            onOcrFailed({ code: 'UNDER_18', msg: '未满18岁，无法申请额度' });
            return;
          }
          if (ocrConfig?.preLicenseCheck) {
            const checkRes = await doLicensePreCheck(ocrRes);
            if (!checkRes) {
              onOcrFailed({
                code: 'LICENSE_CHECK_INVALID',
                msg: '您上传的身份证非本人，请上传本人身份证',
              });
              return;
            }
          }
          // 同人校验
          if (ocrConfig?.preCheckCifSamePerson) {
            try {
              const checkRes = await checkCifSamePerson({
                licenseNo: ocrRes.num,
              });
              if (checkRes?.tbLoginId) {
                onOcrFailed({
                  code: 'SAME_PERSON',
                  payload: checkRes.tbLoginId,
                  msg: `您上传的身份证已在${
                    checkRes.tbLoginId ? `淘宝账号${checkRes.tbLoginId}` : '其他淘宝账号'
                  }申请额度，不可重复申请`,
                });
                return;
              }
            } catch (e) {
              onOcrFailed({ code: 'SAME_PERSON_API_ERROR' });
              return;
            }
          }
        }

        onOcrSuccess({
          ...ocrRes,
          selectedPhoto: selectedPhotoRef.current,
          num: String(ocrRes.num).toUpperCase(),
        });
        onChange(fileObjValue);
        curFileFactoryNoRef.current = fileObjValue.fileFactoryNo;
        return true;
      } else {
        throw new Error();
      }
    } catch (e) {
      onOcrFailed({
        code: 'OCR_API_ERROR',
        msg: `身份证${ocrConfig.ocrType === 'front' ? '人像面' : '国徽面'}识别失败，请重新上传`,
      });
      setLoadingVisible(false);
    }
  };

  const doReadUrl = async (fileObj) => {
    try {
      const readUrlRes = await fileFactoryReadUrl({
        fileFactoryNo: fileObj.fileFactoryNo,
        sourceScene: 'FRONT_END',
      });

      if (!readUrlRes.url) {
        throw new Error('FILE_READURL_EMPTY');
      }

      return {
        ...fileObj,
        type: ocrConfig.ocrType === 'front' ? 'PERSON_ID_CARD_FACE' : 'PERSON_ID_CARD_NATION',
        path: readUrlRes.url,
        url: readUrlRes.url,
      };
    } catch (e) {
      log.addErrorLog('upload-image-read-url-error', { code: e.message });
      Toast.show({
        content: '图片下载服务繁忙，请稍后再试',
      });
    }
  };

  const handleUploadSuccess = async (fileObj) => {
    const fileObjValue = await doReadUrl(fileObj);

    await doOcr(fileObjValue);
    commonPopupRef?.current.toggleVisible(false);
  };

  const onProcess = () => {
    selectedPhotoRef.current = true;
    setLoadingVisible(true);
  };

  const renderImage = useCallback(() => {
    return imageUrl
      ? uploadedImgRender({ imageUrl, type: ocrConfig.ocrType })
      : emptyRender({ type: ocrConfig.ocrType });
  }, [imageUrl]);

  const handleChangeVisible = () => {
    log.addClickLog('upload-image-click', { type: ocrConfig.ocrType });
    commonPopupRef?.current.toggleVisible(true);
  };

  const handleUploadError = (error) => {
    log.addLog('upload-error', 'error', { error });
    const errorCode = _.get(error, 'errorCode');
    if (errorCode === 'OVER_SIZE') {
      Toast.show({
        content: '图片大小超出限制，请重新上传',
      });
    }
    if (errorCode === 'UPLOAD_ERROR') {
      Toast.show({
        content: '图片上传服务繁忙，请稍后再试',
      });
    }

    setLoadingVisible(false);
  };

  useEffect(() => {
    (async () => {
      if (!value || curFileFactoryNoRef.current === value?.fileFactoryNo) return;

      curFileFactoryNoRef.current = value?.fileFactoryNo;
      doOcr(value?.url ? value : await doReadUrl(value));
    })();
  }, [value?.fileFactoryNo]);

  return (
    <div className={`${className}`}>
      <FullLoading visible={loadingVisible} />
      <div className={styles.uploadImgWrap} onClick={handleChangeVisible}>
        {renderImage()}
      </div>
      <CommonPopup ref={commonPopupRef} title={title}>
        <div className={styles.UploadCellsWrap}>
          <UploadCell
            className={styles.uploadItem}
            onUploadError={handleUploadError}
            onUploadSuccess={handleUploadSuccess}
            onProcess={onProcess}
            privacyID={PRIVACY_ID}
            mode="photo"
            needHarmonyOSCompatible={needHarmonyOSCompatible}
            imageMaxSizeAfterCompress={1024 * 1024 * 2}
            permissionType={PermissionKeyType.READ_IMAGES}
          >
            <i className={classNames(styles.icon, styles.images)} />
            从相册上传
          </UploadCell>
          <UploadCell
            className={styles.uploadItem}
            privacyID={PRIVACY_ID}
            onUploadError={handleUploadError}
            onUploadSuccess={handleUploadSuccess}
            onProcess={onProcess}
            mode="camera"
            needHarmonyOSCompatible={needHarmonyOSCompatible}
            imageMaxSizeAfterCompress={1024 * 1024 * 2}
            permissionType={PermissionKeyType.TAKE_CAMERA}
          >
            <i className={classNames(styles.icon, styles.camera)} />
            拍照
          </UploadCell>
        </div>
        <div>
          <Button onClick={toggleCommonPopupVisible} block className="cancel-button">
            取消
          </Button>
        </div>
      </CommonPopup>
    </div>
  );
}

export default memo(forwardRef(UploadImage));
