import styles from './index.module.scss';
import { getPromotionOfferData } from '@/store/lib/format';
import { PromotionOffer } from '@/store/types';
import { Modal } from 'antd-mobile';
import TaoRedPacketRule from '@/components/TaoRedPacketRule';
import { _ } from '@/utils';
import { log } from '@alife/dtao-iec-spm-log';


interface RedPacketOfferProps {
  offerList: PromotionOffer[] | any;
}

const LeftTextMap = {
  TAO_PLATFORM_RED_PACKET: '立即到账',
};

const LeftIconMap = {
  TAO_PLATFORM_RED_PACKET: '¥',
};

const rightSecondLineTextMap = {
  TAO_PLATFORM_RED_PACKET: '现金红包',
};

function RedPacketOffer(props: RedPacketOfferProps) {
  const promotionOfferData = getPromotionOfferData(props.offerList);
  const type = promotionOfferData?.type;

  if (_.isEmpty(promotionOfferData) || !type) {
    return null;
  }
  log.addShowLog('red-packet-offer');

  const handleRedPacketOfferWrapClick = () => {
    if (promotionOfferData.displayPosition === 'HOME_PAGE') {
      log.addClickLog('red-packet-rule');
      Modal.alert({
        title: '活动规则',
        content: <TaoRedPacketRule />,
      });
    }
  };

  return (
    <div className={styles.redPacketOfferWrap} onClick={handleRedPacketOfferWrapClick}>
      <div className={styles.leftArea}>
        <div className={styles.leftFirstLine}>
          <span className={styles.leftIcon}>{LeftIconMap[type]}</span>
          <span className={styles.leftAmount}>{promotionOfferData.amount}</span>
        </div>
        <div className={styles.leftText}>{LeftTextMap[type]}</div>
      </div>
      <div className={styles.rightArea}>
        <div className={styles.rightFirstLineText}>
          {promotionOfferData.promotionOfferDescription}
        </div>
        <div className={styles.rightSecontLineText}>{rightSecondLineTextMap[type]}</div>
      </div>
      {promotionOfferData.displayPosition === 'HOME_PAGE' && (
        <img
          className={styles.whiteArrowIcon}
          src="https://gw.alicdn.com/imgextra/i2/O1CN011CLcNi1TWDFBPABD3_!!6000000002389-2-tps-32-52.png"
        />
      )}
    </div>
  );
}

export default RedPacketOffer;
