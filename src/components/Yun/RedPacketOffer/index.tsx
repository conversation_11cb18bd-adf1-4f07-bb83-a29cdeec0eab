import styles from './index.module.scss';
import { getPromotionOfferData } from '@/store/lib/format';
import { PromotionOffer } from '@/store/types';
import { Modal } from 'antd-mobile';
import TaoRedPacketRule from '@/components/TaoRedPacketRule';
import CutAmount from '@/components/CutAmount';
import { _ } from '@/utils';
import { log } from '@alife/dtao-iec-spm-log';

interface RedPacketOfferProps {
  offerList: PromotionOffer[] | any;
  descriptionDisplayPosition: string;
}

const LeftTextMap = {
  TAO_PLATFORM_RED_PACKET: '立即到账',
};

const LeftIconMap = {
  TAO_PLATFORM_RED_PACKET: '¥',
};

// const rightFirstLineTextMap = {
//   TAO_PLATFORM_RED_PACKET: '开通立得',
// };

// const rightSecondLineTextMap = {
//   TAO_PLATFORM_RED_PACKET: '无门槛购物红包',
// };

function RedPacketOffer(props: RedPacketOfferProps) {
  const promotionOfferData = getPromotionOfferData(
    props.offerList,
    props.descriptionDisplayPosition,
  );
  const type = promotionOfferData?.type;

  if (_.isEmpty(promotionOfferData) || !type) {
    return null;
  }

  log.addShowLog(
    'red-packet-offer',
    _.pick(promotionOfferData, [
      'amount',
      'type',
      'descriptionDisplayPosition',
      'promotionOfferDescription',
    ]),
  );

  const handleRedPacketOfferWrapClick = () => {
    if (!promotionOfferData.offerSendRule) return;
    log.addClickLog('red-packet-rule');
    Modal.alert({
      title: '活动规则',
      content: <TaoRedPacketRule ruleStr={promotionOfferData.offerSendRule} />,
    });
  };

  return (
    <div className={styles.redPacketOfferWrap} onClick={handleRedPacketOfferWrapClick}>
      <div className={styles.leftArea}>
        <div className={styles.leftFirstLine}>
          <span className={styles.leftIcon}>{LeftIconMap[type]}</span>
          <span className={styles.leftAmount}><CutAmount centClassName={styles.leftAmountCent} amount={promotionOfferData.amount} /></span>
        </div>
        <div className={styles.leftText}>{LeftTextMap[type]}</div>
      </div>
      <div className={styles.rightArea}>
        <div className={styles.rightFirstLineText}>
          {promotionOfferData.promotionOfferDescription}
        </div>
        {/* <div className={styles.rightSecontLineText}>{rightSecondLineTextMap[type]}</div> */}
      </div>
      {promotionOfferData.offerSendRule && (
        <img
          className={styles.whiteArrowIcon}
          src="https://gw.alicdn.com/imgextra/i2/O1CN011CLcNi1TWDFBPABD3_!!6000000002389-2-tps-32-52.png"
        />
      )}
    </div>
  );
}

export default RedPacketOffer;
