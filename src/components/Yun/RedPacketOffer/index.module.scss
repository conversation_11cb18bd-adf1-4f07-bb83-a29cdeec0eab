.redPacketOfferWrap {
  position: relative;
  border-radius: 12rpx;
  background: linear-gradient(90deg, #f2000d 23%, #f33 36%);
  height: 150rpx;
  margin-top: 60rpx;
  margin-bottom: 100rpx;
  display: flex;
  // .openMouseImg {
  //   position: absolute;
  //   width: 170rpx;
  //   height: 150rpx;
  //   left: 0;
  //   top: 0;
  // }
  .whiteArrowIcon {
    position: absolute;
    width: 16rpx;
    height: 26rpx;
    right: 36rpx;
    top: 50%;
    transform: translateY(-50%);
  }
  .leftArea {
    background-image: url("https://gw.alicdn.com/imgextra/i4/O1CN01upwUVM23eIOzaNc0p_!!6000000007280-2-tps-338-300.png");
    background-repeat: no-repeat;
    background-size: cover;
    width: 170rpx;
    height: 150rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    color: #ff0021;
    letter-spacing: 0;
    position: relative;
    .leftFirstLine {
      margin-top: -6rpx;
    }
    .leftIcon {
      font-size: 32rpx;
      font-family: 'ALIBABA NUMBER FONT MD';
      font-weight: bold;
    }
    .leftAmount {
      line-height: 56rpx;
      font-size: 56rpx;
      font-weight: bold;
      font-family: 'ALIBABA NUMBER FONT MD';
    }
    .leftAmountCent {
      font-size: 0.8em;
    }
    .leftText {
      font-weight: 600;
    }
  }
  .rightArea {
    display: flex;
    flex-direction: column;
    justify-content: center;
    // align-items: center;
    color: #fff;
    padding-left: 16rpx;
    text-align: left;
    .rightFirstLineText {
      // margin-bottom: 16rpx;
      font-weight: 600;
      font-size: 30rpx;
      line-height: 30rpx;
    }
    .rightSecontLineText {
      line-height: 26rpx;
    }
  }
}
