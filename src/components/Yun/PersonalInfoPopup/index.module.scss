.container {
  padding: 24rpx 32rpx;
}

.personalInfoContent {
  padding: 0 !important;
}

.notice {
  display: flex;
  align-items: center;
  background: rgba(61, 94, 255, 0.08);
  color: #3d5eff;
  padding: 0 32rpx;
  height: 48rpx;
  img {
    width: 28rpx;
    height: 36rpx;
    margin-right: 12rpx;
  }
}

.form {
  margin-bottom: 56rpx;
  :global {
    .adm-list-item-content-prefix {
      flex: 0 0 160rpx;
    }
  }
}

.agreementLine {
  color: #7c889c;

}

.customInput {
  position: relative;
  :global {
    .adm-input-element {
      padding-right: 40rpx;
    }
  }
  .cameraIcon {
    position: absolute;
    right: 24rpx;
    top: 31rpx;
    width: 28rpx;
    height: 28rpx;
  }
}


.submitBtn {
  margin-top: 16rpx;
  margin-bottom: 32rpx;
  border-radius: 12rpx;
  background: #4e6ef2;
}

.smsCodeFormItem {
  :global(.adm-list-item-content-extra) {
    position: absolute;
    text-align: right;
    top: 22rpx;
    right: 24rpx;
    color: #111;
  }
}


.smsBtn {
  color: #111;
}