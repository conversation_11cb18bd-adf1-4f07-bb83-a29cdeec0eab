import React, {
  useState,
  useRef,
  Fragment,
  useImperativeHandle,
  forwardRef,
  useCallback,
} from 'react';
import { Form, Input, Button, Toast } from 'antd-mobile';
import classnames from 'classnames';
import {
  CommonPopup,
  TbPlatformRealPersonIdentifyAgreementPopup,
  SmsCodeInput,
  PhoneNumberInput,
  RedPacketPopover,
} from '@/components';
import { APPLICATION_DATA_FIELD_MAP as FIELD_MAP } from '@/store/credit/model';
import { regex } from '@ali/iec-dtao-utils';
import UploadImage from '@/components/UploadImage';
import { useDebounceFn } from 'ahooks';
import { useSmsCodeCount } from '@/hooks';
import { SMSCODE_REG, PHONE_REG, SMS_BASE_PARAMS } from '@/common/constant';
import { sendSmsCode, checkSmsCode } from '@/store/common/actions';
import { log } from '@alife/dtao-iec-spm-log';
import styles from './index.module.scss';
import { CREDIT_APPLY_ORDER_DTO } from '@/store/types';
import { addAntdFormErrorLog, _ } from '@/utils';

interface PersonalInfoPopupProps {
  onSubmit?: (values: { name: string; idCard: string }) => void;
  // creditOrderData: CREDIT_APPLY_ORDER_DTO;
}

const PersonalInfoPopup = ({ onSubmit }: PersonalInfoPopupProps, ref) => {
  const [form] = Form.useForm();
  const tbPlatformRealPersonIdentifyAgreementPopupRef = useRef<any>(null);
  const [visible, setVisible] = useState(false);
  const [curSmsCodeData, setCurSmsCodeData] = useState<any>(null);
  const [smsCodeDisabled, setSmsCodeDisabled] = useState(true);
  const [creditOrderData, setCreditOrderData] = useState<CREDIT_APPLY_ORDER_DTO | null>(null);
  const { startCount, ...countData } = useSmsCodeCount({
    reSendTxt: '发送验证码',
    countingTextRender: (count) => `${count}s`,
  });

  const creditOrderPhoneNo = _.get(creditOrderData, 'applicationData.phone.phoneNo');

  const { run: handleSubmit } = useDebounceFn(
    async () => {
      log.addClickLog('identity-submit');

      // 表单校验
      const values = await executeFormValidate();
      if (!values) return;
      // 没有手机号 需要做验证码校验
      if (!creditOrderPhoneNo) {
        try {
          const smsCodeCheckRes: any = await checkSmsCode({
            ...curSmsCodeData,
            code: values.smscode,
            phone: _.get(values, FIELD_MAP.phone),
          });
          if (!smsCodeCheckRes.result) {
            throw new Error('SMSCODE_CHECK_FAILED');
          }
        } catch (error) {
          log.addErrorLog('smscode-check', { error: error.message });
          Toast.show('验证码错误');
          return;
        }
      } else {
        _.set(values, FIELD_MAP.phone, creditOrderPhoneNo);
      }

      onSubmit?.(values);
    },
    {
      wait: 1000,
      leading: true,
      trailing: false,
    },
  );

  const executeFormValidate = async (name?) => {
    let validateResult;
    try {
      validateResult = await form.validateFields(name ? [name] : undefined);
      return validateResult;
    } catch (error) {
      addAntdFormErrorLog(error.errorFields);
    }
  };

  const CustomInput = useCallback(({ onChange, value }: any) => {
    const handleOcrSuccess = (res) => {
      log.addErrorLog('ocr-success', { data: JSON.stringify(res) });
      onChange(res.num);
    };
    const handleOcrFailed = (e) => {
      log.addErrorLog('ocr-failed', { e });
      Toast.show('识别失败，请重新上传');
    };
    return (
      <div className={styles.customInput}>
        <Input onChange={onChange} value={value} placeholder="请输入" />
        <span>
          <UploadImage
            needHarmonyOSCompatible
            ocrConfig={{
              ocrType: 'front',
            }}
            emptyRender={cameraRender}
            uploadedImgRender={cameraRender}
            onOcrSuccess={handleOcrSuccess}
            onOcrFailed={handleOcrFailed}
          />
        </span>
      </div>
    );
  }, []);

  const cameraRender = () => {
    return (
      <img
        className={styles.cameraIcon}
        src="https://gw.alicdn.com/imgextra/i4/O1CN01i1sOFY1JdPJZVFReE_!!6000000001051-2-tps-56-56.png"
      />
    );
  };

  const toggleVisible = (v) => {
    setVisible(v);
  };

  const handleValuesChange = (fieldObj, allValues) => {
    const phoneValue = _.get(allValues, FIELD_MAP.phone);

    if (PHONE_REG.test(phoneValue)) {
      setSmsCodeDisabled(false);
    } else {
      setSmsCodeDisabled(true);
    }
  };

  const { run: handleSendSmsCodeBtnClick } = useDebounceFn(
    async () => {
      log.addClickLog('send-sms-code-btn-click');
      if (countData.smsBtnDisabled) {
        return;
      }

      const validateResult = await executeFormValidate(FIELD_MAP.phone);
      if (!validateResult) {
        Toast.show('请先输入手机号');
        return;
      }
      const phoneValue = _.get(validateResult, FIELD_MAP.phone);

      try {
        const sendRes: any = await sendSmsCode({
          phone: phoneValue,
          ...SMS_BASE_PARAMS,
        });
        if (sendRes.result) {
          setCurSmsCodeData({
            ...SMS_BASE_PARAMS,
            requestId: sendRes.requestId,
          });
          Toast.show('短信发送成功');
          startCount();
        } else {
          throw new Error();
        }
      } catch (error) {
        log.addErrorLog('send-smscode-error', { error });
        Toast.show('短信发送失败');
      }
    },
    {
      wait: 1000,
      leading: true,
      trailing: false,
    },
  );

  useImperativeHandle(ref, () => ({
    toggleVisible,
    show: (data) => {
      log.addShowLog('personalinfo-popup', {
        phoneNo: _.get(data?.creditOrderData, 'applicationData.phone.phoneNo'),
      });
      setCreditOrderData(data?.creditOrderData);
      toggleVisible(true);
    },

    hide: () => {
      toggleVisible(false);
    },
  }));

  return (
    <Fragment>
      <CommonPopup
        visible={visible}
        onClose={() => toggleVisible(false)}
        title="完善个人信息"
        contentClassName={styles.personalInfoContent}
      >
        <div>
          <div className={styles.notice}>
            <img src="https://gw.alicdn.com/imgextra/i3/O1CN01SqEtwx1yGPg18vNPT_!!6000000006551-2-tps-56-72.png" />
            该信息将作为您的淘宝网账户认证信息，请务必谨慎填写
          </div>
          <div className={styles.container}>
            <Form
              form={form}
              className={styles.form}
              onValuesChange={handleValuesChange}
              layout="horizontal"
            >
              <Form.Item
                label="姓名"
                name={FIELD_MAP.licenseName}
                rules={[
                  {
                    required: true,
                    message: '请输入姓名',
                  },
                ]}
              >
                <Input placeholder="请输入" />
              </Form.Item>

              <Form.Item
                label="身份证号"
                name={FIELD_MAP.licenseNo}
                rules={[
                  {
                    required: true,
                    message: '请输入身份证号',
                  },
                  {
                    pattern: regex.ID_CARD_IN_LAND,
                    message: '身份证号格式错误',
                  },
                ]}
              >
                <CustomInput />
              </Form.Item>
              {!creditOrderPhoneNo && (
                <Fragment>
                  <Form.Item
                    label="手机号"
                    name={FIELD_MAP.phone}
                    rules={[
                      {
                        required: true,
                        message: '请输入手机号',
                      },
                      {
                        pattern: PHONE_REG,
                        message: '手机号码格式错误',
                      },
                    ]}
                    className={styles.smsCodeFormItem}
                  >
                    <PhoneNumberInput placeholder="请填写手机号" />
                  </Form.Item>
                  <Form.Item
                    label="验证码"
                    name={'smscode'}
                    rules={[
                      {
                        required: true,
                        message: '请输入验证码',
                      },
                      {
                        pattern: SMSCODE_REG,
                        validateTrigger: 'onBlur',
                        message: '验证码格式错误',
                      },
                    ]}
                    className={styles.smsCodeFormItem}
                    extra={
                      <span
                        onClick={handleSendSmsCodeBtnClick}
                        className={classnames({
                          [styles.smsBtn]: true,
                          'sms-code-btn': true,
                          disabled: smsCodeDisabled || countData.smsBtnDisabled,
                        })}
                      >
                        {countData.smsBtnTxt}
                      </span>
                    }
                  >
                    <SmsCodeInput placeholder="请填写验证码" />
                  </Form.Item>
                </Fragment>
              )}
            </Form>

            <div className={styles.agreementLine}>
              查阅
              <a
                onClick={() =>
                  tbPlatformRealPersonIdentifyAgreementPopupRef.current.toggleVisible(true)
                }
              >
                《淘宝平台账户实名认证协议》
              </a>
            </div>

            <RedPacketPopover position="right" offerList={_.get(creditOrderData, 'exposePlatformPromotionOfferList')} descriptionDisplayPosition="LP_PAGE_OPENING_BUTTON_BUBBLE">
              <Button block color="primary" className={styles.submitBtn} onClick={handleSubmit}>
                同意协议并查看额度
              </Button>
            </RedPacketPopover>
          </div>
        </div>
      </CommonPopup>
      <TbPlatformRealPersonIdentifyAgreementPopup
        ref={tbPlatformRealPersonIdentifyAgreementPopupRef}
      />
    </Fragment>
  );
};

export default forwardRef(PersonalInfoPopup);
