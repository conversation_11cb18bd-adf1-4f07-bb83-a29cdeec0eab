import styles from './index.module.scss';
import { getInstitutionCouponData } from '@/store/lib/format';
import { PromotionOffer } from '@/store/types';
import { _ } from '@/utils';
import { log } from '@alife/dtao-iec-spm-log';

interface CouponPopoverProps {
  offerList: PromotionOffer[] | any;
  children?: any;
}

function CouponPopover(props: CouponPopoverProps) {
  const promotionOfferData = getInstitutionCouponData(props.offerList);
  const type = promotionOfferData?.type;

  if (_.isEmpty(promotionOfferData) || !type) {
    return props.children;
  }
  log.addShowLog('red-packet-offer');

  return (
    <div className={styles.couponPopoverWrap}>
      <div className={styles.content}>
        <img className={styles.couponIcon} src="https://gw.alicdn.com/imgextra/i3/O1CN01dKUX6P1gpWxTFHZG2_!!6000000004191-2-tps-48-48.png" />
        {promotionOfferData.promotionOfferDescription}
      </div>
      {props.children}
    </div>
  );
}

export default CouponPopover;
