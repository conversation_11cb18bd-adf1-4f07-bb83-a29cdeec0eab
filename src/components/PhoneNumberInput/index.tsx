/**
 * @file 手机号输入框组件
 * <AUTHOR>
 */

import { useCallback } from 'react';
import { Input } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';
import { replace } from 'lodash-es';

interface PhoneNumberInputProps {
  onChange?: (value: string) => void;
  value?: string;
  placeholder?: string;
  logKey?: string;
  disabled?: boolean;
}

export default function PhoneNumberInput(props: PhoneNumberInputProps) {
  const { onChange, logKey = 'phone-number', placeholder, value = '', disabled = false } = props;

  const handleInputClick = useCallback(() => {
    log.addClickLog(`${logKey}-click`);
  }, [logKey]);

  const handleOnChange = (val: any) => {
    if (val) {
      // 非正整数
      if (!/^[1-9]\d*$/.test(val)) {
        return;
      }
      onChange && onChange(replace(val, /[^0-9]/g, ''));
    } else {
      onChange && onChange(val);
    }
  };

  return (
    <Input
      onChange={handleOnChange}
      onClick={handleInputClick}
      placeholder={placeholder}
      value={value}
      inputMode="numeric"
      type="text"
      maxLength={11}
      disabled={disabled}
    />
  );
}
