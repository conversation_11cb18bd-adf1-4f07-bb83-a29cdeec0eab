/**
 * @file 协议预览
 * <AUTHOR>
 */

import { useEffect, useRef, useState } from 'react';
import { Button, Toast } from 'antd-mobile';
import { isEmpty } from 'lodash-es';
import { log } from '@alife/dtao-iec-spm-log';

import { AGREEMENT_PREVIEW_ALIYUN, ErrorMessageMap } from '@/common/constant';
import type { UnsignedAgreementList } from '@/store/agreement/actions';
import useCountDown from '@/hooks/useCountDown';
import { navigatorToOutside } from '@/utils/link';
import { getList } from '../lib';

import styles from './index.module.scss';
import { forceAgreementPageLog } from '@/utils/goc';

interface CompulsoryProps {
  bizType: string;
  options?: UnsignedAgreementList[];
  params?: any;
  currentIndex?: number;
  onChange?: any;
  onCompleted?: any;
  showOption?: any;
}

export default function Compulsory(props: CompulsoryProps) {
  const { bizType, options, showOption, params, currentIndex, onChange, onCompleted } = props;
  const { count, start } = useCountDown({
    duration: 1000,
  });
  const [isCompleted, setIsCompleted] = useState(false);
  const completedRef = useRef(false);
  const sendRef = useRef(false);
  const optionsRef = useRef<UnsignedAgreementList[]>();

  const postMessage = (iframe: any) => async () => {
    const previewList = await getList(optionsRef.current, bizType, showOption);
    if (isEmpty(previewList)) {
      Toast.show({
        content: ErrorMessageMap.BUSY_DEFUALT,
      });
      forceAgreementPageLog({
        success: false,
        message: 'unsigned-agreements-aliyun-compulsory-error',
        creditPlatform: 'ALIYUN',
      });
    }
    // @ts-ignore
    iframe?.contentWindow?.postMessage({
      module: 'xfd-preview-sandbox',
      data: {
        bizType,
        options: optionsRef.current,
        showOption: {
          action: 'compulsory',
          params,
          currentIndex,
        },
        previewList,
      },
    }, '*');
  };

  const handlePreview = () => {
    if (bizType && optionsRef.current && showOption?.action) {
      const previewContainer = document.getElementById('agreementCompulsory');
      if (previewContainer) {
        postMessage(previewContainer)();
      }
    }
  };

  const handleRead = () => {
    log.addClickLog('xfd-preview-sandbox-completed');
    onChange && onChange(true);
    onCompleted();
  };

  const handleCompulsoryMessage = (event?: any) => {
    const module = event?.data?.module;
    if (!completedRef.current && module === 'xfd-preview-sandbox') {
      forceAgreementPageLog({
        success: true,
        message: 'force',
        creditPlatform: 'ALIYUN',
      });
      completedRef.current = true;
      setIsCompleted(true);
      start(5);
    }
    if (module === 'xfd-preview-click') {
      navigatorToOutside(event?.data?.data?.url);
    }
    if (module === 'xfd-preview-error') {
      completedRef.current = false;
      setIsCompleted(false);
      log.addErrorLog('xfd-preview-compulsory-error', event?.data);
    }
    if (module === 'xfd-preview-ready') {
      log.addOtherLog('xfd-preview-compulsory-ready');
      sendRef.current = true;
      handlePreview();
    }
  };

  useEffect(() => {
    window.addEventListener('message', handleCompulsoryMessage);
    return () => {
      window.removeEventListener('message', handleCompulsoryMessage);
    };
  }, []);

  useEffect(() => {
    optionsRef.current = options;
    if (sendRef.current) {
      handlePreview();
    }
  }, [options]);

  return (
    <div className={styles.agreements}>
      <iframe
        id="agreementCompulsory"
        className={styles.compulsory}
        src={AGREEMENT_PREVIEW_ALIYUN}
      />
      <div className={styles.compulsoryButton}>
        <Button
          className={styles.read}
          color="primary"
          disabled={count < 0 || !options?.length || count > 0 || !isCompleted}
          block
          onClick={handleRead}
        >
          同意以上协议{count ? `（${count}s）` : ''}
        </Button>
      </div>
    </div>
  );
}
