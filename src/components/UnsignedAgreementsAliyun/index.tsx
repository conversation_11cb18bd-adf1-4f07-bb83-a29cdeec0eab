/**
 * @file 协议预览
 * <AUTHOR>
 */

import {
  forwardRef, useCallback, useEffect,
  useImperativeHandle, useMemo, useRef, useState,
} from 'react';
import classNames from 'classnames';
import { Toast } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';

import { queryUnSignAgreementList, QueryUnSignAgreementListResponse } from '@/store/agreement/actions';
import { _ } from '@/utils';
import CommonPopup from '../CommonPopup';
import { ErrorMessageMap, INSTITUTION_NAME_MAP } from '@/common/constant';
import { getAgreementNames } from '../UnsignedAgreements/format';
import Compulsory from './Compulsory';
import Preview from './Preview';

import styles from './index.module.scss';
import { isEmpty, join } from 'lodash-es';

interface CompletedOptions {
  institutionList?: string[];
}

interface UnsignedAgreementsAliyunProps {
  className?: string;
  institutionList?: string[];
  title?: string;
  prefix?: string;
  bizType: 'PLATFORM' | 'LOAN' | 'CREDIT' | 'BIND_CARD';
  popupTitle?: string;
  unInit?: boolean;
  renderTitle?: (result?: QueryUnSignAgreementListResponse) => React.JSX.Element | null;
  onChange?: (value?: boolean) => void;
  onCompleted?: (options?: CompletedOptions) => void;
  onError?: () => void;
  getParams?: () => any;
  onClose?: () => void;
  customListResult?: any;
}

interface ShowOption {
  action?: 'preview' | 'compulsory' | '';
  params?: any;
  currentIndex?: number;
}

export interface UnsignedAgreementsAliyunRef {
  show: (option?: ShowOption) => void;
  close: () => void;
}

export const UnsignedAgreementsAliyun = forwardRef((props: UnsignedAgreementsAliyunProps, ref) => {
  const {
    prefix, institutionList, bizType, className, title, popupTitle = '查看协议',
    onChange, renderTitle, onCompleted, getParams, onError, onClose, unInit = false,
    customListResult,
  } = props;
  const [showOption, setShowOption] = useState<ShowOption>();
  const [listResult, setListResult] = useState<QueryUnSignAgreementListResponse>();
  const [mainTitle, setMainTitle] = useState(popupTitle);
  const [switchInstitution, setSwitchInstitution] = useState<string[]>();
  const popupRef = useRef<any>();

  const institutionListStr = useMemo(() => {
    try {
      if (switchInstitution?.length) {
        return JSON.stringify(switchInstitution);
      }
      return '';
    } catch (e) {
      return '';
    }
  }, [switchInstitution]);

  const handlePopupShow = useCallback((option?: ShowOption) => {
    setShowOption(option || {
      action: 'preview',
    });
    popupRef?.current?.toggleVisible(true);
    log.addShowLog(`unsigned-agreement-aliyun-${showOption?.action}-show`);
  }, [showOption?.action]);

  const handlePopupClose = useCallback(() => {
    log.addClickLog(`unsigned-agreement-aliyun-${showOption?.action}-close`);
    setShowOption({});
    popupRef?.current?.toggleVisible(false);
    onClose && onClose();
    setMainTitle(popupTitle);
  }, [showOption?.action]);

  const handleCompleted = useCallback(() => {
    popupRef?.current?.toggleVisible(false);
    setShowOption({
      action: '',
    });
    onCompleted && onCompleted({
      institutionList: switchInstitution,
    });
    log.addClickLog('unsigned-agreement-aliyun-force-completed');
  }, [onCompleted, switchInstitution]);

  const handlePreviewClick = useCallback(() => {
    setShowOption({
      action: 'preview',
      params: getParams && getParams(),
    });
    popupRef?.current?.toggleVisible(true);
  }, [getParams]);

  const handlePreviewClickIndex = useCallback((index: number) => {
    setShowOption({
      action: 'preview',
      params: getParams && getParams(),
      currentIndex: index,
    });
    popupRef?.current?.toggleVisible(true);
  }, [getParams]);

  const fetchUnSignAgreementList = useCallback(async () => {
    try {
      if (bizType !== 'PLATFORM' && !institutionListStr) {
        return;
      }
      const result = await queryUnSignAgreementList({
        institutionList: institutionListStr,
        bizType,
      });
      if (result?.unSignedAgreementGroupList) {
        setListResult(result);
        log.addSuccessLog('unsigned-agreeement-aliyun-success');
      } else {
        throw new Error();
      }
    } catch (e) {
      onError && onError();
      Toast.show({
        content: ErrorMessageMap.BUSY_DEFUALT,
      });
      log.addOtherLog('unsigned-agreeement-aliyun-error');
    }
  }, [institutionListStr, bizType]);

  const handleInstitutionSwitch = () => {
    log.addClickLog('aliyun-institution-switch');
    if (switchInstitution && switchInstitution?.length > 1 && institutionList) {
      setSwitchInstitution([institutionList[0]]);
    } else {
      setSwitchInstitution(institutionList);
    }
  };

  const renderSwitchPanel = () => {
    if (bizType === 'CREDIT' && switchInstitution && institutionList && switchInstitution?.length) {
      const names = switchInstitution.map((item) => INSTITUTION_NAME_MAP[item]);
      let tips = '由以上机构为您评估额度';
      if (switchInstitution?.length > 1) {
        tips = '由以上机构依次为您评估额度';
      } else if (institutionList?.length > 1) {
        tips = '增加评估机构，更易取得额度';
      }
      return (
        <div className={styles.assessInsititutionWrapC}>
          <div className={styles.assessInsititutionWrap}>
            <div className={styles.assessInsititutionLine1}>评估机构：{join(names, '/')}</div>
            <div className={styles.assessInsititutionLine2}>{tips}</div>
            {institutionList.length > 1 && (
              <div className={styles.assessInsititutionSwitch} onClick={handleInstitutionSwitch}>
                <img src="https://gw.alicdn.com/imgextra/i2/O1CN01BomoMz1uDQcEFPr7i_!!6000000006003-2-tps-64-64.png" />
                {switchInstitution?.length > 1 ? '单机构评估' : '多机构评估'}
              </div>
            )}
          </div>
          <p className={styles.assessInsititutionWrapTips}>
            额度需本人申请，身份验证相关信息将提交至服务机构。
          </p>
        </div>
      );
    }
    return null;
  };

  const renderAgreement = () => {
    switch (showOption?.action) {
      case 'compulsory':
        return (
          <Compulsory
            options={listResult?.unSignedAgreementGroupList}
            bizType={bizType}
            onChange={onChange}
            onCompleted={handleCompleted}
            params={showOption.params}
            showOption={showOption}
          />
        );
      case 'preview':
        return (
          <Preview
            bizType={bizType}
            options={listResult?.unSignedAgreementGroupList}
            params={showOption.params}
            currentIndex={showOption?.currentIndex}
            showOption={showOption}
          />
        );
      default:
        return null;
    }
  };

  const renderAgreementsName = useCallback(() => {
    const unSignedAgreementGroupList = listResult?.unSignedAgreementGroupList;
    if (renderTitle && unSignedAgreementGroupList?.length) {
      return (
        <div className={styles.agreementName} onClick={handlePreviewClick}>
          {renderTitle(listResult)}
        </div>
      );
    }
    if (title) {
      return (
        <div className={styles.agreementName} onClick={handlePreviewClick}>
          <span className={styles.name}>{title}</span>
        </div>
      );
    }
    if (unSignedAgreementGroupList?.length) {
      const names = getAgreementNames(unSignedAgreementGroupList);
      return _.map(names, (name, index) => (
        <p className={styles.name} onClick={() => handlePreviewClickIndex(index)}>
          {name}
        </p>
      ));
    }
    return null;
  }, [listResult, title, renderTitle, handlePreviewClick, handlePreviewClickIndex]);

  useEffect(() => {
    setSwitchInstitution(institutionList);
  }, [institutionList]);

  useEffect(() => {
    if (!unInit) {
      log.addOtherLog('unsigned-agreement-aliyun-fetch');
      fetchUnSignAgreementList();
    }
  }, [unInit, institutionListStr]);

  useEffect(() => {
    if (unInit && !isEmpty(customListResult)) {
      log.addOtherLog('unsigned-agreement-aliyun-custom');
      setListResult(customListResult?.payLoad);
    }
  }, [unInit, customListResult]);

  useEffect(() => {
    log.addShowLog('unsigned-agreement-aliyun-show');
  }, []);

  useImperativeHandle(ref, () => ({
    show: handlePopupShow,
    close: handlePopupClose,
  }));

  return (
    <div className={classNames(styles.agreementPreview, className && className)}>
      <div className={styles.title}>
        {prefix ? <div className={styles.prefix}>{prefix}</div> : null}
        {renderAgreementsName()}
      </div>
      <CommonPopup
        ref={popupRef}
        title={mainTitle}
        onClose={handlePopupClose}
        onMaskClick={handlePopupClose}
        contentClassName={styles.content}
      >
        <div className={styles.container}>
          {renderSwitchPanel()}
          {renderAgreement()}
        </div>
      </CommonPopup>
    </div>
  );
});
