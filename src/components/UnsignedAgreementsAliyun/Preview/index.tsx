/**
 * @file 协议预览
 * <AUTHOR>
 */

import { useEffect, useRef } from 'react';
import { log } from '@alife/dtao-iec-spm-log';
import { isEmpty } from 'lodash-es';
import { Toast } from 'antd-mobile';

import { AGREEMENT_PREVIEW_ALIYUN, ErrorMessageMap } from '@/common/constant';
import type { UnsignedAgreementList } from '@/store/agreement/actions';
import { navigatorToOutside } from '@/utils/link';
import { getList } from '../lib';

import styles from './index.module.scss';

interface PreviewProps {
  bizType: string;
  options?: UnsignedAgreementList[];
  params?: any;
  currentIndex?: number;
  showOption?: any;
}

export default function Preview(props: PreviewProps) {
  const { bizType, options, showOption, params, currentIndex } = props;
  const sendRef = useRef(false);
  const optionsRef = useRef<UnsignedAgreementList[]>();

  const postMessage = (iframe: any) => async () => {
    const previewList = await getList(optionsRef.current, bizType, showOption);
    if (isEmpty(previewList)) {
      Toast.show({
        content: ErrorMessageMap.BUSY_DEFUALT,
      });
      log.addErrorLog('unsigned-agreements-aliyun-preview-error');
    }
    // @ts-ignore
    iframe?.contentWindow?.postMessage({
      module: 'xfd-preview-sandbox',
      data: {
        bizType,
        options: optionsRef.current,
        showOption: {
          action: 'preview',
          params,
          currentIndex,
        },
        previewList,
      },
    }, '*');
  };

  const handlePreview = () => {
    if (bizType && optionsRef.current && showOption?.action) {
      const previewContainer = document.getElementById('agreementPreview');
      if (previewContainer) {
        postMessage(previewContainer)();
      }
    }
  };

  const handlePreviewMessage = (event?: any) => {
    const module = event?.data?.module;
    if (module === 'xfd-preview-click') {
      navigatorToOutside(event?.data?.data?.url);
    }
    if (module === 'xfd-preview-error') {
      log.addErrorLog('xfd-preview-preview-error', event?.data);
    }
    if (module === 'xfd-preview-ready') {
      log.addOtherLog('xfd-preview-preview-ready');
      sendRef.current = true;
      handlePreview();
    }
  };

  useEffect(() => {
    optionsRef.current = options;
    if (sendRef.current) {
      handlePreview();
    }
  }, [options]);

  useEffect(() => {
    window.addEventListener('message', handlePreviewMessage);
    return () => {
      window.removeEventListener('message', handlePreviewMessage);
    };
  }, []);

  return (
    <div className={styles.agreements}>
      <iframe
        id="agreementPreview"
        className={styles.preview}
        src={AGREEMENT_PREVIEW_ALIYUN}
      />
    </div>
  );
}
