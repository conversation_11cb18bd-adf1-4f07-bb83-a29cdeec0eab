import { preview } from '@/store/agreement/actions';
import { AgreementDTO } from '@/store/types';
import { concat, forEach } from 'lodash-es';

// 协议中心协议查询
function previewAgreement(agreements: AgreementDTO[], bizType: string, showOption: any) {
  if (!agreements?.length) {
    throw new Error();
  }
  const promiseList = agreements.map((item) => {
    const params =
      item?.source === 'AGREEMENT_CENTER'
        ? showOption?.params?.agreementCenter
        : showOption?.params?.institution;
    return preview({
      bizType,
      code: item.code,
      source: item.source,
      institution: item.institution,
      name: item.name,
      params,
      product: 'XFD',
    });
  });
  return promiseList;
}

export async function getList(options: any, bizType: string, showOption: string) {
  try {
    let agreementList: any[] = [];
    if (options && options?.length) {
      forEach(options, (option) => {
        const { unSignedAgreementList } = option;
        agreementList = concat(agreementList, unSignedAgreementList);
      });
      const promiseList = previewAgreement(agreementList, bizType, showOption);
      const resList = await Promise.all(promiseList);
      let agreementResults: AgreementDTO[] = [];
      forEach(resList, (item) => {
        if (item?.previewAgreementList) {
          agreementResults = concat(agreementResults, item?.previewAgreementList);
        }
      });
      return agreementResults;
    }
    return [];
  } catch (e) {
    return [];
  }
}
