@import '../../assets/styles/reset.css';

.container {
  background-color: #f5f5f5;
}

.agreementPreview {
  display: inline-block;
  .name {
    color: var(--primary);
    font-size: var(--agreement-name-font-size);
    font-size: 20rpx;
    line-height: 20rpx;
  }
  .agreementName {
    line-height: 1;
  }
  .title {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .prefix {
    position: relative;
    font-size: 20rpx;
    line-height: 20rpx;
    color: #7c889c;
    padding-right: 6rpx;
    top: 1rpx;
  }
}

.content {
  padding: 0 !important;
}

.assessInsititutionWrapC {
  padding: 0 24rpx;
  padding-bottom: 16rpx;
  background-color: #fff;
  .assessInsititutionWrapTips {
    font-size: 26rpx;
    line-height: 26rpx;
    color: #50607a;
    padding-top: 16rpx;
  }
}

.assessInsititutionWrap {
  border-radius: 12rpx;
  padding: 24rpx 0 24rpx 24rpx;
  background: #f3f6f8;
  position: relative;
  .assessInsititutionLine1 {
    color: #11192d;
    font-size: 26rpx;
    margin-bottom: 12rpx;
  }
  .assessInsititutionLine2 {
    display: flex;
    justify-content: space-between;
    font-size: 24rpx;
    color: #50607a;
    margin-bottom: 12rpx;
  }
  .assessInsititutionSwitch {
    color: #11192d;
    border-radius: 403rpx 0 0 403rpx;
    background: #e5e8ec;
    padding: 16rpx;
    position: absolute;
    right: 0;
    bottom: 20rpx;
    img {
      width: 32rpx;
      height: 32rpx;
      margin-right: 4rpx;
    }
  }
}
