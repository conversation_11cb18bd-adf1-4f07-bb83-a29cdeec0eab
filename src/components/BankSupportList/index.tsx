/**
 * @file 支持银行组件
 * <AUTHOR>
 */

import { useCallback, useRef } from 'react';
import classNames from 'classnames';

import IconInstitution from '../IconInstitution';

import styles from './index.module.scss';

import CommonPopup from '../CommonPopup';

interface BankDTO {
  bankCode: string;
  bankName: string;
}

interface BankSupportListProps {
  className?: string;
  options?: BankDTO[];
}

export default function BankSupportList(props: BankSupportListProps) {
  const { options = [], className } = props;
  const listRef = useRef<any>();

  const handleClick = useCallback(() => {
    listRef.current.toggleVisible(true);
  }, []);

  return (
    <div className={classNames(styles.supportList, className && className)}>
      <p className={styles.text} onClick={handleClick}>查看支持银行</p>
      <CommonPopup ref={listRef} title="支持银行">
        <ul className={styles.bankList}>
          {options.map((option) => (
            <li key={`support-${option}`} className={styles.option}>
              <IconInstitution className={styles.icon} type={option?.bankCode} />
              <p className={styles.name}>{option?.bankName}</p>
            </li>
          ))}
        </ul>
      </CommonPopup>
    </div>
  );
}
