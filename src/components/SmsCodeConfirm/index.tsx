/**
 * @file 短信验证
 * <AUTHOR>
 */

import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Form, Toast, Button, Modal } from 'antd-mobile';
import classNames from 'classnames';

import CommonPopup from '../CommonPopup';
import SmsCodeInput from '../SmsCodeInput';
import useCountDown from '@/hooks/useCountDown';

import styles from './index.module.scss';

interface SmsCodeResponse {
  success?: boolean;
}

interface SmsCodeResquest {
  smsCode: string;
}

interface SmsCodeConfirmProps {
  doSend: () => Promise<SmsCodeResponse>;
  doConfirm: (request: SmsCodeResquest) => Promise<SmsCodeResponse>;
  // length?: number;
  title?: any;
  disabled?: boolean;
}

export interface SmsCodeConfirmRef {
  exec: () => void;
}

export const SmsCodeConfirm = forwardRef((props: SmsCodeConfirmProps, ref) => {
  const { title, doSend, doConfirm, disabled } = props;
  // const inputRefs = useRef<any>([]);
  const [form] = Form.useForm();
  const popupRef = useRef<any>();
  const [error, setError] = useState<any>();
  const [confirming, setConfirming] = useState(false);
  const { start, count } = useCountDown({ duration: 1000 });
  const smsCodeValue = Form.useWatch('smsCode', form);

  const handleExec = useCallback(() => {
    // 已经发送了，直接start
    popupRef.current.toggleVisible(true);
    start(60);
  }, []);

  useImperativeHandle(ref, () => ({
    exec: handleExec,
  }));

  const handleSend = useCallback(async () => {
    const loading = Toast.show({
      icon: 'loading',
      content: '发送中，请稍等...',
      duration: 3000,
    });
    try {
      const sendRes = await doSend();
      loading.close();
      if (sendRes?.success) {
        start(60);
      } else {
        throw new Error();
      }
    } catch (e) {
      Toast.show({
        content: '短信发送失败，请重试',
      });
    }
  }, [doSend, start]);

  // const getSmsCode = useCallback(() => {
  //   const formRes = form.getFieldsValue();
  //   const smsCode = join(values(formRes), '');
  //   return smsCode;
  // }, [form]);

  const handleFinish = useCallback(async (smsCode?: string) => {
    try {
      // const smsCode = getSmsCode();
      if (!smsCode?.length || confirming) {
        return;
      }
      if (smsCode.length < 6) {
        return;
      }
      if (smsCode.length > 6) {
        Toast.show({
          content: '请输入正确6位验证码',
        });
        setError({
          message: '短信验证码错误',
        });
        return;
      }
      setConfirming(true);
      const confirmRes = await doConfirm({
        smsCode,
      });
      setConfirming(false);
      if (confirmRes?.success) {
        Toast.show({
          icon: 'success',
          content: '验证成功',
        });
        popupRef.current.toggleVisible(false);
      } else {
        setConfirming(false);
        setError(confirmRes);
      }
    } catch (e) {}
  }, [doConfirm, confirming]);

  // const handleAdd = useCallback((index: number) => {
  //   if (index <= length - 1) {
  //     const nextInput = inputRefs.current[index + 1];
  //     if (nextInput) {
  //       const smsCode = getSmsCode();
  //       if (smsCode?.length < 6) {
  //         nextInput.focus();
  //       } else if (smsCode?.length === 6) {
  //         handleFinish();
  //       }
  //     } else if (!nextInput && index === length - 1) {
  //       handleFinish();
  //     }
  //   }
  // }, [handleFinish, getSmsCode, length]);

  // const handleDel = useCallback((index: number) => {
  //   if (index > 0) {
  //     const prevInput = inputRefs.current[index - 1];
  //     if (prevInput) {
  //       prevInput.focus();
  //     }
  //   }
  // }, []);

  // const handleKeyDown = useCallback((index: number) => (e: any) => {
  //   setError(null);
  //   const curentInput = inputRefs.current[index];
  //   if (e?.code === 'Backspace' || e?.key === 'Backspace') {
  //     curentInput?.clear && curentInput.clear();
  //     handleDel(index);
  //   }
  // }, [handleDel]);

  // const handleKeyUp = useCallback((index: number) => (e: any) => {
  //   setError(null);
  //   if (e?.code !== 'Backspace' && e?.key !== 'Backspace') {
  //     handleAdd(index);
  //   }
  // }, [handleAdd]);

  // const setInputRefGroup = useCallback((index: number) => (el: unknown) => {
  //   inputRefs.current[index] = el;
  // }, []);

  // const renderInputGroup = () => {
  //   const group: any = [];
  //   for (let i = 0; i < length; i++) {
  //     group.push(
  //       <Form.Item
  //         key={`smsCodeInput-${i}`}
  //         name={`smsCodeInput${i}`}
  //         noStyle
  //       >
  //         <Input
  //           ref={setInputRefGroup(i)}
  //           maxLength={1}
  //           minLength={1}
  //           className={classNames(styles.input, error && styles.error)}
  //           type="string"
  //           inputMode="numeric"
  //           disabled={disabled || count === 0}
  //           onKeyUp={handleKeyUp(i)}
  //           onKeyDown={handleKeyDown(i)}
  //         />
  //       </Form.Item>,
  //     );
  //   }
  //   return group;
  // };

  const handleClose = () => {
    if (count) {
      Modal.show({
        title: '提示',
        content: '没有收到验证码？再等等，有的银行发短信比较慢哦',
        closeOnAction: true,
        actions: [{
          key: 'continue',
          text: '继续验证',
          primary: true,
        }],
      });
      return false;
    }
  };

  useEffect(() => {
    if (count === 0) {
      setError(null);
      form.resetFields();
    }
  }, [count, form]);

  useEffect(() => {
    handleFinish(smsCodeValue);
  }, [smsCodeValue]);

  return (
    <CommonPopup
      disableClose={!!count}
      ref={popupRef}
      title="手机号验证"
      onClose={handleClose}
    >
      <div className={styles.smsCodeConfirm}>
        <div className={styles.main}>
          {title()}
          <Form
            form={form}
            hasFeedback={false}
            className={styles.form}
          >
            <Form.Item name="smsCode">
              <SmsCodeInput
                className={classNames(styles.inputLang, error && styles.error)}
                autoFocus
                disabled={disabled}
              />
            </Form.Item>
          </Form>
        </div>
        <div className={styles.send}>
          <Button
            color="primary"
            block
            className={styles.btn}
            disabled={!!count || confirming}
            onClick={handleSend}
          >
            {!count ? '发送验证码' : `${count}s后重新发送`}
          </Button>
        </div>
      </div>
    </CommonPopup>
  );
});
