/**
 * @file 右箭头
 */

import classNames from 'classnames';

import styles from './index.module.scss';

export interface ArrowIconProps {
  inner?: boolean;
  className?: string;
  type?: 'right' | 'up' | 'right-light' | 'right-dark';
  onClick?: () => void;
}

export default function ArrowIcon(props: ArrowIconProps) {
  const { inner, type = 'right', className, onClick } = props;
  return (
    <i
      className={classNames(
        styles.arrow,
        inner && styles.inner,
        styles[type],
        className && className,
      )}
      onClick={onClick}
    />
  );
}
