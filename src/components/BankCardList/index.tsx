/**
 * @file 银行卡列表
 * <AUTHOR>
 */

import { IconInstitution } from '@/components';
import { BankCardDTO } from '@/store/bankcard/actions';

import styles from './index.module.scss';
import { map } from 'lodash-es';
import { useCallback } from 'react';
import { pushPage } from '@/utils/link';
import { getBankCardNo } from '@/utils';
import { PAGES } from '@/common/constant';

interface BankCardListProps {
  options?: BankCardDTO[];
  desc?: string;
  value?: BankCardDTO;
  onChange?: (item: BankCardDTO) => void;
  afterOnChange?: () => void;
  onAddClick?: () => void;
}

export default function BankCardList(props: BankCardListProps) {
  const { options, desc = '', value, onChange, afterOnChange, onAddClick } = props;

  const getBindCardNo = (item?: BankCardDTO) => {
    return item?.bindCardNo || item?.value;
  };

  const handleChange = useCallback((item: BankCardDTO) => () => {
    if (getBindCardNo(value) !== getBindCardNo(item)) {
      onChange && onChange(item);
      afterOnChange && afterOnChange();
    }
  }, [value, onChange, afterOnChange]);

  const handleAdd = () => {
    onAddClick && onAddClick();
    pushPage(PAGES.BindCard);
  };

  const renderItem = (item: BankCardDTO) => {
    const { bankCardNo, bankName, masterCard, bankCode } = item;
    const valueBindCardNo = getBindCardNo(value);
    const itemBindCardNo = getBindCardNo(item);
    return (
      <div className={styles.item} onClick={handleChange(item)}>
        <div className={styles.left}>
          <IconInstitution className={styles.bankIcon} type={bankCode} />
          <div className={styles.info}>
            <div className={styles.title}>
              <p className={styles.name}>{bankName}({getBankCardNo(bankCardNo)})</p>
              {masterCard ? <span className={styles.tag}>主卡</span> : null}
            </div>
            <p className={styles.desc}>{desc}</p>
          </div>
        </div>
        {(valueBindCardNo && itemBindCardNo && itemBindCardNo === valueBindCardNo) ? <i className={styles.addBlue} /> : null}
      </div>
    );
  };

  const renderAdd = () => {
    return (
      <div className={styles.addC}>
        <div className={styles.add} onClick={handleAdd}>
          <i className={styles.addIcon} />
          <p className={styles.addText}>添加银行卡</p>
        </div>
      </div>
    );
  };

  const renderList = () => {
    if (options?.length) {
      return map(options, renderItem);
    }
    return null;
  };

  return (
    <div className={styles.bankCardList}>
      <div className={styles.listC}>
        {renderList()}
      </div>
      {renderAdd()}
    </div>
  );
}
