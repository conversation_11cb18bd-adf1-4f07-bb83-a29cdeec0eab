.bankCardList {
  position: relative;
  .listC {
    padding-bottom: 68rpx;
    height: 604rpx;
    overflow: auto;
  }
  .item {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx;
    margin-bottom: 16rpx;
    .left {
      display: flex;
      flex-direction: row;
      align-items: center;
      .bankIcon {
        font-size: 74rpx;
      }
      .info {
        padding-left: 10rpx;
        .title {
          display: flex;
          flex-direction: row;
          .name {
            font-size: 30rpx;
            font-weight: 500;
            line-height: 30rpx;
            color: #000;
          }
          .tag {
            padding: 6rpx 8rpx;
            text-align: center;
            background: rgba(64, 102, 255, 0.1);
            font-size: 20rpx;
            font-weight: 600;
            line-height: 20rpx;
            color: var(--primary);
            border-radius: 6rpx;
            margin-left: 6rpx;
          }
        }
        .desc {
          font-size: 24rpx;
          line-height: 24rpx;
          color: #50607a;
          padding-top: 13rpx;
        }
      }
    }
    .right {}
    .addBlue {
      display: block;
      width: 36rpx;
      height: 36rpx;
      background-image: url('https://gw.alicdn.com/imgextra/i4/O1CN01HK2Ti91QlNUfnUZ92_!!*************-2-tps-200-200.png');
      background-repeat: no-repeat;
      background-size: cover;
      background-position: 50% 50%;
    }
  }
  .addC {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    padding-top: 24rpx;
  }
  .add {
    width: 100%;
    border-radius: 12rpx;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 25rpx 36rpx;
    background-color: #f3f6f8;
    .addIcon {
      display: block;
      width: 28rpx;
      height: 28rpx;
      background-image: url('https://gw.alicdn.com/imgextra/i2/O1CN01pkigeA1Rb8FWiiZmL_!!6000000002129-2-tps-200-200.png');
      background-repeat: no-repeat;
      background-size: cover;
      background-position: 50% 50%;
      margin-right: 8rpx;
    }
    .addText {
      font-size: 30rpx;
      font-weight: 600;
      line-height: 30rpx;
      text-align: center;
      color: #111;
    }
  }
}
