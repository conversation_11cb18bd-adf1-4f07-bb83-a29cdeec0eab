import { useState, Fragment, useEffect, useRef } from 'react';
import classnames from 'classnames';
import MyCenterComp from '../MyCenter';
import { _, getQueryParams } from '@/utils';
import { LoanSchemaInfoRes } from '@/store/center/actions';

import './index.scss';

interface BottomBarWrapProps {
  HomeComp: any;
  isLp?: boolean;
  PageBottomTipRender?: any;
}

enum PageName {
  home = 'home',
  center = 'center',
}

const homeIconSrc = {
  active:
    'https://gw.alicdn.com/imgextra/i1/O1CN01yq15gn1qeDHulMAHg_!!6000000005520-2-tps-112-112.png',
  default:
    'https://gw.alicdn.com/imgextra/i1/O1CN01sXyeUf1XRrudflPAv_!!6000000002921-2-tps-112-112.png',
};
const myCenterIconSrc = {
  active:
    'https://gw.alicdn.com/imgextra/i4/O1CN01IO6OXW1a9xGmmz04S_!!6000000003288-2-tps-112-112.png',
  default:
    'https://gw.alicdn.com/imgextra/i4/O1CN011de5k81DQgZhmEuUy_!!6000000000211-2-tps-112-112.png',
};

function BottomBarWrap(props: BottomBarWrapProps) {
  const [curPage, setCurPage] = useState<string>('');
  const [showBottomBar, setShowBottomBar] = useState(false);
  const [loanSchema, setLoanSchema] = useState<LoanSchemaInfoRes | null>(null);
  const [centerPageTip, setCenterPageTip] = useState(null);
  const [customStyle, setCustomStyle] = useState({
    home: {
      display: 'block',
    },
    center: {
      display: 'none',
    },
  });
  const homeCompRef = useRef<any>(null);
  const { HomeComp, isLp, PageBottomTipRender } = props;

  useEffect(() => {
    const queryPage = _.get(getQueryParams(), 'to', PageName.home);
    if (queryPage) {
      setCurPage(queryPage);
      // @ts-ignore
      if (queryPage === 'center') {
        setCustomStyle({
          home: {
            display: 'none',
          },
          center: {
            display: 'block',
          },
        });
      }
    }
  }, []);

  const handleShowBottomBar = (show) => {
    setShowBottomBar(show);
  };

  const handleUpdateLoanSchema = (data) => {
    setLoanSchema(data);
  };

  const handleBarItemClick = (pageName) => {
    if (pageName === PageName.home) {
      setCustomStyle({
        home: {
          display: 'block',
        },
        center: {
          display: 'none',
        },
      });
    } else {
      setCustomStyle({
        home: {
          display: 'none',
        },
        center: {
          display: 'block',
        },
      });
    }
    setCurPage(pageName);
  };

  const handleBottomTipClick = () => {
    homeCompRef.current?.handleKnowMoreClick();
  };

  const isHome = curPage === PageName.home;
  const isCenter = curPage === PageName.center;

  useEffect(() => {
    if (loanSchema) {
      setCenterPageTip(
        _.get(
          {
            DXM: '度小满客服热线：95055',
            AI_BANK: '中信百信银行客服热线： 956186',
            XW_BANK: '新网银行客服热线：95394',
            LX: '分期乐客服热线：95730',
            ZYXJ: '中邮消金客服热线：**********',
            XYKD: '小赢卡贷客服热线：952592',
          },
          loanSchema.institution,
        ),
      );
    }
  }, [loanSchema]);

  return (
    <Fragment>
      <HomeComp
        ref={homeCompRef}
        customStyle={customStyle.home}
        onShowBottomBar={handleShowBottomBar}
        onUpdateLoanSchema={handleUpdateLoanSchema}
      />
      <MyCenterComp isLp={isLp} customStyle={customStyle.center} />
      {showBottomBar && (
        <div>
          <div className="bottom-bar">
            {centerPageTip && isCenter && (
              <div className="top-page-tip">
                <div className="topPageTipText">{centerPageTip}</div>
              </div>
            )}
            {PageBottomTipRender && !isCenter && <div className="top-page-tip"><PageBottomTipRender onClick={handleBottomTipClick} /></div>}
            <div
              onClick={() => handleBarItemClick(PageName.home)}
              className={classnames(['bar-item', isHome && 'active'])}
            >
              <div>
                <img src={isHome ? homeIconSrc.active : homeIconSrc.default} />
              </div>
              <div>首页</div>
            </div>
            <div
              onClick={() => handleBarItemClick(PageName.center)}
              className={classnames(['bar-item', isCenter && 'active'])}
            >
              <div>
                <img src={isCenter ? myCenterIconSrc.active : myCenterIconSrc.default} />
              </div>
              <div>我的</div>
            </div>
          </div>
        </div>
      )}
    </Fragment>
  );
}

export default BottomBarWrap;
