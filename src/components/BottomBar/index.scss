.bottom-bar {
  position: fixed;
  width: 100%;
  left: 0;
  bottom: 0;
  z-index: 100;
  padding-bottom: 64rpx;
  background: #fff;
  height: 150rpx;
  display: flex;
  .top-page-tip {
    position: absolute;
    top: -52rpx;
    left: 50%;
    transform: translateX(-50%);
  }
  .bar-item {
    flex: 1 1 auto;
    display: flex;
    justify-content: center;
    flex-direction: column;
    color: #111;
    text-align: center;
    padding-top: 12rpx;
    margin: 0 80rpx;
    img {
      width: 56rpx;
      height: 56rpx;
    }
    &.active {
      color: var(--primary);
    }
  }
}


.topPageTipText {
  display: flex;
  align-items: center;
  color: #7c889c;
  img {
    width: 24rpx;
    height: 24rpx;
    margin-right: 8rpx;
  }
}
