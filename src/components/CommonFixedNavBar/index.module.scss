.CommonFixedNavBar {
  position: fixed;
  top: 0;
  width: 100%;
  left: 0;
  z-index: 100;
}

.safeAreaInsetTop {
  height: var(--safe-area-inset-top);
}

.navbarHeight {
  position: relative;
}

.realNavBarWrap {
  display: flex;
  align-items: center;
  height: var(--navbar-height);
  min-height: 88rpx;
  position: relative;
  z-index: 99999;
  .icon {
    width: 56rpx;
    height: 56rpx;
  }
  .backIcon {
    margin-left: 20rpx;
    margin-right: 15rpx;
  }
  .documentTitle {
    font-weight: 500;
    font-size: 36rpx;
    color: #111;
  }
}

.navbarHeightBackground {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
