import { useState, useCallback, useEffect } from 'react';
import { isThemis } from '@/utils';
import styles from './index.module.scss';


interface CommonFixedNavBarProps {
  bgColor: string;
}

const CommonFixedNavBar = (props: CommonFixedNavBarProps) => {
  const [transparency, setTransparency] = useState('0');
  const doColor = useCallback((scrollTop) => {
    let molecule = scrollTop;
    if (scrollTop === 0) {
      molecule = 0;
    }
    if (scrollTop > 40) {
      molecule = 40;
    }

    setTransparency(Number(molecule / 40).toFixed(2));
  }, []);

  const onScroll = useCallback(() => {
    doColor(window.scrollY || window.pageYOffset);
  }, []);

  useEffect(() => {
    window.addEventListener('scroll', onScroll);
    return () => {
      window.removeEventListener('scroll', onScroll);
    };
  }, []);
  const opacityBgStyle = {
    opacity: transparency,
    backgroundColor: props.bgColor,
  };

  if (!isThemis()) return null;

  return (
    <div className={styles.CommonFixedNavBar}>
      <div className={styles.safeAreaInsetTop} style={opacityBgStyle} />
      <div className={styles.navbarHeight}>
        <div className={styles.navbarHeightBackground} style={opacityBgStyle} />
        <div className={styles.realNavBarWrap} />
      </div>
    </div>
  );
};

export default CommonFixedNavBar;
