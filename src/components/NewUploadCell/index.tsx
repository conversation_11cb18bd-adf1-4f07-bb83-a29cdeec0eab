import { noop, idempotentId, _, getAppVersion, isVersionGreater, getTimeStamp } from '@/utils/';
import { fileFactoryCreateUrl } from '@/store/common/actions';
import { takePhotonWinVane, removeHttpSchema } from '@/utils/file';
import { Modal } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';
import { Photo } from '@ali/uni-api';
import { PermissionKeyType } from '@ali/uniapi-permission';
import { isHarmonyOS } from '@/utils/env';
import { HM_REJECT_UPLOAD_TB_VERSION } from '@/common/constant';
import { checkHmRejectAlert } from '../HmReject';

enum FileErrorCode {
  EMPTY = 'EMPTY',
  OVER_SIZE = 'OVER_SIZE',
  UPLOAD_ERROR = 'UPLOAD_ERROR',
  UPLOAD_EMPTY = 'UPLOAD_EMPTY',
  TYPE_ERROR = 'TYPE_ERROR',
  ABILITY_ERROR = 'ABILITY_ERROR',
}

interface UploadCellProps {
  children: any;
  onUploadError?: Function;
  onProcess?: Function;
  onUploadSuccess?: Function;
  disabled?: boolean;
  className?: string;
  // imageMaxSize?: number;
  mode?: 'both' | 'camera' | 'photo';
  // compress?: boolean;
  imageMaxSizeAfterCompress?: number;
  permissionType?: PermissionKeyType;
  abilityConfig?: any;
  privacyID?: string;
  // 是否需要兼容鸿蒙
  needHarmonyOSCompatible?: boolean;
}

const base64toFile = (base: string, filename: string): File => {
  const arr = base.split(',');
  /* eslint-disable-next-line */
  const mime = arr?.[0]?.match?.(/:(.*?);/)?.[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], filename, { type: mime });
};

export default function UploadCell(props: UploadCellProps) {
  const {
    // imageMaxSize = 1024 * 1024 * 8, // 默认8M
    imageMaxSizeAfterCompress = 1024 * 1024 * 2, // 默认2M
    children = null,
    onUploadError = noop,
    onProcess = noop,
    onUploadSuccess = noop,
    disabled = false,
    className = '',
    mode = 'both',
    // compress = true,
    permissionType = PermissionKeyType.READ_IMAGES,
    abilityConfig = {},
    privacyID,
    needHarmonyOSCompatible = false,
  } = props;

  const handleBeforeUpload = async (file: any): Promise<undefined | File> => {
    if (!file) {
      onUploadError({
        errorCode: FileErrorCode.EMPTY,
      });
      return;
    }

    let _file: any = file;
    if (needHarmonyOSCompatible && isHarmonyOS()) {
      const fileName = `${getTimeStamp()}_temp_camera.jpg`;
      const fileBase64Data = `data:image/jpg;base64,${_file?.thumbBase64}`;
      try {
        _file = base64toFile(fileBase64Data, fileName);
      } catch (error) {
        log.addErrorLog('harmonyOS-base64-error', { code: error.message });
      }
    }

    // if (file.size > imageMaxSize) {
    //   onUploadError({
    //     errorCode: FileErrorCode.OVER_SIZE,
    //   });
    //   return;
    // }

    if (_file.type === 'image/webp') {
      onUploadError({
        errorCode: FileErrorCode.TYPE_ERROR,
      });
      return;
    }

    return _file;
    // if (!compress || !/image/i.test(file.type)) return file;

    // try {
    //   return await compressor(file);
    // } catch (e) {
    //   return file;
    // }
  };

  const handleUpload = async (targetFile) => {
    if (!targetFile) {
      return;
    }
    if (targetFile.size > imageMaxSizeAfterCompress) {
      onUploadError({
        errorCode: FileErrorCode.OVER_SIZE,
      });
      return;
    }

    if (targetFile) {
      const contentType = targetFile.type;
      const suffix = _.get(targetFile.type.split('/'), '[1]', 'unknown');
      try {
        const createUrlRes = await fileFactoryCreateUrl({
          extension: suffix,
          contentType,
          isTemporary: true,
          sourceScene: 'FRONT_END',
          uniqueNo: idempotentId(),
        });
        const body = new Blob([targetFile], { type: contentType });
        const putRes = await fetch(removeHttpSchema(createUrlRes.url), {
          method: 'PUT',
          headers: new Headers({ 'Content-Type': contentType }),
          body,
        });

        if (putRes.status === 200) {
          onUploadSuccess(
            {
              name: targetFile.name,
              suffix,
              fileFactoryNo: createUrlRes.fileFactoryNo,
            },
            targetFile,
          );
        }
      } catch (error) {
        onUploadError({
          errorCode: FileErrorCode.UPLOAD_ERROR,
          ...error,
        });
      }
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    onProcess();
    const targetFile = await handleBeforeUpload(e?.target?.files?.[0]);

    handleUpload(targetFile);
  };

  // 调用Themis能力
  const handleCallAbility = () => {
    const photoMethod = _.get(
      {
        TAKE_CAMERA: 'takeFromCamera',
        READ_IMAGES: 'takeFromPhotoLibrary',
      },
      permissionType,
    );
    if (!photoMethod) {
      throw new Error('unsupport permissionType');
    }

    Photo[photoMethod]({
      bizName: privacyID,
      needThumbBase64: 'true',
      type: 'photo',
      ...abilityConfig,
      onSuccess: async (result) => {
        onProcess();
        const targetFile = await handleBeforeUpload(_.first(result.mediaFiles));
        handleUpload(targetFile);
      },
      onError: (result) => {
        onUploadError({
          errorCode: FileErrorCode.ABILITY_ERROR,
          ...result,
        });
      },
    }).catch(() => {});
  };

  const handleUploadCellWrapClick = async () => {
    log.addClickLog('upload-cell-click');
    if (disabled) return;

    // 鸿蒙上传
    if (needHarmonyOSCompatible && isHarmonyOS()) {
      const appVersion = await getAppVersion(3);
      // 判断是否大于等于版本号 否则拦截
      const checkVersionFlag = isVersionGreater(appVersion, HM_REJECT_UPLOAD_TB_VERSION) || appVersion === HM_REJECT_UPLOAD_TB_VERSION;
      if (!checkVersionFlag && checkHmRejectAlert('upload-cell')) {
        return;
      }

      handleCallAbility();
      return;
    }

    takePhotonWinVane(
      (res) => {
        handleFileChange(res);
      },
      (e) => {
        const { msg, status } = e || {};
        if (msg === 'CANCELED_BY_USER') {
          return;
        }
        if (status === 'RET_PHOTO_CANCLE') {
          return;
        }
        if (msg === 'NO_PERMISSION') {
          Modal.alert({
            title: '系统权限不足',
            content: (
              <div>
                <div>请前往系统设置进行修改</div>
                <div>1、打开手机设置</div>
                <div>2、在应用列表内找到应用</div>
                <div>3、点击进入，查看或修改权限设置</div>
              </div>
            ),
          });
        }
      },
      mode,
    );
  };

  return (
    <div className={className} onClick={handleUploadCellWrapClick}>
      {children}
    </div>
  );
}
