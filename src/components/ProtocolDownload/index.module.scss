.protocolDownloadPopup {
}
.popupBody {
  // min-height: 40vh;
  border-radius: 32rpx 32rpx 0 0;
  :global {
    .adm-popup-close-icon {
      font-size: 44rpx;
    }
    .adm-input-element {
      font-size: 32rpx;
      color: #333; // 用户输入文本颜色
      &::placeholder {
        color: #ccc; // placeholder颜色
      }
    }

  }
  .popupContent {
    height: 498rpx;
    padding: 24rpx 40rpx 35rpx 40rpx;
    .title {
      width: 100%;
      font-family: PingFangSC;
      font-weight: 500;
      font-size: 36rpx;
      color: #333;
      text-align: center;
    }
    .subTitle {
      width: 100%;
      font-family: PingFangSC;
      font-weight: 400;
      font-size: 24rpx;
      color: #999;
      text-align: center;
      margin-top: 16rpx;
      margin-bottom: 48rpx;
    }
    .input {
      background-color: #fff;
      border: 2rpx solid #e5e5e5;
      border-radius: 16rpx;
      height: 110rpx;
    }
    .mainBtn {
      width: 100%;
      margin-top: 51rpx;
      height: 98rpx;
      font-size: 36rpx;
    }

  }
}
.dialogConfirm {
  border-radius: 24rpx;
  :global {
    .adm-dialog-content {
      margin-bottom: 10rpx;
    }
    .adm-dialog-title {
      color: #333;
      font-size: 36rpx;
      font-weight: 500;
    }
    .adm-auto-center-content {
      font-weight: 400;
      font-size: 30rpx;
      color: #333;
    }
  }
}

.dialogCofirmB {
  :global(.adm-dialog-footer .adm-dialog-action-row > .adm-dialog-button) {
    padding: 25rpx 20rpx;
  }
  :global(.adm-dialog-body > .adm-dialog-content) {
    padding-bottom: 40rpx;
  }
}
