import React, { useState, forwardRef, useImperativeHandle, useEffect } from 'react';
import { Button, Input, Popup, Toast, Dialog } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';

import styles from './index.module.scss';

interface ProtocolDownloadPopupProps {
  handleDownload: (email: string) => void;
  downloadIng: boolean;
}

const ProtocolDownloadPopup = forwardRef((props: ProtocolDownloadPopupProps, ref) => {
  const { handleDownload, downloadIng } = props;

  const [email, setEmail] = useState('');
  const [visible, setVisible] = useState(false);

  const EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

  useEffect(() => {
    if (visible) {
      log.addVisitLog('ssd-signed-agreement-detail-download-popup');
    }
  }, [visible]);

  const handleSend = () => {
    log.addClickLog('ssd-signed-agreement-detail-download-popup-send');
    if (!EMAIL_REGEX.test(email)) {
      Toast.show('请输入有效的邮箱地址');
    } else {
      Dialog.confirm({
        className: styles.dialogCofirmB,
        bodyClassName: styles.dialogConfirm,
        title: '请确认邮箱地址',
        content: `将发送到${email}，请确认该邮箱地址是否正确`,
        cancelText: '返回修改',
        onConfirm: () => {
          handleDownload(email);
        },
      });
    }
  };

  const popupContent = (
    <div className={styles.popupContent}>
      <div className={styles.title}>随身贷协议下载</div>
      <div className={styles.subTitle}>协议将以邮件附件的形式发送到指定邮箱</div>
      <Input
        placeholder="请输入接收邮箱"
        className={styles.input}
        value={email}
        onChange={(val) => setEmail(val)}
      />
      <Button
        color="primary"
        shape="rounded"
        className={styles.mainBtn}
        onClick={handleSend}
        loading={downloadIng}
      >
        发送
      </Button>
    </div>
  );

  const toggle = (changeVisible: boolean) => {
    setVisible(changeVisible);
  };

  const handleClose = () => {
    log.addClickLog('ssd-signed-agreement-detail-download-popup-close');
    setVisible(false);
  };

  useImperativeHandle(ref, () => ({
    toggle,
  }));

  return (
    <div className={styles.protocolDownloadPopup}>
      <Popup
        visible={visible}
        showCloseButton
        bodyClassName={styles.popupBody}
        onMaskClick={handleClose}
        onClose={handleClose}
      >
        {popupContent}
      </Popup>
    </div>
  );
});

export default ProtocolDownloadPopup;
