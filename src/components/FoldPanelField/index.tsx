/**
 * @File 折叠面板表单项
 * <AUTHOR>
 */

import React, { useCallback, useMemo, useState } from 'react';
import classNames from 'classnames';
import { log } from '@alife/dtao-iec-spm-log';

import ArrowIcon from '../ArrowIcon';

import FieldPanel from '../FieldPanel';

import styles from './index.module.scss';

interface FormFieldProps<V> {
  onChange?: (value: V) => void;
  value?: V;
}

interface FoldPanelProps<V> extends FormFieldProps<V> {
  placeholder?: string;
  label: string;
  renderPanel?: (value?: V) => React.JSX.Element;
  renderFold: (props?: any) => React.JSX.Element;
  logKey?: string;
}

export default function FoldPanelField<V>(props: FoldPanelProps<V>) {
  const [visible, setVisible] = useState(false);
  const { label, value, placeholder, logKey = 'fold-panel', onChange, renderFold, renderPanel } = props;

  const onToggle = useCallback(() => {
    setVisible((prev) => {
      return !prev;
    });
  }, []);

  const handleFoldClick = useCallback(() => {
    log.addClickLog(visible ? `${logKey}-close` : `${logKey}-open`);
    onToggle();
  }, [visible, logKey, onToggle]);

  const renderArrow = useMemo(() => {
    if (!visible) {
      return <ArrowIcon className={styles.icon} />;
    } else {
      return <ArrowIcon className={styles.icon} type="up" />;
    }
  }, [visible]);

  const renderFoldAction = useCallback(() => {
    if (renderFold) {
      return (
        <div className={classNames(styles.fold, visible && styles.visible)}>
          {renderFold({ value, onChange })}
        </div>
      );
    }
    return null;
  }, [visible, value, onChange, renderFold]);

  return (
    <div className={styles.foldPanelField}>
      <div className={styles.foldPanel} onClick={handleFoldClick}>
        <div className={styles.label}>
          <span>{label}</span>
        </div>
        <div className={styles.panel}>
          <FieldPanel<V>
            className={styles.fieldPanel}
            placeholder={placeholder}
            value={value}
            renderPanel={renderPanel}
          />
          {renderArrow}
        </div>
      </div>
      {renderFoldAction()}
    </div>
  );
}
