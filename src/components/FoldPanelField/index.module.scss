.foldPanelField {
  --field-panel-text-align: right;
  .foldPanel {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 12rpx;
    .panel {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      flex: 1 1;
      .fieldPanel {
        flex: 1 1;
      }
      .icon {
        margin-left: 8rpx;
      }
    }
  }
  .fold {
    width: 0;
    opacity: 0;
    height: 0;
  }
  .visible {
    opacity: 1;
    height: auto;
    width: auto;
  }
}
