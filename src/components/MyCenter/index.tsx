import { useState, useEffect, Fragment, useCallback } from 'react';
import styles from './index.module.scss';
import { _ } from '@/utils';
import ArrowIcon from '@/components/ArrowIcon';
import classnames from 'classnames';
import { queryCustomerProfile } from '@/store/center/actions';
import { PAGES, yunCustomerServiceLink } from '@/common/constant';
import LinkUtil from '@/utils/link';

import { log } from '@alife/dtao-iec-spm-log';
import { Popup, Button } from 'antd-mobile';
import { useVisibilityChangeRefresh } from '@/hooks';

const CenterSignedAgreementsItem = {
  key: 'SignedAgreements',
  title: '相关协议',
  pageName: PAGES.CenterSignedAgreements,
  icon: 'https://gw.alicdn.com/imgextra/i1/O1CN01zuOAZl1hCt6v0sFFg_!!6000000004242-2-tps-64-64.png',
};

const CenterDiscountCouponItem = {
  key: 'DiscountCoupon',
  title: '优惠券',
  pageName: PAGES.CenterDiscountCoupon,
  icon: 'https://gw.alicdn.com/imgextra/i4/O1CN01HG86PS1uqodzLK8Zo_!!*************-2-tps-64-64.png',
};

const CenterCustomerService = {
  key: 'CustomerService',
  title: '客服',
  url: yunCustomerServiceLink,
  newWindow: true,
  icon: 'https://gw.alicdn.com/imgextra/i3/O1CN01cfLv9b1rmHsbVdFFG_!!*************-2-tps-64-64.png',
};

const CenterPhoneManage = {
  key: 'PhoneManage',
  title: '手机号管理',
  pageName: PAGES.CenterPhoneManage,
  icon: 'https://gw.alicdn.com/imgextra/i2/O1CN01BxJOr31nrXxveOuNW_!!*************-2-tps-64-64.png',
};

const CenterBankCard = {
  key: 'BankCard',
  title: '银行卡管理',
  pageName: PAGES.CenterBankCard,
  icon: 'https://gw.alicdn.com/imgextra/i1/O1CN01vgkZDP1WiWi24sx7o_!!*************-2-tps-64-64.png',
};

const CenterCertManage = {
  key: 'CertManage',
  title: '证件管理',
  pageName: PAGES.CenterCertManage,
  icon: 'https://gw.alicdn.com/imgextra/i3/O1CN016SuUhn1TS5dU7Wb73_!!*************-2-tps-64-64.png',
};

const HomeMyCenterServiceConfig = [
  {
    key: 'QuotaRate',
    title: '额度与利率',
    pageName: PAGES.CenterQuotaRate,
    icon: 'https://gw.alicdn.com/imgextra/i2/O1CN01xjBF4Z1SRnlrraHN8_!!*************-2-tps-64-64.png',
  },
  CenterPhoneManage,
  CenterCertManage,
  CenterBankCard,
  CenterDiscountCouponItem,
  CenterSignedAgreementsItem,
  {
    key: 'ServiceManage',
    title: '服务管理',
    needQUALIFIED: true,
    icon: 'https://gw.alicdn.com/imgextra/i1/O1CN019uAYuo26jjvL1CClC_!!*************-2-tps-64-64.png',
  },
  CenterCustomerService,
];

const LpMyCenterServiceConfig = [
  CenterSignedAgreementsItem,
  CenterCustomerService,
];

export default function MyCenter(props) {
  const [customerPhone, setCustomerPhone] = useState({});
  const [identityCard, setIdentityCard] = useState<any>({});
  const [closePopupVisible, setClosePopupVisible] = useState(false);
  const { isLp, customStyle } = props;

  const handleServiceManageClick = () => {
    setClosePopupVisible(true);
  };

  const handleClose = () => {
    setClosePopupVisible(false);
  };

  const handleCloseServiceBtnClick = async () => {
    LinkUtil.pushPage(PAGES.CenterClose);
  };

  const serviceItemRender = useCallback((item: any) => {
    if (!item) return;
    let customRight;
    if (item.key === 'PhoneManage') {
      customRight = _.get(customerPhone, 'phoneNo');
    }
    if (item.key === 'CertManage') {
      customRight = _.get(identityCard, 'status') === 'PENDING_ID_PHOTO' ? '请上传身份证' : null;
    }
    return (
      <div
        key={item.key}
        onClick={() => {
          log.addClickLog('my-center-item-click', { key: item.key });
          if (item.key === 'ServiceManage') {
            handleServiceManageClick();
            return;
          }
          if (item.onClick) {
            item.onClick();
            return;
          }
          if (item.newWindow) {
            LinkUtil.navigatorOpenURL(item.url);
          } else {
            LinkUtil.pushPage(item.pageName);
          }
        }}
        className={styles.serviceItem}
      >
        <img className={styles.leftIcon} src={item.icon} />
        <span className={styles.itemName}>{item.title}</span>
        <span className={styles.right}>
          {customRight}
          <ArrowIcon />
        </span>
      </div>
    );
  }, [customerPhone, identityCard]);

  const LpMyCenterRender = () => {
    const centerTitle = identityCard?.name ? `${identityCard.name}，你好` : '您当前未完成额度申请';
    return (
      <Fragment>
        <div className={styles.personName}>{centerTitle}</div>
        <div className={classnames([styles.serviceWrap])}>
          {[
            // !_.isEmpty(customerPhone) && CenterPhoneManage,
            // !_.isEmpty(identityCard) && CenterCertManage,
            !isLp && CenterPhoneManage,
            !isLp && CenterCertManage,
            ...LpMyCenterServiceConfig,
          ].map(serviceItemRender)}
        </div>
      </Fragment>
    );
  };

  const homeMyCenterRender = () => {
    return (
      <Fragment>
        <div className={styles.personName}>{identityCard.name && `${identityCard.name}，你好`}</div>
        <div className={classnames([styles.recordWrap])}>
          <div
            onClick={() => {
              LinkUtil.pushPage(PAGES.RecordList, { tabKey: 'LOAN' });
            }}
            className={styles.recordItem}
          >
            <img src="https://gw.alicdn.com/imgextra/i2/O1CN01sb9O5F1Kmr3S5aLHg_!!6000000001207-2-tps-112-112.png" />
            借款记录
          </div>
          <div
            onClick={() => {
              LinkUtil.pushPage(PAGES.RecordList, { tabKey: 'REPAY' });
            }}
            className={styles.recordItem}
          >
            <img src="https://gw.alicdn.com/imgextra/i1/O1CN01DQmVIC1Eg5VYfTtL3_!!6000000000380-2-tps-112-112.png" />
            还款记录
          </div>
        </div>
        <div className={classnames([styles.serviceWrap])}>
          {HomeMyCenterServiceConfig.map(serviceItemRender)}
        </div>
        <Popup
          className={styles.closePopup}
          onClose={handleClose}
          onMaskClick={handleClose}
          visible={closePopupVisible}
        >
          <div className={styles.closePopupBody}>
            <div onClick={handleCloseServiceBtnClick} className={styles.blockItem}>
              关闭服务
            </div>
            <div className={styles.middleEmpty} />
            <Button onClick={handleClose} block className="cancel-button">
              取消
            </Button>
          </div>
        </Popup>
      </Fragment>
    );
  };

  const pageInit = () => {
    queryCustomerProfile({
      typeList: JSON.stringify(['PHONE', 'IDENTITY_CARD']),
    }).then((res) => {
      const phoneData = _.find(res.result, (item) => item.type === 'PHONE');
      const identityData = _.find(res.result, (item) => item.type === 'IDENTITY_CARD');

      setCustomerPhone(phoneData?.content || {});
      setIdentityCard(identityData?.content || {});
    }).catch((err) => {
      log.addErrorLog('my-center-query-customer-profile', { err });
    });
  };

  useEffect(() => {
    pageInit();
  }, []);

  useVisibilityChangeRefresh(pageInit);

  return (
    <div style={customStyle} className={classnames([styles.myCenterWrap])}>
      {isLp ? LpMyCenterRender() : homeMyCenterRender()}
    </div>
  );
}
