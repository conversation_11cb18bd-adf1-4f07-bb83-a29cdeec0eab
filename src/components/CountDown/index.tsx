/**
 * @file 倒计时
 */

import { forwardRef, useImperativeHandle, useEffect, useRef, useState, useCallback } from 'react';
import { env } from '@ali/iec-dtao-utils';
import classNames from 'classnames';

import styles from './index.module.scss';

interface CountDownProps {
  onEnd?: () => void;
}

export interface CountDownRef {
  start: (count: number) => void;
}

export const CountDown = forwardRef((props: CountDownProps, ref) => {
  const { onEnd } = props;
  const [count, setCount] = useState(0);
  const timer = useRef<any>();

  const set = (value = 0) => {
    const range = document.querySelector('#countDownRange');
    const circle = document.querySelectorAll('circle')[1];
    if (range && circle) {
      const percent = value / 100;
      const perimeter = Math.PI * 2 * 170;
      circle.setAttribute('stroke-dasharray', `${perimeter * percent } ${ perimeter * (1 - percent)}`);
    }
  };

  const handleStart = useCallback((value: number) => {
    setTimeout(() => {
      set(100);
    }, 0);
    setCount(value);
  }, []);

  useEffect(() => {
    if (count > 0) {
      timer.current = setTimeout(() => {
        setCount((current) => {
          return current - 1;
        });
      }, 1000);
    } else if (timer.current) {
      onEnd && onEnd();
      clearTimeout(timer.current);
    }
  }, [count]);

  useImperativeHandle(ref, () => ({
    start: handleStart,
  }));

  return (
    <div className={styles.countDown}>
      <p className={classNames(styles.num, env.isAndroid() && styles.numA)}>{count}</p>
      <svg className={styles.range} id="countDownRange" width="96" height="96" viewBox="0 0 144 144">
        <circle
          className={styles.circle}
          cx="48"
          cy="48"
          r="36"
          strokeWidth="9"
          stroke="#1677ff"
          fill="none"
        />
        <circle
          className={styles.circle}
          cx="48"
          cy="48"
          r="36"
          strokeWidth="10"
          stroke="#f0f0f0"
          fill="none"
          transform="matrix(0,-1,1,0,0,96)"
          strokeDasharray="0 1069"
        />
      </svg>
    </div>
  );
});
