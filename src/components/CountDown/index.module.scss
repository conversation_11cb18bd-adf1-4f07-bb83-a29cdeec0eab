.countDown {
  position: relative;
  margin: auto;
  width: 96rpx;
  height: 96rpx;
  .circle {
    -webkit-transition: stroke-dasharray 18s ease;
    transition: stroke-dasharray 18s ease;
  }
  .num {
    position: absolute;
    font-size: 48rpx;
    color: rgba(#000, 80%);
    text-align: center;
    left: 0;
    right: 0;
    top: 17rpx;
    margin: auto;
  }
  .numA {
    top: 22rpx;
    left: 2rpx;
  }
  .range {
    margin-left: -12rpx;
    margin-top: -12rpx;
  }
}
