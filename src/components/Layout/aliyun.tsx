/**
 * @file 阿里云SSR layout
 * <AUTHOR>
 */

import { useCallback, useEffect } from 'react';
import { NavBar } from '@ali/uni-api';
import { includes } from 'lodash-es';

import { PAGES } from '@/common/constant';
import { getPathname } from '@/utils/ssr';
import { isDev } from '@/utils/env';
import { initAes } from '@/utils/aes';

import '@/assets/styles/aliyun.css';

const PageClassMap = {
  [PAGES.CreditLpSSR]: 'common-page-bg-1',
  [PAGES.CreditSimpleLp]: 'common-page-bg-1',
};

const NoSafeAreaPages = [
  PAGES.BindCard,
];

interface AliyunProps {
  children: any;
}

export default function Aliyun(props: AliyunProps) {
  const { children } = props;

  const handleSetPageTitleImg = useCallback(async () => {
    try {
      const pathName = getPathname();
      const list = [PAGES.Home, PAGES.CreditLpSSR, PAGES.CreditSimpleLp, PAGES.CreditLp, PAGES.CreditFallback];
      if (includes(list, pathName)) {
        await NavBar.setTitleImage({
          url: 'https://gw.alicdn.com/imgextra/i3/O1CN01o1lBeN1M5SlR1r7Fl_!!6000000001383-2-tps-138-66.png',
        });
      }
    } catch (error) {}
  }, []);

  const renderSafeArea = () => {
    if (isDev()) return null;

    const pathName = getPathname();
    if (!includes(NoSafeAreaPages, pathName)) {
      return (
        <>
          <div className="safe-area-inset-top" />
          <div className="navbar-height" />
        </>
      );
    }
    return null;
  };

  const getClassName = () => {
    const pathName = getPathname();
    const pageClassName = PageClassMap[pathName];
    const layoutClassName = 'layout';
    if (pageClassName) {
      return `${layoutClassName} ${pageClassName}`;
    }
    return layoutClassName;
  };

  useEffect(() => {
    handleSetPageTitleImg();
    // 初始化 AES 埋点
    initAes();
  }, []);


  return (
    <div className={getClassName()}>
      {renderSafeArea()}
      <div className="page-container">
        {children}
      </div>
    </div>
  );
}
