/**
 * @file 随身贷SSR layout
 * <AUTHOR>
 */

import { useCallback, useEffect } from 'react';
import { log } from '@alife/dtao-iec-spm-log';
import { NavBar } from '@ali/uni-api';
import { includes, get } from 'lodash-es';
import { env } from '@ali/iec-dtao-utils';

import { navigatorOpenURL, replacePage } from '@/utils/link';
import { PAGES, ZZFW_TB_MARKET_LINK_PRE, HomeCsLink, KFLOGO_LIGHT, KFLOGO_BLACK } from '@/common/constant';
import { getPathname, getQueryParams } from '@/utils/ssr';
import { isDev } from '@/utils/env';
import { initAes } from '@/utils/aes';

const PageCSLinkMap = {
  [PAGES.SsdCreditLpSSR]: HomeCsLink,
  [PAGES.SsdHomePlus]: HomeCsLink,
};

const PageClassMap = {
  [PAGES.SsdCreditLpSSR]: 'common-page-ssd',
  [PAGES.SsdHomePlus]: 'common-page-ssd',
};

const NoSafeAreaPages = [
  PAGES.SsdRepayEarlyApply,
];

interface SSDProps {
  children: any;
  hiddenSafe?: boolean;
  customClassName?: string;
}

export default function SSD(props: SSDProps) {
  const { children, hiddenSafe, customClassName } = props;

  const renderSafeArea = () => {
    const pathName = getPathname();
    if (hiddenSafe) {
      return null;
    }
    if (!includes(NoSafeAreaPages, pathName)) {
      return (
        <>
          <div className="safe-area-inset-top" />
          <div className="navbar-height" />
        </>
      );
    }
    return null;
  };

  const getClassName = () => {
    const pathName = getPathname();
    const pageClassName = PageClassMap[pathName];
    let layoutClassName = 'layout-ssd';
    if (pageClassName) {
      layoutClassName = `${layoutClassName} ${pageClassName}`;
    }
    if (customClassName) {
      layoutClassName = `layout-ssd ${customClassName}`;
    }
    return layoutClassName;
  };

  const handleSetPageTitleImg = useCallback(async () => {
    try {
      const pathName = getPathname();
      const kcClick = () => {
        navigatorOpenURL(PageCSLinkMap[pathName] || HomeCsLink);
      };
      await NavBar.setRightItem({
        lightImageUrl: KFLOGO_LIGHT,
        darkImageUrl: KFLOGO_BLACK,
        onClick: kcClick,
      });
    } catch (error) {}
  }, []);

  const handleTBCheck = () => {
    const isWhite = get(getQueryParams(), 'white');
    if (!isDev() && !env.isTb() && !isWhite) {
      log.addOtherLog('ssd-not-tb', { ua: navigator?.userAgent });
      replacePage(`${ZZFW_TB_MARKET_LINK_PRE}${encodeURIComponent(window.location.href)}`);
    }
  };

  useEffect(() => {
    handleTBCheck();
    // 使用 initAes 替代原来的 handleAemLoad
    initAes();
    handleSetPageTitleImg();
  }, []);

  return (
    <div className={getClassName()}>
      {renderSafeArea()}
      <div className="page-container">
        {children}
      </div>
    </div>
  );
}
