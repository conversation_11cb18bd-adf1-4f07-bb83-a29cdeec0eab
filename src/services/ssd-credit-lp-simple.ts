/**
 * @file 智信未开通首页（极简开通）
 */

import { queryCreditApplyConsult } from '@/store/credit/actions';
import { queryUnSignAgreementList, QueryUnSignAgreementListResponse } from '@/store/agreement/actions';
import { ORIGIN_TYPE } from '@/common.types';
import type { CREDIT_APPLY_CONSULT_DTO } from '@/store/types';
import { switchDataOrigin } from './config/lib';
import { hsfInvoke } from './utils/hsf';
import { getMayizhixinParams, getExtension } from './utils/params';

export interface SsdCreditLpSimpleFSPData {
  creditApplyConsult?: CREDIT_APPLY_CONSULT_DTO;
  unsignedAgreementQuery?: QueryUnSignAgreementListResponse;
}


function getCreditType(consultData?: CREDIT_APPLY_CONSULT_DTO) {
  return consultData?.creditType;
}

function isInstitutionCrediting(consultData?: CREDIT_APPLY_CONSULT_DTO) {
  if (
    consultData?.latestCreditApplyOrder?.status === 'PROCESSING' &&
    (
      consultData?.latestCreditApplyOrder?.subStatus === 'INSTITUTION_CREDITING' ||
      consultData?.latestCreditApplyOrder?.subStatus === 'AUTHENTICATED'
    )
  ) {
    return true;
  }
  return false;
}

export async function ssdCreditLpSimpleSSR() {
  try {
    switchDataOrigin('SSR');
    const { product, tenant, customerId, alipayUserId, origin } = getMayizhixinParams();
    const consultArg = {
      product,
      customerId,
      tenant,
      alipayUserId,
      requestPurpose: 'CREDIT_CONSULT',
      extension: getExtension(origin),
    };
    const consultData: any = await hsfInvoke({
      id: 'com.alibaba.fin.tao.blp.credit.mtop.api.CreditApplyCommandMtopFacade',
      method: 'consult',
      parameterTypes: ['com.alibaba.fin.tao.blp.credit.mtop.api.request.CreditApplyConsultMtopRequest'],
      args: [consultArg],
    });
    if (!consultData?.success) {
      throw new Error();
    }
    // 机构授信中或者不准入，则不用查询了
    // 这个判断可以用一个公共的方法
    if (
      consultData?.model?.admitted === false ||
      consultData?.model?.canCreditApply === false ||
      isInstitutionCrediting(consultData?.model)
    ) {
      return {
        success: true,
        originType: 'SSR',
        data: {
          creditApplyConsult: consultData?.model,
        },
      };
    }
    const queryUnsignedAgreementListArg = {
      product,
      customerId,
      bizType: 'PLATFORM_AND_CREDIT',
      creditPlatform: 'MAYI_ZHIXIN',
      institutionList: ['MAYI_ZHIXIN'],
      alipayUserId,
      creditType: getCreditType(consultData?.model),
      extension: '{"requestOrigin":"SSR"}',
    };
    const agreementData: any = await hsfInvoke({
      id: 'com.alibaba.fin.tao.blp.customer.mtop.api.AgreementQueryMtopFacade',
      method: 'queryUnSignAgreementList',
      parameterTypes: ['com.alibaba.fin.tao.blp.customer.mtop.api.request.UnSignedAgreementListQueryMtopRequest'],
      args: [queryUnsignedAgreementListArg],
    });
    if (!agreementData?.success) {
      throw new Error();
    }
    return {
      success: true,
      originType: 'SSR',
      data: {
        creditApplyConsult: consultData?.model,
        unsignedAgreementQuery: agreementData?.model,
      },
    };
  } catch (e) {
    return {
      success: false,
      originType: 'SSR',
    };
  }
}
export async function ssdCreditLpSimpleCSR(originType: ORIGIN_TYPE) {
  try {
    switchDataOrigin(originType);
    const consultData = await queryCreditApplyConsult({
      requestPurpose: 'CREDIT_CONSULT',
    });
    // 不准入就不用查询了，其他请求都要查询平台协议，因为需要extension字段
    // 可以用一个公共的方法
    if (
      consultData?.admitted === false ||
      consultData?.canCreditApply === false ||
      isInstitutionCrediting(consultData)
    ) {
      return {
        success: true,
        originType,
        data: {
          creditApplyConsult: consultData,
        },
      };
    }
    const agreementData = await queryUnSignAgreementList({
      bizType: 'PLATFORM_AND_CREDIT',
      creditPlatform: 'MAYI_ZHIXIN',
      institutionList: ['MAYI_ZHIXIN'],
      creditType: getCreditType(consultData),
    });
    return {
      success: true,
      originType,
      data: {
        creditApplyConsult: consultData,
        unsignedAgreementQuery: agreementData,
      },
    };
  } catch (e) {
    return {
      success: false,
      originType,
    };
  }
}
