/**
 * @file 基础参数方法
 */

import { getContext } from '@ali/wormhole-context';
import { getQueryParams } from '@/utils/ssr';

function getOrigin() {
  try {
    const queryParams: any = getQueryParams();
    if (queryParams?.tracelog) {
      return queryParams?.tracelog;
    }
    return '';
  } catch (e) {
    return '';
  }
}

export function getExtension(origin?: string) {
  try {
    const base = {
      requestOrigin: 'SSR',
      origin,
    };
    return JSON.stringify(base);
  } catch (e) {
    return '{"requestOrigin":"SSR"}';
  }
}

export function getAliyunParams() {
  const { mtopEvent } = getContext() || {};
  const origin = getOrigin();
  return {
    product: 'XFD',
    tenant: 'DTAO',
    customerId: mtopEvent?.sessionInfo?.userId,
    origin,
  };
}

export function getMayizhixinParams() {
  const { mtopEvent } = getContext() || {};
  const origin = getOrigin();
  return {
    product: 'XFD',
    tenant: 'DTAO',
    customerId: mtopEvent?.sessionInfo?.userId,
    alipayUserId: mtopEvent?.sessionInfo?.attributes?.alipayId,
    origin,
  };
}
