/**
 * @file SSR用的hsf请求方法，主要是为了区分环境获取配置
 * <AUTHOR>
 */
import { getContext } from '@ali/wormhole-context';
import { getConfig } from '../config/lib';

interface HsfInvokeOptions {
  id: string;
  group?: 'HSF';
  method: string;
  parameterTypes: string[];
  args: any[];
}

export async function hsfInvoke(options: HsfInvokeOptions) {
  const { hsfClient, system, eagleeye } = getContext() || {};
  const { id, group = 'HSF', method, parameterTypes, args } = options;
  const config = getConfig(system?.env);
  const versionSuffix = config?.versionSuffix || '1.0.0';
  // 日常环境直接降级，如果是其他环境要降级也可以用改这个
  if (config?.renderType === 'CSR') {
    throw new Error();
  }
  if (config?.dpath_env) {
    let data;
    await eagleeye?.putUserData('dpath_env', config?.dpath_env, async () => {
      data = await hsfClient?.invoke({
        id: `${id}:${versionSuffix}`,
        group,
        method,
        parameterTypes,
        args,
      });
    });
    return data;
  }
  const hsfData: any = await hsfClient?.invoke({
    id: `${id}:${versionSuffix}`,
    group,
    method,
    parameterTypes,
    args,
  });
  return hsfData;
}
