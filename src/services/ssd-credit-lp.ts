/**
 * @file 智信未开通首页
 */

import { queryCreditApplyConsult } from '@/store/credit/actions';
import { queryUnSignAgreementList, QueryUnSignAgreementListResponse } from '@/store/agreement/actions';
import { ORIGIN_TYPE } from '@/common.types';
import type { CREDIT_APPLY_CONSULT_DTO } from '@/store/types';
import { switchDataOrigin } from './config/lib';
import { hsfInvoke } from './utils/hsf';
import { getMayizhixinParams, getExtension } from './utils/params';

export interface SsdCreditLpFSPData {
  creditApplyConsult?: CREDIT_APPLY_CONSULT_DTO;
  unsignedAgreementQuery?: QueryUnSignAgreementListResponse;
}

function getCreditType(consultData?: CREDIT_APPLY_CONSULT_DTO) {
  return consultData?.creditType;
}

export async function ssdCreditLpSSR() {
  try {
    switchDataOrigin('SSR');
    const { product, tenant, customerId, alipayUserId, origin } = getMayizhixinParams();
    const consultArg = {
      product,
      customerId,
      tenant,
      alipayUserId,
      extension: getExtension(origin),
    };
    const queryUnsignedAgreementListArg = {
      product,
      customerId,
      bizType: 'PLATFORM',
      creditPlatform: 'MAYI_ZHIXIN',
      institutionList: ['MAYI_ZHIXIN'],
      alipayUserId,
      extension: '{"requestOrigin":"SSR"}',
    };
    const consultData: any = await hsfInvoke({
      id: 'com.alibaba.fin.tao.blp.credit.mtop.api.CreditApplyCommandMtopFacade',
      method: 'consult',
      parameterTypes: ['com.alibaba.fin.tao.blp.credit.mtop.api.request.CreditApplyConsultMtopRequest'],
      args: [consultArg],
    });
    // 已签署平台协议或者不准入，则不用查询了
    // 这个判断可以用一个公共的方法
    if (consultData?.model?.admitted === false) {
      return {
        success: true,
        originType: 'SSR',
        data: {
          creditApplyConsult: consultData?.model,
        },
      };
    }
    const creditType = getCreditType(consultData?.model);
    if (creditType) {
      // @ts-ignore
      queryUnsignedAgreementListArg.creditType = creditType;
    }
    const agreementData: any = await hsfInvoke({
      id: 'com.alibaba.fin.tao.blp.customer.mtop.api.AgreementQueryMtopFacade',
      method: 'queryUnSignAgreementList',
      parameterTypes: ['com.alibaba.fin.tao.blp.customer.mtop.api.request.UnSignedAgreementListQueryMtopRequest'],
      args: [queryUnsignedAgreementListArg],
    });
    return {
      success: true,
      originType: 'SSR',
      data: {
        creditApplyConsult: consultData?.model,
        unsignedAgreementQuery: agreementData?.model,
      },
    };
  } catch (e) {
    return {
      success: false,
      originType: 'SSR',
    };
  }
}
export async function ssdCreditLpCSR(originType: ORIGIN_TYPE) {
  try {
    switchDataOrigin(originType);
    const consultData = await queryCreditApplyConsult();
    // 不准入就不用查询了，其他请求都要查询平台协议，因为需要extension字段
    // 可以用一个公共的方法
    if (consultData?.admitted === false) {
      return {
        success: true,
        originType,
        data: {
          creditApplyConsult: consultData,
        },
      };
    }
    const agreementData = await queryUnSignAgreementList({
      bizType: 'PLATFORM',
      creditPlatform: 'MAYI_ZHIXIN',
      institutionList: ['MAYI_ZHIXIN'],
      creditType: getCreditType(consultData),
    });
    return {
      success: true,
      originType,
      data: {
        creditApplyConsult: consultData,
        unsignedAgreementQuery: agreementData,
      },
    };
  } catch (e) {
    return {
      success: false,
      originType,
    };
  }
}
