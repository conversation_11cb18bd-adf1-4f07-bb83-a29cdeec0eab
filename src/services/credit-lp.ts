/**
 * @file 阿里云未开通首页
 */

import { queryCreditApplyConsult } from '@/store/credit/actions';
import { queryUnSignAgreementList, QueryUnSignAgreementListResponse } from '@/store/agreement/actions';
import type { ORIGIN_TYPE } from '@/common.types';
import type { CREDIT_APPLY_CONSULT_DTO } from '@/store/types';
import { switchDataOrigin } from './config/lib';
import { hsfInvoke } from './utils/hsf';
import { getAliyunParams, getExtension } from './utils/params';

export interface CreditLpFSPData {
  creditApplyConsult?: CREDIT_APPLY_CONSULT_DTO;
  unsignedAgreementQuery?: QueryUnSignAgreementListResponse;
}

export async function creditLpSSR() {
  try {
    switchDataOrigin('SSR');
    const { product, tenant, customerId, origin } = getAliyunParams();
    const consultArg = {
      product,
      customerId,
      tenant,
      requestPurpose: 'CREDIT_CONSULT',
      extension: getExtension(origin),
    };
    const queryUnsignedAgreementListArg = {
      product,
      customerId,
      bizType: 'PLATFORM',
      extension: '{"requestOrigin":"SSR"}',
    };
    const consultData: any = await hsfInvoke({
      id: 'com.alibaba.fin.tao.blp.credit.mtop.api.CreditApplyCommandMtopFacade',
      method: 'consult',
      parameterTypes: ['com.alibaba.fin.tao.blp.credit.mtop.api.request.CreditApplyConsultMtopRequest'],
      args: [consultArg],
    });
    if (!consultData?.success) {
      throw new Error();
    }
    // 已签署平台协议或者不准入，则不用查询了
    // 这个判断可以用一个公共的方法
    if (
      consultData?.model?.isPlatformAgreementSigned === true ||
      consultData?.model?.admitted === false
    ) {
      return {
        success: true,
        originType: 'SSR',
        data: {
          creditApplyConsult: consultData?.model,
        },
      };
    }
    const agreementData: any = await hsfInvoke({
      id: 'com.alibaba.fin.tao.blp.customer.mtop.api.AgreementQueryMtopFacade',
      method: 'queryUnSignAgreementList',
      parameterTypes: ['com.alibaba.fin.tao.blp.customer.mtop.api.request.UnSignedAgreementListQueryMtopRequest'],
      args: [queryUnsignedAgreementListArg],
    });
    if (!agreementData?.success) {
      throw new Error();
    }
    return {
      success: true,
      originType: 'SSR',
      data: {
        creditApplyConsult: consultData?.model,
        unsignedAgreementQuery: agreementData?.model,
      },
    };
  } catch (e) {
    return {
      success: false,
    };
  }
}

export async function creditLpCSR(originType: ORIGIN_TYPE) {
  try {
    switchDataOrigin(originType);
    const consultData = await queryCreditApplyConsult({
      requestPurpose: 'CREDIT_CONSULT',
    });
    // 已签署平台协议或者不准入，则不用查询了
    // 这个判断可以用一个公共的方法
    if (
      consultData?.isPlatformAgreementSigned === true ||
      consultData?.admitted === false
    ) {
      return {
        success: true,
        originType,
        data: {
          creditApplyConsult: consultData,
        },
      };
    }
    const agreementData = await queryUnSignAgreementList({
      bizType: 'PLATFORM',
    });
    return {
      success: true,
      originType,
      data: {
        creditApplyConsult: consultData,
        unsignedAgreementQuery: agreementData,
      },
    };
  } catch (e) {
    return {
      success: false,
      originType,
    };
  }
}
