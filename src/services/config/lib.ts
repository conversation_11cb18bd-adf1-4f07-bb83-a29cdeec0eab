/**
 * @file 获取配置方法
 */

import { get } from 'lodash-es';

import defaultConfig from './config.default';
import preConfig from './config.pre';
import prodConfig from './config.prod';
import dailyConfig from './config.daily';

import { getQueryParams } from '@/utils/ssr';

import type { ORIGIN_TYPE, SYSTEM_ENV, CONFIG_OPTIONS } from '@/common.types';

export function getConfig(env: SYSTEM_ENV): CONFIG_OPTIONS {
  switch (env) {
    case 'pre':
      return {
        ...defaultConfig,
        ...preConfig,
      };
    case 'production':
      return {
        ...defaultConfig,
        ...prodConfig,
      };
    case 'dev':
    case 'daily':
      return {
        ...defaultConfig,
        ...dailyConfig,
      };
    default:
      return defaultConfig;
  }
}

export function switchDataOrigin(originType: ORIGIN_TYPE) {
  const forceOriginType = get(getQueryParams(), 'forceOriginType', originType);
  if (originType !== forceOriginType) {
    throw new Error();
  }
}

export function reducerRank() {
  throw new Error();
}
