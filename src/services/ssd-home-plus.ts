/**
 * @file 智信未开通首页
 */

import { queryCreditApplyConsult } from '@/store/credit/actions';
import { ORIGIN_TYPE } from '@/common.types';
import type { CREDIT_APPLY_CONSULT_DTO } from '@/store/types';
import { switchDataOrigin } from './config/lib';
import { hsfInvoke } from './utils/hsf';
import { getMayizhixinParams, getExtension } from './utils/params';
import { LoanSchemaInfoRes, queryLoanSchemaInfo } from '@/store/center/actions';
import { PRODUCT } from '@/common/constant';
import { getOriginFromSessionStorage } from '@/utils/session';

export interface SsdHomePlusPData {
  creditApplyConsult?: CREDIT_APPLY_CONSULT_DTO;
  loanSchemaInfo?: LoanSchemaInfoRes;
}

export async function ssdHomePlusSSR() {
  try {
    switchDataOrigin('SSR');
    const { product, tenant, customerId, alipayUserId, origin } = getMayizhixinParams();
    const consultArg = {
      product,
      customerId,
      tenant,
      alipayUserId,
      extension: '{"requestOrigin":"SSR"}',
    };
    const consultData: any = await hsfInvoke({
      id: 'com.alibaba.fin.tao.blp.credit.mtop.api.CreditApplyCommandMtopFacade',
      method: 'consult',
      parameterTypes: ['com.alibaba.fin.tao.blp.credit.mtop.api.request.CreditApplyConsultMtopRequest'],
      args: [consultArg],
    });

    if (!consultData?.model?.creditContractStatus || consultData?.model?.creditContractStatus === 'CLOSED') {
      return {
        success: true,
        originType: 'SSR',
        data: {
          creditApplyConsult: consultData?.model,
        },
      };
    }

    const queryLoanAccountArg = {
      product,
      customerId,
      alipayUserId,
      tenant,
      extension: getExtension(origin),
    };
    const loanSchemaInfo: any = await hsfInvoke({
      id: 'com.alibaba.fin.tao.blp.loan.mtop.api.LoanAccountQueryMtopFacade',
      method: 'query',
      parameterTypes: ['com.alibaba.fin.tao.blp.loan.mtop.api.request.LoanAccountQueryMtopRequest'],
      args: [queryLoanAccountArg],
    });
    return {
      success: true,
      originType: 'SSR',
      data: {
        creditApplyConsult: consultData?.model,
        loanSchemaInfo: loanSchemaInfo?.model,
      },
    };
  } catch (e) {
    return {
      success: false,
      originType: 'SSR',
    };
  }
}

export async function ssdHomePlusCSR(originType: ORIGIN_TYPE) {
  try {
    switchDataOrigin(originType);
    const consultData = await queryCreditApplyConsult();
    if (!consultData?.creditContractStatus || consultData.creditContractStatus === 'CLOSED') {
      return {
        success: true,
        originType,
        data: {
          creditApplyConsult: consultData,
        },
      };
    }
    const loanSchemaInfo = await queryLoanSchemaInfo({
      product: PRODUCT,
      extension: {
        origin: getOriginFromSessionStorage(),
      },
    });
    return {
      success: true,
      originType,
      data: {
        creditApplyConsult: consultData,
        loanSchemaInfo,
      },
    };
  } catch (e) {
    return {
      success: false,
      originType,
    };
  }
}
