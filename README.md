# ice.js 3 Wormhole

## Usage

```bash
$ npm install

$ npm start
```

## Directories

```md
.
├── README.md
├── ice.config.mts                  # The project config
├── package.json
├── .browserslistrc                 # Browsers that we support
├── public
│   ├── favicon.ico   
├── src
│   ├── app.ts                      # The app entry
│   ├── assets
│   │   └── logo.png
│   ├── document.tsx
│   ├── pages                       # Pages directory
│   │   ├── index.module.css
│   │   └── index.tsx               # Index page entry
│   └── typings.d.ts
└── tsconfig.json
```

For more detail, please visit [docs](https://v3.ice.work/).